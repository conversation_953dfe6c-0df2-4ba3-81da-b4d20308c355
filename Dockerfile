FROM hub.iot.chinamobile.com/geom/openjdk-font-zh:8-jre-alpine
ADD ./report-service/target/report-service.tar.gz /opt/fgeo
WORKDIR /opt/fgeo
ENV TZ Asia/Shanghai
EXPOSE 8081
CMD ["java", "-XX:+UseContainerSupport", "-XX:InitialRAMPercentage=50.0", "-XX:MaxRAMPercentage=75.0","-XX:+UnlockExperimentalVMOptions", "-XX:+UseCGroupMemoryLimitForHeap","-XX:SurvivorRatio=8", "-XX:NewRatio=1","-XX:MetaspaceSize=256m","-XX:MaxMetaspaceSize=256m","-XX:+UseConcMarkSweepGC","-XX:ParallelGCThreads=8","-XX:+CMSScavengeBeforeRemark","-XX:+UseCMSInitiatingOccupancyOnly","-XX:CMSInitiatingOccupancyFraction=70","-XX:+ExplicitGCInvokesConcurrent", "-XX:+CMSParallelInitialMarkEnabled", "-XX:+CMSParallelRemarkEnabled" ,"-XX:+ParallelRefProcEnabled", "-XX:-OmitStackTraceInFastThrow", "-XX:+UnlockDiagnosticVMOptions", "-XX:+PrintCompressedOopsMode", "-XX:-UseCounterDecay", "-XX:+AlwaysPreTouch", "-XX:+ExplicitGCInvokesConcurrentAndUnloadsClasses",  "-XX:ConcGCThreads=4", "-Xloggc:./gc.log", "-XX:ErrorFile=./logs/hs_err_.log", "-XX:+PrintGCApplicationStoppedTime", "-XX:+PrintGCDateStamps","-XX:+PrintGCDetails", "-Dfastjson.parser.safeMode=true", "-Dspring.config.location=conf/application.yaml", "-Dlogging.config=conf/logback.xml", "-jar", "report-service.jar"]