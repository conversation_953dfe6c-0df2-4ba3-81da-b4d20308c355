package com.cmiot.report.facade.dto.plugin;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname PluginDetailOverview
 * @Description
 * @Date 2024/1/26 8:59
 * @Created by lei
 */
@Data
public class PluginDetailOverview implements Serializable {
    private static final long serialVersionUID = -1674691079548058261L;


    @JsonSerialize(using = ToStringSerializer.class)
    private Long installTotal;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long monthAddNum;
    private String monthAddRate;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long useNum;
    private String useRate;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long dayActiveNum;
    private String dayActiveRate;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long monthActiveNum;
    private String monthActiveRate;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long versionUpdateNum;
    private String versionUpdateRate;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long deprecatedNum;

    private List<PluginDetailOverviewProvinceInfo> list;

}
