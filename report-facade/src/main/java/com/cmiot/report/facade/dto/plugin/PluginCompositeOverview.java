package com.cmiot.report.facade.dto.plugin;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname PluginCompositeOverview
 * @Description
 * @Date 2024/1/25 8:58
 * @Created by lei
 */
@Data
public class PluginCompositeOverview implements Serializable {
    private static final long serialVersionUID = 2697902433745033872L;

    /**
     * 数据库主键id
     */
    private Integer id;
    /**
     * 插件名称
     */
    private String pluginName;
    /**
     * 插件编号
     */
    private String pluginNum;

    /**
     * 插件提供商-开发者
     */
    private String manufacture;
    /**
     * 插件版本
     */
    private String version;
    /**
     * 是否预置
     */
    private String isPreset;
    /**
     * 创建者
     */
    private String creator;

    /**
     * 插件描述
     */
    private String pluginDes;

    /**
     *  插件总安装量
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long installTotal;
    /**
     * 环比新增
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long monthAddNum;
    private String monthAddRate;
    /**
     * 插件启用率
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long useNum;
    private String useRate;
    /**
     * 日活数量
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dayActiveNum;
    private String dayActiveRate;
    /**
     * 月活数量
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long monthActiveNum;
    private String monthActiveRate;
    /**
     * 疑似弃用
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long deprecatedNum;
    /**
     * 平台分配接入识别号
     */
    private String bundleId;

    /**
     * 每个版本的统计数据
     */
    private List<PluginDetailOverviewProvinceInfo> list;
}
