package com.cmiot.report.facade.gateway;


import com.cmiot.report.bean.GatewayNotOnline;
import com.cmiot.report.dto.*;
import com.cmiot.report.facade.dto.PageRes;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RequestMapping("/v1/m/statistic/gateway")
public interface GatewayFacade {

    /**
     * 网关详细-流量状态
     *
     * @param eid
     * @param sn
     */
    @GetMapping("/{sn}/detail/flowStatus")
    public GatewayDetailResult gatewayDetail(
            @PathVariable("sn") String sn);


    /**
     * 网关版本详细
     *
     * @param GatewayQuery
     */
    @GetMapping("/gatewayVersionStatistic")
    public Map gatewayVersionStatistic(GatewayQuery gatewayQuery, @RequestHeader("province") String province);

    /**
     * 网关流量概览
     *
     * @param GatewayQuery
     */
    @GetMapping("/flowOverview")
    public GatewayFlowTrendResult flowOverview(GatewayQuery gatewayQuery, @RequestHeader("province") String province);

    /**
     * 网关流量趋势图
     *
     * @param GatewayQuery
     */
    @GetMapping("/gatewayRunningAnalysis/gatewayFlowStatistic")
    public List<GatewayFlowTrendResult> gatewayFlowStatistic(GatewayQuery gatewayQuery, @RequestHeader("province") String province);

    /**
     * 网关流量数据列表
     *
     * @param GatewayQuery
     */
    @GetMapping("/gatewayFlowList")
    public Map gatewayFlowList(GatewayQuery gatewayQuery, @RequestHeader("province") String province);

    /**
     * 网关流量数据列表导出
     *
     * @param GatewayQuery
     */
    @GetMapping("/exportGatewayFlowList")
    public ExportGatewayFlowList exportGatewayFlowList(@RequestHeader(value = "uid") Long uid, GatewayQuery gatewayQuery);

    /**
     * 网关流量趋势图
     *
     * @param GatewayQuery
     */
    @GetMapping("/gatewayFlowTrend")
    public List<GatewayFlowTrendResult> gatewayFlowTrend(GatewayQuery gatewayQuery);

    /**
     * 网关cpu、内存均值统计
     *
     * @param GatewayQuery
     */
    @PostMapping("/cpuAndRamOverview")
    public GatewayCpuAndRamResult cpuAndRamOverview(@RequestBody GatewayQuery gatewayQuery, @RequestHeader("province") String province);

    /**
     * 网关cpu、内存均值统计
     *
     * @param GatewayQuery
     */
    @PostMapping("/typeCpuAndRamAverage")
    public Map typeCpuAndRamAverage(@RequestBody GatewayQuery gatewayQuery, @RequestHeader("province") String province);


    /**
     * CPU、内存在各个范围内的数量、占比
     *
     * @param GatewayQuery
     */
    @PostMapping("/rangeCpuAndRamNumRatio")
    public Map<String, Object> rangeCpuAndRamNumRatio(@RequestBody GatewayQuery gatewayQuery, @RequestHeader("province") String province);


    /**
     * 网关cpu ram运行数据 列表明细
     *
     * @param GatewayQuery
     */
    @PostMapping("/gatewayCpuRamDatalist")
    public Map<String, Object> gatewayCpuRamDatalist(@RequestBody GatewayQuery gatewayQuery,
                                                     @RequestHeader("province") String province);

    /**
     * 网关cpu ram运行数据 列表明细导出
     *
     * @param GatewayQuery
     */
    @GetMapping("/exportGatewayCpuRamDatalist")
    public ExportGatewayFlowList exportGatewayCpuRamDatalist(@RequestHeader(value = "uid") Long uid,
                                                             @RequestHeader("province") String province,
                                                             GatewayQuery gatewayQuery);


    /**
     * 网关cpu、内存均值统计
     *
     * @param GatewayQuery
     */
    @PostMapping("/rangeCpuAndRamOverLevelRatio")
    public Map rangeCpuAndRamOverLevelRatio(@RequestBody GatewayQuery gatewayQuery, @RequestHeader("province") String province);



    /**
     * 连接数在各个范围内的数量、占比
     * @param gatewayQuery
     * @return
     */
    @PostMapping("/connectNumber/quantityProportion")
    Map<String, List<ConnectNumberDataDto>> quantityProportion(@RequestBody GatewayQuery gatewayQuery);


    /**
     * 连接数在各个范围内的数量、占比
     * 统计95峰最大值：去除统计周期最高的5%数据，在剩下95%的数据中取最大值
     * @param gatewayQuery
     * @return
     */
    @PostMapping("/connectNumber/quantityProportion95")
    Map<String, List<ConnectNumberDataDto>> quantityProportion95(@RequestBody GatewayQuery gatewayQuery);


    /**
     * 分页查询连接数监控列表
     * @param gatewayQuery
     * @return
     */
    @PostMapping("/connectNumber/detailList")
    Map<String, Object> getPageConnectNumberDetail(@RequestBody GatewayQuery gatewayQuery);

    /**
     * 连接数折线图
     * @param gatewayQuery
     * @return
     */
    @PostMapping("/connectNumber/lineChart")
    Map<String, Object> getConnectNumberLineChart(@RequestBody GatewayQuery gatewayQuery);


    @PostMapping("/connectNumber/getGatewayWanConnServiceList")
    List<Map<String, String>> getGatewayWanConnServiceList(@RequestBody GatewayQuery gatewayQuery);

    /**
     * 导出
     * @param gatewayQuery
     * @param response
     */
    @PostMapping(value = "/connectNumber/detailExport")
    ExportGatewayFlowList export(@RequestHeader(value = "uid") Long uid, GatewayQuery gatewayQuery);

    /**
     * 网关WAN口带宽
     * @param gatewayQuery
     */
    @PostMapping("/dailyFlowMonitor")
    public Map<String, Object> gatewayDailyFlowMonitor(@RequestBody GatewayQuery gatewayQuery);


    /**
     * 用户访问网络分析
     * @param gatewayQuery
     * @return
     */
    @PostMapping("/accessNetworkAnalysis")
    PageRes<AccessNetworkAnalysisResult> accessNetworkAnalysis(@RequestBody GatewayQuery gatewayQuery);

}
