package com.cmiot.report.facade.dto.plugin;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PluginInstallAnalysisRes implements Serializable {

    private List<String> yaxis;

    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private List<Long> installedNumber;

    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private List<Long> totalQuantity;

    private List<String> coverage;

    public PluginInstallAnalysisRes() {
        yaxis=new ArrayList<>();
        installedNumber=new ArrayList<>();
        totalQuantity=new ArrayList<>();
        coverage=new ArrayList<>();
    }
}
