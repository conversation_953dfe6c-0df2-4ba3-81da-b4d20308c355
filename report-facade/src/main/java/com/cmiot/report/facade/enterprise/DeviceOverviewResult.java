package com.cmiot.report.facade.enterprise;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.ToString;

import java.util.List;

/**
 * @Classname GatewayOverviewResult
 * @Description
 * @Date 2023/8/1 9:15
 * @Created by lei
 */
public class DeviceOverviewResult {

    @JsonSerialize(using = ToStringSerializer.class)
    private Integer allNum;
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer dayNum;
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer weekNum;
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer monthNum;
    private List<DeviceOverviewRateInfo> deviceRate;


    public Integer getAllNum() {
        return allNum;
    }

    public void setAllNum(Integer allNum) {
        this.allNum = allNum;
    }

    public Integer getDayNum() {
        return dayNum;
    }

    public void setDayNum(Integer dayNum) {
        this.dayNum = dayNum;
    }

    public Integer getWeekNum() {
        return weekNum;
    }

    public void setWeekNum(Integer weekNum) {
        this.weekNum = weekNum;
    }

    public Integer getMonthNum() {
        return monthNum;
    }

    public void setMonthNum(Integer monthNum) {
        this.monthNum = monthNum;
    }

    public List<DeviceOverviewRateInfo> getDeviceRate() {
        return deviceRate;
    }

    public void setDeviceRate(List<DeviceOverviewRateInfo> deviceRate) {
        this.deviceRate = deviceRate;
    }


    @Override
    public String toString() {
        return "GatewayOverviewResult{" +
                "allNum=" + allNum +
                ", dayNum=" + dayNum +
                ", weekNum=" + weekNum +
                ", monthNum=" + monthNum +
                ", deviceRate=" + deviceRate +
                '}';
    }
}
