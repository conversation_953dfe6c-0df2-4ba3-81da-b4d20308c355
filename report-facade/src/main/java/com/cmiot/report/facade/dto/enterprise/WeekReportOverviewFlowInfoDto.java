package com.cmiot.report.facade.dto.enterprise;


import java.util.List;

/**
 * @Classname WeekReportOverviewResult
 * @Description
 * @Date 2023/12/15 11:29
 * @Created by lei
 */
public class WeekReportOverviewFlowInfoDto {

    private List<String> times;

    private List<String> flows;
    private List<String> averages;

    public List<String> getTimes() {
        return times;
    }

    public void setTimes(List<String> times) {
        this.times = times;
    }

    public List<String> getFlows() {
        return flows;
    }

    public void setFlows(List<String> flows) {
        this.flows = flows;
    }

    public List<String> getAverages() {
        return averages;
    }

    public void setAverages(List<String> averages) {
        this.averages = averages;
    }
}
