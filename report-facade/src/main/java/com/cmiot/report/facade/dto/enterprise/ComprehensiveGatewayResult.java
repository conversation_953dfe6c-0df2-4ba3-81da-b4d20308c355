package com.cmiot.report.facade.dto.enterprise;

/**
 * @Classname ComprehensiveGatewayResult
 * @Description
 * @Date 2023/10/11 17:37
 * @Created by lei
 */
public class ComprehensiveGatewayResult {
    private Long gatewayId;
    private String sn;
    private String mac;
    /**
     * 厂商
     */
    private String factory;
    /**
     * 型号
     */
    private String deviceModel;
    /**
     * 满速运行时长
     */
    private String highLoadingTime;
    /**
     * 总运行时长
     */
    private String runningTime;
    /**
     * 满速运行时长/总运行时长（%）
     */
    private String highLoadingPercent;
    /**
     * 低速运行时长
     */
    private String lowLoadingTime;
    /**
     * 低速运行时长/总运行时长（%）
     */
    private String lowLoadingPercent;
    /**
     * 连续不在线时间
     */
    private String idleTime;
    /**
     * 已使用时间
     */
    private String useTime;
    /**
     * 建议使用时长
     */
    private String planUseTime;
    /**
     * 套餐带宽
     */
    private String bandwidth;

    /**
     * 设备支持带宽
     */
    private String supportBandwidth;
    /**
     * ping时延
     */
    private String pingTime;

    /**
     * 测试最大带宽
     */
    private String speedTestBandwidth;

    /**
     * cpu使用率
     */
    private String cpu;
    /**
     * 内存使用率
     */
    private String ram;

    public Long getGatewayId() {
        return gatewayId;
    }

    public void setGatewayId(Long gatewayId) {
        this.gatewayId = gatewayId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getFactory() {
        return factory;
    }

    public void setFactory(String factory) {
        this.factory = factory;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getHighLoadingTime() {
        return highLoadingTime;
    }

    public void setHighLoadingTime(String highLoadingTime) {
        this.highLoadingTime = highLoadingTime;
    }

    public String getRunningTime() {
        return runningTime;
    }

    public void setRunningTime(String runningTime) {
        this.runningTime = runningTime;
    }

    public String getHighLoadingPercent() {
        return highLoadingPercent;
    }

    public void setHighLoadingPercent(String highLoadingPercent) {
        this.highLoadingPercent = highLoadingPercent;
    }

    public String getLowLoadingTime() {
        return lowLoadingTime;
    }

    public void setLowLoadingTime(String lowLoadingTime) {
        this.lowLoadingTime = lowLoadingTime;
    }

    public String getLowLoadingPercent() {
        return lowLoadingPercent;
    }

    public void setLowLoadingPercent(String lowLoadingPercent) {
        this.lowLoadingPercent = lowLoadingPercent;
    }

    public String getIdleTime() {
        return idleTime;
    }

    public void setIdleTime(String idleTime) {
        this.idleTime = idleTime;
    }

    public String getUseTime() {
        return useTime;
    }

    public void setUseTime(String useTime) {
        this.useTime = useTime;
    }

    public String getPlanUseTime() {
        return planUseTime;
    }

    public void setPlanUseTime(String planUseTime) {
        this.planUseTime = planUseTime;
    }

    public String getBandwidth() {
        return bandwidth;
    }

    public void setBandwidth(String bandwidth) {
        this.bandwidth = bandwidth;
    }

    public String getSupportBandwidth() {
        return supportBandwidth;
    }

    public void setSupportBandwidth(String supportBandwidth) {
        this.supportBandwidth = supportBandwidth;
    }

    public String getPingTime() {
        return pingTime;
    }

    public void setPingTime(String pingTime) {
        this.pingTime = pingTime;
    }

    public String getSpeedTestBandwidth() {
        return speedTestBandwidth;
    }

    public void setSpeedTestBandwidth(String speedTestBandwidth) {
        this.speedTestBandwidth = speedTestBandwidth;
    }

    public String getCpu() {
        return cpu;
    }

    public void setCpu(String cpu) {
        this.cpu = cpu;
    }

    public String getRam() {
        return ram;
    }

    public void setRam(String ram) {
        this.ram = ram;
    }
}
