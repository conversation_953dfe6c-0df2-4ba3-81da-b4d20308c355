package com.cmiot.report.facade.enterprise;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * @Classname SubDeivceGraphDataResultData
 * @Description
 * @Date 2023/8/15 8:58
 * @Created by lei
 */
public class SubDeivceGraphDataResultData {
    private String dateTime;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long allStaDevice;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wireStaDevice;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wirelessStaDevice;

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public Long getAllStaDevice() {
        return allStaDevice;
    }

    public void setAllStaDevice(Long allStaDevice) {
        this.allStaDevice = allStaDevice;
    }

    public Long getWireStaDevice() {
        return wireStaDevice;
    }

    public void setWireStaDevice(Long wireStaDevice) {
        this.wireStaDevice = wireStaDevice;
    }

    public Long getWirelessStaDevice() {
        return wirelessStaDevice;
    }

    public void setWirelessStaDevice(Long wirelessStaDevice) {
        this.wirelessStaDevice = wirelessStaDevice;
    }
}
