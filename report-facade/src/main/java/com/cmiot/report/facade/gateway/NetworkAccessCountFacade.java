package com.cmiot.report.facade.gateway;

import com.cmiot.report.dto.customer.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @Classname NetworkAccessCountFacade
 * @Description
 * @Date 2022/8/9 15:48
 * @Created by lei
 */
public interface NetworkAccessCountFacade {

    /**
     * 智慧运营分析 > 企业客户分析 > 用户行为分析 > 网络使用情况
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/v1/m/statistic/customerAnalysis/networkUsage")
    NetworkUsageChartData getNetworkUsage(@RequestHeader(value = "province", required = false) String province,
                                          @RequestBody @Validated NetworkUsageChartRequest request);

    /**
     * 智慧运营分析 > 企业客户分析 > 用户行为分析 > 网络使用情况 > 设备状态
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/v1/m/statistic/customerAnalysis/networkUsage/deviceStatus")
    NetworkUsagePieData getNetworkUsageDeviceStatus(@RequestHeader(value = "province", required = false) String province,
                                                    @RequestBody @Validated NetworkUsageChartRequest request);


    /**
     * 智慧运营分析 > 企业客户分析 > 用户行为分析 > 网络使用偏好 > 节目偏好
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/v1/m/statistic/customerAnalysis/programPreference")
    NetworkPerferChartData getProgramPreference(@RequestHeader(value = "province", required = false) String province,
                                                @RequestBody @Validated NetworkPerferRequest request);

    /**
     * 智慧运营分析 > 企业客户分析 > 用户行为分析 > 网络使用偏好 > 网站偏好
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/v1/m/statistic/customerAnalysis/sitePreference")
    NetworkPerferChartData getSitePreference(@RequestHeader(value = "province", required = false) String province,
                                             @RequestBody @Validated NetworkPerferRequest request);
}
