package com.cmiot.report.facade.enterprise;

/**
 * @Classname DeviceGraphData
 * @Description
 * @Date 2023/8/1 11:26
 * @Created by lei
 */
public class DeviceGraphDataResult {

    private String time;
    private Integer allDeviceNum;
    private Integer onlineDeviceNum;

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Integer getAllDeviceNum() {
        return allDeviceNum;
    }

    public void setAllDeviceNum(Integer allDeviceNum) {
        this.allDeviceNum = allDeviceNum;
    }

    public Integer getOnlineDeviceNum() {
        return onlineDeviceNum;
    }

    public void setOnlineDeviceNum(Integer onlineDeviceNum) {
        this.onlineDeviceNum = onlineDeviceNum;
    }

    @Override
    public String toString() {
        return "DeviceGraphDataResult{" +
                "time='" + time + '\'' +
                ", allDeviceNum=" + allDeviceNum +
                ", onlineDeviceNum=" + onlineDeviceNum +
                '}';
    }
}
