package com.cmiot.report.facade.customer;


import com.cmiot.report.dto.ServiceStatisticRequest;
import com.cmiot.report.dto.ServiceStatisticResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 客服统计分析
 */

@RequestMapping("/v1/m/statistic")
public interface ServiceStatisticFacade {

    /**
     * 客服统计分析
     *
     * @return
     */
    @PostMapping(value = "/customerAnalysis/customerServiceAnalysis")
    ServiceStatisticResult getCustomerServiceStatistic(@RequestHeader(value = "province", required = false) String province,
                                                       @RequestBody ServiceStatisticRequest request);


}
