package com.cmiot.report.facade.device;

import com.cmiot.report.dto.device.*;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

public interface DeviceFacade {

    /**
     * web
     * 网关分布全景视图概览-下挂设备情况概览（无需查询参数）
     * @param province
     */
    @RequestMapping(value = "/v1/m/statistic/overview/underDevice", method = RequestMethod.GET)
    DeviceOverviewCount queryDeviceOverviewCount(@RequestHeader(value = "province", required = false) String province);

    /**
     * web
     * 获取网关下挂设备数量（带查询参数）
     */
    @RequestMapping(value = "/v1/m/statistic/totalUnderDevice", method = RequestMethod.POST)
    DeviceOverviewResult queryDeviceOverviewResult(@RequestHeader(value = "province", required = false)String province,
                                                   @RequestBody DeviceQuery deviceQuery);

    /**
     * web
     * 下挂设备WLAN信号强度分析
     */
    @RequestMapping(value = "/v1/m/statistic/hangingDevice/signalIntensity", method = RequestMethod.POST)
    DeviceWLANResult queryDeviceWLANResult(@RequestBody DeviceQuery deviceQuery, @RequestHeader("province") String province);

    /**
     * 小程序
     * 用户设备累积量、活跃量查询 1070102
     */
    @RequestMapping(value = "/v1/e/statistic/total/subDevice", method = RequestMethod.GET)
    DeviceAppStatCount queryDeviceAppStatCount(@RequestHeader("eid") Long enterpriseId);

    /**
     * 小程序
     * 用户设备活跃量趋势、平均无线信号强度统计 1070101
     */
    @RequestMapping(value = "/v1/e/statistic/trend/subDevice", method = RequestMethod.GET)
    Map queryDeviceAppStatTrend(@RequestHeader("eid") Long enterpriseId,
                                @RequestParam String startDate,
                                @RequestParam String endDate);
}
