package com.cmiot.report.facade.gateway;


import com.cmiot.report.dto.*;
import com.cmiot.report.facade.dto.PageRes;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RequestMapping("/v1/e/statistic")
public interface GatewayAppFacade {

    /**
     * 网关流量趋势图
     *
     * @param GatewayQuery
     */
    @GetMapping("/trend/gatewayFlow")
    public List<GatewayFlowTrendResult> gatewayFlowTrend(GatewayQuery gatewayQuery, @RequestHeader("eid") String eid);

    /**
     * 网关cpu、内存均值统计
     *
     * @param GatewayQuery
     */
    @GetMapping("/trend/cpuMemory")
    public Map typeCpuAndRamAverage(GatewayQuery gatewayQuery, @RequestHeader("eid") String eid);


    /**
     * 运行报告
     *
     * @param GatewayQuery
     */
    @GetMapping("/runReport")
    public Map runReport(GatewayQuery gatewayQuery, @RequestHeader("eid") Long eid);


    /**
     * 运行时长列表
     *
     * @param GatewayQuery
     */
    @GetMapping("/runReport/gatewayList")
    public List<GatewayRunTimeDetail> gatewayList(GatewayQuery gatewayQuery, @RequestHeader("eid") Long eid);


    /**
     * 组网设备运行超时列表
     *
     * @param request
     * @param eid
     * @return
     */
    @GetMapping("/runReport/networkDeviceList")
    public List<NetworkDeviceRunTimeDetail> networkDeviceList(NetworkDeviceRequset request, @RequestHeader("eid") Long eid);


    /**
     * 根据企业用户绑定设备查询相关网关的上下行流速
     * @param eid
     * @return
     */
    @GetMapping("/gatewayFlowVelocityList")
    List<Object> getGatewayFlowVelocityList(@RequestHeader("eid") Long eid);


    @PostMapping("/getPageGatewayFlowVelocity")
    PageRes<Map<String, Object>> getPageGatewayFlowVelocity(@RequestHeader(value = "uid") Long uid,
                                                            @RequestHeader("eid") Long eid,
                                                            @RequestBody GatewayQuery gatewayQuery);

    //前端轮询获取网关流量
    @GetMapping("/getPollingRealtimeFlowVelocityMonitorReport")
    RealtimeFlowVelocityMessageVO getPollingRealtimeFlowVelocityMonitorReport(@RequestHeader("eid") Long eid,
                                                            GatewayQuery gatewayQuery);


    @GetMapping("/getGatewayWanConnNumList")
    List<Map<String, Object>> getGatewayWanConnNumList(@RequestHeader("eid") Long eid);

    @PostMapping("/getPageGatewayWanConnNum")
    PageRes<Map<String, Object>> getPageGatewayWanConnNum(@RequestHeader(value = "uid") Long uid,
                                                          @RequestHeader("eid") Long eid,
                                                          @RequestBody GatewayQuery gatewayQuery);

    @GetMapping("/getPollingRealtimeWanConnReport")
    RealtimeWanConnMessageVO getPollingRealtimeWanConnReport(@RequestHeader("eid") Long eid,
                                                        GatewayQuery gatewayQuery);
}
