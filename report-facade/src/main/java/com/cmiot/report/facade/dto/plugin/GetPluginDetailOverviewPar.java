package com.cmiot.report.facade.dto.plugin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname GetPluginDetailOverviewPar
 * @Description
 * @Date 2024/1/26 9:18
 * @Created by lei
 */
@Data
public class GetPluginDetailOverviewPar implements Serializable {
    private static final long serialVersionUID = -5664601313868804017L;

    private Integer id;
    private List<String> province;
    private List<String> factory;
    private List<String> model;
    private String startTime;
    private String endTime;
    private String version;
}
