package com.cmiot.report.facade.dto.enterprise;

/**
 * @Classname GetComprehensiveGatewayParam
 * @Description
 * @Date 2023/10/11 17:24
 * @Created by lei
 */
public class GetComprehensiveGatewayParam {
    private Long eid;
    private Integer page;
    private Integer pageSize;

    /**
     *    highLoading 高负载网关;
     *    lowLoading  低负载网关;
     *    idle 空闲网关;
     *    overLife 超期使用设备;
     *    bandwidthNonsupport 带宽不匹配网关;
     *    netBadPing ping网络质量差;
     *    netBadSpeed 测速质量差;
     *    cpuRam cpu内存告警网关
     */
    private String queryType;

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }
}
