package com.cmiot.report.facade.customer;

import com.cmiot.report.dto.customerOrder.*;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 *  1. 线路业务全景视图
 *  2. 套餐订购情况运营分析
 */
@RequestMapping("/v1/m/statistic/customerOrder")
public interface CustomerOrderFacade {

    // 1.1 业务量总览饼图
    @GetMapping("/overviewPieChart")
    OverviewPieChartData customerOrderOverviewPieChart(@RequestHeader(value = "province", required = false) String province);

    // 1.2 业务量总览柱状图
    @GetMapping("/overviewHistogram")
    List<OverviewHistogramData> customerOrderOverviewHistogram(@RequestHeader(value = "province", required = false) String province);

    // 1.2 业务发展趋势折线图
    @PostMapping("/trendLineChart")
    List<OrderTrendLineChartData> customerOrderTrendLineChart(@RequestHeader(value = "province", required = false) String province,
                                                        @RequestBody OrderTrendLineChartRequest request);

    // 2.1 累计套餐订购情况
    @PostMapping("/allPackageOrderStatus")
    Map<String, Object> getCumulativePackageSubscription(@RequestHeader(value = "province", required = false) String province,
                                                         @RequestBody PackageSubscriptionRequest request);

    // 2.2 新增套餐订购情况
    @PostMapping("/newPackageOrderStatus")
    Map<String, Object> getNewPackageSubscription(@RequestHeader(value = "province", required = false) String province,
                                                  @RequestBody PackageSubscriptionRequest request);

    // 单个省份套餐总量/新增
    @PostMapping("/packagesNumByProvince")
    Map<String, Object> getSinglePackageSubscription(@RequestHeader(value = "province", required = false) String province,
                                                     @RequestBody PackageSubscriptionRequestExtend request);

    // 2.3 套餐增长趋势折线图
    @PostMapping("/packagesIncreaseTrend")
    List<PackageGrowthTrendLineChartData> getPackageGrowthTrendLineChart(@RequestHeader(value = "province", required = false) String province,
                                                                         @RequestBody PackageGrowthTrendRequest request);

    // 2.4 套餐增长趋势柱状图
    @PostMapping("/packagesIncreaseTrendByArea")
    List<PackageGrowthTrendHistogramData> getPackageGrowthTrendHistogram(@RequestHeader(value = "province", required = false) String province,
                                                                         @RequestBody PackageGrowthTrendRequest request);

    // 2.5 带宽统计分析
    @PostMapping("/bandWidthStatisticAnalysis")
    List<BandwidthStatisticalData> getBandwidthStatisticalAnalysis(@RequestHeader(value = "province", required = false) String province,
                                                                   @RequestBody BandwidthStatisticalRequest request);

}
