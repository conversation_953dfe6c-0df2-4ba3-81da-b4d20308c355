package com.cmiot.report.facade.enterprise;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * @Classname SubDeivceGraphDataResultDataRate
 * @Description
 * @Date 2023/8/15 8:57
 * @Created by lei
 */
public class SubDeivceGraphDataResultDataRate {
    private String name;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long num;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getNum() {
        return num;
    }

    public void setNum(Long num) {
        this.num = num;
    }
}
