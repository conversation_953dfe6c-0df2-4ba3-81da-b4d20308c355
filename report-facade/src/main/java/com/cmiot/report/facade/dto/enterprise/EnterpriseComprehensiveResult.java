package com.cmiot.report.facade.dto.enterprise;

/**
 * @Classname EnterpriseComprehensiveResponse
 * @Description
 * @Date 2023/10/11 15:14
 * @Created by lei
 */
public class EnterpriseComprehensiveResult {
    /**
     * 企业行业
     */
    private String industry;
    /**
     * 所属区域
     */
    private String area;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 业务办理量
     */
    private Integer businessCount;
    /**
     * 套餐数量
     */
    private Integer packageCount;
    /**
     * 网关数量
     */
    private Integer gatewayCount;
    /**
     * 上月度长时间高带宽运行设备数
     */
    private Integer highLoadingGatewayCount;
    /**
     * 上月度长时间低带宽设备数
     */
    private Integer lowLoadingGatewayCount;
    /**
     * 长时间未使用设备数
     */
    private Integer longTimeIdleCount;
    /**
     * 设备使用超期数量
     */
    private Integer overLifeCount;
    /**
     * 设备支持带宽低
     */
    private Integer bandwidthNonsupport;
    /**
     * 网速不达标
     */
    private Integer networkSpeedExp;
    /**
     * 网络质量异常
     */
    private Integer networkPingExp;
    /**
     * Cpu/内存高占用
     */
    private Integer cpuRamExp;


    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getCustomerStatus() {
        return customerStatus;
    }

    public void setCustomerStatus(String customerStatus) {
        this.customerStatus = customerStatus;
    }

    public Integer getBusinessCount() {
        return businessCount;
    }

    public void setBusinessCount(Integer businessCount) {
        this.businessCount = businessCount;
    }

    public Integer getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(Integer packageCount) {
        this.packageCount = packageCount;
    }

    public Integer getGatewayCount() {
        return gatewayCount;
    }

    public void setGatewayCount(Integer gatewayCount) {
        this.gatewayCount = gatewayCount;
    }

    public Integer getHighLoadingGatewayCount() {
        return highLoadingGatewayCount;
    }

    public void setHighLoadingGatewayCount(Integer highLoadingGatewayCount) {
        this.highLoadingGatewayCount = highLoadingGatewayCount;
    }

    public Integer getLowLoadingGatewayCount() {
        return lowLoadingGatewayCount;
    }

    public void setLowLoadingGatewayCount(Integer lowLoadingGatewayCount) {
        this.lowLoadingGatewayCount = lowLoadingGatewayCount;
    }

    public Integer getLongTimeIdleCount() {
        return longTimeIdleCount;
    }

    public void setLongTimeIdleCount(Integer longTimeIdleCount) {
        this.longTimeIdleCount = longTimeIdleCount;
    }

    public Integer getOverLifeCount() {
        return overLifeCount;
    }

    public void setOverLifeCount(Integer overLifeCount) {
        this.overLifeCount = overLifeCount;
    }

    public Integer getBandwidthNonsupport() {
        return bandwidthNonsupport;
    }

    public void setBandwidthNonsupport(Integer bandwidthNonsupport) {
        this.bandwidthNonsupport = bandwidthNonsupport;
    }

    public Integer getNetworkSpeedExp() {
        return networkSpeedExp;
    }

    public void setNetworkSpeedExp(Integer networkSpeedExp) {
        this.networkSpeedExp = networkSpeedExp;
    }

    public Integer getNetworkPingExp() {
        return networkPingExp;
    }

    public void setNetworkPingExp(Integer networkPingExp) {
        this.networkPingExp = networkPingExp;
    }

    public Integer getCpuRamExp() {
        return cpuRamExp;
    }

    public void setCpuRamExp(Integer cpuRamExp) {
        this.cpuRamExp = cpuRamExp;
    }
}
