package com.cmiot.report.facade.gateway;

import com.cmiot.report.dto.plugin.PluginBaseInfo;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.bean.plugin.EkitRestartAlertAnalysisParam;
import com.cmiot.report.facade.dto.plugin.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 插件分析
 * <AUTHOR>
 */
public interface PluginReportFacade {
    @PostMapping("/v1/m/statistic/plugin/InstallationAnalysis")
    PluginInstallAnalysisRes getInstallationAnalysis(@RequestBody PluginInstallAnalysisParam param);

    @PostMapping("/v1/m/statistic/plugin/ekitRestartAlertAnalysis")
    PageRes<EkitRestartAlertAnalysisRes> ekitRestartAlertAnalysis(@RequestBody EkitRestartAlertAnalysisParam param);

    @PostMapping("/v1/m/statistic/plugin/ekitRestartNumberList")
    List<String> ekitRestartNumberList(@RequestBody EkitRestartNumberListParam param);

    @GetMapping("/v1/m/statistic/plugininfo/overview")
    PageRes<PluginCompositeOverview> getPluginInfoOverview(@RequestParam("column") Integer column,
                                                           @RequestParam("order") Integer order,
                                                           @RequestParam("page") Integer page,
                                                           @RequestParam("pageSize") Integer pageSize);


    @PostMapping("/v1/m/statistic/plugininfo/detail")
    PluginDetailOverview getPluginDetailOverview(@RequestBody GetPluginDetailOverviewPar getPluginDetailOverviewPar);


    /**
     * OSGi插件运营--统计总览
     */
    @GetMapping(value = "/v1/m/statistic/osgiPluginInfo/overview")
    PageRes<PluginCompositeOverview> getPluginInfoStatistic(@RequestParam("column") Integer column,
                                                            @RequestParam("order") Integer order,
                                                            @RequestParam("page") Integer page,
                                                            @RequestParam("pageSize") Integer pageSize);

    /**
     * OSGi插件运营--统计详情
     */
    @PostMapping(value = "/v1/m/statistic/osgiPluginInfo/detail")
    PluginDetailOverview getPluginInfoStatisticDetail(@RequestBody GetPluginDetailOverviewPar pluginInfoStatisticDetailPar);


    /**
     * OSGi插件运营--插件详情
     */
    @GetMapping(value = "v1/m/statistic/pluginInfo")
    PluginBaseInfo getPluginInfo(@RequestParam("id") Integer pluginId);

    /**
     * 聚合所有的版本号用于条件查询参数
     */
    @GetMapping(value = "v1/m/statistic/getListPluginVersion")
    List<String> getListPluginVersion(@RequestParam("id") Integer pluginId);
}
