package com.cmiot.report.facade.customer;


import com.cmiot.report.dto.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 企业客户全景视图
 */

@RequestMapping("/v1/m/statistic")
public interface CustomerAllViewFacade {

    /**
     * 企业客户区域分布
     *
     * @return
     */
    @PostMapping(value = "/companyCustomerDistribution")
    EnterCustDistResult getEnterpriseCustomerDistribution(@RequestHeader(value = "province", required = false) String province,
                                                          @RequestBody EnterCustomerRequest request);


    /**
     * 新增企业客户统计
     *
     * @return
     */
    @PostMapping(value = "/newCustomerStatistic")
    NewCustomerStatisticResult getNewCustomerStatistic(@RequestHeader(value = "province", required = false) String province,
                                                       @RequestBody EnterCustomerRequest request);


    /**
     * 企业存量客户统计
     *
     * @return
     */
    @PostMapping(value = "/hadCustomerStatistic")
    CustomerStatisticResult getCustomerStatistic(@RequestHeader(value = "province", required = false) String province,
                                                 @RequestBody EnterCustomerRequest request);


    /**
     * 企业行业新增客户统计
     *
     * @return
     */
    @PostMapping(value = "/companyIndustryStatistic")
    NewIndustryCustomerResult getNewIndustryCustomerStatistic(@RequestHeader(value = "province", required = false)String province,
                                                              @RequestBody EnterCustomerRequest request);


}
