package com.cmiot.report.facade.dto.enterprise;

/**
 * @Classname WeekReportSubDeviceConnectDurationDto
 * @Description
 * @Date 2023/12/20 18:47
 * @Created by lei
 */
public class SubDeviceConnectDurationDto {
    private String deviceType;

    private String deviceName;

    private String deviceMac;

    /**
     * 时间 单位小时
     */
    private String duration;


    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceMac() {
        return deviceMac;
    }

    public void setDeviceMac(String deviceMac) {
        this.deviceMac = deviceMac;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }
}
