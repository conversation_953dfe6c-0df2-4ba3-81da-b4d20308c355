package com.cmiot.report.facade.dto.enterprise;

/**
 * @Classname SubDeviceConnectFlowDto
 * @Description
 * @Date 2023/12/20 18:48
 * @Created by lei
 */
public class SubDeviceConnectFlowDto {
    private String deviceType;

    private String deviceName;

    private String deviceMac;

    /**
     * 最多保留两位小数，单位GB
     */
    private String flow;

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceMac() {
        return deviceMac;
    }

    public void setDeviceMac(String deviceMac) {
        this.deviceMac = deviceMac;
    }

    public String getFlow() {
        return flow;
    }

    public void setFlow(String flow) {
        this.flow = flow;
    }
}
