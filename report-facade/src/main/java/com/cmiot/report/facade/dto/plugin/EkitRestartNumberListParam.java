package com.cmiot.report.facade.dto.plugin;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class EkitRestartNumberListParam implements Serializable {

    private String sn;

    /**
     *告警日期
     */
    @JsonFormat(pattern="yyyy年MM月dd日",timezone = "GMT+8")
    private Date alarmTime;

}
