package com.cmiot.report.facade.gateway;

import com.cmiot.report.GatewayOldResult;
import com.cmiot.report.dto.ExportGatewayFlowList;
import com.cmiot.report.dto.GatewayCpuAndRamResult;
import com.cmiot.report.dto.GatewayDelayDeviceResult;
import com.cmiot.report.dto.GatewayQuery;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RequestMapping("/v1/m/statistic/oldGateway")
public interface GatewayOldCountFacade {

    /**
     * 老旧网关数量与占比统计
     *
     * @param gatewayQuery
     */
    @PostMapping("/oldGatewayOverview")
    public GatewayOldResult oldGatewayOverview(@RequestBody GatewayQuery gatewayQuery, @RequestHeader("province") String province);


    /**
     * 老旧网关在各个范围内的数量、占比
     *
     * @param gatewayQuery
     */
    @PostMapping("/rangeOldGatewayNumRatio")
    public Map<String, Object> rangeOldGatewayNumRatio(@RequestBody GatewayQuery gatewayQuery, @RequestHeader("province") String province);


    /**
     * 老旧网关数据 列表明细
     *
     * @param gatewayQuery
     */
    @PostMapping("/oldGatewayDatalist")
    public Map<String, Object> oldGatewayDatalist(@RequestBody GatewayQuery gatewayQuery,
                                                     @RequestHeader("province") String province);

    /**
     *老旧网关数据 列表明细导出
     *
     * @param gatewayQuery
     */
    @GetMapping("/exportoldGatewayDatalist")
    public ExportGatewayFlowList exportOldGatewayDatalist(@RequestHeader(value = "uid") Long uid,
                                                             @RequestHeader("province") String province,
                                                             GatewayQuery gatewayQuery);

    /**
     * 根据网关SN和时间段查询设备时延信息
     *
     * @param gatewayQuery 查询条件（包含sn、startTime、endTime）
     */
    @PostMapping("/queryDelayDevicesBySn")
    public List<GatewayDelayDeviceResult> queryDelayDevicesBySn(@RequestBody GatewayQuery gatewayQuery);
}
