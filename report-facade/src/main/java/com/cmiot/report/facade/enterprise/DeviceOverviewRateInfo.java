package com.cmiot.report.facade.enterprise;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * @Classname GatewayOverviewRateInfo
 * @Description
 * @Date 2023/8/1 9:16
 * @Created by lei
 */
public class DeviceOverviewRateInfo {
    private String name;
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer deviceNum;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getDeviceNum() {
        return deviceNum;
    }

    public void setDeviceNum(Integer deviceNum) {
        this.deviceNum = deviceNum;
    }

    @Override
    public String toString() {
        return "GatewayOverviewRateInfo{" +
                "name='" + name + '\'' +
                ", deviceNum=" + deviceNum +
                '}';
    }
}
