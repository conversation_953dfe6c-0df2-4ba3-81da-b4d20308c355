package com.cmiot.report.facade.dto.plugin;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PluginInstallAnalysisParam implements Serializable {
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private List<Long> vendor;
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private List<Long> model;
    private List<String> version;
    private String plugin;

    /**
     * 统计类型 1安装量2覆盖率
     */
    private Integer statisticType;

}
