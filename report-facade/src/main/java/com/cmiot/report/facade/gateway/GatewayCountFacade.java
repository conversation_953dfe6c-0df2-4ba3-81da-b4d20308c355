package com.cmiot.report.facade.gateway;


import com.cmiot.report.dto.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RequestMapping("/v1")
public interface GatewayCountFacade {


    /**
     * 网关概览
     *
     * @return
     * @param province
     */
    @GetMapping(value = "/m/statistic/overview/gateway")
    GatewayOverviewResult gatewayOverview(@RequestHeader(value = "province", required = false) String province);


    /**
     * 关全景视图—网关设备总量
     *
     *
     * @param province
     * @param request
     * @return
     */
    @PostMapping(value = "/m/statistic/totalDevice")
    GatewayCountResult getGatewayCount(@RequestHeader(value = "province", required = false) String province,
                                       @RequestBody @Validated GatewayCountRequest request);


    /**
     * 网关全景视图—新增网关设备
     *
     *
     * @param province
     * @param request
     * @return
     */
    @PostMapping(value = "/m/statistic/addDevice")
    GatewayIncrementCountResult getGatewayIncrementCount(@RequestHeader(value = "province", required = false) String province,
                                                         @RequestBody @Validated GatewayIncrementCountRequst request);


    /**
     * 网关运行分析—网关在线离线数量
     *
     * @param request
     * @return
     */
    @GetMapping(value = "/m/statistic/gatewayRunningAnalysis/gatewayOnOfflineNumStatistic")
    List<GatewayOnOffLineNumResult> gatewayOnOfflineNumStatistic(@Validated GatewayOnOfflineCountRequest request,
                                                                 @RequestHeader("province") String province);


    /**
     * 网关运行分析—网关在线离线数量概览
     *
     * @param request
     * @return
     */
//    @GetMapping(value = "/m/statistic/gatewayRunningAnalysis/overview")
//    GatewayOnOffLineNumOverviewResult gatewayOnOfflineNumOverviewStatistic(@Validated GatewayOnOfflineCountRequest request);


    /**
     * 小程序-在线离线网关数据统计
     *
     * @param eid
     * @param request
     * @return
     */
    @GetMapping(value = "/e/statistic/trend/onlineOffline")
    MiniProgramOnOfflineCountResult gatewayOnOfflineTrendStatistic(@Validated @RequestHeader("eid") String eid, MiniProgramOnOfflineCountRequest request);


    /**
     * 网关质量分析—网关告警阈值设置
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/m/statistic/terminalAnalysis/alarmThreshold")
    void setAlarmThreshold(@RequestBody AlarmThresholdRequest request);


    /**
     * 网关质量分析—网关告警阈值查询
     *
     * @return
     */
    @GetMapping(value = "/m/statistic/terminalAnalysis/getAlarmThreshold")
    AlarmThresholdResult getAlarmThreshold();


    /**
     * 网关质量分析—网关告警分析
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/m/statistic/terminalAnalysis/alarm")
    GatewayAlarmCountResult getGatewayAlarmCount(@RequestBody GatewayAlarmRequst request, @RequestHeader("province") String province);


    /**
     * 网关质量分析—网关告警列表
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/m/statistic/terminalAnalysis/alarmList")
    GatewayAlarmListResult getGatewayAlarmList(@RequestBody GatewayAlarmListRequst request, @RequestHeader("province") String province);


    /**
     * 网关质量分析—网关PON口统计数据
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/m/statistic/ponStatistic")
    PonStatisticResult getGatewayPonStatistic(@RequestHeader(value = "province", required = false) String province,
                                              @RequestBody PonStatisticRequst request);


    /**
     * 网关质量分析—网关PON口列表明细
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/m/statistic/ponList")
    PonListResult getGatewayPonList(@RequestHeader(value = "province", required = false) String province,
                                    @RequestBody PonListRequst request);


    /**
     * 网关质量分析—网关PON口列表明细导出
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/m/statistic/ponListExport")
    String getGatewayPonListExport(@RequestBody PonListExportRequest request,
                                   @RequestHeader(value = "province", required = false) String province,
                                   @RequestHeader("uid") String uid);

}
