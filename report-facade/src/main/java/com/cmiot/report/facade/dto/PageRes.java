package com.cmiot.report.facade.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageRes<T>  implements Serializable {

   // @JsonSerialize(using = ToStringSerializer.class)
    private Integer page;
   // @JsonSerialize(using = ToStringSerializer.class)
    private Integer pageSize;
    //@JsonSerialize(using = ToStringSerializer.class)
    private Long total;

    private List<T> list;
}
