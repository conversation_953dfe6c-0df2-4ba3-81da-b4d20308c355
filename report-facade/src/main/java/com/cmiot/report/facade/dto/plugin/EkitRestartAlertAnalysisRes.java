package com.cmiot.report.facade.dto.plugin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class EkitRestartAlertAnalysisRes implements Serializable {

    /**
     * 告警等级 1普通 2重要 3严重
     */
    private Integer alarmLevel;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long restartsNumber;

    /**
     *告警日期
     */
    @JsonFormat(pattern="yyyy年MM月dd日",timezone = "GMT+8")
    private Date alarmTime;

    private String province;

    private String businessName;
    private String vendor;
    private String model;
    private String sn;
    private String mac;
    private String deviceType;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long gatewayId;
}
