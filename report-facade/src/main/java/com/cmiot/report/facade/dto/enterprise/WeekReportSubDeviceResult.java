package com.cmiot.report.facade.dto.enterprise;

import java.util.List;

/**
 * @Classname WeekReportVisitsResult
 * @Description
 * @Date 2023/12/20 17:53
 * @Created by lei
 */
public class WeekReportSubDeviceResult {
    private String subDeviceCount;
    private List<SubDeviceConnectDurationDto> subDeviceConnectduration;
    private List<SubDeviceConnectFlowDto> flowAnalysis;

    public String getSubDeviceCount() {
        return subDeviceCount;
    }

    public void setSubDeviceCount(String subDeviceCount) {
        this.subDeviceCount = subDeviceCount;
    }

    public List<SubDeviceConnectDurationDto> getSubDeviceConnectduration() {
        return subDeviceConnectduration;
    }

    public void setSubDeviceConnectduration(List<SubDeviceConnectDurationDto> subDeviceConnectduration) {
        this.subDeviceConnectduration = subDeviceConnectduration;
    }

    public List<SubDeviceConnectFlowDto> getFlowAnalysis() {
        return flowAnalysis;
    }

    public void setFlowAnalysis(List<SubDeviceConnectFlowDto> flowAnalysis) {
        this.flowAnalysis = flowAnalysis;
    }
}
