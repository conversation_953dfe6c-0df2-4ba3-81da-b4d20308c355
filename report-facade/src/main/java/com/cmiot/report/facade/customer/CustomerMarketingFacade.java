package com.cmiot.report.facade.customer;

import com.cmiot.report.dto.ExportDataResult;
import com.cmiot.report.dto.customer.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @Classname CustomerMarketingFacade
 * @Description
 * @Date 2022/8/25 11:54
 * @Created by lei
 */
public interface CustomerMarketingFacade {

    /**
     * 企业客户分析 --> 精准客户营销
     * @param province
     * @param request
     * @return
     */
    @PostMapping(value = "/v1/m/statistic/customerAnalysis/targetCustomerDistribution")
    CustomerMarketingDistributionData targetCustomerDistribution(@RequestHeader(value = "province", required = false) String province,
                                                                 @RequestBody TargetCustomerDistributionRequest request);

    @PostMapping(value = "/v1/m/statistic/customerAnalysis/changesInTargetUsers")
    ChangesInTargetUsersData changesInTargetUsers(@RequestHeader(value = "province", required = false) String province,
                                                  @RequestBody ChangesInTargetUsersRequest request);

    @PostMapping(value = "/v1/m/statistic/customerAnalysis/precisionMarketingCustomerList")
    MarketingCustomerListData precisionMarketingCustomerList(@RequestHeader(value = "province", required = false) String province,
                                                             @RequestBody MarketingCustomerListRequest request);

    @PostMapping(value = "/v1/m/statistic/customerAnalysis/precisionMarketingCustomerList/export")
    ExportDataResult precisionMarketingCustomerListExport(@RequestHeader("uid") Long uid,
                                                          @RequestHeader(value = "province", required = false) String province,
                                                          @RequestBody MarketingCustomerListRequest request);
}
