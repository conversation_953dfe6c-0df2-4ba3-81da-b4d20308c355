package com.cmiot.report.facade.customer;


import com.cmiot.report.bean.CustomerUneffect;
import com.cmiot.report.bean.GatewayAnomalyRecord;
import com.cmiot.report.bean.GatewayNotOnline;
import com.cmiot.report.dto.*;
import com.cmiot.report.facade.dto.PageRes;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 离网预警
 */

@RequestMapping("/v1/m/statistic/customerAnalysis")
public interface CustomerOffGridFacade {

    /**
     * 1.获取离网因素
     *
     * @return
     */
    @GetMapping(value = "/offNetworkFactor")
    List getOffGridFactorThreshold();


    /**
     * 2.获取离网因素详情
     *
     * @return
     */
    @GetMapping(value = "/offNetworkFactor/{factorKey}")
    OffGridFactorThresholdDetail getOffGridFactorThresholdDetail(@PathVariable("factorKey") String factorKey);


    /**
     * 3.设置离网因素指标权重
     *
     * @param factorSet
     * @return
     */
    @PutMapping(value = "/offNetworkFactor/setOneSynthetical")
    void setOffGridFactorIndicaterThreshold(@RequestBody OffGridFactorIndicatorThresholdSet factorSet);


    /**
     * 4.设置综合离网指数阈值
     *
     * @param threshold
     * @return
     */
    @PutMapping(value = "/offNetworkFactor/setSynthetical")
    void setOffGridOverallFactorThreshold(@RequestBody Object threshold);


    // 5.获取综合离网指数信息
    @GetMapping(value = "/offNetworkFactor/synthetical")
    int getOffGridOverallFactorThreshold();


    /**
     * 6.设置离网因素权重阈值
     *
     * @param request
     * @return
     */
    @PutMapping(value = "/offNetworkFactor/syntheticalAll")
    void setOffGridFactorThreshold(@RequestBody SetOffGridFactorThresholdRequest request);


    /**
     * 7.获取客户状态
     *
     * @return
     */
    @GetMapping(value = "/customerStatus")
    List<BassType> getCustomerStatus();


    /**
     * 8.获取企业价值分类
     *
     * @return
     */
    @GetMapping(value = "/enterpriseValueCategory")
    List<BassType> getEnterpriseValueCategory();


    /**
     * 9.查询离网预计信息统计值
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/offNetworkStatistics")
    OffGridCustStatisticResult getOffGridCustStatistic(@RequestHeader(value = "province", required = false) String province,
                                                       @RequestBody OffGridCustStatisticRequest request);

    /**
     * 10.查询离网预警信息列表
     *
     * @return
     */
    @PostMapping(value = "/offNetworkList")
    OffGridCustListResult getOffGridCustList(@RequestHeader(value = "province", required = false) String province,
                                             @RequestBody OffGridCustListRequest request);

    /**
     * 11.导出离网预计信息列表
     */
    @PostMapping(value = "/offNetworkList/export")
    String exportOffGridCustomer(@RequestBody OffGridCustExportRequest request, @RequestHeader(value = "province", required = false) String province,
                                 @RequestHeader("uid") String uid);

    @PostMapping("/netQuality/list")
    PageRes queryAnomalyRecordList(@RequestHeader(value = "uid") Long uid, @RequestHeader("province") String province,@RequestBody GatewayAnomalyRecordQuery query);

    @PostMapping("/idleDevice")
    PageRes<GatewayNotOnline> queryNotOnlineList(@RequestHeader(value = "uid") Long uid, @RequestHeader("province") String province,@RequestBody GatewayAnomalyRecordQuery query);

    @PostMapping("/contractExpirationUser")
    PageRes<CustomerUneffect> queryUneffect(@RequestHeader(value = "uid") Long uid, @RequestHeader("province") String province,@RequestBody GatewayAnomalyRecordQuery query);
}
