package com.cmiot.report.facade.gateway;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

import com.cmiot.report.dto.pppoe.PppoeFailListRequest;
import com.cmiot.report.dto.pppoe.PppoeFailListResult;
import com.cmiot.report.dto.pppoe.PppoeListExportRequest;
import com.cmiot.report.dto.pppoe.PppoeListExportResult;
import com.cmiot.report.dto.pppoe.PppoeListRequest;
import com.cmiot.report.dto.pppoe.PppoeListResult;
import com.cmiot.report.dto.pppoe.PppoeStatisticRequest;
import com.cmiot.report.dto.pppoe.PppoeStatisticResult;

/**
 * 网关PPPOE状态相关统计。
 * @version v103
 * <AUTHOR>
 */
@RequestMapping("/v1/m/statistic")
public interface GatewayPppoeFacade {
	/**
	 * 网关PPPOE统计数据
	 * post : /v1/m/statistic/pppoeStatistic
	 */
	@RequestMapping("/pppoeStatistic")
	public PppoeStatisticResult pppoeStatistic(@RequestHeader(value = "province", required = false) String province,
											   @RequestBody @Validated PppoeStatisticRequest request);
	
	/**
	 * 网关PPPOE口列表明细
	 * post : /v1/m/statistic/pppoeList
	 */
	@RequestMapping("/pppoeList")
	public PppoeListResult pppoeList(@RequestHeader(value = "province", required = false) String province,
									 @RequestBody @Validated PppoeListRequest request);
	
	/**
	 * 网关PPPOE列表导出
	 * post : /v1/m/statistic/pppoeListExport
	 */
	@RequestMapping("/pppoeListExport")
	public PppoeListExportResult pppoeListExport(
			@RequestHeader(value = "uid") Long uid,
			@RequestHeader(value = "province", required = false) String province,
			@RequestBody @Validated PppoeListExportRequest request);
	
	/**
	 * 网关PPPOE失败原因列表
	 * post : /v1/m/statistic/pppoeFailList
	 */
	@RequestMapping("/pppoeFailList")
	public PppoeFailListResult pppoeFailList(@RequestBody @Validated PppoeFailListRequest request);
	
}
