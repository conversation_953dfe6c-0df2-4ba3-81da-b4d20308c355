package com.cmiot.report.facade.enterprise;

import com.cmiot.report.dto.enterprise.DeviceCpuRamDataResult;
import com.cmiot.report.dto.enterprise.DeviceFlowDataResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Classname EnterpriseDeviceStatisticFeignClient
 * @Description
 * @Date 2023/8/21 18:57
 * @Created by lei
 */
@FeignClient(value = "ge-report", contextId = "enterpriseDeviceStatisticFeignClient")
public interface EnterpriseDeviceStatisticFeignClient {

    /**
     * https://yapi.iotxmm.com/project/960/interface/api/31808
     * 获取企业设备概览
     * 统计当前企业网关的 累计数量（设备总数）、日活、周活（自然周）、月活（自然月） （截止前一天、网关+路由）
     * 饼图：网关、路由器总数占比（截止前一天）
     *
     * @param eid
     * @return
     */
    //enterpriseStats
    @GetMapping("/v1/e/statistic/enterprise/getDeviceOverview")
    DeviceOverviewResult getDeviceOverview(@RequestHeader(value = "eid") Long eid);


    /**
     * https://yapi.iotxmm.com/project/960/interface/api/31820
     * 获取设备概览图表数据
     * 获取设备总数和在线设备总数（活跃数），纵轴是数量，横轴是时间，以月为单位，展示最近12个月的信息
     *
     * @param eid
     * @return
     */
    @GetMapping("/v1/e/statistic/enterprise/getDeviceGraphData")
    List<DeviceGraphDataResult> getDeviceGraphData(@RequestHeader(value = "eid") Long eid);

    /**
     * 获取下挂设备图表信息
     * https://yapi.iotxmm.com/project/960/interface/api/31828
     * @param eid
     * @param dateType
     * @return
     */
    @GetMapping("/v1/e/statistic/enterprise/getStaDeivceGraphData")
    SubDeivceGraphDataResult getSubDeivceGraphData(@RequestHeader(value = "eid") Long eid,
                                                   @RequestParam(value = "dateType") Integer dateType);

    /**
     * 设备流量统计
     * https://yapi.iotxmm.com/project/960/interface/api/38037
     * @param eid
     * @param startTime
     * @return
     */
    @GetMapping("/v1/e/statistic/enterprise/deviceFlow")
    DeviceFlowDataResult deviceFlow(@RequestHeader(value = "eid") Long eid,
                                    @RequestParam(value = "startTime") String startTime,
                                    @RequestParam(value = "endTime") String endTime);

    /**
     * CPU内存
     * https://yapi.iotxmm.com/project/960/interface/api/38040
     * @param eid
     * @param startTime
     * @return
     */
    @GetMapping("/v1/e/statistic/enterprise/getCpuRam")
    DeviceCpuRamDataResult getCpuRam(@RequestHeader(value = "eid") Long eid,
                                     @RequestParam(value = "startTime") String startTime,
                                     @RequestParam(value = "endTime") String endTime);
}
