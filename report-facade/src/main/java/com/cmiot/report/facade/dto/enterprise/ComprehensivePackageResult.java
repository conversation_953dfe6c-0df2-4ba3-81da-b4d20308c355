package com.cmiot.report.facade.dto.enterprise;

/**
 * @Classname ComprehensivePackageResponse
 * @Description
 * @Date 2023/10/13 8:55
 * @Created by lei
 */
public class ComprehensivePackageResult {

    /**
     * 套餐编码
     */
    private String packageCode;

    /**
     * 业务带宽
     */
    private String bandwidth;

    public String getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(String packageCode) {
        this.packageCode = packageCode;
    }

    public String getBandwidth() {
        return bandwidth;
    }

    public void setBandwidth(String bandwidth) {
        this.bandwidth = bandwidth;
    }
}
