package com.cmiot.report.facade.device;

import com.cmiot.report.dto.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/v1/m/statistic/hangingDevice")
public interface SubDeviceFacade {

    /**
     * 下挂设备统计概览-1.0.3
     *
     * @param request
     * @return
     */
    @PostMapping("/overview")
    SubOverviewResult getSubOverview(@RequestBody SubOverviewRequest request, @RequestHeader("province") String province);


    /**
     * 下挂设备统计指标模块-1.0.3
     *
     * @param request
     * @return
     */
    @PostMapping("/statisticIndex")
    SubDeviceStatisticResult getStatisticIndex(@RequestBody SubDeviceStatisticRequest request, @RequestHeader("province") String province);


    /**
     * 下挂设备趋势统计-1.0.3
     *
     * @param request
     * @return
     */
    @PostMapping("/trendStatistic")
    SubDeviceTrendResult getTrendStatistic(@RequestBody SubDeviceTrendRequest request, @RequestHeader("province") String province);

    /**
     * 下挂设备列表-1.0.3
     *
     * @param request
     * @return
     */
    @PostMapping("/analysisList")
    SubDeviceListResult getSubDeviceList(@RequestBody SubDeviceListRequest request, @RequestHeader("province") String province);


    /**
     * 下挂设备列表导出-1.0.3
     *
     * @param request
     * @return
     */
    @PostMapping("/analysisListExport")
    String subDeviceExport(@RequestBody SubDeviceListRequest request,
                           @RequestHeader("uid") String uid,
                           @RequestHeader("province") String province);

}
