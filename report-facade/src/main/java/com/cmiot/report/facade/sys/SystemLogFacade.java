package com.cmiot.report.facade.sys;

import com.cmiot.report.dto.syslog.SysLogDetailQueryRequest;
import com.cmiot.report.dto.syslog.SysLogDetailQueryResult;
import com.cmiot.report.dto.syslog.SysLogQueryRequest;
import com.cmiot.report.dto.syslog.SysLogQueryResult;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Classname SystemLogFacade
 * @Description
 * @Date 2022/7/20 16:15
 * @Created by lei
 */
public interface SystemLogFacade {

    @GetMapping(value = "/v1/m/statistic/syslog/list")
    SysLogQueryResult querySystemLogPage(SysLogQueryRequest sysLogQueryRequest);

    @GetMapping(value = "/v1/m/statistic/syslog/userLogList")
    SysLogQueryResult userLogPage(SysLogQueryRequest sysLogQueryRequest);

    @GetMapping(value = "/v1/m/statistic/syslog/{traceId}/detailList")
    SysLogDetailQueryResult querySystemLogDetailPage(@PathVariable(value = "traceId") String traceId,
                                                     SysLogDetailQueryRequest sysLogDetailQueryRequest);

    //根据企业按所有用户分组的日志分页
    @PostMapping(value = "/v1/e/statistic/logs/allUsers")
    SysLogQueryResult allUsersLogPage(@RequestHeader("eid") Long eid,
                                      @RequestHeader("uid") Long uid,
                                      @RequestBody SysLogQueryRequest sysLogQueryRequest);


    //按单个用户的日志分页
    @PostMapping(value = "/v1/e/statistic/logs/user")
    SysLogQueryResult userLogsPage(@RequestHeader("eid") Long eid,
                                   @RequestHeader("uid") Long uid,
                                   @RequestBody SysLogQueryRequest sysLogQueryRequest);

    //查看日志详情
    @GetMapping(value = "/v1/e/statistic/logs/logDetail/{id}")
    Map<String, String> logDetail(@PathVariable(value = "id") String traceId);

}
