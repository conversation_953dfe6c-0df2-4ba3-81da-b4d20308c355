package com.cmiot.report.facade.dto.enterprise;

import java.util.List;

/**
 * @Classname WeekReportOverviewResult
 * @Description
 * @Date 2023/12/15 11:29
 * @Created by lei
 */
public class WeekReportOverviewResult {

    private Integer gatewayCount;
    private Integer gatewayOnlineCount;

    private WeekReportOverviewFlowInfoDto flowStatistics;
    private List<String> restartDevice;


    public Integer getGatewayCount() {
        return gatewayCount;
    }

    public void setGatewayCount(Integer gatewayCount) {
        this.gatewayCount = gatewayCount;
    }

    public Integer getGatewayOnlineCount() {
        return gatewayOnlineCount;
    }

    public void setGatewayOnlineCount(Integer gatewayOnlineCount) {
        this.gatewayOnlineCount = gatewayOnlineCount;
    }

    public WeekReportOverviewFlowInfoDto getFlowStatistics() {
        return flowStatistics;
    }

    public void setFlowStatistics(WeekReportOverviewFlowInfoDto flowStatistics) {
        this.flowStatistics = flowStatistics;
    }

    public List<String> getRestartDevice() {
        return restartDevice;
    }

    public void setRestartDevice(List<String> restartDevice) {
        this.restartDevice = restartDevice;
    }
}
