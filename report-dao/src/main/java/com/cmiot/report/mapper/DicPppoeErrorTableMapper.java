package com.cmiot.report.mapper;

import com.cmiot.report.bean.DicPppoeErrorTable;
import com.cmiot.report.bean.DicPppoeErrorTableExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DicPppoeErrorTableMapper {
    long countByExample(DicPppoeErrorTableExample example);

    int deleteByExample(DicPppoeErrorTableExample example);

    int insert(DicPppoeErrorTable record);

    int insertSelective(DicPppoeErrorTable record);

    List<DicPppoeErrorTable> selectByExampleWithRowbounds(DicPppoeErrorTableExample example, RowBounds rowBounds);

    List<DicPppoeErrorTable> selectByExample(DicPppoeErrorTableExample example);

    int updateByExampleSelective(@Param("record") DicPppoeErrorTable record, @Param("example") DicPppoeErrorTableExample example);

    int updateByExample(@Param("record") DicPppoeErrorTable record, @Param("example") DicPppoeErrorTableExample example);
}