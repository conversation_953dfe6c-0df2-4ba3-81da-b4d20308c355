package com.cmiot.report.mapper;

import com.cmiot.report.bean.DeviceAllByCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Classname DeviceDayCountMapper
 * @Description
 * @Date 2023/11/28 15:50
 * @Created by lei
 */
public interface DeviceDayCountMapper {
    List<DeviceAllByCount> selectDeviceDayCount(@Param(value = "provinces") List<String> provinces,
                                                @Param(value = "vendorsIdList") List<String> vendorsIdList,
                                                @Param(value = "startTime") String startTime,
                                                @Param(value = "endTime") String endTime);

    List<DeviceAllByCount> selectDeviceAllCount(@Param(value = "provinces") List<String> provinces,
                                                @Param(value = "vendorsIdList") List<String> vendorsIdList,
                                                @Param(value = "startTime") String startTime,
                                                @Param(value = "endTime") String endTime);
}
