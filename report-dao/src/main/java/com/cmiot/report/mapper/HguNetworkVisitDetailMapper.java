package com.cmiot.report.mapper;

import com.cmiot.report.bean.HguNetworkVisitDetail;
import com.cmiot.report.bean.HguNetworkVisitDetailExample;
import java.util.List;

import com.cmiot.report.bean.NetworkPerferCount;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface HguNetworkVisitDetailMapper {
    long countByExample(HguNetworkVisitDetailExample example);

    int deleteByExample(HguNetworkVisitDetailExample example);

    int insert(HguNetworkVisitDetail record);

    int insertSelective(HguNetworkVisitDetail record);

    List<HguNetworkVisitDetail> selectByExampleWithRowbounds(HguNetworkVisitDetailExample example, RowBounds rowBounds);

    List<HguNetworkVisitDetail> selectByExample(HguNetworkVisitDetailExample example);

    int updateByExampleSelective(@Param("record") HguNetworkVisitDetail record, @Param("example") HguNetworkVisitDetailExample example);

    int updateByExample(@Param("record") HguNetworkVisitDetail record, @Param("example") HguNetworkVisitDetailExample example);

    List<NetworkPerferCount> selectCount(@Param("provinceCodes") List<String> provinceCodes,
                                         @Param("cityCodes") List<String> cityCodes,
                                         @Param("startTime") Long startTime,
                                         @Param("endTime") Long endTime);
}