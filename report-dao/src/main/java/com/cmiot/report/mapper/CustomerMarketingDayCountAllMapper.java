package com.cmiot.report.mapper;

import com.cmiot.report.bean.customer.CustomerMarketingDayCountAll;
import com.cmiot.report.bean.customer.CustomerMarketingDayCountAllExample;
import com.cmiot.report.bean.customer.CustomerMarketingScoreDistribution;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface CustomerMarketingDayCountAllMapper {
    long countByExample(CustomerMarketingDayCountAllExample example);

    int deleteByExample(CustomerMarketingDayCountAllExample example);

    int insert(CustomerMarketingDayCountAll record);

    int insertSelective(CustomerMarketingDayCountAll record);

    List<CustomerMarketingDayCountAll> selectByExampleWithRowbounds(CustomerMarketingDayCountAllExample example, RowBounds rowBounds);

    List<CustomerMarketingDayCountAll> selectByExample(CustomerMarketingDayCountAllExample example);

    int updateByExampleSelective(@Param("record") CustomerMarketingDayCountAll record, @Param("example") CustomerMarketingDayCountAllExample example);

    int updateByExample(@Param("record") CustomerMarketingDayCountAll record, @Param("example") CustomerMarketingDayCountAllExample example);

    List<CustomerMarketingDayCountAll> selectByExampleByProvince(CustomerMarketingDayCountAllExample example);

    List<CustomerMarketingDayCountAll> selectByExampleByCity(CustomerMarketingDayCountAllExample example);

    List<CustomerMarketingScoreDistribution> selectScoreDistribution(@Param("time") String time,
                                                                     @Param("provList") List<String> provList,
                                                                     @Param("cityList") List<String> cityList);

    List<CustomerMarketingDayCountAll> selectByExampleByDate(CustomerMarketingDayCountAllExample example);
}