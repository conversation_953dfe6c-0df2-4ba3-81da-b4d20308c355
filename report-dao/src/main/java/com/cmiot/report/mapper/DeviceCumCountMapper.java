package com.cmiot.report.mapper;

import com.cmiot.report.bean.DeviceCumCount;
import com.cmiot.report.bean.DeviceCumCountExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DeviceCumCountMapper {
    long countByExample(DeviceCumCountExample example);

    int deleteByExample(DeviceCumCountExample example);

    int insert(DeviceCumCount record);

    int insertSelective(DeviceCumCount record);

    List<DeviceCumCount> selectByExampleWithRowbounds(DeviceCumCountExample example, RowBounds rowBounds);

    List<DeviceCumCount> selectByExample(DeviceCumCountExample example);

    int updateByExampleSelective(@Param("record") DeviceCumCount record, @Param("example") DeviceCumCountExample example);

    int updateByExample(@Param("record") DeviceCumCount record, @Param("example") DeviceCumCountExample example);
}