package com.cmiot.report.mapper;


import com.cmiot.report.bean.customerOrder.OrderTrendLineChartAll;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerOrderTrendsMapper {

    List<OrderTrendLineChartAll> queryCustomerOrderTrendLineChart(@Param("provList") List<String> provList,
                                                                  @Param("businessStatus") int businessStatus,
                                                                  @Param("startTime") int startTime,
                                                                  @Param("endTime") int endTime);

}
