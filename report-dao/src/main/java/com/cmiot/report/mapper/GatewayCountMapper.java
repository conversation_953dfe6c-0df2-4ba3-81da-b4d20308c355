package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayCount;
import com.cmiot.report.bean.GatewayCountExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface GatewayCountMapper {
    long countByExample(GatewayCountExample example);

    int deleteByExample(GatewayCountExample example);

    int insert(GatewayCount record);

    int insertSelective(GatewayCount record);

    List<GatewayCount> selectByExampleWithRowbounds(GatewayCountExample example, RowBounds rowBounds);

    List<GatewayCount> selectByExample(GatewayCountExample example);

    int updateByExampleSelective(@Param("record") GatewayCount record, @Param("example") GatewayCountExample example);

    int updateByExample(@Param("record") GatewayCount record, @Param("example") GatewayCountExample example);
}