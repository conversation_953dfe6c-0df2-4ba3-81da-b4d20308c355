package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayModelAll;
import com.cmiot.report.bean.GatewayModelAllExample;

import java.util.List;

import com.cmiot.report.dto.plugin.PluginModelCount;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayModelAllMapper {
    long countByExample(GatewayModelAllExample example);

    int deleteByExample(GatewayModelAllExample example);

    int insert(GatewayModelAll record);

    int insertSelective(GatewayModelAll record);

    List<GatewayModelAll> selectByExampleWithRowbounds(GatewayModelAllExample example, RowBounds rowBounds);

    List<GatewayModelAll> selectByExample(GatewayModelAllExample example);

    int updateByExampleSelective(@Param("record") GatewayModelAll record, @Param("example") GatewayModelAllExample example);

    int updateByExample(@Param("record") GatewayModelAll record, @Param("example") GatewayModelAllExample example);

    List<PluginModelCount> getCountByModelQuery(@Param("vendor") List<Long> vendor, @Param("model") List<Long> model);
}