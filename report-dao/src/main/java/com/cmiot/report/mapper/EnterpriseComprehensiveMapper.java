package com.cmiot.report.mapper;

import com.cmiot.report.bean.CustomerOrderAll;
import com.cmiot.report.dto.enterprise.ComprehensiveGatewayInfo;
import com.cmiot.report.dto.enterprise.EnterpriseComprehensiveInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Classname EnterpriseComprehensiveMapper
 * @Description
 * @Date 2023/10/11 15:31
 * @Created by lei
 */
public interface EnterpriseComprehensiveMapper {

    EnterpriseComprehensiveInfo getComprehensive(@Param("eid") Long eid,
                                                 @Param("day") String day);

    Long countHighLoadingGateway(@Param("eid") Long eid,
                                 @Param("month") String month);

    List<ComprehensiveGatewayInfo> getHighLoadingGateway(@Param("eid") Long eid,
                                                         @Param("month") String month,
                                                         @Param("offset") Long offset,
                                                         @Param("limitSize") Integer limitSize);

    Long countLowLoadingGateway(@Param("eid") Long eid,
                                @Param("month") String month);

    List<ComprehensiveGatewayInfo> getLowLoadingGateway(@Param("eid") Long eid,
                                                        @Param("month") String month,
                                                        @Param("offset") Long offset,
                                                        @Param("limitSize") Integer limitSize);

    Long countIdleGateway(@Param("eid") Long eid,
                          @Param("day") String day);

    List<ComprehensiveGatewayInfo> getIdleGateway(@Param("eid") Long eid,
                                                  @Param("day") String day,
                                                  @Param("offset") Long offset,
                                                  @Param("limitSize") Integer limitSize);

    Long countOverLifeGateway(@Param("eid") Long eid,
                              @Param("day") String day);

    List<ComprehensiveGatewayInfo> getOverLifeGateway(@Param("eid") Long eid,
                                                      @Param("day") String day,
                                                      @Param("offset") Long offset,
                                                      @Param("limitSize") Integer limitSize);

    Long countBandwidthNonsupportGateway(@Param("eid") Long eid,
                                         @Param("day") String day);

    List<ComprehensiveGatewayInfo> getBandwidthNonsupportGateway(@Param("eid") Long eid,
                                                                 @Param("day") String day,
                                                                 @Param("offset") Long offset,
                                                                 @Param("limitSize") Integer limitSize);

    Long countNetBadPingGateway(@Param("eid") Long eid,
                                @Param("day") String day);

    List<ComprehensiveGatewayInfo> getNetBadPingGateway(@Param("eid") Long eid,
                                                        @Param("day") String day,
                                                        @Param("offset") Long offset,
                                                        @Param("limitSize") Integer limitSize);

    Long countCpuRamGateway(@Param("eid") Long eid,
                            @Param("day") String day);

    List<ComprehensiveGatewayInfo> getCpuRamGateway(@Param("eid") Long eid,
                                                    @Param("day") String day,
                                                    @Param("offset") Long offset,
                                                    @Param("limitSize") Integer limitSize);


    Long countPackage(@Param("eid") Long eid,
                      @Param("day") String day);

    List<CustomerOrderAll> getPackage(@Param("eid") Long eid,
                                      @Param("day") String day,
                                      @Param("offset") Long offset,
                                      @Param("limitSize") Integer limitSize);

    Long countBaseGateway(@Param("eid") Long eid,
                          @Param("day") String day);

    List<ComprehensiveGatewayInfo> getBaseGateway(@Param("eid") Long eid,
                                                  @Param("day") String day,
                                                  @Param("offset") Long offset,
                                                  @Param("limitSize") Integer limitSize);

    List<CustomerOrderAll> getOrdersByCustomerIds(@Param("customerIds") List<String> customerIds,
                                                  @Param("day") String day);
}
