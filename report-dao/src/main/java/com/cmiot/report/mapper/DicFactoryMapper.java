package com.cmiot.report.mapper;

import com.cmiot.report.bean.DicFactory;
import com.cmiot.report.bean.DicFactoryExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface DicFactoryMapper {
    long countByExample(DicFactoryExample example);

    int deleteByExample(DicFactoryExample example);

    int insert(DicFactory record);

    int insertSelective(DicFactory record);

    List<DicFactory> selectByExampleWithRowbounds(DicFactoryExample example, RowBounds rowBounds);

    List<DicFactory> selectByExample(DicFactoryExample example);

    int updateByExampleSelective(@Param("record") DicFactory record, @Param("example") DicFactoryExample example);

    int updateByExample(@Param("record") DicFactory record, @Param("example") DicFactoryExample example);

    List<DicFactory> selectByWindowExample(DicFactoryExample example);

}