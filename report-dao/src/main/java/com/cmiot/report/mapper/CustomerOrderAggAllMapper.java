package com.cmiot.report.mapper;

import com.cmiot.report.bean.CustomerOrderAggAll;
import com.cmiot.report.bean.CustomerOrderAggAllExample;

import java.util.List;

import com.cmiot.report.dto.CustBussQuery;
import com.cmiot.report.dto.CustNameCount;
import com.cmiot.report.dto.NewCustNameCount;
import com.cmiot.report.dto.NewIndusCustCount;
import com.cmiot.report.dto.customerOrder.CustomerOrderCountData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface CustomerOrderAggAllMapper {
    long countByExample(CustomerOrderAggAllExample example);

    int deleteByExample(CustomerOrderAggAllExample example);

    int insert(CustomerOrderAggAll record);

    int insertSelective(CustomerOrderAggAll record);

    List<CustomerOrderAggAll> selectByExampleWithRowbounds(CustomerOrderAggAllExample example, RowBounds rowBounds);

    List<CustomerOrderAggAll> selectByExample(CustomerOrderAggAllExample example);

    int updateByExampleSelective(@Param("record") CustomerOrderAggAll record, @Param("example") CustomerOrderAggAllExample example);

    int updateByExample(@Param("record") CustomerOrderAggAll record, @Param("example") CustomerOrderAggAllExample example);

    // 1.企业客户区域分布
    // 分省地市存量客户统计
    List<CustNameCount> countCustByQuery(@Param("query") CustBussQuery query);

    // 分省地市办理过业务的存量客户统计
    List<CustNameCount> countBussCustByExample(CustomerOrderAggAllExample example);

    // 业务生效中客户占比
    List<CustNameCount> countActiveCustByQuery(@Param("query") CustBussQuery query);

    // 2.新增企业客户统计
    // 新增企业客户统计
    long countNewCustByExample(CustomerOrderAggAllExample example);

    // 新增企业客户分日期列表
    List<NewCustNameCount> countNewCustList(CustomerOrderAggAllExample example);

    // 新增且办理了业务的企业客户统计
    long countNewCustBussByExample(CustomerOrderAggAllExample example);

    // 新增且办理了业务的企业客户列表
    List<NewCustNameCount> countNewCustBussList(CustomerOrderAggAllExample example);


    // 3.存量企业客户统计
    // 存量客户数(个)
    long countAllCustByQuery(@Param("query") CustBussQuery query);

    // 未办理业务的客户(包含从未办理,已退订,已到期)
    long countCustNoBussByQuery(@Param("query") CustBussQuery query);

    // 已办理客户总数(包含已办理未生效,已生效)
    long countCustExistBussByQuery(@Param("query") CustBussQuery query);

    // 周期内新办理业务的存量客户数：包含已办理未生效，已生效。（包含已退订、已到期但重新办理了）
    long countCustNewBussByQuery(@Param("query") CustBussQuery query);

    // 周期内新办理业务的存量客户列表：包含已办理未生效，已生效。（包含已退订、已到期但重新办理了）
    List<NewCustNameCount> countCustNewBussListByQuery(@Param("query") CustBussQuery query);

    // 周期内首次办理业务的存量客户数：包含已办理未生效，已生效。（不包含已退订、已到期但重新办理了）
    long queryCustFirstNewBuss(@Param("query") CustBussQuery query);
    long queryCustFirstNewBussV2(@Param("query") CustBussQuery query);


    // 分日期,周期内首次办理业务的存量客户列表：包含已办理未生效，已生效。（不包含已退订、已到期但重新办理了）
    List<NewCustNameCount> queryCustFirstNewBussList(@Param("query") CustBussQuery query);
    List<NewCustNameCount> queryCustFirstNewBussListV2(@Param("query") CustBussQuery query);

    // 退订客户数
    long countUnsubBussByQuery(@Param("query") CustBussQuery query);

    // 退订客户列表
    List<NewCustNameCount> countCustUnsubBussListByQuery(@Param("query") CustBussQuery query);


    // 4.企业行业新增客户统计
    // 企业行业新增客户统计
    List<NewIndusCustCount> countNewIndusCustListByExample(CustomerOrderAggAllExample example);

    CustomerOrderCountData countBase(@Param("query") CustBussQuery query);

}