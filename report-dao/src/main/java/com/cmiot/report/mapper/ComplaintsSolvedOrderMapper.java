package com.cmiot.report.mapper;

import com.cmiot.report.bean.ComplaintsSolvedOrder;
import com.cmiot.report.bean.ComplaintsSolvedOrderExample;
import java.util.List;

import com.cmiot.report.dto.ComplaintsSolOrder;
import com.cmiot.report.dto.ComplaintsSolOrderExt;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ComplaintsSolvedOrderMapper {
    long countByExample(ComplaintsSolvedOrderExample example);

    int deleteByExample(ComplaintsSolvedOrderExample example);

    int insert(ComplaintsSolvedOrder record);

    int insertSelective(ComplaintsSolvedOrder record);

    List<ComplaintsSolvedOrder> selectByExampleWithRowbounds(ComplaintsSolvedOrderExample example, RowBounds rowBounds);

    List<ComplaintsSolvedOrder> selectByExample(ComplaintsSolvedOrderExample example);

    int updateByExampleSelective(@Param("record") ComplaintsSolvedOrder record, @Param("example") ComplaintsSolvedOrderExample example);

    int updateByExample(@Param("record") ComplaintsSolvedOrder record, @Param("example") ComplaintsSolvedOrderExample example);

    // 统计各个工单处理效率
    ComplaintsSolOrder countOrdersByExample(ComplaintsSolvedOrderExample example);

    ComplaintsSolOrderExt selectOrdersByExample(ComplaintsSolvedOrderExample example);
}