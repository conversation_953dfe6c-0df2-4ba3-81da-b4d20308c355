package com.cmiot.report.mapper;

import com.cmiot.report.bean.DictDeviceModel;
import com.cmiot.report.bean.DictDeviceModelExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DictDeviceModelMapper {
    long countByExample(DictDeviceModelExample example);

    int deleteByExample(DictDeviceModelExample example);

    int insert(DictDeviceModel record);

    int insertSelective(DictDeviceModel record);

    List<DictDeviceModel> selectByExampleWithRowbounds(DictDeviceModelExample example, RowBounds rowBounds);

    List<DictDeviceModel> selectByExample(DictDeviceModelExample example);

    int updateByExampleSelective(@Param("record") DictDeviceModel record, @Param("example") DictDeviceModelExample example);

    int updateByExample(@Param("record") DictDeviceModel record, @Param("example") DictDeviceModelExample example);

    List<DictDeviceModel> selectFactModelByExample(DictDeviceModelExample example);

}