package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayPeriodAggrAll;
import com.cmiot.report.bean.GatewayPeriodAggrAllExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayPeriodAggrAllMapper {
    long countByExample(GatewayPeriodAggrAllExample example);

    int deleteByExample(GatewayPeriodAggrAllExample example);

    int insert(GatewayPeriodAggrAll record);

    int insertSelective(GatewayPeriodAggrAll record);

    List<GatewayPeriodAggrAll> selectByExampleWithRowbounds(GatewayPeriodAggrAllExample example, RowBounds rowBounds);

    List<GatewayPeriodAggrAll> selectByExample(GatewayPeriodAggrAllExample example);

    int updateByExampleSelective(@Param("record") GatewayPeriodAggrAll record, @Param("example") GatewayPeriodAggrAllExample example);

    int updateByExample(@Param("record") GatewayPeriodAggrAll record, @Param("example") GatewayPeriodAggrAllExample example);
}