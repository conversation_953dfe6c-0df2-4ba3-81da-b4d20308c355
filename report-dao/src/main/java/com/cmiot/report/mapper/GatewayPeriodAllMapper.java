package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayPeriodAll;
import com.cmiot.report.bean.GatewayPeriodAllExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayPeriodAllMapper {
    long countByExample(GatewayPeriodAllExample example);

    int deleteByExample(GatewayPeriodAllExample example);

    int insert(GatewayPeriodAll record);

    int insertSelective(GatewayPeriodAll record);

    List<GatewayPeriodAll> selectByExampleWithRowbounds(GatewayPeriodAllExample example, RowBounds rowBounds);

    List<GatewayPeriodAll> selectByExample(GatewayPeriodAllExample example);

    int updateByExampleSelective(@Param("record") GatewayPeriodAll record, @Param("example") GatewayPeriodAllExample example);

    int updateByExample(@Param("record") GatewayPeriodAll record, @Param("example") GatewayPeriodAllExample example);

    List<GatewayPeriodAll> selectContinuousRunningOverTimeDevice(@Param("eid") Long eid,
                                                                 @Param("timePreStr") String timePreStr,
                                                                 @Param("continuousRunningTimeLimit") Integer continuousRunningTimeLimit);

    List<GatewayPeriodAll> getLastPeriodInfo(@Param("gatewaySns") List<String> gatewaySns,
                                             @Param("finalperiodDetailQueryLimitHour") String finalperiodDetailQueryLimitHour);

}