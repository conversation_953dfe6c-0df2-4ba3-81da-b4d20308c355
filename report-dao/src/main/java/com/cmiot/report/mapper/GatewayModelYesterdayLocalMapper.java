package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayModelYesterdayLocal;
import com.cmiot.report.bean.GatewayModelYesterdayLocalExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayModelYesterdayLocalMapper {
    long countByExample(GatewayModelYesterdayLocalExample example);

    int deleteByExample(GatewayModelYesterdayLocalExample example);

    int insert(GatewayModelYesterdayLocal record);

    int insertSelective(GatewayModelYesterdayLocal record);

    List<GatewayModelYesterdayLocal> selectByExampleWithRowbounds(GatewayModelYesterdayLocalExample example, RowBounds rowBounds);

    List<GatewayModelYesterdayLocal> selectByExample(GatewayModelYesterdayLocalExample example);

    int updateByExampleSelective(@Param("record") GatewayModelYesterdayLocal record, @Param("example") GatewayModelYesterdayLocalExample example);

    int updateByExample(@Param("record") GatewayModelYesterdayLocal record, @Param("example") GatewayModelYesterdayLocalExample example);
}