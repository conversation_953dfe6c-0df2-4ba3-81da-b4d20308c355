package com.cmiot.report.mapper;

import com.cmiot.report.bean.PonPowerStatisticsDetail;
import com.cmiot.report.bean.PonPowerStatisticsDetailExample;

import java.util.List;

import com.cmiot.report.bean.PonPowerStatisticsQ;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface PonPowerStatisticsDetailMapper {
    long countByExample(PonPowerStatisticsDetailExample example);

    int deleteByExample(PonPowerStatisticsDetailExample example);

    int insert(PonPowerStatisticsDetail record);

    int insertSelective(PonPowerStatisticsDetail record);

    List<PonPowerStatisticsDetail> selectByExampleWithRowbounds(PonPowerStatisticsDetailExample example, RowBounds rowBounds);

    List<PonPowerStatisticsDetail> selectByExample(PonPowerStatisticsDetailExample example);

    int updateByExampleSelective(@Param("record") PonPowerStatisticsDetail record, @Param("example") PonPowerStatisticsDetailExample example);

    int updateByExample(@Param("record") PonPowerStatisticsDetail record, @Param("example") PonPowerStatisticsDetailExample example);

    List<PonPowerStatisticsQ> selectByProvExample(PonPowerStatisticsDetailExample example);

    List<PonPowerStatisticsQ> selectByCityExample(PonPowerStatisticsDetailExample example);

    List<PonPowerStatisticsQ> selectByFactoryExample(PonPowerStatisticsDetailExample example);

    List<PonPowerStatisticsQ> selectByFactoryModelExample(PonPowerStatisticsDetailExample example);

}