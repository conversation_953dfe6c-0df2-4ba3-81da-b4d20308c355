package com.cmiot.report.mapper;

import com.cmiot.report.bean.DictBassType;
import com.cmiot.report.bean.DictBassTypeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DictBassTypeMapper {
    long countByExample(DictBassTypeExample example);

    int deleteByExample(DictBassTypeExample example);

    int insert(DictBassType record);

    int insertSelective(DictBassType record);

    List<DictBassType> selectByExampleWithRowbounds(DictBassTypeExample example, RowBounds rowBounds);

    List<DictBassType> selectByExample(DictBassTypeExample example);

    int updateByExampleSelective(@Param("record") DictBassType record, @Param("example") DictBassTypeExample example);

    int updateByExample(@Param("record") DictBassType record, @Param("example") DictBassTypeExample example);
}