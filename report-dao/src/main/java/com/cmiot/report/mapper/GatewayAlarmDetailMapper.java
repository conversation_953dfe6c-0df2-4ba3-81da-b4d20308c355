package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayAlarmDetail;
import com.cmiot.report.bean.GatewayAlarmDetailExample;

import java.util.List;

import com.cmiot.report.dto.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayAlarmDetailMapper {
    long countByExample(GatewayAlarmDetailExample example);

    int deleteByExample(GatewayAlarmDetailExample example);

    int insert(GatewayAlarmDetail record);

    int insertSelective(GatewayAlarmDetail record);

    List<GatewayAlarmDetail> selectByExampleWithRowbounds(GatewayAlarmDetailExample example, RowBounds rowBounds);

    List<GatewayAlarmDetail> selectByExample(GatewayAlarmDetailExample example);

    int updateByExampleSelective(@Param("record") GatewayAlarmDetail record, @Param("example") GatewayAlarmDetailExample example);

    int updateByExample(@Param("record") GatewayAlarmDetail record, @Param("example") GatewayAlarmDetailExample example);

    List<GatewayAlarmCount> selectCountByProvExample(GatewayAlarmDetailExample example);

    List<GatewayAlarmCount> selectCountByVendorExample(GatewayAlarmDetailExample example);

    List<GatewayAlarmCount> selectCountByCityExample(GatewayAlarmDetailExample example);

    List<GatewayAlarmDetailCount> selectDetailCountByExample(GatewayAlarmDetailExample example);

    List<GatewayAlarmDetailVo> selectAlarmDetailListByExample(GatewayAlarmDetailExample example);

    List<GatewayAlarmDetailVo> selectAlarmDetailListByQuery(@Param("query") GatewayAlarmQuery query);
    
    //List<GatewayAlarmDetailVo> selectAlarrmDetailListByProv(@Param("query") GatewayAlarmQuery query);
    //
    //List<GatewayAlarmDetailVo> selectAlarmDetailListByCity(@Param("query") GatewayAlarmQuery query);
    //
    //List<GatewayAlarmDetailVo> selectAlarmDetailListByVendor(@Param("query") GatewayAlarmQuery query);

    List<GatewayAlarmTypeQueryCount> selectCountByProvTypeQuery(@Param("query") GatewayAlarmQuery query);

    List<GatewayAlarmQueryCount> selectCountByProvQuery(@Param("query") GatewayAlarmQuery query);

    List<GatewayAlarmTypeQueryCount> selectCountByCityTypeQuery(@Param("query") GatewayAlarmQuery query);

    List<GatewayAlarmQueryCount> selectCountByCityQuery(@Param("query") GatewayAlarmQuery query);

    List<GatewayAlarmTypeQueryCount> selectCountByVendorTypeQuery(@Param("query") GatewayAlarmQuery query);

    List<GatewayAlarmQueryCount> selectCountByVendorQuery(@Param("query") GatewayAlarmQuery query);

    int insertBatch(List<GatewayAlarmDetail> list);

    // 小程序运行报告告警网关数统计
    int appReportCount(GatewayQuery gatewayQuery);

}