package com.cmiot.report.mapper;

import com.cmiot.report.bean.customerOrder.OrderBandwidthAll;
import com.cmiot.report.bean.customerOrder.OrderTrendLineChartAll;
import com.cmiot.report.bean.customerOrder.OverviewHistogramAll;
import com.cmiot.report.bean.customerOrder.OverviewPieChartAll;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerOrderAllMapper {

    List<OverviewPieChartAll> queryCustomerOrderOverviewPieChart(@Param("time") String time,
                                                           @Param("provList") List<String> provList);

    List<OverviewHistogramAll> queryCustomerOrderOverviewHistogram(@Param("time") String time,
                                                             @Param("provList") List<String> provList);

    List<OrderTrendLineChartAll> queryCustomerOrderTrendLineChart(@Param("time") String time,
                                                            @Param("provList") List<String> provList,
                                                            @Param("businessStatus") int businessStatus,
                                                            @Param("startTime") String startTime,
                                                            @Param("endTime") String endTime);

    List<OrderBandwidthAll> queryBandwidthStatistical(@Param("time") String time,
                                                      @Param("provList") List<String> provList,
                                                      @Param("businessType") String businessType);

}
