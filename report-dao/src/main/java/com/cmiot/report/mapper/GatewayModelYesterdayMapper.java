package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayModelYesterday;
import com.cmiot.report.bean.GatewayModelYesterdayExample;
import java.util.List;

import com.cmiot.report.dto.plugin.PluginModelCount;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayModelYesterdayMapper {
    long countByExample(GatewayModelYesterdayExample example);

    int deleteByExample(GatewayModelYesterdayExample example);

    int insert(GatewayModelYesterday record);

    int insertSelective(GatewayModelYesterday record);

    List<GatewayModelYesterday> selectByExampleWithRowbounds(GatewayModelYesterdayExample example, RowBounds rowBounds);

    List<GatewayModelYesterday> selectByExample(GatewayModelYesterdayExample example);

    int updateByExampleSelective(@Param("record") GatewayModelYesterday record, @Param("example") GatewayModelYesterdayExample example);

    int updateByExample(@Param("record") GatewayModelYesterday record, @Param("example") GatewayModelYesterdayExample example);

    List<PluginModelCount> getCountByModelQuery(@Param("vendor") List<Long> vendor, @Param("model") List<Long> model);
}