package com.cmiot.report.mapper;


import com.cmiot.report.bean.customerOrder.OrderPackageAddAll;
import com.cmiot.report.bean.customerOrder.OrderPackageCountAll;
import com.cmiot.report.bean.customerOrder.OrderPackageTop3All;
import com.cmiot.report.bean.customerOrder.OrderTrendLineChartAll;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerOrderPackageMapper {

    List<OrderPackageCountAll> queryCustomerOrderPackageCount(@Param("provList") List<String> provList,
                                                              @Param("packageStatus") int packageStatus,
                                                              @Param("startTime") int startTime,
                                                              @Param("endTime") int endTime);

    List<OrderPackageAddAll> queryCustomerOrderPackageAddNum(@Param("provList") List<String> provList,
                                                             @Param("packageStatus") int packageStatus,
                                                             @Param("startTime") int startTime,
                                                             @Param("endTime") int endTime);

    int queryCustomerOrderPackageAddSum(@Param("provList") List<String> provList,
                                                                 @Param("packageStatus") int packageStatus,
                                                                 @Param("startTime") int startTime,
                                                                 @Param("endTime") int endTime);

    OrderPackageAddAll queryCustomerOrderPackageAddTrend(@Param("provList") List<String> provList,
                                                         @Param("packageStatus") int packageStatus,
                                                         @Param("startTime") int startTime,
                                                         @Param("endTime") int endTime);

    List<OrderPackageAddAll> queryCustomerOrderPackageAddTrendByArea(@Param("provList") List<String> provList,
                                                                     @Param("packageStatus") int packageStatus,
                                                                     @Param("startTime") int startTime,
                                                                     @Param("endTime") int endTime);

    // 查询套餐订购TOP3
    List<OrderPackageTop3All> queryOrderPackageTop3(@Param("provList") List<String> provList,
                                                    @Param("packageStatus") int packageStatus,
                                                    @Param("startTime") int startTime,
                                                    @Param("endTime") int endTime);

    List<OrderPackageTop3All> queryOrderCountPackageTopN(@Param("provList") List<String> provList,
                                                    @Param("packageStatus") int packageStatus,
                                                    @Param("startTime") int startTime,
                                                    @Param("endTime") int endTime);

    List<OrderPackageTop3All> queryOrderAddPackageTopN(@Param("provList") List<String> provList,
                                                         @Param("packageStatus") int packageStatus,
                                                         @Param("startTime") int startTime,
                                                         @Param("endTime") int endTime);
}
