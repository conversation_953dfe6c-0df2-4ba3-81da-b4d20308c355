package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayIncrementCount;
import com.cmiot.report.bean.GatewayIncrementCountExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface GatewayIncrementCountMapper {
    long countByExample(GatewayIncrementCountExample example);

    int deleteByExample(GatewayIncrementCountExample example);

    int insert(GatewayIncrementCount record);

    int insertSelective(GatewayIncrementCount record);

    List<GatewayIncrementCount> selectByExampleWithRowbounds(GatewayIncrementCountExample example, RowBounds rowBounds);

    List<GatewayIncrementCount> selectByExample(GatewayIncrementCountExample example);

    int updateByExampleSelective(@Param("record") GatewayIncrementCount record, @Param("example") GatewayIncrementCountExample example);

    int updateByExample(@Param("record") GatewayIncrementCount record, @Param("example") GatewayIncrementCountExample example);
}