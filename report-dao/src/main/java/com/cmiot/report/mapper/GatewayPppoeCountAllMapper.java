package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayPppoeCountAll;
import com.cmiot.report.bean.GatewayPppoeCountAllExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayPppoeCountAllMapper {
    long countByExample(GatewayPppoeCountAllExample example);

    int deleteByExample(GatewayPppoeCountAllExample example);

    int insert(GatewayPppoeCountAll record);

    int insertSelective(GatewayPppoeCountAll record);

    List<GatewayPppoeCountAll> selectByExampleWithRowbounds(GatewayPppoeCountAllExample example, RowBounds rowBounds);

    List<GatewayPppoeCountAll> selectByExample(GatewayPppoeCountAllExample example);

    int updateByExampleSelective(@Param("record") GatewayPppoeCountAll record, @Param("example") GatewayPppoeCountAllExample example);

    int updateByExample(@Param("record") GatewayPppoeCountAll record, @Param("example") GatewayPppoeCountAllExample example);
}