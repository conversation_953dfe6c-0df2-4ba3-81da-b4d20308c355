package com.cmiot.report.mapper;

import com.cmiot.report.bean.NetworkDeviceAll;
import com.cmiot.report.bean.NetworkDeviceAllExample;
import java.util.List;

import com.cmiot.report.dto.GatewayQuery;
import com.cmiot.report.dto.NetworkDeviceQuery;
import com.cmiot.report.dto.NetworkDeviceRunTimeDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface NetworkDeviceAllMapper {
    long countByExample(NetworkDeviceAllExample example);

    int deleteByExample(NetworkDeviceAllExample example);

    int insert(NetworkDeviceAll record);

    int insertSelective(NetworkDeviceAll record);

    List<NetworkDeviceAll> selectByExampleWithRowbounds(NetworkDeviceAllExample example, RowBounds rowBounds);

    List<NetworkDeviceAll> selectByExample(NetworkDeviceAllExample example);

    int updateByExampleSelective(@Param("record") NetworkDeviceAll record, @Param("example") NetworkDeviceAllExample example);

    int updateByExample(@Param("record") NetworkDeviceAll record, @Param("example") NetworkDeviceAllExample example);

    // 这些组网设备的连续运行时长
    double getNetRunTime(@Param("query") GatewayQuery gatewayQuery);

    // 小程序运行报告组网设备运行超时列表
    List<NetworkDeviceRunTimeDetail> networkDeviceList(@Param("query") NetworkDeviceQuery query);

    // 组网设备运行超时数量
    long networkDeviceCount(@Param("query") GatewayQuery query);
}