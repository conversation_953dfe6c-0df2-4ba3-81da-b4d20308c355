package com.cmiot.report.mapper;

import com.cmiot.report.bean.AlarmThreshold;
import com.cmiot.report.bean.AlarmThresholdExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface AlarmThresholdMapper {
    long countByExample(AlarmThresholdExample example);

    int deleteByExample(AlarmThresholdExample example);

    int insert(AlarmThreshold record);

    int insertSelective(AlarmThreshold record);

    List<AlarmThreshold> selectByExampleWithRowbounds(AlarmThresholdExample example, RowBounds rowBounds);

    List<AlarmThreshold> selectByExample(AlarmThresholdExample example);

    int updateByExampleSelective(@Param("record") AlarmThreshold record, @Param("example") AlarmThresholdExample example);

    int updateByExample(@Param("record") AlarmThreshold record, @Param("example") AlarmThresholdExample example);

    List<AlarmThreshold> selectNewestValueByExample();

    int insertBatch(List<AlarmThreshold> list);
}