package com.cmiot.report.mapper;

import com.cmiot.report.bean.CustomerAll;
import com.cmiot.report.bean.CustomerAllExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface CustomerAllMapper {
    long countByExample(CustomerAllExample example);

    int deleteByExample(CustomerAllExample example);

    int insert(CustomerAll record);

    int insertSelective(CustomerAll record);

    List<CustomerAll> selectByExampleWithRowbounds(CustomerAllExample example, RowBounds rowBounds);

    List<CustomerAll> selectByExample(CustomerAllExample example);

    int updateByExampleSelective(@Param("record") CustomerAll record, @Param("example") CustomerAllExample example);

    int updateByExample(@Param("record") CustomerAll record, @Param("example") CustomerAllExample example);

    // 根据eid查询企业名称
    String selectNameByExample(@Param("eid") long eid);
}