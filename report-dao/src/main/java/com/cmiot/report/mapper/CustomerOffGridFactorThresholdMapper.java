package com.cmiot.report.mapper;

import com.cmiot.report.bean.CustomerOffGridFactorThreshold;
import com.cmiot.report.bean.CustomerOffGridFactorThresholdExample;

import java.util.List;

import com.cmiot.report.dto.OffGridFactorThresholdDetail;
import com.cmiot.report.dto.OffGridFactorThresholdResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface CustomerOffGridFactorThresholdMapper {
    long countByExample(CustomerOffGridFactorThresholdExample example);

    int deleteByExample(CustomerOffGridFactorThresholdExample example);

    int insert(CustomerOffGridFactorThreshold record);

    int insertSelective(CustomerOffGridFactorThreshold record);

    List<CustomerOffGridFactorThreshold> selectByExampleWithRowbounds(CustomerOffGridFactorThresholdExample example, RowBounds rowBounds);

    List<CustomerOffGridFactorThreshold> selectByExample(CustomerOffGridFactorThresholdExample example);

    int updateByExampleSelective(@Param("record") CustomerOffGridFactorThreshold record, @Param("example") CustomerOffGridFactorThresholdExample example);

    int updateByExample(@Param("record") CustomerOffGridFactorThreshold record, @Param("example") CustomerOffGridFactorThresholdExample example);

    // 1.获取离网因素权重和阈值
    List<OffGridFactorThresholdResult> selectFactorByExample(CustomerOffGridFactorThresholdExample example);

    // 2.获取离网因素详情
    List<CustomerOffGridFactorThreshold> selectFactorDetailByExample(CustomerOffGridFactorThresholdExample example);

    // 4.设置离网因素权重阈值
    int insertFactorByBatch(List<CustomerOffGridFactorThreshold> list);

}