package com.cmiot.report.mapper;

import com.cmiot.report.bean.DeviceAll;
import com.cmiot.report.bean.DeviceAllByCount;
import com.cmiot.report.bean.DeviceAllExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DeviceAllMapper {
    long countByExample(DeviceAllExample example);

    int deleteByExample(DeviceAllExample example);

    int insert(DeviceAll record);

    int insertSelective(DeviceAll record);

    List<DeviceAll> selectByExampleWithRowbounds(DeviceAllExample example, RowBounds rowBounds);

    List<DeviceAll> selectByExample(DeviceAllExample example);

    int updateByExampleSelective(@Param("record") DeviceAll record, @Param("example") DeviceAllExample example);

    int updateByExample(@Param("record") DeviceAll record, @Param("example") DeviceAllExample example);
    
    /**
     * 根据日期、省份、地市编码分组统计查询设备量。
     */
    List<DeviceAllByCount> selectDeviceAllCountByExample(DeviceAllExample example);
    
    /**
     * 不根据日期分组查询。
     */
    List<DeviceAllByCount> selectDeviceAllCountNotByPdate(DeviceAllExample example);
}