package com.cmiot.report.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmiot.report.bean.FgeoOperation;
import com.cmiot.report.bean.FgeoOperationLog;
import com.cmiot.report.dto.syslog.SysLogDetailQueryRequest;
import com.cmiot.report.dto.syslog.SysLogQueryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Classname FgeoOperationMapper
 * @Description
 * @Date 2022/7/20 16:41
 * @Created by lei
 */

@Mapper
@Repository
public interface FgeoOperationMapper {
    long countOperation(SysLogQueryRequest sysLogQueryRequest);

    List<FgeoOperation> selectOperation(SysLogQueryRequest sysLogQueryRequest);

    long countOperationLog(SysLogDetailQueryRequest sysLogDetailQueryRequest);

    List<FgeoOperationLog> selectOperationLog(SysLogDetailQueryRequest sysLogDetailQueryRequest);

    long countOperationByGroupAccount(SysLogQueryRequest sysLogQueryRequest);

    List<FgeoOperation> selectOperationByGroupAccount(SysLogQueryRequest sysLogQueryRequest);


    long selectCountByGroupAccount4e(@Param("pam") SysLogQueryRequest sysLogQueryRequest);

    List<FgeoOperation> selectPageByGroupAccount4e(@Param("pam") SysLogQueryRequest sysLogQueryRequest);

    IPage<FgeoOperation> selectPageUserLogs(Page<FgeoOperation> pageData, @Param("pam") SysLogQueryRequest sysLogQueryRequest);

    FgeoOperation getUserLogByTraceId(String traceId);

    List<FgeoOperationLog> getLogDetail(String traceId);

}
