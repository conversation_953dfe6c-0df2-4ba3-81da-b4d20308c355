package com.cmiot.report.mapper;

import com.cmiot.report.bean.DictIndustry;
import com.cmiot.report.bean.DictIndustryExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface DictIndustryMapper {
    long countByExample(DictIndustryExample example);

    int deleteByExample(DictIndustryExample example);

    int insert(DictIndustry record);

    int insertSelective(DictIndustry record);

    List<DictIndustry> selectByExampleWithRowbounds(DictIndustryExample example, RowBounds rowBounds);

    List<DictIndustry> selectByExample(DictIndustryExample example);

    int updateByExampleSelective(@Param("record") DictIndustry record, @Param("example") DictIndustryExample example);

    int updateByExample(@Param("record") DictIndustry record, @Param("example") DictIndustryExample example);
}