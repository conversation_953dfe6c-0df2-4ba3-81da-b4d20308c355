package com.cmiot.report.mapper;

import com.cmiot.report.bean.*;

import java.util.List;

import com.cmiot.report.dto.GatewayAnomalyRecordQuery;
import com.cmiot.report.dto.OffGridCustExportVO;
import com.cmiot.report.dto.OffGridCustList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface OffGridCustomerMapper {
    long countByExample(OffGridCustomerExample example);

    int deleteByExample(OffGridCustomerExample example);

    int insert(OffGridCustomer record);

    int insertSelective(OffGridCustomer record);

    List<OffGridCustomer> selectByExampleWithRowbounds(OffGridCustomerExample example, RowBounds rowBounds);

    List<OffGridCustomer> selectByExample(OffGridCustomerExample example);

    int updateByExampleSelective(@Param("record") OffGridCustomer record, @Param("example") OffGridCustomerExample example);

    int updateByExample(@Param("record") OffGridCustomer record, @Param("example") OffGridCustomerExample example);

    List<OffGridCustList> selectListByExample(OffGridCustomerExample example);

    List<OffGridCustExportVO> selectExportByExample(OffGridCustomerExample example);

    //路线质量不佳用户
    List<GatewayAnomalyRecord> queryAnomalyRecordList(GatewayAnomalyRecordQuery query);

    //长期离线用户
    List<GatewayNotOnline> queryNotOnline(GatewayAnomalyRecordQuery query);

    List<CustomerUneffect> queryUneffect(GatewayAnomalyRecordQuery query);
}