package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayNetworkFlow;
import com.cmiot.report.bean.GatewayNetworkFlowExample;
import java.util.List;

import com.cmiot.report.bean.NetworkUsageDeviceCountData;
import com.cmiot.report.bean.NetworkUsageStaData;
import com.cmiot.report.dto.customer.GatewayNetworkFlowHourRequest;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayNetworkFlowMapper {
    long countByExample(GatewayNetworkFlowExample example);

    int deleteByExample(GatewayNetworkFlowExample example);

    int insert(GatewayNetworkFlow record);

    int insertSelective(GatewayNetworkFlow record);

    List<GatewayNetworkFlow> selectByExampleWithRowbounds(GatewayNetworkFlowExample example, RowBounds rowBounds);

    List<GatewayNetworkFlow> selectByExample(GatewayNetworkFlowExample example);

    int updateByExampleSelective(@Param("record") GatewayNetworkFlow record, @Param("example") GatewayNetworkFlowExample example);

    int updateByExample(@Param("record") GatewayNetworkFlow record, @Param("example") GatewayNetworkFlowExample example);


    List<NetworkUsageStaData> selectByCityExample(GatewayNetworkFlowExample example);

    List<NetworkUsageStaData> selectByProvExample(GatewayNetworkFlowExample example);

    List<NetworkUsageStaData> selectByIndustryExample(GatewayNetworkFlowExample example);

    List<NetworkUsageStaData> selectByTimeExample(GatewayNetworkFlowExample example);

    List<NetworkUsageDeviceCountData> selectDeviceCountByExample(GatewayNetworkFlowExample example);

    List<NetworkUsageDeviceCountData> selectDeviceHourCountByExample(GatewayNetworkFlowExample example);


    List<NetworkUsageStaData> selectByTimeHourExample(GatewayNetworkFlowExample example);

    List<NetworkUsageStaData> selectByTimeMonthExample(GatewayNetworkFlowExample example);


}