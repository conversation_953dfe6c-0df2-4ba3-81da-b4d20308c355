package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayReboot;
import com.cmiot.report.bean.GatewayRebootExample;
import java.util.List;

import com.cmiot.report.bean.plugin.EkitRestartAlertAnalysisDto;
import com.cmiot.report.bean.plugin.EkitRestartAlertAnalysisParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayRebootMapper {
    long countByExample(GatewayRebootExample example);

    int deleteByExample(GatewayRebootExample example);

    int insert(GatewayReboot record);

    int insertSelective(GatewayReboot record);

    List<GatewayReboot> selectByExampleWithRowbounds(GatewayRebootExample example, RowBounds rowBounds);

    List<GatewayReboot> selectByExample(GatewayRebootExample example);

    int updateByExampleSelective(@Param("record") GatewayReboot record, @Param("example") GatewayRebootExample example);

    int updateByExample(@Param("record") GatewayReboot record, @Param("example") GatewayRebootExample example);

    List<EkitRestartAlertAnalysisDto> ekitRestartAlertAnalysis(@Param("param") EkitRestartAlertAnalysisParam param,
                                                               @Param("pdate") String pdate);
}