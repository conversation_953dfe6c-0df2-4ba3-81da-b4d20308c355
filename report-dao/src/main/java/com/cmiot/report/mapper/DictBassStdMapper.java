package com.cmiot.report.mapper;

import com.cmiot.report.bean.DictBassStd;
import com.cmiot.report.bean.DictBassStdExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DictBassStdMapper {
    long countByExample(DictBassStdExample example);

    int deleteByExample(DictBassStdExample example);

    int insert(DictBassStd record);

    int insertSelective(DictBassStd record);

    List<DictBassStd> selectByExampleWithRowbounds(DictBassStdExample example, RowBounds rowBounds);

    List<DictBassStd> selectByExample(DictBassStdExample example);

    int updateByExampleSelective(@Param("record") DictBassStd record, @Param("example") DictBassStdExample example);

    int updateByExample(@Param("record") DictBassStd record, @Param("example") DictBassStdExample example);
}