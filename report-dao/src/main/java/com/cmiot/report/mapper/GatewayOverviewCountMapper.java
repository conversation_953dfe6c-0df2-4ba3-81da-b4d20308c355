package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayOverviewCount;
import com.cmiot.report.bean.GatewayOverviewCountExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface GatewayOverviewCountMapper {
    long countByExample(GatewayOverviewCountExample example);

    int deleteByExample(GatewayOverviewCountExample example);

    int insert(GatewayOverviewCount record);

    int insertSelective(GatewayOverviewCount record);

    List<GatewayOverviewCount> selectByExampleWithRowbounds(GatewayOverviewCountExample example, RowBounds rowBounds);

    List<GatewayOverviewCount> selectByExample(GatewayOverviewCountExample example);

    int updateByExampleSelective(@Param("record") GatewayOverviewCount record, @Param("example") GatewayOverviewCountExample example);

    int updateByExample(@Param("record") GatewayOverviewCount record, @Param("example") GatewayOverviewCountExample example);

    List<GatewayOverviewCount> selectByDay(@Param("day") Long day,
                                           @Param("provinceCodes")  List<String> provinceCodes);

}