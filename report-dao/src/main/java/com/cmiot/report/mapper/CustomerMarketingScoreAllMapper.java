package com.cmiot.report.mapper;

import com.cmiot.report.bean.customer.CustomerMarketingScoreAll;
import com.cmiot.report.bean.customer.CustomerMarketingScoreAllExample;
import java.util.List;

import com.cmiot.report.bean.customer.CustomerMarketingScoreQueryParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface CustomerMarketingScoreAllMapper {
    long countByExample(CustomerMarketingScoreAllExample example);

    int deleteByExample(CustomerMarketingScoreAllExample example);

    int insert(CustomerMarketingScoreAll record);

    int insertSelective(CustomerMarketingScoreAll record);

    List<CustomerMarketingScoreAll> selectByExampleWithRowbounds(CustomerMarketingScoreAllExample example, RowBounds rowBounds);

    List<CustomerMarketingScoreAll> selectByExample(CustomerMarketingScoreAllExample example);

    int updateByExampleSelective(@Param("record") CustomerMarketingScoreAll record, @Param("example") CustomerMarketingScoreAllExample example);

    int updateByExample(@Param("record") CustomerMarketingScoreAll record, @Param("example") CustomerMarketingScoreAllExample example);

    List<CustomerMarketingScoreAll> selectByParam(CustomerMarketingScoreQueryParam param);

    Long countByParam(CustomerMarketingScoreQueryParam queryParam);

}