package com.cmiot.report.mapper;

import com.cmiot.report.bean.DicAreaInfo;
import com.cmiot.report.bean.DicAreaInfoExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface DicAreaInfoMapper {
    long countByExample(DicAreaInfoExample example);

    int deleteByExample(DicAreaInfoExample example);

    int insert(DicAreaInfo record);

    int insertSelective(DicAreaInfo record);

    List<DicAreaInfo> selectByExampleWithRowbounds(DicAreaInfoExample example, RowBounds rowBounds);

    List<DicAreaInfo> selectByExample(DicAreaInfoExample example);

    int updateByExampleSelective(@Param("record") DicAreaInfo record, @Param("example") DicAreaInfoExample example);

    int updateByExample(@Param("record") DicAreaInfo record, @Param("example") DicAreaInfoExample example);

    List<DicAreaInfo> selectSubProvNameByExample(DicAreaInfoExample example);
}