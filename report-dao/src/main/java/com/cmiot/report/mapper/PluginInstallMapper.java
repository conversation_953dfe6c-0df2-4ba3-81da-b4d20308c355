package com.cmiot.report.mapper;

import com.cmiot.report.bean.PluginInstall;
import com.cmiot.report.bean.PluginInstallExample;

import java.util.Date;
import java.util.List;

import com.cmiot.report.dto.plugin.PluginModelCount;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface PluginInstallMapper {
    long countByExample(PluginInstallExample example);

    int deleteByExample(PluginInstallExample example);

    int insert(PluginInstall record);

    int insertSelective(PluginInstall record);

    List<PluginInstall> selectByExampleWithRowbounds(PluginInstallExample example, RowBounds rowBounds);

    List<PluginInstall> selectByExample(PluginInstallExample example);

    int updateByExampleSelective(@Param("record") PluginInstall record, @Param("example") PluginInstallExample example);

    int updateByExample(@Param("record") PluginInstall record, @Param("example") PluginInstallExample example);
    List<PluginModelCount> getInstallationByModelQuery(@Param("vendor") List<Long> vendor, @Param("model") List<Long> model
            , @Param("plugin") String plugin, @Param("version") List<String> version);
}