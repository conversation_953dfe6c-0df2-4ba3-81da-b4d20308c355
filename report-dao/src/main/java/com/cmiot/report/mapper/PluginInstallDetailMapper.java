package com.cmiot.report.mapper;

import com.cmiot.report.bean.PluginInstallDetail;
import com.cmiot.report.bean.PluginInstallDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface PluginInstallDetailMapper {
    long countByExample(PluginInstallDetailExample example);

    int deleteByExample(PluginInstallDetailExample example);

    int insert(PluginInstallDetail record);

    int insertSelective(PluginInstallDetail record);

    List<PluginInstallDetail> selectByExampleWithRowbounds(PluginInstallDetailExample example, RowBounds rowBounds);

    List<PluginInstallDetail> selectByExample(PluginInstallDetailExample example);

    int updateByExampleSelective(@Param("record") PluginInstallDetail record, @Param("example") PluginInstallDetailExample example);

    int updateByExample(@Param("record") PluginInstallDetail record, @Param("example") PluginInstallDetailExample example);
}