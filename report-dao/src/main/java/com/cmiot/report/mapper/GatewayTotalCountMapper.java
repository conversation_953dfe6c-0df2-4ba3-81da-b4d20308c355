package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayTotalCount;
import com.cmiot.report.bean.GatewayTotalCountDTO;
import com.cmiot.report.bean.GatewayTotalCountExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayTotalCountMapper {
    long countByExample(GatewayTotalCountExample example);

    int deleteByExample(GatewayTotalCountExample example);

    int insert(GatewayTotalCount record);

    int insertSelective(GatewayTotalCount record);

    List<GatewayTotalCount> selectByExampleWithRowbounds(GatewayTotalCountExample example, RowBounds rowBounds);

    List<GatewayTotalCount> selectByExample(GatewayTotalCountExample example);

    int updateByExampleSelective(@Param("record") GatewayTotalCount record, @Param("example") GatewayTotalCountExample example);

    int updateByExample(@Param("record") GatewayTotalCount record, @Param("example") GatewayTotalCountExample example);

    List<GatewayTotalCountDTO> selectByProvExample(GatewayTotalCountExample example);

    List<GatewayTotalCountDTO> selectByCityExample(GatewayTotalCountExample example);

    List<GatewayTotalCountDTO> selectByFactoryExample(GatewayTotalCountExample example);

    List<GatewayTotalCountDTO> selectByFactoryModelExample(GatewayTotalCountExample example);

}