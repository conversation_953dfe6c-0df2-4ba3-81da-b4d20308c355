package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayIncrementDetail;
import com.cmiot.report.bean.GatewayIncrementDetailCount;
import com.cmiot.report.bean.GatewayIncrementDetailExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface GatewayIncrementDetailMapper {
    long countByExample(GatewayIncrementDetailExample example);

    int deleteByExample(GatewayIncrementDetailExample example);

    int insert(GatewayIncrementDetail record);

    int insertSelective(GatewayIncrementDetail record);

    List<GatewayIncrementDetail> selectByExampleWithRowbounds(GatewayIncrementDetailExample example, RowBounds rowBounds);

    List<GatewayIncrementDetail> selectByExample(GatewayIncrementDetailExample example);

    int updateByExampleSelective(@Param("record") GatewayIncrementDetail record, @Param("example") GatewayIncrementDetailExample example);

    int updateByExample(@Param("record") GatewayIncrementDetail record, @Param("example") GatewayIncrementDetailExample example);

    List<GatewayIncrementDetailCount> selectCountByExample(GatewayIncrementDetailExample example);

}