package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayRebootDetail;
import com.cmiot.report.bean.GatewayRebootDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GatewayRebootDetailMapper {
    long countByExample(GatewayRebootDetailExample example);

    int deleteByExample(GatewayRebootDetailExample example);

    int insert(GatewayRebootDetail record);

    int insertSelective(GatewayRebootDetail record);

    List<GatewayRebootDetail> selectByExampleWithRowbounds(GatewayRebootDetailExample example, RowBounds rowBounds);

    List<GatewayRebootDetail> selectByExample(GatewayRebootDetailExample example);

    int updateByExampleSelective(@Param("record") GatewayRebootDetail record, @Param("example") GatewayRebootDetailExample example);

    int updateByExample(@Param("record") GatewayRebootDetail record, @Param("example") GatewayRebootDetailExample example);
}