package com.cmiot.report.mapper;

import com.cmiot.report.bean.CustomerOffGridoverallFactorThreshold;
import com.cmiot.report.bean.CustomerOffGridoverallFactorThresholdExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface CustomerOffGridoverallFactorThresholdMapper {
    long countByExample(CustomerOffGridoverallFactorThresholdExample example);

    int deleteByExample(CustomerOffGridoverallFactorThresholdExample example);

    int insert(CustomerOffGridoverallFactorThreshold record);

    int insertSelective(CustomerOffGridoverallFactorThreshold record);

    List<CustomerOffGridoverallFactorThreshold> selectByExampleWithRowbounds(CustomerOffGridoverallFactorThresholdExample example, RowBounds rowBounds);

    List<CustomerOffGridoverallFactorThreshold> selectByExample(CustomerOffGridoverallFactorThresholdExample example);

    int updateByExampleSelective(@Param("record") CustomerOffGridoverallFactorThreshold record, @Param("example") CustomerOffGridoverallFactorThresholdExample example);

    int updateByExample(@Param("record") CustomerOffGridoverallFactorThreshold record, @Param("example") CustomerOffGridoverallFactorThresholdExample example);

    int selectNewestByExample(CustomerOffGridoverallFactorThresholdExample example);

}