package com.cmiot.report.mapper;

import com.cmiot.report.bean.PonPowerListDetail;
import com.cmiot.report.bean.PonPowerListDetailExample;

import java.util.List;

import com.cmiot.report.dto.PonListDTO;
import com.cmiot.report.dto.PonPowerListDetailVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface PonPowerListDetailMapper {
    long countByExample(PonPowerListDetailExample example);

    int deleteByExample(PonPowerListDetailExample example);

    int insert(PonPowerListDetail record);

    int insertSelective(PonPowerListDetail record);

    List<PonPowerListDetail> selectByExampleWithRowbounds(PonPowerListDetailExample example, RowBounds rowBounds);

    List<PonPowerListDetail> selectByExample(PonPowerListDetailExample example);

    int updateByExampleSelective(@Param("record") PonPowerListDetail record, @Param("example") PonPowerListDetailExample example);

    int updateByExample(@Param("record") PonPowerListDetail record, @Param("example") PonPowerListDetailExample example);

    List<PonListDTO> selectDetailByExample(PonPowerListDetailExample example);

}