package com.cmiot.report.mapper;

import com.cmiot.report.bean.DeviceCumulativeAll;
import com.cmiot.report.bean.DeviceCumulativeAllCount;
import com.cmiot.report.bean.DeviceCumulativeAllExample;
import java.util.List;

import com.cmiot.report.dto.SubDeviceList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DeviceCumulativeAllMapper {
    long countByExample(DeviceCumulativeAllExample example);

    int deleteByExample(DeviceCumulativeAllExample example);

    int insert(DeviceCumulativeAll record);

    int insertSelective(DeviceCumulativeAll record);

    List<DeviceCumulativeAll> selectByExampleWithRowbounds(DeviceCumulativeAllExample example, RowBounds rowBounds);

    List<DeviceCumulativeAll> selectByExample(DeviceCumulativeAllExample example);

    int updateByExampleSelective(@Param("record") DeviceCumulativeAll record, @Param("example") DeviceCumulativeAllExample example);

    int updateByExample(@Param("record") DeviceCumulativeAll record, @Param("example") DeviceCumulativeAllExample example);

    List<SubDeviceList> selectListByExample(DeviceCumulativeAllExample example);
    
    /**
     * 统计各维度新增设备数量
     */
    List<DeviceCumulativeAllCount> countDistinctMacByExample(DeviceCumulativeAllExample example);
}