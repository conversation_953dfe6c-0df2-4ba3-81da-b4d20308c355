package com.cmiot.report.mapper;

import com.cmiot.report.bean.MediaNetworkVisitCount;
import com.cmiot.report.bean.MediaNetworkVisitCountExample;
import java.util.List;

import com.cmiot.report.bean.NetworkPerferCount;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface MediaNetworkVisitCountMapper {
    long countByExample(MediaNetworkVisitCountExample example);

    int deleteByExample(MediaNetworkVisitCountExample example);

    int insert(MediaNetworkVisitCount record);

    int insertSelective(MediaNetworkVisitCount record);

    List<MediaNetworkVisitCount> selectByExampleWithRowbounds(MediaNetworkVisitCountExample example, RowBounds rowBounds);

    List<MediaNetworkVisitCount> selectByExample(MediaNetworkVisitCountExample example);

    int updateByExampleSelective(@Param("record") MediaNetworkVisitCount record, @Param("example") MediaNetworkVisitCountExample example);

    int updateByExample(@Param("record") MediaNetworkVisitCount record, @Param("example") MediaNetworkVisitCountExample example);

    List<NetworkPerferCount> selectCount(@Param("provinceCodes") List<String> provinceCodes,
                                         @Param("cityCodes") List<String> cityCodes,
                                         @Param("startTime") Long startTime,
                                         @Param("endTime") Long endTime,
                                         @Param("type") String type);


}