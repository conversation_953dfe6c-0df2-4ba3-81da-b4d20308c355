package com.cmiot.report.mapper;

import com.cmiot.report.bean.GatewayAnomalyRecord;
import com.cmiot.report.bean.GatewayNotOnline;
import com.cmiot.report.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface GatewayMapper {

    List<GatewayDetailResult> getGatewayBaseDetail(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayDetailResult> getGatewayRunDataDetail(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayVersionResult> getGatewayVersionStatistics(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayFlowListResult> getGatewayFlowList(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayFlowTrendResult> getGatewayFlowOverview(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayFlowTrendResult> getGatewayFlowTrend(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayCpuAndRamResult> getGatewayCpuAndRamOverview(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayCpuAndRamResult> getGatewayCpuAndRamStatistics(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayCpuAndRamResult> rangeCpuAndRamOverLevelRatio(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayCpuOrRamRateResult> getGatewayCpuGradStatistics(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayCpuOrRamRateResult> getGatewayRamGradStatistics(@Param("query") GatewayQuery gatewayQuery);

//    List<GatewayCpuAndRamMaxTimeResult> getGatewayMaxCpuRamTime(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayCurrentResult> getGatewayLatestFlow(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayCpuRamListResult> getGatewayCpuRamList(@Param("query") GatewayQuery gatewayQuery);

    List<Integer> getGatewayRunTime(@Param("query") GatewayQuery gatewayQuery);

    // 网关时间超长列表
    List<GatewayRunTimeDetail> getGatewayRunTimeOver(@Param("query") GatewayQuery gatewayQuery);

    // 网关时间超长数量
    long getGatewayRunTimeOverCount(@Param("query") GatewayQuery gatewayQuery);

    // WAN口速率列表
    List<GatewayWanBandwidthDetail> getGatewayWanBandwidth(@Param("query") GatewayQuery gatewayQuery);


    List<GatewayWanBandwidthDetail> getGatewayFlowVelocity(@Param("query") GatewayQuery gatewayQuery);


    List<GatewayWanBandwidthDetail> getGatewayFlowVelocityByParam(@Param("query") GatewayQuery gatewayQuery);

    List<GatewayWanConnDetail> getGatewayWanConnByParam(@Param("query") GatewayQuery gatewayQuery);


    GateWayConnectNumberStatisticsResult getGatewayConnectNumberStatistics(@Param("query") GatewayQuery gatewayQuery,
                                                                         @Param("factoryIdList") List<Integer> factoryIdList,
                                                                         @Param("deviceModelIdList") List<Integer> deviceModelIdList);

    GateWayConnectNumberStatisticsResult getGatewayConnectNumberStatistics95(@Param("query") GatewayQuery gatewayQuery,
                                                                             @Param("factoryIdList") List<Integer> factoryIdList,
                                                                             @Param("deviceModelIdList") List<Integer> deviceModelIdList);

    List<GateWayConnectNumberResult> getGatewayConnectNumberList(@Param("query") GatewayQuery gatewayQuery,
                                                                 @Param("factoryIdList") List<Integer> factoryIdList,
                                                                 @Param("deviceModelIdList") List<Integer> deviceModelIdList);


    List<GateWayConnectNumberStaticResult> getGatewayConnectNumberBySn(@Param("query") GatewayQuery gatewayQuery);

    List<String> getGatewayWanConnServiceList(@Param("query") GatewayQuery gatewayQuery);


    List<AccessNetworkAnalysisResult> accessNetworkAnalysisList(@Param("query") GatewayQuery gatewayQuery);

}
