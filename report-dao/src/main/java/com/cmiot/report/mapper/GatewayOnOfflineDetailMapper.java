package com.cmiot.report.mapper;

import com.cmiot.report.bean.*;
import com.cmiot.report.dto.GatewayOnOfflineAllCount;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface GatewayOnOfflineDetailMapper {
    long countByExample(GatewayOnOfflineDetailExample example);

    int deleteByExample(GatewayOnOfflineDetailExample example);

    int insert(GatewayOnOfflineDetail record);

    int insertSelective(GatewayOnOfflineDetail record);

    List<GatewayOnOfflineDetail> selectByExampleWithRowbounds(GatewayOnOfflineDetailExample example, RowBounds rowBounds);

    List<GatewayOnOfflineDetail> selectByExample(GatewayOnOfflineDetailExample example);

    int updateByExampleSelective(@Param("record") GatewayOnOfflineDetail record, @Param("example") GatewayOnOfflineDetailExample example);

    int updateByExample(@Param("record") GatewayOnOfflineDetail record, @Param("example") GatewayOnOfflineDetailExample example);

    List<GatewayOnOfflineCount> selectCountByExample(GatewayOnOfflineDetailExample example);

    List<GatewayOnOfflineMiniCount> selectMiniCountByExample(GatewayOnOfflineDetailExample example);

    List<GatewayOnOffLineNumOverviewCount> selectOverviewCountByExample(GatewayOnOfflineDetailExample example);

    List<GatewayOnOfflineCount> selectWeekOrMonthOnCountByExample(GatewayOnOfflineDetailExample example);

    List<GatewayOnOfflineCount> selectWeekOrMonthOffCountByExample(GatewayOnOfflineDetailExample example);

    List<GatewayOnOfflineAllCount> selectAllCountByProv(GatewayOnOfflineDetailExample example);

    List<GatewayOnOfflineAllCount> selectAllCountByCity(GatewayOnOfflineDetailExample example);

    List<GatewayOnOfflineAllCount> selectAllCountByVendor(GatewayOnOfflineDetailExample example);

    List<GatewayOnOfflineCount> selectAllCountByExample(GatewayOnOfflineDetailExample example);
}