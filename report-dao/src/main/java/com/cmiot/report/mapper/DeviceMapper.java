package com.cmiot.report.mapper;

import com.cmiot.report.dto.DeviceAppStatRunVo;
import com.cmiot.report.dto.GatewayQuery;
import com.cmiot.report.dto.device.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeviceMapper {

    //累计设备表查询设备累计总量
    DeviceOverviewCount queryDeviceOverviewCountAll(@Param("provinceCodes") List<String> provinceCodes);

    //设备数量趋势表，查询活跃总量、有线活跃总量、无线活跃总量
    int queryOverviewSelectCount(DeviceQuery deviceQuery);

    //设备数量趋势表，查询按区域、网关厂商、用户行业统计排名
    List<DeviceOverviewObject> queryDeviceOverviewResult(DeviceQuery deviceQuery);

    //设备wlan趋势表查询，
    List<DeviceWLANObject> queryDeviceWLANResult(DeviceQuery deviceQuery);

    //小程序：用户设备数量概览
    DeviceAppStatCount queryDeviceAppStatCountTotal(Long enterpriseId);
    //小程序：用户设备数量概览
    DeviceAppStatCount queryDeviceAppStatCountTotalWeek(@Param("enterpriseId") Long enterpriseId,
                                                        @Param("dayStart")int dayStart,
                                                        @Param("dayEnd")int dayEnd);
    //小程序：用户设备数量概览
    DeviceAppStatCount queryDeviceAppStatCountTotalMonth(@Param("enterpriseId") Long enterpriseId,
                                                        @Param("lastMonth")int lastMonth);
    //小程序：用户设备数量趋势
    List<DeviceAppStatTrendObject> queryDeviceAppStatTrendCount(Long enterpriseId, String startDate, String endDate);

    //小程序：用户wlan信号趋势
    List<DeviceAppStatTrendObject> queryDeviceAppStatTrendWlan(Long enterpriseId, String startDate, String endDate);

    //小程序：用户运行报告，统计周期内累计下挂设备数量
    Integer queryDeviceAppStatRunCount(GatewayQuery gatewayQuery);

    //小程序：用户运行报告,统计周期内通过Wifi连接的下挂设备数量,下挂设备平均网速(单位mb/s,保留两位小数),平均连接比例(保留两位小数),下挂设备使用总流量(单位GB,保留两位小数)
    DeviceAppStatRunVo queryDeviceAppStatRunSpeed(GatewayQuery gatewayQuery);


    List<DeviceFlowInfo> queryDeviceFlowRank(@Param("enterpriseId") Long enterpriseId,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate,
                                             @Param("limit") Integer limit);

    List<DeviceTimesInfo> queryDeviceTimesRank(@Param("enterpriseId") Long enterpriseId,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate,
                                               @Param("limit") Integer limit);
}
