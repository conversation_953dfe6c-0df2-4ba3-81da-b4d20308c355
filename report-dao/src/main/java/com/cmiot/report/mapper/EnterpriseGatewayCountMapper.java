package com.cmiot.report.mapper;

import com.cmiot.report.dto.enterprise.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Classname EnterpriseGatewayCountMapper
 * @Description
 * @Date 2023/8/1 9:05
 * @Created by lei
 */
public interface EnterpriseGatewayCountMapper {
    /**
     * 企业下，网关概览统计,昨日统计值
     *
     * @param eid
     * @param day
     * @return
     */
    EnterpriseGatewayCountInfo getDeviceOverviewDay(@Param("eid") Long eid, @Param("day") String day);


    /**
     * 企业下，网关概览统计,周统计值
     *
     * @param eid
     * @param weekDay
     * @return
     */
    EnterpriseGatewayCountInfo getDeviceOverviewWeek(@Param("eid") Long eid, @Param("weekDay") String weekDay);


    /**
     * 企业下，网关概览统计,月统计值
     *
     * @param eid
     * @param month
     * @return
     */
    EnterpriseGatewayCountInfo getDeviceOverviewMonth(@Param("eid") Long eid, @Param("month") String month);

    /**
     * 企业下，历史活跃统计，月度统计
     *
     * @param eid
     * @param monthStart
     * @param monthEnd
     * @return
     */
    List<DeviceMonthActiveInfo> getDeviceMonthActiveRecord(@Param("eid") Long eid,
                                                           @Param("monthStart") String monthStart,
                                                           @Param("monthEnd") String monthEnd);

    /**
     * @param eid
     * @param startDate
     * @param endDate
     * @return
     */
    List<DeviceEnterpriseCountInfo> getSubDeviceDayCount(@Param("eid") Long eid,
                                                         @Param("startDate") String startDate,
                                                         @Param("endDate") String endDate);

    /**
     * @param eid
     * @param startDate
     * @param endDate
     * @return
     */
    List<DeviceEnterpriseCountInfo> getSubDeviceWeekCount(@Param("eid") Long eid,
                                                          @Param("startDate") String startDate,
                                                          @Param("endDate") String endDate);

    /**
     * @param eid
     * @param startDate
     * @param endDate
     * @return
     */
    List<DeviceEnterpriseCountInfo> getSubDeviceMonthCount(@Param("eid") Long eid,
                                                           @Param("startDate") String startDate,
                                                           @Param("endDate") String endDate);

    List<GatewayEnterpriseUseCountInfo> getEnterpriseDeviceUseCount(@Param("eid") Long eid,
                                                                    @Param("startDate") String startDate,
                                                                    @Param("endDate") String endDate);

    List<EnterpriseNetworkTotalInfo> getEnterpriseNetworkTotalCount(@Param("enterpriseId") Long enterpriseId,
                                                                    @Param("startDate") String startDate,
                                                                    @Param("endDate") String endDate);

    List<EnterpriseVisitsCountInfo> getWeekReportVisitsCountRank(@Param("enterpriseId") Long enterpriseId,
                                                                 @Param("startDate") String startDate,
                                                                 @Param("endDate") String endDate,
                                                                 @Param("rankLimit") Integer rankLimit);


}
