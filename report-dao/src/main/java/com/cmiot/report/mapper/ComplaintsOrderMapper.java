package com.cmiot.report.mapper;

import com.cmiot.report.bean.ComplaintsOrder;
import com.cmiot.report.bean.ComplaintsOrderExample;
import java.util.List;

import com.cmiot.report.dto.ServicePieData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ComplaintsOrderMapper {
    long countByExample(ComplaintsOrderExample example);

    int deleteByExample(ComplaintsOrderExample example);

    int insert(ComplaintsOrder record);

    int insertSelective(ComplaintsOrder record);

    List<ComplaintsOrder> selectByExampleWithRowbounds(ComplaintsOrderExample example, RowBounds rowBounds);

    List<ComplaintsOrder> selectByExample(ComplaintsOrderExample example);

    int updateByExampleSelective(@Param("record") ComplaintsOrder record, @Param("example") ComplaintsOrderExample example);

    int updateByExample(@Param("record") ComplaintsOrder record, @Param("example") ComplaintsOrderExample example);

    // 投诉类型统计
    List<ServicePieData> selectCountByExample(ComplaintsOrderExample example);
}