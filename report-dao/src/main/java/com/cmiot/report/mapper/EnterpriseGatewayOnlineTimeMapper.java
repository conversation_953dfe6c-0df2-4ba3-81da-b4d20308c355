package com.cmiot.report.mapper;

import com.cmiot.report.dto.enterprise.EnterpriseGatewayOnlineTimeInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EnterpriseGatewayOnlineTimeMapper {
    //根据企业网关mac集合查询运行时长
    List<EnterpriseGatewayOnlineTimeInfo> getOnlineTimeList(@Param("macList") List<String> macList,
                                                         @Param("startDate") String startDate,
                                                         @Param("endDate") String endDate);
}
