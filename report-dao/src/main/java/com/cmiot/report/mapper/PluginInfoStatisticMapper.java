package com.cmiot.report.mapper;

import com.cmiot.report.dto.plugin.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Classname PluginInfoStatisticMapper
 * @Description
 * @Date 2024/1/25 15:43
 * @Created by lei
 */
public interface PluginInfoStatisticMapper {
    List<PluginInfoMonthCount> getPluginInfoOverview(@Param("lastMonth") String lastMonth,
                                                     @Param("bundleIds") List<String> bundleIds,
                                                     @Param("provinceIds") List<String> provinceIds,
                                                     @Param("deviceModelIds") List<String> deviceModelIds);


    List<PluginInfoMonthCount> getPluginInfoOverviewGroupProvince(@Param("lastMonth") String lastMonth,
                                                                  @Param("bundleIds") List<String> bundleIds,
                                                                  @Param("provinceIds") List<String> provinceIds,
                                                                  @Param("deviceModelIds") List<String> deviceModelIds);

    List<PluginInfoDayCount> getPluginInfoDayOverview(@Param("lastDay") String lastDay,
                                                      @Param("bundleIds") List<String> bundleIds,
                                                      @Param("provinceIds") List<String> provinceIds,
                                                      @Param("deviceModelIds") List<String> deviceModelIds);

    List<PluginInfoTotalOverviewCount> getPluginOverviewActiveCount(@Param("activeTime") Integer activeTime,
                                                                    @Param("bundleIds") List<String> bundleIds,
                                                                    @Param("provinceIds") List<String> provinceIds,
                                                                    @Param("deviceModelIds") List<String> deviceModelIds);


    List<PluginInfoTotalOverviewCount> getPluginOverviewTotalCount(@Param("bundleIds") List<String> bundleIds,
                                                                   @Param("provinceIds") List<String> provinceIds,
                                                                   @Param("deviceModelIds") List<String> deviceModelIds);


    List<PluginInfoTotalOverviewCount> getPluginOverviewTotalCountGroupProvince(@Param("bundleIds") List<String> bundleIds,
                                                                                @Param("provinceIds") List<String> provinceIds,
                                                                                @Param("deviceModelIds") List<String> deviceModelIds);

    List<PluginInfoDayCount> getPluginInfoIncrOverview(@Param("startTime") String startTime,
                                                       @Param("endTime") String endTime,
                                                       @Param("bundleIds") List<String> bundleIds,
                                                       @Param("provinceIds") List<String> provinceIds,
                                                       @Param("deviceModelIds") List<String> deviceModelIds);


    List<PluginBaseInfo> getPluginBaseInfo();


    PluginBaseInfo getPluginBaseInfoById(@Param("pluginId") Integer pluginId);

    List<PluginInfoDayCount> getPluginInfoDayCount(@Param("pdate") String pdate, @Param("bundleId")  String bundleId);

    void insertPluginInfoDayCount(PluginInfoDayCount pluginInfoDayCount);

    List<PluginInfoMonthCount> getPluginInfoMonthCount(@Param("pdate") String pdate, @Param("bundleId")  String bundleId);

    void insertPluginInfoMonthCount(PluginInfoMonthCount pluginInfoMonthCount);

    /**
     * 获取插件Osgi信息
     * @param pluginId
     * @return
     */
    List<PluginBaseInfo> getPluginOsgiBaseInfo(@Param("pluginId")Integer pluginId);

    /**
     * 近N月插件安装量--求环比
     * @param pluginId
     * @param month
     * @return
     */
    List<PluginStatisticInfo> getPluginOsgiInstallCount(@Param("pluginName") String pluginName,
                                                        @Param("provinceCodes") List<String> provinceCodes,
                                                        @Param("factoryIds") List<String> factoryIds,
                                                        @Param("deviceModelIds") List<String> deviceModelIds,
                                                        @Param("pluginVersion") String pluginVersion,
                                                        @Param("month") Integer month);

    /**
     * 统计各个插件不同版本的激活量
     * @return
     */
    List<PluginStatisticInfo> getPluginOsgiInstallCountGroupVersion();

    /**
     * 插件近一天活跃量
     * @param pluginName
     * @return
     */
    List<PluginStatisticInfo> getPluginOsgiDayLiveNum(String pluginName);

    /**
     * 插件近一个月活跃量
     * @param pluginName
     * @return
     */
    List<PluginStatisticInfo> getPluginOsgiMonthLiveNum(String pluginName);


    /**
     * 分省插件安装量
     * @param pluginName 插件名称
     * @param provinceCodes 省份：默认全选
     * @param factoryIds 厂商型号
     * @param deviceModelIds 设备类型
     * @param pluginVersion  插件版本：默认全量版本，可以下拉选择各个版本号
     * @param startDate 开始时间-结束时间，弹出日历选择年、月、日，不填表示全时间段
     * @param endDate 开始时间-结束时间，弹出日历选择年、月、日，不填表示全时间段
     * @return
     */
    @MapKey("pluginId")
    List<PluginStatisticInfo> getPluginOsgiInstallCountByParams(@Param("pluginName") String pluginName,
                                                        @Param("provinceCodes") List<String> provinceCodes,
                                                        @Param("factoryIds") List<String> factoryIds,
                                                        @Param("deviceModelIds") List<String> deviceModelIds,
                                                        @Param("pluginVersion") String pluginVersion,
                                                        @Param("startDate") String startDate,
                                                        @Param("endDate") String endDate);

    //分省 插件近一天
    Long getPluginOsgiLiveNumByParams(@Param("pluginName") String pluginName,
                                      @Param("provinceCodes") List<String> provinceCodes,
                                      @Param("factoryIds") List<String> factoryIds,
                                      @Param("deviceModelIds") List<String> deviceModelIds,
                                      @Param("pluginVersion") String pluginVersion);
    //分省 一月活跃量
    Long getPluginOsgiMonthLiveNumByParams(@Param("pluginName") String pluginName,
                                      @Param("provinceCodes") List<String> provinceCodes,
                                      @Param("factoryIds") List<String> factoryIds,
                                      @Param("deviceModelIds") List<String> deviceModelIds,
                                      @Param("pluginVersion") String pluginVersion);

    //按省分组查询每个省的企业网关数量
    List<PluginStatisticInfo> getGatewayCountByGroupProvince(@Param("provinceCodes") List<String> provinceCodes);


}
