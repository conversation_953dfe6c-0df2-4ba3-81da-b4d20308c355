package com.cmiot.report.dto;

import lombok.Data;

@Data
public class GatewayDetailResult {
    private String gatewaySn;
    private String provinceCode;
    private String adslAccount;
    private String gatewayMac;
    private String wanIpAddr;
    private String wanIpv6Addr;
    private String factoryName;
    private String productType;
    private String productBand;
    private String hardwareVersion;
    private String firmwareVersion;
    private String osVersion;
    private String registerTime;
    private Integer lanCount;
    private Integer usbCount;
    private String cpuClass;
    private Integer ramSize;
    private Double cpuRate;
    private Double ramRate;
    private Long runingTime;
    private String isOnline;
    //上行流量均值
    private Double avgTxrate;
    //下行流量均值
    private Double avgRxrate;
    //上行流量峰值
    private Double maxTxrate;
    //下行流峰值
    private Double maxRxrate;
}
