package com.cmiot.report.dto.pppoe;

import java.io.Serializable;
import java.util.List;

public class PppoeListResult implements Serializable {
	private static final long serialVersionUID = -3441758173409264494L;

	/**
	 * 当前页
	 */
	private Integer page;
	
	/**
	 * 每页展示数据量
	 */
	private Integer pageSize;
	
	/**
	 * 总数据量
	 */
	private Long total;
	
	/**
	 * 数据明细
	 */
	private List<PppoeGateway> list;
	
	
	
	public Integer getPage() {
		return page;
	}



	public void setPage(Integer page) {
		this.page = page;
	}



	public Integer getPageSize() {
		return pageSize;
	}



	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}



	public Long getTotal() {
		return total;
	}



	public void setTotal(Long total) {
		this.total = total;
	}



	public List<PppoeGateway> getList() {
		return list;
	}



	public void setList(List<PppoeGateway> list) {
		this.list = list;
	}



	@Override
	public String toString() {
		return "PppoeListResult [page=" + page + ", pageSize=" + pageSize + ", total=" + total + ", list=" + list + "]";
	}



	/**
	 * pppoe页面展示网关信息
	 * <AUTHOR>
	 */
	public static class PppoeGateway implements Serializable {
		private static final long serialVersionUID = -7997277754805210527L;

		/**
		 * 数据编号
		 */
		private Long id;
		
		/**
		 * 省份
		 */
		private String province;
		
		/**
		 * 地市
		 */
		private String city;
		
		/**
		 * 集团名称
		 */
		private String groupName;
		
		/**
		 * 网关SN
		 */
		private String sn;
		
		/**
		 * 网关MAC
		 */
		private String mac;
		
		/**
		 * 网关厂商
		 */
		private String vendor;
		
		/**
		 * 网关型号
		 */
		private String model;
		
		/**
		 * 累计运行时长 (秒)
		 */
		private Long runningTime;
		
		@Override
		public String toString() {
			return "PppoeGateway [id=" + id + ", province=" + province + ", city=" + city + ", groupName=" + groupName
					+ ", sn=" + sn + ", mac=" + mac + ", vendor=" + vendor + ", model=" + model + ", runningTime="
					+ runningTime + "]";
		}

		public Long getId() {
			return id;
		}

		public void setId(Long id) {
			this.id = id;
		}

		public String getProvince() {
			return province;
		}

		public void setProvince(String province) {
			this.province = province;
		}

		public String getCity() {
			return city;
		}

		public void setCity(String city) {
			this.city = city;
		}

		public String getGroupName() {
			return groupName;
		}

		public void setGroupName(String groupName) {
			this.groupName = groupName;
		}

		public String getSn() {
			return sn;
		}

		public void setSn(String sn) {
			this.sn = sn;
		}

		public String getMac() {
			return mac;
		}

		public void setMac(String mac) {
			this.mac = mac;
		}

		public String getVendor() {
			return vendor;
		}

		public void setVendor(String vendor) {
			this.vendor = vendor;
		}

		public String getModel() {
			return model;
		}

		public void setModel(String model) {
			this.model = model;
		}

		public Long getRunningTime() {
			return runningTime;
		}

		public void setRunningTime(Long runningTime) {
			this.runningTime = runningTime;
		}
		
		
	}
	
}
