package com.cmiot.report.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cmiot.report.poi.ExcelColumn;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value="t_gateway_connect_number_day_count_all")
public class GateWayConnectNumberResult implements Serializable {
    private static final long serialVersionUID = 7453991100858453400L;

    @TableField(value="pdate")
    private String pdate;

//    @ExcelProperty(value = "网关SN", index = 0)
    @ExcelColumn(value = "网关SN", col = 4)
    @TableField(value="gateway_sn")
    private String gatewaySn;

//    @ExcelProperty(value = "网关MAC", index = 1)
    @ExcelColumn(value = "网关MAC", col = 5)
    @TableField(value="gateway_mac")
    private String gatewayMac;

    @TableField(value="province_code")
    private String provinceCode;

//    @ExcelProperty(value = "省份", index = 2)
    @ExcelColumn(value = "省份", col = 1)
    @TableField(value="province")
    private String province;

    @TableField(value="city_code")
    private String cityCode;

//    @ExcelProperty(value = "地市", index = 3)
    @ExcelColumn(value = "地市", col = 2)
    @TableField(value="city")
    private String city;

//    @ExcelProperty(value = "企业名称", index = 4)
    @ExcelColumn(value = "企业名称", col = 3)
    @TableField(value="customer_name")
    private String customerName;

//    @ExcelProperty(value = "厂商", index = 5)
    @ExcelColumn(value = "厂商", col = 6)
    @TableField(value="vendor")
    private String vendor;

//    @ExcelProperty(value = "型号", index = 6)
    @ExcelColumn(value = "型号", col = 7)
    @TableField(value="model")
    private String model;

//    @ExcelProperty(value = "平均连接数", index = 7)
    @ExcelColumn(value = "平均连接数", col = 9)
    @TableField(value="avg_connect_number")
    private Integer avgConnectNumber;

//    @ExcelProperty(value = "最大连接数", index = 8)
    @ExcelColumn(value = "最大连接数", col = 10)
    @TableField(value="max_connect_number")
    private Integer maxConnectNumber;

//    @ExcelProperty(value = "累计运行时长（秒）", index = 9)
    @ExcelColumn(value = "累计运行时长（秒）", col = 8)
    @TableField(value="runingTime")
    private Integer runingTime;

    @TableId(value="id", type = IdType.AUTO)
    private String id;

    @ExcelColumn(value = "统计周期", col = 11)
    private String statisticalPeriod;

}
