package com.cmiot.report.dto;


import java.util.List;

public class SubDeviceListResult {
    private long page;
    private long pageSize;
    private long total;
    private List<SubDeviceList> list;

    public long getPage() {
        return page;
    }

    public void setPage(long page) {
        this.page = page;
    }

    public long getPageSize() {
        return pageSize;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public List<SubDeviceList> getList() {
        return list;
    }

    public void setList(List<SubDeviceList> list) {
        this.list = list;
    }
}


