package com.cmiot.report.dto;


import lombok.Data;

import java.io.Serializable;

@Data
public class GatewayOnOfflineCountRequest implements Serializable {

    private static final long serialVersionUID = 6790118149230293812L;

    private String province;

    private String city;

    private String vendorId;

    private String startTime;

    private String endTime;

    // 1厂商 2区域
    private String type;

    private String vendorCode;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }


    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getVendorId() {
        return vendorId;
    }

    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    @Override
    public String toString() {
        return "GatewayOnOfflineCountRequest{" +
                "province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", vendorId='" + vendorId + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", type='" + type + '\'' +
                ", vendorCode='" + vendorCode + '\'' +
                '}';
    }
}
