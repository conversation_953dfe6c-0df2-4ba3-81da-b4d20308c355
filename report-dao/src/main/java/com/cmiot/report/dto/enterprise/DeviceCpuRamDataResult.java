package com.cmiot.report.dto.enterprise;

import java.util.List;

/**
 * @Classname SubDeviceFlowDataResult
 * @Description
 * @Date 2023/8/15 10:15
 * @Created by lei
 */
public class DeviceCpuRamDataResult {

    private List<String> time;
    private List<String> cpuAverage;
    private List<String> ramAverage;

    public List<String> getTime() {
        return time;
    }

    public void setTime(List<String> time) {
        this.time = time;
    }

    public List<String> getCpuAverage() {
        return cpuAverage;
    }

    public void setCpuAverage(List<String> cpuAverage) {
        this.cpuAverage = cpuAverage;
    }

    public List<String> getRamAverage() {
        return ramAverage;
    }

    public void setRamAverage(List<String> ramAverage) {
        this.ramAverage = ramAverage;
    }
}
