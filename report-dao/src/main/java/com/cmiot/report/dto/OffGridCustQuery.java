package com.cmiot.report.dto;

import lombok.Data;

import java.util.List;

@Data
public class OffGridCustQuery {
    // 省份
    private List<String> province;

    // 地市
    private List<String> city;

    // 企业名称
    private String business;

    // 集团客户标识
    private String groupCustomer;

    // 企业价值分类
    private String enterpriseValueCategory;

    // 客户状态
    private String customerStatus;

    // 综合离网指数开始值
    private int syntheticalStart;

    // 综合离网指数结束值
    private int syntheticalEnd;

    // 终端质量因素分开始值
    private int terminalQualityStart;

    // 终端质量因素分结束值
    private int terminalQualityEnd;

    // 网络质量因素分开始值
    private int networkQualityStart;

    // 网络质量因素分结束值
    private int networkQualityEnd;

    // 客户服务因素分开始值
    private int customerServiceStart;

    // 客户服务因素分结束值
    private int customerServiceEnd;

    // 业务订购因素分开始值
    private int businessOrderStart;

    // 业务订购因素分结束值
    private int businessOrderEnd;

    // 装维服务因素分开始值
    private int installationAndMaintenanceServiceStart;

    // 装维服务因素分结束值
    private int installationAndMaintenanceServiceEnd;

}
