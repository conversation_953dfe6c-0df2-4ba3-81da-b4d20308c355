package com.cmiot.report.dto.device;

/**
 * @Classname DeviceFlowInfo
 * @Description
 * @Date 2023/12/25 18:03
 * @Created by lei
 */
public class DeviceFlowInfo {
    private String mac;
    private String deviceName;
    private String dhcpName;
    private String deviceType;
    private Double flow;

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDhcpName() {
        return dhcpName;
    }

    public void setDhcpName(String dhcpName) {
        this.dhcpName = dhcpName;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public Double getFlow() {
        return flow;
    }

    public void setFlow(Double flow) {
        this.flow = flow;
    }
}
