package com.cmiot.report.dto;


import lombok.Data;

@Data
public class OffGridCustList {
    // 企业名称
    private String business;

    // 归属地区
    private String areaName;

    // 集团客户标识
    private String groupCustomer;

    // 企业价值分类
    private String enterpriseValueCategory;

    // 客户状态
    private String customerStatus;

    // 综合离网指数
    private String syntheticalValue;

    // 综合离网指数是否超过阈值 1-是,0-否
    private String syntheticalTag;

    // 终端质量因素分
    private String terminalQualityValue;

    // 终端质量因素分是否超过阈值 1-是,0-否
    private String terminalQualityTag;

    // 网络质量因素分
    private String networkQualityValue;

    // 网络质量因素分是否超过阈值 1-是,0-否
    private String networkQualityTag;

    // 客户服务因素分
    private String customerServiceValue;

    // 客户服务因素分是否超过阈值 1-是,0-否
    private String customerServiceTag;

    // 业务订购因素分
    private String businessOrderValue;

    // 业务订购因素分是否超过阈值 1-是,0-否
    private String businessOrderTag;

    // 装维服务因素分
    private String installationAndMaintenanceServiceValue;

    // 装维服务因素分是否超过阈值 1-是,0-否
    private String installationAndMaintenanceServiceTag;

    // 企业客户id
    private String id;
    
}
