package com.cmiot.report.dto;


import java.io.Serializable;
import java.util.List;

public class GatewayAlarmListResult implements Serializable {

    private static final long serialVersionUID = 230885701181305631L;

    private List<GatewayAlarmListDTO> list;

    private long page;

    private long pageSize;

    private long total;

    public List<GatewayAlarmListDTO> getList() {
        return list;
    }

    public void setList(List<GatewayAlarmListDTO> list) {
        this.list = list;
    }

    public long getPage() {
        return page;
    }

    public void setPage(long page) {
        this.page = page;
    }

    public long getPageSize() {
        return pageSize;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }
}

