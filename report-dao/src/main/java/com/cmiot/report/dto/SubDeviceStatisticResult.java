package com.cmiot.report.dto;


import java.io.Serializable;
import java.util.List;

public class SubDeviceStatisticResult implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = -8345844784937068830L;
	// 下挂设备数量排行
    private List<HangingDeviceRange> hangingDeviceRange;
    //
    private List<String> xAxis;
    // 活跃量
    private List<Long> activeTotal;
    // 无线设备活跃量
    private List<Long> wirelessActiveNum;
    // 有线设备活跃量
    private List<Long> wiredActiveNum;

    public List<HangingDeviceRange> getHangingDeviceRange() {
        return hangingDeviceRange;
    }

    public void setHangingDeviceRange(List<HangingDeviceRange> hangingDeviceRange) {
        this.hangingDeviceRange = hangingDeviceRange;
    }

    public List<String> getxAxis() {
        return xAxis;
    }

    public void setxAxis(List<String> xAxis) {
        this.xAxis = xAxis;
    }

    public List<Long> getActiveTotal() {
        return activeTotal;
    }

    public void setActiveTotal(List<Long> activeTotal) {
        this.activeTotal = activeTotal;
    }

    public List<Long> getWirelessActiveNum() {
        return wirelessActiveNum;
    }

    public void setWirelessActiveNum(List<Long> wirelessActiveNum) {
        this.wirelessActiveNum = wirelessActiveNum;
    }

    public List<Long> getWiredActiveNum() {
        return wiredActiveNum;
    }

    public void setWiredActiveNum(List<Long> wiredActiveNum) {
        this.wiredActiveNum = wiredActiveNum;
    }
    
    
    
    @Override
	public String toString() {
		return "SubDeviceStatisticResult [hangingDeviceRange=" + hangingDeviceRange + ", xAxis=" + xAxis
				+ ", activeTotal=" + activeTotal + ", wirelessActiveNum=" + wirelessActiveNum + ", wiredActiveNum="
				+ wiredActiveNum + "]";
	}



	public static class HangingDeviceRange implements Serializable {
        /**
		 * 
		 */
		private static final long serialVersionUID = 7372049693768194806L;
		// 区域
        private String name;
        // 数量
        private long num;
        
        public HangingDeviceRange() {}
        public HangingDeviceRange(String name, long num) {
        	this.name = name;
        	this.num = num;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public long getNum() {
            return num;
        }

        public void setNum(long num) {
            this.num = num;
        }

		@Override
		public String toString() {
			return "HangingDeviceRange [name=" + name + ", num=" + num + "]";
		}
        
        
        
    }
}



