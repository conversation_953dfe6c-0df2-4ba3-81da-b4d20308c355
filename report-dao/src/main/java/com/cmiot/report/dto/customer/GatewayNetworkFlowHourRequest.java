package com.cmiot.report.dto.customer;

import java.util.List;

/**
 * @Classname GatewayNetworkFlowHourRequest
 * @Description
 * @Date 2022/9/6 17:25
 * @Created by lei
 */


public class GatewayNetworkFlowHourRequest {

    private List<String> provinceCode;
    private List<String> cityCode;
    private List<String> industry;
    private List<String> groupBusinessType;
    private Long gdate;


    public List<String> getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(List<String> provinceCode) {
        this.provinceCode = provinceCode;
    }

    public List<String> getCityCode() {
        return cityCode;
    }

    public void setCityCode(List<String> cityCode) {
        this.cityCode = cityCode;
    }

    public List<String> getIndustry() {
        return industry;
    }

    public void setIndustry(List<String> industry) {
        this.industry = industry;
    }

    public List<String> getGroupBusinessType() {
        return groupBusinessType;
    }

    public void setGroupBusinessType(List<String> groupBusinessType) {
        this.groupBusinessType = groupBusinessType;
    }

    public Long getGdate() {
        return gdate;
    }

    public void setGdate(Long gdate) {
        this.gdate = gdate;
    }
}
