package com.cmiot.report.dto;


import java.io.Serializable;

public class GatewayOnOffLineNumOverviewResult implements Serializable {

    private static final long serialVersionUID = 4877435005116003137L;

    private String totalDevice;

    private String onlineDevice;

    private String offlineDevice;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getTotalDevice() {
        return totalDevice;
    }

    public void setTotalDevice(String totalDevice) {
        this.totalDevice = totalDevice;
    }

    public String getOnlineDevice() {
        return onlineDevice;
    }

    public void setOnlineDevice(String onlineDevice) {
        this.onlineDevice = onlineDevice;
    }

    public String getOfflineDevice() {
        return offlineDevice;
    }

    public void setOfflineDevice(String offlineDevice) {
        this.offlineDevice = offlineDevice;
    }
}
