package com.cmiot.report.dto;


import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class ServiceStatisticResult {

    // 装移机及时率
    float timelyRateOfInstallation;

    // 故障维修及时率
    float timelyMaintenanceRate;

    // 故障解决率
    float failureResolutionRate;

    // 投诉解决率
    float complaintResolutionRate;

    // 投诉处理及时率
    float timelyComplaintRate;

    // 客服准确率
    float customerServiceAccuracy;

    // 饼图
    private List<ServicePieData> pieData;


}
