package com.cmiot.report.dto;


import java.math.BigDecimal;

public class AlarmThresholdRequest {

    private BigDecimal dnsAnalysisDuration;

    private  BigDecimal tcpHandshakeDuration;

    private  BigDecimal httpResponseDuration;

    private  BigDecimal cpuRamHighUseNum;

    private  BigDecimal toAlarmTimes;

    public BigDecimal getDnsAnalysisDuration() {
        return dnsAnalysisDuration;
    }

    public void setDnsAnalysisDuration(BigDecimal dnsAnalysisDuration) {
        this.dnsAnalysisDuration = dnsAnalysisDuration;
    }

    public BigDecimal getTcpHandshakeDuration() {
        return tcpHandshakeDuration;
    }

    public void setTcpHandshakeDuration(BigDecimal tcpHandshakeDuration) {
        this.tcpHandshakeDuration = tcpHandshakeDuration;
    }

    public BigDecimal getHttpResponseDuration() {
        return httpResponseDuration;
    }

    public void setHttpResponseDuration(BigDecimal httpResponseDuration) {
        this.httpResponseDuration = httpResponseDuration;
    }

    public BigDecimal getCpuRamHighUseNum() {
        return cpuRamHighUseNum;
    }

    public void setCpuRamHighUseNum(BigDecimal cpuRamHighUseNum) {
        this.cpuRamHighUseNum = cpuRamHighUseNum;
    }

    public BigDecimal getToAlarmTimes() {
        return toAlarmTimes;
    }

    public void setToAlarmTimes(BigDecimal toAlarmTimes) {
        this.toAlarmTimes = toAlarmTimes;
    }

    @Override
    public String toString() {
        return "AlarmThresholdRequest{" +
                "dnsAnalysisDuration=" + dnsAnalysisDuration +
                ", tcpHandshakeDuration=" + tcpHandshakeDuration +
                ", httpResponseDuration=" + httpResponseDuration +
                ", cpuRamHighUseNum=" + cpuRamHighUseNum +
                ", toAlarmTimes=" + toAlarmTimes +
                '}';
    }
}
