package com.cmiot.report.dto;

import lombok.Data;


@Data
public class GatewayFlowTrendResult {
    private String recordTime;
    //网关上行带宽周期均值
    private Double averTxrate;
    //网关下行带宽周期均值
    private Double averRxrate;
    //网关上行带宽周期峰值
    private Double maxTxrate;
    //网关下行带宽周期峰值
    private Double maxRxrate;

    public GatewayFlowTrendResult(String recordTime) {
        this.recordTime = recordTime;
        this.averTxrate = 0d;
        this.averRxrate = 0d;
        this.maxTxrate = 0d;
        this.maxRxrate = 0d;
    }


}
