package com.cmiot.report.dto.pppoe;

import java.io.Serializable;
import java.util.List;

public class PppoeFailListResult implements Serializable {

	private static final long serialVersionUID = -6235689699982432690L;
	
	/**
	 * 当前页
	 */
	private Integer page;
	
	/**
	 * 每页展示数据量
	 */
	private Integer pageSize;
	
	/**
	 * 总数据量
	 */
	private Long total;
	
	/**
	 * 数据明细
	 */
	private List<Item> list;
	
	
	
	public Integer getPage() {
		return page;
	}



	public void setPage(Integer page) {
		this.page = page;
	}



	public Integer getPageSize() {
		return pageSize;
	}



	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}



	public Long getTotal() {
		return total;
	}



	public void setTotal(Long total) {
		this.total = total;
	}



	public List<Item> getList() {
		return list;
	}



	public void setList(List<Item> list) {
		this.list = list;
	}



	@Override
	public String toString() {
		return "PppoeFailListResult [page=" + page + ", pageSize=" + pageSize + ", total=" + total + ", list=" + list
				+ "]";
	}



	public static class Item implements Serializable {
		private static final long serialVersionUID = 6770932498661746346L;
		private String time;
		private String reason;
		public String getTime() {
			return time;
		}
		public void setTime(String time) {
			this.time = time;
		}
		public String getReason() {
			return reason;
		}
		public void setReason(String reason) {
			this.reason = reason;
		}
		@Override
		public String toString() {
			return "Item [time=" + time + ", reason=" + reason + "]";
		}
		
	}

}
