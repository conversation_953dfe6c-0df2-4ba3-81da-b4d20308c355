package com.cmiot.report.dto;

import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class GatewayQuery {
    private Long uid;
    private String eid;
    private String sn;
    private List<String> gatewaySnList;
    private String province;
    private List<String> provinceList;
    private String city;
    private List<String> cityList;
    private String vendor;
    private List<String> vendorList;
    private String model;
    private List<String> modelList;
    private String startTime;
    private String endTime;
    private Integer groupType;
    private Integer page;
    private Integer pageSize;
    private Integer overTime;
    private String gatewayMac;
    private List<String> gatewayMacList;
    private Integer orderType = -1;//1表示升序，-1表示降序
    private Integer timeType = 1;//1 日， 2 周， 3 月， 4 年，5 分时
    private String ipVersion;//ip协议：为空：双栈、ipv4、ipv6
    private String wanConnType;//wan连接类型：为空：全量、internet、voip、other


    public void initParams() {
        gatewaySnList = StringUtils.isEmpty(sn) ?
                new ArrayList<>() : Arrays.stream(sn.split(",")).map(i -> i.trim()).collect(Collectors.toList());
        provinceList = StringUtils.isEmpty(province) ?
                new ArrayList<>() : Arrays.stream(province.split(",")).map(i -> i.trim()).collect(Collectors.toList());
        cityList = StringUtils.isEmpty(city) ?
                new ArrayList<>() : Arrays.stream(city.split(",")).map(i -> i.trim()).collect(Collectors.toList());
        vendorList = StringUtils.isEmpty(vendor) ?
                new ArrayList<>() : Arrays.stream(vendor.split(",")).map(i -> i.trim()).collect(Collectors.toList());
        modelList = StringUtils.isEmpty(model) ?
                new ArrayList<>() : Arrays.stream(model.split(",")).map(i -> i.trim()).collect(Collectors.toList());
        gatewayMacList = StringUtils.isEmpty(gatewayMac) ?
                new ArrayList<>() : Arrays.stream(gatewayMac.split(",")).map(i -> i.trim()).collect(Collectors.toList());
    }
}
