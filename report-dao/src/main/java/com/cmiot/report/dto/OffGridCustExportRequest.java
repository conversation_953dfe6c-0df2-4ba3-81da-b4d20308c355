package com.cmiot.report.dto;

import lombok.Data;

import java.util.List;

@Data
public class OffGridCustExportRequest {
    // 省份
    private List<String> province;

    // 地市
    private List<String> city;

    // 企业名称
    private String business;

    // 集团客户标识
    private String groupCustomer;

    // 企业价值分类
    private String enterpriseValueCategory;

    // 客户状态
    private String customerStatus;

    // 综合离网指数开始值
    private Integer syntheticalStart;

    // 综合离网指数结束值
    private Integer syntheticalEnd;

    // 终端质量因素分开始值
    private Integer terminalQualityStart;

    // 终端质量因素分结束值
    private Integer terminalQualityEnd;

    // 网络质量因素分开始值
    private Integer networkQualityStart;

    // 网络质量因素分结束值
    private Integer networkQualityEnd;

    // 客户服务因素分开始值
    private Integer customerServiceStart;

    // 客户服务因素分结束值
    private Integer customerServiceEnd;

    // 业务订购因素分开始值
    private Integer businessOrderStart;

    // 业务订购因素分结束值
    private Integer businessOrderEnd;

    // 装维服务因素分开始值
    private Integer installationAndMaintenanceServiceStart;

    // 装维服务因素分结束值
    private Integer installationAndMaintenanceServiceEnd;

    // 企业id
    private String uid;

}
