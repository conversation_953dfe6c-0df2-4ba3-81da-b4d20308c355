package com.cmiot.report.dto;


import java.io.Serializable;

public class GatewayCountDtoResult implements Serializable {


    private static final long serialVersionUID = -2986216674300858310L;


    private String name;

    private Long value;

    private String percent;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getValue() {
        return value;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public String getPercent() {
        return percent;
    }

    public void setPercent(String percent) {
        this.percent = percent;
    }

    @Override
    public String toString() {
        return "GatewayCountByProvince{" +
                "name='" + name + '\'' +
                ", value=" + value +
                ", percent='" + percent + '\'' +
                '}';
    }
}
