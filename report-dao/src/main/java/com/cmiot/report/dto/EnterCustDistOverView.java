package com.cmiot.report.dto;


import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EnterCustDistOverView {

    // 客户数
    @Builder.Default
    private long customerNum = 0;

    // 办理过业务的客户占比
    @Builder.Default
    private float businessCustomerRate = 0.0f;

    // 业务生效中的客户占比
    @Builder.Default
    private float onBusinessCustomerRate= 0.0f;

}
