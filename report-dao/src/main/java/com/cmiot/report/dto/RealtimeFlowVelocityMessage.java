package com.cmiot.report.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RealtimeFlowVelocityMessage {
    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 网关mac
     */
    private String mac;

    /**
     * 速率数据数组
     */
    private List<Velocity> velocity;


    @Data
    public static class Velocity {
        /**
         * 采样时间，13位时间戳
         */
        private Long timestamp;

        /**
         * 上行速率，单位为kbps
         */
        private Long upload;

        /**
         * 下行速率，单位为kbps
         */
        private Long download;

        // 当前连接数
        @JsonProperty(value = "ConnectionCount")
        private Long connectionCount;
    }
}
