package com.cmiot.report.dto.device;

import lombok.Data;

@Data
public class DeviceQuery {
     public String province;
     public String[] provinceCode;
     public String city;
     public String[] cityCode;
     public String vendor;
     public String[] vendorCode;
     public String model;
     public String[] modelCode;
     public String startTime;
     public String endTime;
     public String date;
     public String type;
     public String period;
     public int industry;
     /**
      * 接入网关类型，0，全量、1，政企网关、2，家庭网关（1.13.0新增）
      */
     private Integer connectType;
}
