package com.cmiot.report.dto.enterprise;

import java.util.List;

/**
 * @Classname SubDeviceFlowDataResult
 * @Description
 * @Date 2023/8/15 10:15
 * @Created by lei
 */
public class DeviceFlowDataResult {

    private List<String> time;
    private List<String> maxTxrate;
    private List<String> maxRxrate;
    private List<String> averTxrate;
    private List<String> averRxrate;

    public List<String> getTime() {
        return time;
    }

    public void setTime(List<String> time) {
        this.time = time;
    }

    public List<String> getMaxTxrate() {
        return maxTxrate;
    }

    public void setMaxTxrate(List<String> maxTxrate) {
        this.maxTxrate = maxTxrate;
    }

    public List<String> getMaxRxrate() {
        return maxRxrate;
    }

    public void setMaxRxrate(List<String> maxRxrate) {
        this.maxRxrate = maxRxrate;
    }

    public List<String> getAverTxrate() {
        return averTxrate;
    }

    public void setAverTxrate(List<String> averTxrate) {
        this.averTxrate = averTxrate;
    }

    public List<String> getAverRxrate() {
        return averRxrate;
    }

    public void setAverRxrate(List<String> averRxrate) {
        this.averRxrate = averRxrate;
    }
}
