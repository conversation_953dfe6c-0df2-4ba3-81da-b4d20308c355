package com.cmiot.report.dto;

import lombok.Data;

@Data
public class GatewayCpuOrRamRateResult {
    private Integer lv0;  //未知 数量统计
    private Integer lv1;  //0-10% 数量统计
    private Integer lv2;  //10%-20% 数量统计
    private Integer lv3;  //20%-30% 数量统计
    private Integer lv4;  //30%-40% 数量统计
    private Integer lv5;  //40%-50% 数量统计
    private Integer lv6;  //50%-60% 数量统计
    private Integer lv7;  //60%-70% 数量统计
    private Integer lv8;  //70%-80% 数量统计
    private Integer lv9;  //80%-90% 数量统计
    private Integer lv10;  //90%-100% 数量统计


    public Integer total() {
        return lv0 + lv1 + lv2 + lv3 + lv4 + lv5 + lv6 + lv7 + lv8 + lv9 + lv10;
    }
}
