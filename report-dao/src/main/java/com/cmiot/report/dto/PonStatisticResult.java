package com.cmiot.report.dto;


import java.util.List;

public class PonStatisticResult {

    private List<String> xAxis;

    // 强光接收功率平均值
    private List<String> receivePowerStrongAverage;

    // 光接收功率强光占比，单位%
    private List<String> receivePowerStrongRate;

    // 弱光接收功率平均值
    private List<String> receivePowerWeakAverage;

    // 光接收功率弱光占比，单位%
    private List<String> receivePowerWeakRate;

    public List<String> getxAxis() {
        return xAxis;
    }

    public void setxAxis(List<String> xAxis) {
        this.xAxis = xAxis;
    }

    public List<String> getReceivePowerStrongRate() {
        return receivePowerStrongRate;
    }

    public void setReceivePowerStrongRate(List<String> receivePowerStrongRate) {
        this.receivePowerStrongRate = receivePowerStrongRate;
    }

    public List<String> getReceivePowerWeakRate() {
        return receivePowerWeakRate;
    }

    public void setReceivePowerWeakRate(List<String> receivePowerWeakRate) {
        this.receivePowerWeakRate = receivePowerWeakRate;
    }

    public List<String> getReceivePowerStrongAverage() {
        return receivePowerStrongAverage;
    }

    public void setReceivePowerStrongAverage(List<String> receivePowerStrongAverage) {
        this.receivePowerStrongAverage = receivePowerStrongAverage;
    }

    public List<String> getReceivePowerWeakAverage() {
        return receivePowerWeakAverage;
    }

    public void setReceivePowerWeakAverage(List<String> receivePowerWeakAverage) {
        this.receivePowerWeakAverage = receivePowerWeakAverage;
    }
}
