package com.cmiot.report.dto.syslog;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname SysLogQueryRequest
 * @Description
 * @Date 2022/7/20 16:24
 * @Created by lei
 */
@Data
public class SysLogQueryRequest implements Serializable {
    private static final long serialVersionUID = 4087069049199357992L;

    private Long eid;
    private Long uid;
    private List<Long> userIds;
    private String userAccount;
    private Long roleId;
    private Long operationId;
    private String startTime;
    private String endTime;

    private Long page;
    private Long pageSize;

    private Long offset;

}
