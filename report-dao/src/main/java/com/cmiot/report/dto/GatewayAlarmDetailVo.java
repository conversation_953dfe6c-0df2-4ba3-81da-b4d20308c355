package com.cmiot.report.dto;


public class GatewayAlarmDetailVo {

    private String provinceName;

    private String cityName;

    private String customerName;

    private String adslAccount;

    private String gatewaySn;

    private String gatewayMac;

    private String factoryName;

    private String deviceModel;

    private String alarmName;

    private String mainChipTemperature;

    private String alarmTimes;


    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getAdslAccount() {
        return adslAccount;
    }

    public void setAdslAccount(String adslAccount) {
        this.adslAccount = adslAccount;
    }

    public String getGatewaySn() {
        return gatewaySn;
    }

    public void setGatewaySn(String gatewaySn) {
        this.gatewaySn = gatewaySn;
    }

    public String getGatewayMac() {
        return gatewayMac;
    }

    public void setGatewayMac(String gatewayMac) {
        this.gatewayMac = gatewayMac;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getAlarmName() {
        return alarmName;
    }

    public void setAlarmName(String alarmName) {
        this.alarmName = alarmName;
    }

    public String getMainChipTemperature() {
        return mainChipTemperature;
    }

    public void setMainChipTemperature(String mainChipTemperature) {
        this.mainChipTemperature = mainChipTemperature;
    }

    public String getAlarmTimes() {
        return alarmTimes;
    }

    public void setAlarmTimes(String alarmTimes) {
        this.alarmTimes = alarmTimes;
    }
}
