package com.cmiot.report.dto.pppoe;

import java.io.Serializable;

public class PppoeListExportRequest implements Serializable {

	private static final long serialVersionUID = -5261676792035171216L;
	
	private String province;
	
	private String city;
	
	private String vendor;
	
	private String sn;

	@Override
	public String toString() {
		return "PppoeListExportRequest [province=" + province + ", city=" + city + ", vendor=" + vendor + ", sn=" + sn
				+ "]";
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getVendor() {
		return vendor;
	}

	public void setVendor(String vendor) {
		this.vendor = vendor;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}
	
	

}
