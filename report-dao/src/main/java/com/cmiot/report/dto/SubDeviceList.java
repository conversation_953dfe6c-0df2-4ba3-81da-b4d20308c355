package com.cmiot.report.dto;

import com.cmiot.report.poi.ExcelColumn;
import lombok.Data;

@Data
public class SubDeviceList {
    // 网关省份
    @ExcelColumn(value = "省份", col = 1)
    private String province;

    //网关地市
    @ExcelColumn(value = "地市", col = 2)
    private String city;

    //上联网关SN
    @ExcelColumn(value = "上联网关SN", col = 3)
    private String gatewaySn;

    // 网关mac
    @ExcelColumn(value = "上联网关MAC", col = 4)
    private String gatewayMac;

    //网关厂商
    @ExcelColumn(value = "上联网关厂商", col = 5)
    private String gatewayVendor;

    //上联网关型号
    @ExcelColumn(value = "上联网关型号", col = 6)
    private String gatewayModel;

    //下挂设备名称
    @ExcelColumn(value = "下挂设备名称", col = 7)
    private String hangingDeviceName;

    //下挂设备mac
    @ExcelColumn(value = "下挂设备mac", col = 8)
    private String hangingDeviceMac;

    //首次上联时间
    @ExcelColumn(value = "首次上联时间", col = 9)
    private String firstConnectTime;

    //最近上联时间
    @ExcelColumn(value = "最近上联时间", col = 10)
    private String lastConnectTime;

    //连接的WIFI名称
    @ExcelColumn(value = "连接的WIFI名称", col = 11)
    private String connectWifiName;

    //下挂设备IP
    @ExcelColumn(value = "下挂设备IP", col = 12)
    private String hangingDeviceIp;

    //WLAN平均信号强度
    @ExcelColumn(value = "WLAN平均信号强度", col = 13)
    private String signalIntensity;

    //连接的LAN口
    @ExcelColumn(value = "连接的LAN口", col = 14)
    private String lanPort;

    //下挂设备出现次数
    @ExcelColumn(value = "下挂设备出现天数", col = 15)
    private String hangingDeviceTimes;

    //连接LAN口的协商速率
    @ExcelColumn(value = "连接LAN口的协商速率", col = 16)
    private String lanPortPlanRate;

    //下挂设备型号
    @ExcelColumn(value = "下挂设备型号", col = 17)
    private String hangingDeviceModel;

    //下挂设备厂商
    @ExcelColumn(value = "下挂设备厂商", col = 18)
    private String hangingDeviceVendor;

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getGatewaySn() {
        return gatewaySn;
    }

    public void setGatewaySn(String gatewaySn) {
        this.gatewaySn = gatewaySn;
    }

    public String getGatewayMac() {
        return gatewayMac;
    }

    public void setGatewayMac(String gatewayMac) {
        this.gatewayMac = gatewayMac;
    }

    public String getGatewayVendor() {
        return gatewayVendor;
    }

    public void setGatewayVendor(String gatewayVendor) {
        this.gatewayVendor = gatewayVendor;
    }

    public String getGatewayModel() {
        return gatewayModel;
    }

    public void setGatewayModel(String gatewayModel) {
        this.gatewayModel = gatewayModel;
    }

    public String getHangingDeviceName() {
        return hangingDeviceName;
    }

    public void setHangingDeviceName(String hangingDeviceName) {
        this.hangingDeviceName = hangingDeviceName;
    }

    public String getHangingDeviceMac() {
        return hangingDeviceMac;
    }

    public void setHangingDeviceMac(String hangingDeviceMac) {
        this.hangingDeviceMac = hangingDeviceMac;
    }

    public String getFirstConnectTime() {
        return firstConnectTime;
    }

    public void setFirstConnectTime(String firstConnectTime) {
        this.firstConnectTime = firstConnectTime;
    }

    public String getConnectWifiName() {
        return connectWifiName;
    }

    public void setConnectWifiName(String connectWifiName) {
        this.connectWifiName = connectWifiName;
    }

    public String getHangingDeviceIp() {
        return hangingDeviceIp;
    }

    public void setHangingDeviceIp(String hangingDeviceIp) {
        this.hangingDeviceIp = hangingDeviceIp;
    }

    public String getSignalIntensity() {
        return signalIntensity;
    }

    public void setSignalIntensity(String signalIntensity) {
        this.signalIntensity = signalIntensity;
    }

    public String getLanPort() {
        return lanPort;
    }

    public void setLanPort(String lanPort) {
        this.lanPort = lanPort;
    }

    public String getLanPortPlanRate() {
        return lanPortPlanRate;
    }

    public void setLanPortPlanRate(String lanPortPlanRate) {
        this.lanPortPlanRate = lanPortPlanRate;
    }

    public String getHangingDeviceTimes() {
        return hangingDeviceTimes;
    }

    public void setHangingDeviceTimes(String hangingDeviceTimes) {
        this.hangingDeviceTimes = hangingDeviceTimes;
    }


    public String getHangingDeviceModel() {
        return hangingDeviceModel;
    }

    public void setHangingDeviceModel(String hangingDeviceModel) {
        this.hangingDeviceModel = hangingDeviceModel;
    }

    public String getHangingDeviceVendor() {
        return hangingDeviceVendor;
    }

    public void setHangingDeviceVendor(String hangingDeviceVendor) {
        this.hangingDeviceVendor = hangingDeviceVendor;
    }

    public String getLastConnectTime() {
        return lastConnectTime;
    }

    public void setLastConnectTime(String lastConnectTime) {
        this.lastConnectTime = lastConnectTime;
    }
}
