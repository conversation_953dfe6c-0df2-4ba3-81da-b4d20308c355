package com.cmiot.report.dto.plugin;

/**
 * @Classname PluginDayCountDto
 * @Description
 * @Date 2024/1/25 15:55
 * @Created by lei
 */
public class PluginInfoDayCount {
    private String countDate;
    private String bundleId;
    private String area;
    private String factoryId;
    private String deviceModelId;
    private Long totalNum;
    private Long incrNum;
    private Long noUseNum;
    private Long thirtyDayActiveNum;


    public String getCountDate() {
        return countDate;
    }

    public void setCountDate(String countDate) {
        this.countDate = countDate;
    }

    public String getBundleId() {
        return bundleId;
    }

    public void setBundleId(String bundleId) {
        this.bundleId = bundleId;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(String factoryId) {
        this.factoryId = factoryId;
    }

    public String getDeviceModelId() {
        return deviceModelId;
    }

    public void setDeviceModelId(String deviceModelId) {
        this.deviceModelId = deviceModelId;
    }

    public Long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }

    public Long getIncrNum() {
        return incrNum;
    }

    public void setIncrNum(Long incrNum) {
        this.incrNum = incrNum;
    }

    public Long getNoUseNum() {
        return noUseNum;
    }

    public void setNoUseNum(Long noUseNum) {
        this.noUseNum = noUseNum;
    }

    public Long getThirtyDayActiveNum() {
        return thirtyDayActiveNum;
    }

    public void setThirtyDayActiveNum(Long thirtyDayActiveNum) {
        this.thirtyDayActiveNum = thirtyDayActiveNum;
    }
}
