package com.cmiot.report.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RealtimeWanConnMessage {
    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 网关mac
     */
    private String mac;

    /**
     * 速率数据数组
     */
    private List<Velocity> velocity;


    @Data
    public static class Velocity {
        /**
         * 采样时间，13位时间戳
         */
        private Long timestamp;

//        // ipv4连接数，包含udp+tcp
//        @JsonProperty(value = "IPv4ConnectionCount")
//        private Long ipv4ConnectionCount;
//
//        // ipv6连接数，包含udp+tcp
//        @JsonProperty(value = "IPv6ConnectionCount")
//        private Long ipv6ConnectionCount;

        //连接数
        @JsonProperty(value = "ConnectionCount")
        private Long connectionCount;
    }
}
