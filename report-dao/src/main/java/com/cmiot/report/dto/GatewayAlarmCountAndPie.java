package com.cmiot.report.dto;


import java.io.Serializable;
import java.util.List;

public class GatewayAlarmCountAndPie implements Serializable {

    private static final long serialVersionUID = 308857011813052631L;

    private String name;

    private String ratio;

    private String value;

    private List<PieData> pieData;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRatio() {
        return ratio;
    }

    public void setRatio(String ratio) {
        this.ratio = ratio;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public List<PieData> getPieData() {
        return pieData;
    }

    public void setPieData(List<PieData> pieData) {
        this.pieData = pieData;
    }
}