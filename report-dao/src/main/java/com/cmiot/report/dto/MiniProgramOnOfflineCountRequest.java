package com.cmiot.report.dto;


import java.io.Serializable;

public class MiniProgramOnOfflineCountRequest implements Serializable {

    private static final long serialVersionUID = 6790118130563812L;

    private String eid;

    private String startDate;

    private String endDate;


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return "MiniProgramOnOfflineCountRequest{" +
                "eid='" + eid + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                '}';
    }
}
