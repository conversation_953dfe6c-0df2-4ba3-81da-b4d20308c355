package com.cmiot.report.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RealtimeWanConnMessageVO {
    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 网关mac
     */
    private String mac;

    /**
     * 速率数据数组
     */
    private List<Velocity> velocity;


    @Data
    public static class Velocity {
        /**
         * 采样时间，13位时间戳
         */
        private String timestamp;

        /**
         * 总连接数
         */
        private Long connCount;
    }
}
