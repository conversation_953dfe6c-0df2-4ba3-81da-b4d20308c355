package com.cmiot.report.dto;

import com.cmiot.report.poi.ExcelColumn;
import lombok.Data;

@Data
public class GatewayOldListResult {
    private String provinceCode;
    @ExcelColumn(value = "省份", col = 1)
    private String province;
    @ExcelColumn(value = "地市", col = 2)
    private String city;
    @ExcelColumn(value = "企业名称", col = 3)
    private String customerName;
    @ExcelColumn(value = "网关SN", col = 4)
    private String gatewaySn;
    @ExcelColumn(value = "网关MAC", col = 5)
    private String mac;
    @ExcelColumn(value = "厂商", col = 6)
    private String vendor;
    @ExcelColumn(value = "型号", col = 7)
    private String model;
    @ExcelColumn(value = "最小时延（秒）", col = 8)
    private Integer minTotalDelay;
}
