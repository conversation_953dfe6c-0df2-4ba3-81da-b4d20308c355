package com.cmiot.report.dto;


import java.io.Serializable;


public class GatewayOnOffLineNumResult implements Serializable {


    private static final long serialVersionUID = 1877435005116003137L;


    private String name;

    private String onlineNum;

    private String offlineNum;



    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOnlineNum() {
        return onlineNum;
    }

    public void setOnlineNum(String onlineNum) {
        this.onlineNum = onlineNum;
    }

    public String getOfflineNum() {
        return offlineNum;
    }

    public void setOfflineNum(String offlineNum) {
        this.offlineNum = offlineNum;
    }


    @Override
    public String toString() {
        return "GatewayOnOffLineNumResult{" +
                "name='" + name + '\'' +
                ", onlineNum='" + onlineNum + '\'' +
                ", offlineNum='" + offlineNum + '\'' +
                '}';
    }
}
