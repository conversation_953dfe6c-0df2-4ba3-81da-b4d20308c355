package com.cmiot.report.dto.pppoe;

import java.io.Serializable;

public class PppoeListRequest implements Serializable {

	private static final long serialVersionUID = -5519040696516974450L;
	
	/**
	 * 省份编码。例如： 110000
	 */
	private String province;

	/**
	 * 地市编码。例如： 110101,110102,110105,110106
	 */
	private String city;

	/**
	 * 厂商编码。例如： 8,16
	 */
	private String vendor;
	
	/**
	 * 网关sn。
	 */
	private String sn;
	
	/**
	 * 当前页
	 */
	private Integer page;
	
	/**
	 * 每页展示数据量
	 */
	private Integer pageSize;

	@Override
	public String toString() {
		return "PppoeListRequest [province=" + province + ", city=" + city + ", vendor=" + vendor + ", sn=" + sn
				+ ", page=" + page + ", pageSize=" + pageSize + "]";
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getVendor() {
		return vendor;
	}

	public void setVendor(String vendor) {
		this.vendor = vendor;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		this.page = page;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	
	

}
