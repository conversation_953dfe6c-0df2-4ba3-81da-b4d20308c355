package com.cmiot.report.dto.plugin;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Classname PluginBaseInfo
 * @Description
 * @Date 2024/2/1 16:41
 * @Created by lei
 */
@Data
public class PluginBaseInfo implements Serializable {
    private static final long serialVersionUID = 8923148590523728127L;

    private Integer id;
    private String pluginName;
    private String deviceType;
    private String version;
    private String manufacture;
    private String bundleId;
    private String pluginDes;
    private String pluginNum;
    private String isPreset;
    private String createId;
    private String creator;

}
