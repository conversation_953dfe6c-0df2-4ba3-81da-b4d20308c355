package com.cmiot.report.dto.customer;

import java.util.List;

/**
 * @Classname NetworkUsageChartData
 * @Description
 * @Date 2022/8/9 15:53
 * @Created by lei
 */
public class NetworkUsageChartData {

    private List<String> xAxis;

    private List<NetworkUsageRatioData> useBroadbandRatioData;

    private List<Double> actualNetworkTrafficData;

    public List<String> getxAxis() {
        return xAxis;
    }

    public void setxAxis(List<String> xAxis) {
        this.xAxis = xAxis;
    }

    public List<NetworkUsageRatioData> getUseBroadbandRatioData() {
        return useBroadbandRatioData;
    }

    public void setUseBroadbandRatioData(List<NetworkUsageRatioData> useBroadbandRatioData) {
        this.useBroadbandRatioData = useBroadbandRatioData;
    }

    public List<Double> getActualNetworkTrafficData() {
        return actualNetworkTrafficData;
    }

    public void setActualNetworkTrafficData(List<Double> actualNetworkTrafficData) {
        this.actualNetworkTrafficData = actualNetworkTrafficData;
    }

    @Override
    public String toString() {
        return "NetworkUsageChartData{" +
                "xAxis=" + xAxis +
                ", useBroadbandRatioData=" + useBroadbandRatioData +
                ", actualNetworkTrafficData=" + actualNetworkTrafficData +
                '}';
    }
}
