package com.cmiot.report.dto;


import com.cmiot.report.poi.ExcelColumn;
import lombok.Data;

import java.util.Date;

@Data
public class PonPowerListDetailVo {
    @ExcelColumn(value = "省份", col = 1)
    private String provinceName;

    @ExcelColumn(value = "地市", col = 2)
    private String cityName;

    @ExcelColumn(value = "集团名称", col = 3)
    private String customerName;

    @ExcelColumn(value = "网关SN", col = 4)
    private String gatewaySn;

    @ExcelColumn(value = "网关MAC", col = 5)
    private String gatewayMac;

    @ExcelColumn(value = "网关厂商", col = 6)
    private String factoryName;

    @ExcelColumn(value = "网关型号", col = 7)
    private String deviceModel;

    @ExcelColumn(value = "累计运行时长(秒)", col = 8)
    private Long runingTimeTotal;

    @ExcelColumn(value = "接收光功率平均值", col = 9)
    private Integer ponRxPowerAvg;

    @ExcelColumn(value = "接收光功率最小值(dbm)", col = 10)
    private Integer ponRxPowerMinValue;

    @ExcelColumn(value = "接收光功率平均值出现时间", col = 11)
    private Date ponRxPowerMinValueTime;

}
