package com.cmiot.report.dto;


import java.io.Serializable;
import java.util.List;

public class GatewayIncrementCountResult implements Serializable {


    private static final long serialVersionUID = -1655612871599744117L;


    private List<GatewayCountDtoResult> listByProvince;

    private List<GatewayCountDtoResult> listByVendor;

    private List<GatewayCountDtoResult> listByIndustry;

    private String addGatewayNum;


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public List<GatewayCountDtoResult> getListByProvince() {
        return listByProvince;
    }

    public void setListByProvince(List<GatewayCountDtoResult> listByProvince) {
        this.listByProvince = listByProvince;
    }

    public List<GatewayCountDtoResult> getListByVendor() {
        return listByVendor;
    }

    public void setListByVendor(List<GatewayCountDtoResult> listByVendor) {
        this.listByVendor = listByVendor;
    }

    public List<GatewayCountDtoResult> getListByIndustry() {
        return listByIndustry;
    }

    public void setListByIndustry(List<GatewayCountDtoResult> listByIndustry) {
        this.listByIndustry = listByIndustry;
    }

    public String getAddGatewayNum() {
        return addGatewayNum;
    }

    public void setAddGatewayNum(String addGatewayNum) {
        this.addGatewayNum = addGatewayNum;
    }


    @Override
    public String toString() {
        return "GatewayIncrementCountResult{" +
                "listByProvince=" + listByProvince +
                ", listByVendor=" + listByVendor +
                ", listByIndustry=" + listByIndustry +
                ", addGatewayNum='" + addGatewayNum + '\'' +
                '}';
    }
}
