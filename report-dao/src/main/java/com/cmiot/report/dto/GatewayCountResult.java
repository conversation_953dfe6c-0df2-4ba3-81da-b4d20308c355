package com.cmiot.report.dto;


import java.io.Serializable;
import java.util.List;

public class GatewayCountResult implements Serializable {


    private static final long serialVersionUID = -6508205744162477704L;

    private List<GatewayCountDtoResult> listByProvince;

    private List<GatewayCountDtoResult> listByVendor;

    private List<GatewayCountDtoResult> listByIndustry;

    private String totalGatewayNum;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public List<GatewayCountDtoResult> getListByProvince() {
        return listByProvince;
    }

    public void setListByProvince(List<GatewayCountDtoResult> listByProvince) {
        this.listByProvince = listByProvince;
    }

    public List<GatewayCountDtoResult> getListByVendor() {
        return listByVendor;
    }

    public void setListByVendor(List<GatewayCountDtoResult> listByVendor) {
        this.listByVendor = listByVendor;
    }

    public List<GatewayCountDtoResult> getListByIndustry() {
        return listByIndustry;
    }

    public void setListByIndustry(List<GatewayCountDtoResult> listByIndustry) {
        this.listByIndustry = listByIndustry;
    }

    public String getTotalGatewayNum() {
        return totalGatewayNum;
    }

    public void setTotalGatewayNum(String totalGatewayNum) {
        this.totalGatewayNum = totalGatewayNum;
    }

    @Override
    public String toString() {
        return "GatewayCountResult{" +
                "listByProvince=" + listByProvince +
                ", listByVendor=" + listByVendor +
                ", listByIndustry=" + listByIndustry +
                ", totalGatewayNum='" + totalGatewayNum + '\'' +
                '}';
    }
}
