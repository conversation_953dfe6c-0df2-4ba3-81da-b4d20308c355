package com.cmiot.report.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 小程序：用户运行报告
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceAppStatRunVo {

    // 周期内通过Wifi连接的下挂设备数量
    @Builder.Default
    private int subDeviceCount = 0;

    // 下挂设备平均网速(单位mb/s,保留两位小数)
    @Builder.Default
    private double subDeviceRateAverage = 0;

    // 平均连接比例(wifi连接设备数/总的设备数,保留两位小数)
    @Builder.Default
    private double subDeviceConnectRateAvg = 0;

    // 下挂设备使用总流量,上行(up_staticstics/单位byte)+下行(down_staticstics/单位byte)(结果单位GB,保留两位小数)
    @Builder.Default
    private double subDeviceTraffic = 0;


    // 每个设备每天使用wifi平均时长(t_subdevice_app_report_all-存在一条数据则为15分钟)
    @Builder.Default
    private double subDeviceWifiConnectTime = 0;

    // 正在连接wifi上网用户数(t_subdevice_app_report_all,在15分钟以内上报过数据的用户)
    @Builder.Default
    private int wifiUserCount = 0;

    // 一年内最常访问的网站名称(t_hgu_network_visit_detail_all,开窗函数)
    @Builder.Default
    private String favoriteWebsite = "-";

    // 每个设备使用wifi平均访问网站时长(t_hgu_network_visit_detail_all-visit_duration[当天访问时长(单位分钟)])
    @Builder.Default
    private double subDeviceWifiVisitWebTime = 0;

    // 每个设备使用wifi平均访问网站次数(t_hgu_network_visit_detail_all 记录次数count)
    @Builder.Default
    private double subDeviceVisitWebCount = 0;

}
