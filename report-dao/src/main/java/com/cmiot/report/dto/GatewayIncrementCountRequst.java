package com.cmiot.report.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@EqualsAndHashCode
public class GatewayIncrementCountRequst implements Serializable {
    private static final long serialVersionUID = -6067177749580466016L;


    private String province;

    private String vendor;

    private String industry;

    private String startTime;

    private String endTime;

    /**
     * 接入网关类型，0，全量、1，政企网关、2，家庭网关（1.13.0新增）
     */
    private Integer connectType;

}
