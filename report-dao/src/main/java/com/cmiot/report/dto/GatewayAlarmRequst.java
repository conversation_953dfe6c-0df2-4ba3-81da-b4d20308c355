package com.cmiot.report.dto;


import java.io.Serializable;

public class GatewayAlarmRequst implements Serializable {

    private static final long serialVersionUID = 30885701181305631L;

    private String province;

    private String city;

    private String vendor;

    private String startTime;

    private String endTime;

    private String type;

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "GatewayAlarmRequst{" +
                "province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", vendor='" + vendor + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}
