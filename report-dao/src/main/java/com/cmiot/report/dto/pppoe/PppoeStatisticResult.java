package com.cmiot.report.dto.pppoe;

import java.io.Serializable;
import java.util.List;

public class PppoeStatisticResult implements Serializable {

	private static final long serialVersionUID = -1829134333283125320L;
	
	private List<Data> list;
	
	public List<Data> getList() {
		return list;
	}

	public void setList(List<Data> list) {
		this.list = list;
	}

	@Override
	public String toString() {
		return "PppoeStatisticResult [list=" + list + "]";
	}

	public static class Data implements Serializable{
		private static final long serialVersionUID = -3744937008499377191L;

		/**
		 * 名称
		 */
		private String name;
		
		/**
		 * 值
		 */
		private Long value;
		
		/**
		 * 占比
		 */
		private Double rate;
		
		/**
		 * 拨号失败占比
		 */
		private List<Reason> reasonList;

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public Long getValue() {
			return value;
		}

		public void setValue(Long value) {
			this.value = value;
		}

		public Double getRate() {
			return rate;
		}

		public void setRate(Double rate) {
			this.rate = rate;
		}

		public List<Reason> getReasonList() {
			return reasonList;
		}

		public void setReasonList(List<Reason> reasonList) {
			this.reasonList = reasonList;
		}

		@Override
		public String toString() {
			return "Data [name=" + name + ", value=" + value + ", rate=" + rate + ", reasonList=" + reasonList + "]";
		}
		
	}
	
	public static class Reason implements Serializable {
		private static final long serialVersionUID = 1127395148871114705L;
		
		/**
		 * 拨号失败原因名称
		 */
		private String name;
		
		/**
		 * 拨号失败数量
		 */
		private Long value;
		
		/**
		 * 拨号失败占比
		 */
		private Double rate;
		
		public Reason() {}
		
		public Reason(String name, int value, int rate) {
			this.name = name;
			this.value = Long.valueOf(value);
			this.rate = Double.valueOf(rate);
		}
		@Override
		public String toString() {
			return "Reason [name=" + name + ", value=" + value + ", rate=" + rate + "]";
		}
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
		public Long getValue() {
			return value;
		}
		public void setValue(Long value) {
			this.value = value;
		}
		public Double getRate() {
			return rate;
		}
		public void setRate(Double rate) {
			this.rate = rate;
		}

		
	}
	
}
