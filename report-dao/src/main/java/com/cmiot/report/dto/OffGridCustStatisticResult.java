package com.cmiot.report.dto;


import lombok.Data;

@Data
public class OffGridCustStatisticResult {
    
    // 综合离网指数超阈值的客户数量
    private String syntheticalValue;

    // 综合离网指数超阈值的客户占比
    private String syntheticalRatio;

    // 终端质量因素分超阈值的客户数量
    private String terminalQualityValue;

    // 终端质量因素分超阈值的客户占比
    private String terminalQualityRatio;

    // 网络质量因素分超阈值的客户数量
    private String networkQualityValue;

    // 网络质量因素分超阈值的客户占比
    private String networkQualityRatio;

    // 客户服务因素分超阈值的客户数量
    private String customerServiceValue;

    // 客户服务因素分超阈值的客户占比
    private String customerServiceRatio;

    // 业务订购因素分超阈值的客户数量
    private String businessOrderValue;

    // 业务订购因素分超阈值的客户占比
    private String businessOrderRatio;

    // 装维服务因素分超阈值的客户数量
    private String installationAndMaintenanceServiceValue;

    // 装维服务因素分超阈值的客户占比
    private String installationAndMaintenanceServiceRatio;

}
