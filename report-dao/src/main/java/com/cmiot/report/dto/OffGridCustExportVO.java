package com.cmiot.report.dto;


import com.cmiot.report.poi.ExcelColumn;
import lombok.Data;

@Data
public class OffGridCustExportVO {
    @ExcelColumn(value = "企业名称", col = 1)
    private String customerName;

    @ExcelColumn(value = "归属地区", col = 2)
    private String provinceName;

    @ExcelColumn(value = "集团客户标识", col = 3)
    private String customerId;

    @ExcelColumn(value = "企业价值分类", col = 4)
    private String valueCategory;

    @ExcelColumn(value = "客户状态", col = 5)
    private String customerStatus;

    @ExcelColumn(value = "综合离网指数", col = 6)
    private Integer offGridScore;

    @ExcelColumn(value = "终端质量因素分", col = 7)
    private Integer terminalScore;

    @ExcelColumn(value = "网络质量因素分", col = 8)
    private Integer networkScore;

    @ExcelColumn(value = "客户服务因素分", col = 9)
    private Integer serviceScore;

    @ExcelColumn(value = "业务订购因素分", col = 10)
    private Integer businessScore;

    @ExcelColumn(value = "装维服务因素分", col = 11)
    private Integer maintenanceScore;

}
