package com.cmiot.report.dto;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
public class GatewayRunTimeResult {
    //运行的网关数
    @Builder.Default
    private Integer gatewayNumberRunning = 0;

    //平均运行时长
    @Builder.Default
    private Integer runDurationAverage = 0;

    //这些网关总的运行时长
    @Builder.Default
    private Integer runDuration = 0;

    // 这些组网设备的运行时长
    @Builder.Default
    private double netRunDuration = 0.0;

    // 网关时间超长数量
    @Builder.Default
    private long macOverCount = 0;

    // 组网设备时间超长数量
    @Builder.Default
    private long subOverCount = 0;
}
