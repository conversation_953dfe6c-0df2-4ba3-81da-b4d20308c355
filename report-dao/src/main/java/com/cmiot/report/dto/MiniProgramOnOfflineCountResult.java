package com.cmiot.report.dto;


import java.io.Serializable;
import java.util.List;

public class MiniProgramOnOfflineCountResult implements Serializable {

    private static final long serialVersionUID = 3088570118130563812L;

    private List<String> time;

    private List<Long> online;

    private List<Long> offline;


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public List<String> getTime() {
        return time;
    }

    public void setTime(List<String> time) {
        this.time = time;
    }

    public List<Long> getOnline() {
        return online;
    }

    public void setOnline(List<Long> online) {
        this.online = online;
    }

    public List<Long> getOffline() {
        return offline;
    }

    public void setOffline(List<Long> offline) {
        this.offline = offline;
    }
}
