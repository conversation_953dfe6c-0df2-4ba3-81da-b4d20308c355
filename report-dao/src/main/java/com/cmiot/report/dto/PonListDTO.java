package com.cmiot.report.dto;


import com.cmiot.report.poi.ExcelColumn;
import lombok.Data;

@Data
public class PonListDTO {
    @ExcelColumn(value = "省份", col = 1)
    private String province;

    @ExcelColumn(value = "地市", col = 2)
    private String city;

    @ExcelColumn(value = "集团名称", col = 3)
    private String groupName;

    @ExcelColumn(value = "网关SN", col = 4)
    private String sn;

    @ExcelColumn(value = "网关MAC", col = 5)
    private String mac;

    @ExcelColumn(value = "网关厂商", col = 6)
    private String vendor;

    @ExcelColumn(value = "网关型号", col = 7)
    private String model;

    @ExcelColumn(value = "累计运行时长(秒)", col = 8)
    private Long runningTime;

    @ExcelColumn(value = "接收光功率平均值(dbm)", col = 9)
    private String receiveLightPowerAverage;

    @ExcelColumn(value = "接收光功率最小值(dbm)", col = 10)
    private String receiveLightPowerMin;

    @ExcelColumn(value = "接收光功率最小值出现时间", col = 11)
    private String receiveTime;

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Long getRunningTime() {
        return runningTime;
    }

    public void setRunningTime(Long runningTime) {
        this.runningTime = runningTime;
    }

    public String getReceiveLightPowerAverage() {
        return receiveLightPowerAverage;
    }

    public void setReceiveLightPowerAverage(String receiveLightPowerAverage) {
        this.receiveLightPowerAverage = receiveLightPowerAverage;
    }

    public String getReceiveLightPowerMin() {
        return receiveLightPowerMin;
    }

    public void setReceiveLightPowerMin(String receiveLightPowerMin) {
        this.receiveLightPowerMin = receiveLightPowerMin;
    }

    public String getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(String receiveTime) {
        this.receiveTime = receiveTime;
    }
}
