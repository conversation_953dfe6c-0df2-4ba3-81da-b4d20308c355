package com.cmiot.report.dto.enterprise;

/**
 * @Classname ComprehensiveGatewayResult
 * @Description
 * @Date 2023/10/11 17:37
 * @Created by lei
 */
public class ComprehensiveGatewayInfo {

    private String sn;
    private String mac;
    /**
     * 厂商
     */
    private String factoryId;
    private String factoryName;
    /**
     * 型号
     */
    private String deviceModelId;
    private String deviceModelName;
    /**
     * 满速运行时长
     */
    private Long highLoadingTime;
    /**
     * 总运行时长
     */
    private Long runningTime;
    /**
     * 满速运行时长/总运行时长（%）
     */
    private String highLoadingPercent;
    /**
     * 低速运行时长
     */
    private Long lowLoadingTime;
    /**
     * 低速运行时长/总运行时长（%）
     */
    private String lowLoadingPercent;
    /**
     * 连续不在线时间
     */
    private Integer idleTime;
    /**
     * 已使用时间
     */
    private Integer useTime;
    /**
     * 建议使用时长
     */
    private Integer planUseTime;
    /**
     * 套餐带宽
     */
    private Integer bandwidth;

    /**
     * 设备支持带宽
     */
    private Integer supportBandwidth;
    /**
     * ping时延
     */
    private Integer pingTime;

    /**
     * 测试最大带宽
     */
    private Integer speedTestBandwidth;

    /**
     * cpu使用率
     */
    private Integer cpu;
    /**
     * 内存使用率
     */
    private Integer ram;

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(String factoryId) {
        this.factoryId = factoryId;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public String getDeviceModelId() {
        return deviceModelId;
    }

    public void setDeviceModelId(String deviceModelId) {
        this.deviceModelId = deviceModelId;
    }

    public String getDeviceModelName() {
        return deviceModelName;
    }

    public void setDeviceModelName(String deviceModelName) {
        this.deviceModelName = deviceModelName;
    }

    public Long getHighLoadingTime() {
        return highLoadingTime;
    }

    public void setHighLoadingTime(Long highLoadingTime) {
        this.highLoadingTime = highLoadingTime;
    }

    public Long getRunningTime() {
        return runningTime;
    }

    public void setRunningTime(Long runningTime) {
        this.runningTime = runningTime;
    }

    public String getHighLoadingPercent() {
        return highLoadingPercent;
    }

    public void setHighLoadingPercent(String highLoadingPercent) {
        this.highLoadingPercent = highLoadingPercent;
    }

    public Long getLowLoadingTime() {
        return lowLoadingTime;
    }

    public void setLowLoadingTime(Long lowLoadingTime) {
        this.lowLoadingTime = lowLoadingTime;
    }

    public String getLowLoadingPercent() {
        return lowLoadingPercent;
    }

    public void setLowLoadingPercent(String lowLoadingPercent) {
        this.lowLoadingPercent = lowLoadingPercent;
    }

    public Integer getIdleTime() {
        return idleTime;
    }

    public void setIdleTime(Integer idleTime) {
        this.idleTime = idleTime;
    }

    public Integer getUseTime() {
        return useTime;
    }

    public void setUseTime(Integer useTime) {
        this.useTime = useTime;
    }

    public Integer getPlanUseTime() {
        return planUseTime;
    }

    public void setPlanUseTime(Integer planUseTime) {
        this.planUseTime = planUseTime;
    }

    public Integer getBandwidth() {
        return bandwidth;
    }

    public void setBandwidth(Integer bandwidth) {
        this.bandwidth = bandwidth;
    }

    public Integer getSupportBandwidth() {
        return supportBandwidth;
    }

    public void setSupportBandwidth(Integer supportBandwidth) {
        this.supportBandwidth = supportBandwidth;
    }

    public Integer getPingTime() {
        return pingTime;
    }

    public void setPingTime(Integer pingTime) {
        this.pingTime = pingTime;
    }

    public Integer getSpeedTestBandwidth() {
        return speedTestBandwidth;
    }

    public void setSpeedTestBandwidth(Integer speedTestBandwidth) {
        this.speedTestBandwidth = speedTestBandwidth;
    }

    public Integer getCpu() {
        return cpu;
    }

    public void setCpu(Integer cpu) {
        this.cpu = cpu;
    }

    public Integer getRam() {
        return ram;
    }

    public void setRam(Integer ram) {
        this.ram = ram;
    }

    @Override
    public String toString() {
        return "ComprehensiveGatewayInfo{" +
                "sn='" + sn + '\'' +
                ", mac='" + mac + '\'' +
                ", factoryId='" + factoryId + '\'' +
                ", factoryName='" + factoryName + '\'' +
                ", deviceModelId='" + deviceModelId + '\'' +
                ", deviceModelName='" + deviceModelName + '\'' +
                ", highLoadingTime=" + highLoadingTime +
                ", runningTime=" + runningTime +
                ", highLoadingPercent='" + highLoadingPercent + '\'' +
                ", lowLoadingTime=" + lowLoadingTime +
                ", lowLoadingPercent='" + lowLoadingPercent + '\'' +
                ", idleTime=" + idleTime +
                ", useTime=" + useTime +
                ", planUseTime=" + planUseTime +
                ", bandwidth=" + bandwidth +
                ", supportBandwidth=" + supportBandwidth +
                ", pingTime=" + pingTime +
                ", speedTestBandwidth=" + speedTestBandwidth +
                ", cpu=" + cpu +
                ", ram=" + ram +
                '}';
    }
}
