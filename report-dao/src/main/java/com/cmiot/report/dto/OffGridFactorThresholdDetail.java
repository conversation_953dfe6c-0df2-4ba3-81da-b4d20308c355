package com.cmiot.report.dto;


import lombok.Data;

import java.util.List;

@Data
public class OffGridFactorThresholdDetail {

    // 离网因素中文，如：终端质量因素
    private String factorName;

    // 离网因素的标识,如：1-终端质量因素,2-网络质量因素,3-客户服务因素,4-业务订购因素,5-装维服务因素
    private String factorKey;

    // 权重
    private double weight;

    // 阈值
    private int threshold;

    // 指标
    private List<OffGridFactorIndicator> indicators;
}
