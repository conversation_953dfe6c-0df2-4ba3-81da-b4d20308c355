package com.cmiot.report.dto.enterprise;

/**
 * @Classname EnterpriseComprehensiveInfo
 * @Description
 * @Date 2023/10/11 16:25
 * @Created by lei
 */
public class EnterpriseComprehensiveInfo {

    private Long enterpriseId;
    private String customerId;
    private String customerName;
    private String provinceCode;
    private String provinceName;
    private String cityCode;
    private String cityName;
    private String countyCode;
    private String countyName;
    private String industryCode;
    private String industryName;
    private String customerStatus;
    private String customerStatusName;
    private Integer businessCount;
    private Integer packageCount;
    private Integer gatewayCount;
    private Integer highLoadingGatewayCount;
    private Integer lowLoadingGatewayCount;
    private Integer longTimeIdleCount;
    private Integer overLifeCount;
    private Integer bandwidthNonsupport;
    private Integer networkSpeedExp;
    private Integer networkPingExp;
    private Integer cpuRamExp;

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public String getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(String industryCode) {
        this.industryCode = industryCode;
    }


    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }

    public String getCustomerStatus() {
        return customerStatus;
    }

    public void setCustomerStatus(String customerStatus) {
        this.customerStatus = customerStatus;
    }

    public String getCustomerStatusName() {
        return customerStatusName;
    }

    public void setCustomerStatusName(String customerStatusName) {
        this.customerStatusName = customerStatusName;
    }

    public Integer getBusinessCount() {
        return businessCount;
    }

    public void setBusinessCount(Integer businessCount) {
        this.businessCount = businessCount;
    }

    public Integer getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(Integer packageCount) {
        this.packageCount = packageCount;
    }

    public Integer getGatewayCount() {
        return gatewayCount;
    }

    public void setGatewayCount(Integer gatewayCount) {
        this.gatewayCount = gatewayCount;
    }

    public Integer getHighLoadingGatewayCount() {
        return highLoadingGatewayCount;
    }

    public void setHighLoadingGatewayCount(Integer highLoadingGatewayCount) {
        this.highLoadingGatewayCount = highLoadingGatewayCount;
    }

    public Integer getLowLoadingGatewayCount() {
        return lowLoadingGatewayCount;
    }

    public void setLowLoadingGatewayCount(Integer lowLoadingGatewayCount) {
        this.lowLoadingGatewayCount = lowLoadingGatewayCount;
    }

    public Integer getLongTimeIdleCount() {
        return longTimeIdleCount;
    }

    public void setLongTimeIdleCount(Integer longTimeIdleCount) {
        this.longTimeIdleCount = longTimeIdleCount;
    }

    public Integer getOverLifeCount() {
        return overLifeCount;
    }

    public void setOverLifeCount(Integer overLifeCount) {
        this.overLifeCount = overLifeCount;
    }

    public Integer getBandwidthNonsupport() {
        return bandwidthNonsupport;
    }

    public void setBandwidthNonsupport(Integer bandwidthNonsupport) {
        this.bandwidthNonsupport = bandwidthNonsupport;
    }

    public Integer getNetworkSpeedExp() {
        return networkSpeedExp;
    }

    public void setNetworkSpeedExp(Integer networkSpeedExp) {
        this.networkSpeedExp = networkSpeedExp;
    }

    public Integer getNetworkPingExp() {
        return networkPingExp;
    }

    public void setNetworkPingExp(Integer networkPingExp) {
        this.networkPingExp = networkPingExp;
    }

    public Integer getCpuRamExp() {
        return cpuRamExp;
    }

    public void setCpuRamExp(Integer cpuRamExp) {
        this.cpuRamExp = cpuRamExp;
    }
}
