package com.cmiot.report.dto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class GatewayAnomalyRecordQuery {
    private String eid;
    private String province;
    private List<String> provinceListStr;
    private List<Long> provinceListNum;
    private String city;
    private List<String> cityListStr;
    private List<Long> cityListNum;
    private String gatewaySn;
    private String customerName;//企业名称
    private String vendor;//厂商id
    private List<Long> vendorListNum;
    private String model;//设备类型id
    private List<Long> modelListNum;
    private String orderType;//排序类型
    private String orderName;//排序字段


    private Integer page = 1;
    private Integer pageSize = 20;

    private Integer lastday;

    public void formatParamList() {
        if (this.province != null && this.province.length() > 0) {
            String[] provinces = this.province.split(",");
            this.provinceListStr = Arrays.asList(provinces);
            this.provinceListNum = new ArrayList<>(this.provinceListStr.size());
            for (String s : this.provinceListStr) {
                this.provinceListNum.add(Long.valueOf(s));
            }
        }

        if (this.city != null && this.city.length() > 0) {
            String[] citys = this.city.split(",");
            this.cityListStr = Arrays.asList(citys);
            this.cityListNum = new ArrayList<>(this.cityListStr.size());
            for (String s : this.cityListStr) {
                this.cityListNum.add(Long.valueOf(s));
            }
        }

        if (this.vendor != null && this.vendor.length() > 0) {
            this.vendorListNum = new ArrayList<>();
            for (String s : this.vendor.split(",")) {
                this.vendorListNum.add(Long.valueOf(s));
            }
        }

        if (this.model != null && this.model.length() > 0) {
            this.modelListNum = new ArrayList<>();
            for (String s : this.model.split(",")) {
                this.modelListNum.add(Long.valueOf(s));
            }
        }

    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public List<String> getProvinceListStr() {
        return provinceListStr;
    }

    public void setProvinceListStr(List<String> provinceListStr) {
        this.provinceListStr = provinceListStr;
    }

    public List<Long> getProvinceListNum() {
        return provinceListNum;
    }

    public void setProvinceListNum(List<Long> provinceListNum) {
        this.provinceListNum = provinceListNum;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public List<String> getCityListStr() {
        return cityListStr;
    }

    public void setCityListStr(List<String> cityListStr) {
        this.cityListStr = cityListStr;
    }

    public List<Long> getCityListNum() {
        return cityListNum;
    }

    public void setCityListNum(List<Long> cityListNum) {
        this.cityListNum = cityListNum;
    }

    public String getGatewaySn() {
        return gatewaySn;
    }

    public void setGatewaySn(String gatewaySn) {
        this.gatewaySn = gatewaySn;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public List<Long> getVendorListNum() {
        return vendorListNum;
    }

    public void setVendorListNum(List<Long> vendorListNum) {
        this.vendorListNum = vendorListNum;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<Long> getModelListNum() {
        return modelListNum;
    }

    public void setModelListNum(List<Long> modelListNum) {
        this.modelListNum = modelListNum;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getLastday() {
        return lastday;
    }

    public void setLastday(Integer lastday) {
        this.lastday = lastday;
    }
}
