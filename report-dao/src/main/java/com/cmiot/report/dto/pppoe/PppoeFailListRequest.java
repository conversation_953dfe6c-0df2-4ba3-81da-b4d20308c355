package com.cmiot.report.dto.pppoe;

import java.io.Serializable;

public class PppoeFailListRequest implements Serializable {

	private static final long serialVersionUID = -6467255332475937655L;
	/**
	 * 当前页
	 */
	private Integer page;
	
	/**
	 * 每页展示数据量
	 */
	private Integer pageSize;
	
	/**
	 * 网关SN
	 */
	private String sn;

	@Override
	public String toString() {
		return "PppoeFailListRequest [page=" + page + ", pageSize=" + pageSize + ", sn=" + sn + "]";
	}

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		this.page = page;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}
	
}
