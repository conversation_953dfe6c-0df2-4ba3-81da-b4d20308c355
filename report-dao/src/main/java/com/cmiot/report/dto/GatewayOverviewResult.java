package com.cmiot.report.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@EqualsAndHashCode
public class GatewayOverviewResult implements Serializable {
    private static final long serialVersionUID = 3034663411492394000L;

    /**
     * 累计数量
     */
    private String totalNum;

    /**
     * 新增数量
     */
    private String addNum;

    /**
     * 日活数量
     */
    private String dayActiveNum;

    /**
     * 流失数量
     */
    private String runOffNum;

    /**
     * 累计环比
     */
    private String totalMonthOnMonth;

    /**
     * 累计同比
     */
    private String totalYearOnYear;

    /**
     * 使用家庭网关接入的数量（1.13.0新增）
     */
    private String connectFamilyGatewayNum;

    /**
     * 使用家庭网关接入的占比（1.13.0新增）
     */
    private String connectFamilyGatewayRate;

}
