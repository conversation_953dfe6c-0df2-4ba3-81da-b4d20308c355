package com.cmiot.report.dto;


public class PonPowerStatisticsM {

    // 省份&地市,厂商,型号
    private String name;

    // 弱光网关数量
    private long lowGtwCount;

    // 弱光功率平均值
    private long lowPonRxPowerCurrAvg;

    // 强光网关数量
    private long hardGtwCount;

    // 强光功率平均值
    private long hardPonRxPowerCurrAvg;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getLowGtwCount() {
        return lowGtwCount;
    }

    public void setLowGtwCount(long lowGtwCount) {
        this.lowGtwCount = lowGtwCount;
    }

    public long getLowPonRxPowerCurrAvg() {
        return lowPonRxPowerCurrAvg;
    }

    public void setLowPonRxPowerCurrAvg(long lowPonRxPowerCurrAvg) {
        this.lowPonRxPowerCurrAvg = lowPonRxPowerCurrAvg;
    }

    public long getHardGtwCount() {
        return hardGtwCount;
    }

    public void setHardGtwCount(long hardGtwCount) {
        this.hardGtwCount = hardGtwCount;
    }

    public long getHardPonRxPowerCurrAvg() {
        return hardPonRxPowerCurrAvg;
    }

    public void setHardPonRxPowerCurrAvg(long hardPonRxPowerCurrAvg) {
        this.hardPonRxPowerCurrAvg = hardPonRxPowerCurrAvg;
    }
}
