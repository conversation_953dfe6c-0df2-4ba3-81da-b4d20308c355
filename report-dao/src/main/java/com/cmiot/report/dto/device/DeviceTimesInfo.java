package com.cmiot.report.dto.device;

/**
 * @Classname DeviceFlowInfo
 * @Description
 * @Date 2023/12/25 18:03
 * @Created by lei
 */
public class DeviceTimesInfo {
    private String mac;
    private String deviceName;
    private String dhcpName;
    private String deviceType;
    private Long times;

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDhcpName() {
        return dhcpName;
    }

    public void setDhcpName(String dhcpName) {
        this.dhcpName = dhcpName;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public Long getTimes() {
        return times;
    }

    public void setTimes(Long times) {
        this.times = times;
    }
}
