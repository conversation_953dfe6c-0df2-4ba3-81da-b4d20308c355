package com.cmiot.report.dto.customer;

import lombok.Data;

import java.util.List;

/**
 * @Classname NetworkUsageChartRequest
 * @Description
 * @Date 2022/8/9 16:08
 * @Created by lei
 */
@Data
public class NetworkUsageChartRequest {

    // 省份
    private String province;

    // 地市
    private String city;

    // 业务类型
    private String businessType;
    //行业
    private String industry;

    // 开始日期 yyyy-MM-dd
    private String startTime;

    // 结束日期 yyyy-MM-dd
    private String endTime;

    //类型：区域1，行业2，时间3
    private String type;

    private String selectValue;
    //时间类型 1 天， 2 近7天，3 近30天，4 近1年
    //按小时， 按天， 间隔5天， 按月
    private String timeType;

}
