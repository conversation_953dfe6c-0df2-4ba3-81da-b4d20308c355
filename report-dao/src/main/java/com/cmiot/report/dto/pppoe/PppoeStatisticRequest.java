package com.cmiot.report.dto.pppoe;

import java.io.Serializable;

public class PppoeStatisticRequest implements Serializable {

	private static final long serialVersionUID = -2710436771137797093L;

	/**
	 * 省份编码。例如： 110000
	 */
	private String province;

	/**
	 * 地市编码。例如： 110101,110102,110105,110106
	 */
	private String city;

	/**
	 * 厂商编码。例如： 8,16
	 */
	private String vendor;

	/**
	 * 开始时间。例如： 2022-05-02
	 */
	private String startTime;

	/**
	 * 结束时间。例如： 2022-05-08
	 */
	private String endTime;

	/**
	 * 类型。区域1，厂商2
	 */
	private String type;

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getVendor() {
		return vendor;
	}

	public void setVendor(String vendor) {
		this.vendor = vendor;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Override
	public String toString() {
		return "PppoeStatisticRequest [province=" + province + ", city=" + city + ", vendor=" + vendor + ", startTime="
				+ startTime + ", endTime=" + endTime + ", type=" + type + "]";
	}

}
