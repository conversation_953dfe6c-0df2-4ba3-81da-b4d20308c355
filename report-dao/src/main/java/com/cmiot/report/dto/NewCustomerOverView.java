package com.cmiot.report.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class NewCustomerOverView {
    // 企业新增客户数
    @Builder.Default
    private long newCustomerNum = 0;

    // 首次新增客户且办理业务的客户数
    @Builder.Default
    private long firstBusinessCustomer = 0;

}
