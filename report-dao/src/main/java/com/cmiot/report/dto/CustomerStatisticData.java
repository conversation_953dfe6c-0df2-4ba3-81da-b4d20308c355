package com.cmiot.report.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerStatisticData {
    // 存量客户总数
    @Builder.Default
    private long approvalCustomerNum = 0;

    // 未办理客户总数
    @Builder.Default
    private long unBusinessCustomerNum = 0;

    // 已办理客户总数
    @Builder.Default
    private long businessCustomerNum = 0;

    // 业务办理率
    @Builder.Default
    private float businessRate = 0.0f;

    // 周期内新办理业务的存量客户数
    @Builder.Default
    private long newBusinessCustomerNum = 0;

    // 首次办理客户的存量客户总数
    @Builder.Default
    private long firstBusinessCustomerNum = 0;

    // 退订客户的存量客户总数
    @Builder.Default
    private long cancelOderCustomerNum= 0;
}
