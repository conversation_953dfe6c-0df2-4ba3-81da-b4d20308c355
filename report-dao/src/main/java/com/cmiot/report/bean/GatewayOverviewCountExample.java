package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.List;

public class GatewayOverviewCountExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GatewayOverviewCountExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andGatewayCountIsNull() {
            addCriterion("gateway_count is null");
            return (Criteria) this;
        }

        public Criteria andGatewayCountIsNotNull() {
            addCriterion("gateway_count is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayCountEqualTo(Long value) {
            addCriterion("gateway_count =", value, "gatewayCount");
            return (Criteria) this;
        }

        public Criteria andGatewayCountNotEqualTo(Long value) {
            addCriterion("gateway_count <>", value, "gatewayCount");
            return (Criteria) this;
        }

        public Criteria andGatewayCountGreaterThan(Long value) {
            addCriterion("gateway_count >", value, "gatewayCount");
            return (Criteria) this;
        }

        public Criteria andGatewayCountGreaterThanOrEqualTo(Long value) {
            addCriterion("gateway_count >=", value, "gatewayCount");
            return (Criteria) this;
        }

        public Criteria andGatewayCountLessThan(Long value) {
            addCriterion("gateway_count <", value, "gatewayCount");
            return (Criteria) this;
        }

        public Criteria andGatewayCountLessThanOrEqualTo(Long value) {
            addCriterion("gateway_count <=", value, "gatewayCount");
            return (Criteria) this;
        }

        public Criteria andGatewayCountIn(List<Long> values) {
            addCriterion("gateway_count in", values, "gatewayCount");
            return (Criteria) this;
        }

        public Criteria andGatewayCountNotIn(List<Long> values) {
            addCriterion("gateway_count not in", values, "gatewayCount");
            return (Criteria) this;
        }

        public Criteria andGatewayCountBetween(Long value1, Long value2) {
            addCriterion("gateway_count between", value1, value2, "gatewayCount");
            return (Criteria) this;
        }

        public Criteria andGatewayCountNotBetween(Long value1, Long value2) {
            addCriterion("gateway_count not between", value1, value2, "gatewayCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountIsNull() {
            addCriterion("gateway_inc_count is null");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountIsNotNull() {
            addCriterion("gateway_inc_count is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountEqualTo(Long value) {
            addCriterion("gateway_inc_count =", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountNotEqualTo(Long value) {
            addCriterion("gateway_inc_count <>", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountGreaterThan(Long value) {
            addCriterion("gateway_inc_count >", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountGreaterThanOrEqualTo(Long value) {
            addCriterion("gateway_inc_count >=", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountLessThan(Long value) {
            addCriterion("gateway_inc_count <", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountLessThanOrEqualTo(Long value) {
            addCriterion("gateway_inc_count <=", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountIn(List<Long> values) {
            addCriterion("gateway_inc_count in", values, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountNotIn(List<Long> values) {
            addCriterion("gateway_inc_count not in", values, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountBetween(Long value1, Long value2) {
            addCriterion("gateway_inc_count between", value1, value2, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountNotBetween(Long value1, Long value2) {
            addCriterion("gateway_inc_count not between", value1, value2, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountIsNull() {
            addCriterion("gateway_active_count is null");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountIsNotNull() {
            addCriterion("gateway_active_count is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountEqualTo(Long value) {
            addCriterion("gateway_active_count =", value, "gatewayActiveCount");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountNotEqualTo(Long value) {
            addCriterion("gateway_active_count <>", value, "gatewayActiveCount");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountGreaterThan(Long value) {
            addCriterion("gateway_active_count >", value, "gatewayActiveCount");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountGreaterThanOrEqualTo(Long value) {
            addCriterion("gateway_active_count >=", value, "gatewayActiveCount");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountLessThan(Long value) {
            addCriterion("gateway_active_count <", value, "gatewayActiveCount");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountLessThanOrEqualTo(Long value) {
            addCriterion("gateway_active_count <=", value, "gatewayActiveCount");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountIn(List<Long> values) {
            addCriterion("gateway_active_count in", values, "gatewayActiveCount");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountNotIn(List<Long> values) {
            addCriterion("gateway_active_count not in", values, "gatewayActiveCount");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountBetween(Long value1, Long value2) {
            addCriterion("gateway_active_count between", value1, value2, "gatewayActiveCount");
            return (Criteria) this;
        }

        public Criteria andGatewayActiveCountNotBetween(Long value1, Long value2) {
            addCriterion("gateway_active_count not between", value1, value2, "gatewayActiveCount");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountIsNull() {
            addCriterion("gateway_lost_count is null");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountIsNotNull() {
            addCriterion("gateway_lost_count is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountEqualTo(Long value) {
            addCriterion("gateway_lost_count =", value, "gatewayLostCount");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountNotEqualTo(Long value) {
            addCriterion("gateway_lost_count <>", value, "gatewayLostCount");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountGreaterThan(Long value) {
            addCriterion("gateway_lost_count >", value, "gatewayLostCount");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountGreaterThanOrEqualTo(Long value) {
            addCriterion("gateway_lost_count >=", value, "gatewayLostCount");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountLessThan(Long value) {
            addCriterion("gateway_lost_count <", value, "gatewayLostCount");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountLessThanOrEqualTo(Long value) {
            addCriterion("gateway_lost_count <=", value, "gatewayLostCount");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountIn(List<Long> values) {
            addCriterion("gateway_lost_count in", values, "gatewayLostCount");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountNotIn(List<Long> values) {
            addCriterion("gateway_lost_count not in", values, "gatewayLostCount");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountBetween(Long value1, Long value2) {
            addCriterion("gateway_lost_count between", value1, value2, "gatewayLostCount");
            return (Criteria) this;
        }

        public Criteria andGatewayLostCountNotBetween(Long value1, Long value2) {
            addCriterion("gateway_lost_count not between", value1, value2, "gatewayLostCount");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioIsNull() {
            addCriterion("gateway_yoy_ratio is null");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioIsNotNull() {
            addCriterion("gateway_yoy_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioEqualTo(Double value) {
            addCriterion("gateway_yoy_ratio =", value, "gatewayYoyRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioNotEqualTo(Double value) {
            addCriterion("gateway_yoy_ratio <>", value, "gatewayYoyRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioGreaterThan(Double value) {
            addCriterion("gateway_yoy_ratio >", value, "gatewayYoyRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioGreaterThanOrEqualTo(Double value) {
            addCriterion("gateway_yoy_ratio >=", value, "gatewayYoyRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioLessThan(Double value) {
            addCriterion("gateway_yoy_ratio <", value, "gatewayYoyRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioLessThanOrEqualTo(Double value) {
            addCriterion("gateway_yoy_ratio <=", value, "gatewayYoyRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioIn(List<Double> values) {
            addCriterion("gateway_yoy_ratio in", values, "gatewayYoyRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioNotIn(List<Double> values) {
            addCriterion("gateway_yoy_ratio not in", values, "gatewayYoyRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioBetween(Double value1, Double value2) {
            addCriterion("gateway_yoy_ratio between", value1, value2, "gatewayYoyRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayYoyRatioNotBetween(Double value1, Double value2) {
            addCriterion("gateway_yoy_ratio not between", value1, value2, "gatewayYoyRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioIsNull() {
            addCriterion("gateway_mom_ratio is null");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioIsNotNull() {
            addCriterion("gateway_mom_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioEqualTo(Double value) {
            addCriterion("gateway_mom_ratio =", value, "gatewayMomRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioNotEqualTo(Double value) {
            addCriterion("gateway_mom_ratio <>", value, "gatewayMomRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioGreaterThan(Double value) {
            addCriterion("gateway_mom_ratio >", value, "gatewayMomRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioGreaterThanOrEqualTo(Double value) {
            addCriterion("gateway_mom_ratio >=", value, "gatewayMomRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioLessThan(Double value) {
            addCriterion("gateway_mom_ratio <", value, "gatewayMomRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioLessThanOrEqualTo(Double value) {
            addCriterion("gateway_mom_ratio <=", value, "gatewayMomRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioIn(List<Double> values) {
            addCriterion("gateway_mom_ratio in", values, "gatewayMomRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioNotIn(List<Double> values) {
            addCriterion("gateway_mom_ratio not in", values, "gatewayMomRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioBetween(Double value1, Double value2) {
            addCriterion("gateway_mom_ratio between", value1, value2, "gatewayMomRatio");
            return (Criteria) this;
        }

        public Criteria andGatewayMomRatioNotBetween(Double value1, Double value2) {
            addCriterion("gateway_mom_ratio not between", value1, value2, "gatewayMomRatio");
            return (Criteria) this;
        }

        public Criteria andGdateIsNull() {
            addCriterion("gdate is null");
            return (Criteria) this;
        }

        public Criteria andGdateIsNotNull() {
            addCriterion("gdate is not null");
            return (Criteria) this;
        }

        public Criteria andGdateEqualTo(Long value) {
            addCriterion("gdate =", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotEqualTo(Long value) {
            addCriterion("gdate <>", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThan(Long value) {
            addCriterion("gdate >", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThanOrEqualTo(Long value) {
            addCriterion("gdate >=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThan(Long value) {
            addCriterion("gdate <", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThanOrEqualTo(Long value) {
            addCriterion("gdate <=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateIn(List<Long> values) {
            addCriterion("gdate in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotIn(List<Long> values) {
            addCriterion("gdate not in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateBetween(Long value1, Long value2) {
            addCriterion("gdate between", value1, value2, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotBetween(Long value1, Long value2) {
            addCriterion("gdate not between", value1, value2, "gdate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}