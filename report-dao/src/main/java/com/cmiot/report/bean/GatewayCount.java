package com.cmiot.report.bean;

import java.util.Objects;

public class GatewayCount {
    private String factoryCode;

    private String provinceCode;

    private String industry;

    private Long gatewayCount;

    private Long gdate;

    /**
     * 接入网关类型，0，全量、1，政企网关、2，家庭网关（1.13.0新增）
     */
    private Long connectType;

    public String getFactoryCode() {
        return factoryCode;
    }

    public void setFactoryCode(String factoryCode) {
        this.factoryCode = factoryCode == null ? null : factoryCode.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry == null ? null : industry.trim();
    }

    public Long getGatewayCount() {
        return gatewayCount;
    }

    public void setGatewayCount(Long gatewayCount) {
        this.gatewayCount = gatewayCount;
    }

    public Long getGdate() {
        return gdate;
    }

    public void setGdate(Long gdate) {
        this.gdate = gdate;
    }

    public Long getConnectType() {
        return connectType;
    }

    public void setConnectType(Long connectType) {
        this.connectType = connectType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GatewayCount that = (GatewayCount) o;
        return Objects.equals(factoryCode, that.factoryCode) &&
                Objects.equals(provinceCode, that.provinceCode) &&
                Objects.equals(industry, that.industry) &&
                Objects.equals(connectType, that.connectType) &&
                Objects.equals(gatewayCount, that.gatewayCount) &&
                Objects.equals(gdate, that.gdate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(factoryCode, provinceCode, industry, gatewayCount, gdate, connectType);
    }
}