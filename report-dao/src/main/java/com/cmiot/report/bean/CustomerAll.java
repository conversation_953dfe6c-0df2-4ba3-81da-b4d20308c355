package com.cmiot.report.bean;

import java.util.Date;

public class CustomerAll {
    private Long enterpriseId;

    private String customerId;

    private String customerName;

    private String address;

    private Integer idType;

    private String idNumber;

    private String parentCustomerId;

    private String contactPhone;

    private String contactEmail;

    private String provinceCode;

    private String cityCode;

    private String countyCode;

    private Date createTime;

    private Long industry;

    private String industryCategory;

    private String alias;

    private String enterpriseMark;

    private Integer customerStatus;

    private String customerCodeBoss;

    private String customerSize;

    private Date lastBusinessTime;

    private Integer businessCount;

    private String valueCategory;

    private String managerNumber;

    private String website;

    private String regionFeature;

    private String ownership;

    private String organizationCode;

    private String postCode;

    private Date lastUpdateTime;

    private Date sampleTime;

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public Integer getIdType() {
        return idType;
    }

    public void setIdType(Integer idType) {
        this.idType = idType;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber == null ? null : idNumber.trim();
    }

    public String getParentCustomerId() {
        return parentCustomerId;
    }

    public void setParentCustomerId(String parentCustomerId) {
        this.parentCustomerId = parentCustomerId == null ? null : parentCustomerId.trim();
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail == null ? null : contactEmail.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode == null ? null : countyCode.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getIndustry() {
        return industry;
    }

    public void setIndustry(Long industry) {
        this.industry = industry;
    }

    public String getIndustryCategory() {
        return industryCategory;
    }

    public void setIndustryCategory(String industryCategory) {
        this.industryCategory = industryCategory == null ? null : industryCategory.trim();
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias == null ? null : alias.trim();
    }

    public String getEnterpriseMark() {
        return enterpriseMark;
    }

    public void setEnterpriseMark(String enterpriseMark) {
        this.enterpriseMark = enterpriseMark == null ? null : enterpriseMark.trim();
    }

    public Integer getCustomerStatus() {
        return customerStatus;
    }

    public void setCustomerStatus(Integer customerStatus) {
        this.customerStatus = customerStatus;
    }

    public String getCustomerCodeBoss() {
        return customerCodeBoss;
    }

    public void setCustomerCodeBoss(String customerCodeBoss) {
        this.customerCodeBoss = customerCodeBoss == null ? null : customerCodeBoss.trim();
    }

    public String getCustomerSize() {
        return customerSize;
    }

    public void setCustomerSize(String customerSize) {
        this.customerSize = customerSize == null ? null : customerSize.trim();
    }

    public Date getLastBusinessTime() {
        return lastBusinessTime;
    }

    public void setLastBusinessTime(Date lastBusinessTime) {
        this.lastBusinessTime = lastBusinessTime;
    }

    public Integer getBusinessCount() {
        return businessCount;
    }

    public void setBusinessCount(Integer businessCount) {
        this.businessCount = businessCount;
    }

    public String getValueCategory() {
        return valueCategory;
    }

    public void setValueCategory(String valueCategory) {
        this.valueCategory = valueCategory == null ? null : valueCategory.trim();
    }

    public String getManagerNumber() {
        return managerNumber;
    }

    public void setManagerNumber(String managerNumber) {
        this.managerNumber = managerNumber == null ? null : managerNumber.trim();
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website == null ? null : website.trim();
    }

    public String getRegionFeature() {
        return regionFeature;
    }

    public void setRegionFeature(String regionFeature) {
        this.regionFeature = regionFeature == null ? null : regionFeature.trim();
    }

    public String getOwnership() {
        return ownership;
    }

    public void setOwnership(String ownership) {
        this.ownership = ownership == null ? null : ownership.trim();
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode == null ? null : organizationCode.trim();
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode == null ? null : postCode.trim();
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }
}