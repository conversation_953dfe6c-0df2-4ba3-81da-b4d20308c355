package com.cmiot.report.bean;

import java.util.Date;

public class CustomerOrderAggAll {
    private Long enterpriseId;

    private String customerId;

    private String customerName;

    private String provinceCode;

    private String cityCode;

    private Date createTime;

    private Long industry;

    private Integer customerStatus;

    private String businessProductId;

    private String businessType;

    private Date businessSubscribeTime;

    private Date businessUnsubscribeTime;

    private Byte businessStatus;

    private Date packageEffectTime;

    private Date packageUneffectTime;

    private Byte packageStatus;

    private Date orderCreateTime;

    private Date orderUpdateTime;

    private Date sampleTime;

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getIndustry() {
        return industry;
    }

    public void setIndustry(Long industry) {
        this.industry = industry;
    }

    public Integer getCustomerStatus() {
        return customerStatus;
    }

    public void setCustomerStatus(Integer customerStatus) {
        this.customerStatus = customerStatus;
    }

    public String getBusinessProductId() {
        return businessProductId;
    }

    public void setBusinessProductId(String businessProductId) {
        this.businessProductId = businessProductId == null ? null : businessProductId.trim();
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType == null ? null : businessType.trim();
    }

    public Date getBusinessSubscribeTime() {
        return businessSubscribeTime;
    }

    public void setBusinessSubscribeTime(Date businessSubscribeTime) {
        this.businessSubscribeTime = businessSubscribeTime;
    }

    public Date getBusinessUnsubscribeTime() {
        return businessUnsubscribeTime;
    }

    public void setBusinessUnsubscribeTime(Date businessUnsubscribeTime) {
        this.businessUnsubscribeTime = businessUnsubscribeTime;
    }

    public Byte getBusinessStatus() {
        return businessStatus;
    }

    public void setBusinessStatus(Byte businessStatus) {
        this.businessStatus = businessStatus;
    }

    public Date getPackageEffectTime() {
        return packageEffectTime;
    }

    public void setPackageEffectTime(Date packageEffectTime) {
        this.packageEffectTime = packageEffectTime;
    }

    public Date getPackageUneffectTime() {
        return packageUneffectTime;
    }

    public void setPackageUneffectTime(Date packageUneffectTime) {
        this.packageUneffectTime = packageUneffectTime;
    }

    public Byte getPackageStatus() {
        return packageStatus;
    }

    public void setPackageStatus(Byte packageStatus) {
        this.packageStatus = packageStatus;
    }

    public Date getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public Date getOrderUpdateTime() {
        return orderUpdateTime;
    }

    public void setOrderUpdateTime(Date orderUpdateTime) {
        this.orderUpdateTime = orderUpdateTime;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }
}