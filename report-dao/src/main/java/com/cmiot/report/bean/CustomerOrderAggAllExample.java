package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CustomerOrderAggAllExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CustomerOrderAggAllExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andEnterpriseIdIsNull() {
            addCriterion("enterprise_id is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNotNull() {
            addCriterion("enterprise_id is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdEqualTo(Long value) {
            addCriterion("enterprise_id =", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotEqualTo(Long value) {
            addCriterion("enterprise_id <>", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThan(Long value) {
            addCriterion("enterprise_id >", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("enterprise_id >=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThan(Long value) {
            addCriterion("enterprise_id <", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThanOrEqualTo(Long value) {
            addCriterion("enterprise_id <=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIn(List<Long> values) {
            addCriterion("enterprise_id in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotIn(List<Long> values) {
            addCriterion("enterprise_id not in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdBetween(Long value1, Long value2) {
            addCriterion("enterprise_id between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotBetween(Long value1, Long value2) {
            addCriterion("enterprise_id not between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(String value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(String value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(String value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(String value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(String value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(String value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLike(String value) {
            addCriterion("customer_id like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotLike(String value) {
            addCriterion("customer_id not like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<String> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<String> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(String value1, String value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(String value1, String value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNull() {
            addCriterion("industry is null");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNotNull() {
            addCriterion("industry is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryEqualTo(Long value) {
            addCriterion("industry =", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotEqualTo(Long value) {
            addCriterion("industry <>", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThan(Long value) {
            addCriterion("industry >", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThanOrEqualTo(Long value) {
            addCriterion("industry >=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThan(Long value) {
            addCriterion("industry <", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThanOrEqualTo(Long value) {
            addCriterion("industry <=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryIn(List<Long> values) {
            addCriterion("industry in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotIn(List<Long> values) {
            addCriterion("industry not in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryBetween(Long value1, Long value2) {
            addCriterion("industry between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotBetween(Long value1, Long value2) {
            addCriterion("industry not between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIsNull() {
            addCriterion("customer_status is null");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIsNotNull() {
            addCriterion("customer_status is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusEqualTo(Integer value) {
            addCriterion("customer_status =", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotEqualTo(Integer value) {
            addCriterion("customer_status <>", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusGreaterThan(Integer value) {
            addCriterion("customer_status >", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_status >=", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusLessThan(Integer value) {
            addCriterion("customer_status <", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusLessThanOrEqualTo(Integer value) {
            addCriterion("customer_status <=", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIn(List<Integer> values) {
            addCriterion("customer_status in", values, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotIn(List<Integer> values) {
            addCriterion("customer_status not in", values, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusBetween(Integer value1, Integer value2) {
            addCriterion("customer_status between", value1, value2, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_status not between", value1, value2, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdIsNull() {
            addCriterion("business_product_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdIsNotNull() {
            addCriterion("business_product_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdEqualTo(String value) {
            addCriterion("business_product_id =", value, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdNotEqualTo(String value) {
            addCriterion("business_product_id <>", value, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdGreaterThan(String value) {
            addCriterion("business_product_id >", value, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdGreaterThanOrEqualTo(String value) {
            addCriterion("business_product_id >=", value, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdLessThan(String value) {
            addCriterion("business_product_id <", value, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdLessThanOrEqualTo(String value) {
            addCriterion("business_product_id <=", value, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdLike(String value) {
            addCriterion("business_product_id like", value, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdNotLike(String value) {
            addCriterion("business_product_id not like", value, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdIn(List<String> values) {
            addCriterion("business_product_id in", values, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdNotIn(List<String> values) {
            addCriterion("business_product_id not in", values, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdBetween(String value1, String value2) {
            addCriterion("business_product_id between", value1, value2, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessProductIdNotBetween(String value1, String value2) {
            addCriterion("business_product_id not between", value1, value2, "businessProductId");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNull() {
            addCriterion("business_type is null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNotNull() {
            addCriterion("business_type is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeEqualTo(String value) {
            addCriterion("business_type =", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotEqualTo(String value) {
            addCriterion("business_type <>", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThan(String value) {
            addCriterion("business_type >", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThanOrEqualTo(String value) {
            addCriterion("business_type >=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThan(String value) {
            addCriterion("business_type <", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThanOrEqualTo(String value) {
            addCriterion("business_type <=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLike(String value) {
            addCriterion("business_type like", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotLike(String value) {
            addCriterion("business_type not like", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIn(List<String> values) {
            addCriterion("business_type in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotIn(List<String> values) {
            addCriterion("business_type not in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeBetween(String value1, String value2) {
            addCriterion("business_type between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotBetween(String value1, String value2) {
            addCriterion("business_type not between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeIsNull() {
            addCriterion("business_subscribe_time is null");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeIsNotNull() {
            addCriterion("business_subscribe_time is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeEqualTo(Date value) {
            addCriterion("business_subscribe_time =", value, "businessSubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeNotEqualTo(Date value) {
            addCriterion("business_subscribe_time <>", value, "businessSubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeGreaterThan(Date value) {
            addCriterion("business_subscribe_time >", value, "businessSubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("business_subscribe_time >=", value, "businessSubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeLessThan(Date value) {
            addCriterion("business_subscribe_time <", value, "businessSubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeLessThanOrEqualTo(Date value) {
            addCriterion("business_subscribe_time <=", value, "businessSubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeIn(List<Date> values) {
            addCriterion("business_subscribe_time in", values, "businessSubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeNotIn(List<Date> values) {
            addCriterion("business_subscribe_time not in", values, "businessSubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeBetween(Date value1, Date value2) {
            addCriterion("business_subscribe_time between", value1, value2, "businessSubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessSubscribeTimeNotBetween(Date value1, Date value2) {
            addCriterion("business_subscribe_time not between", value1, value2, "businessSubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeIsNull() {
            addCriterion("business_unsubscribe_time is null");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeIsNotNull() {
            addCriterion("business_unsubscribe_time is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeEqualTo(Date value) {
            addCriterion("business_unsubscribe_time =", value, "businessUnsubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeNotEqualTo(Date value) {
            addCriterion("business_unsubscribe_time <>", value, "businessUnsubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeGreaterThan(Date value) {
            addCriterion("business_unsubscribe_time >", value, "businessUnsubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("business_unsubscribe_time >=", value, "businessUnsubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeLessThan(Date value) {
            addCriterion("business_unsubscribe_time <", value, "businessUnsubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeLessThanOrEqualTo(Date value) {
            addCriterion("business_unsubscribe_time <=", value, "businessUnsubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeIn(List<Date> values) {
            addCriterion("business_unsubscribe_time in", values, "businessUnsubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeNotIn(List<Date> values) {
            addCriterion("business_unsubscribe_time not in", values, "businessUnsubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeBetween(Date value1, Date value2) {
            addCriterion("business_unsubscribe_time between", value1, value2, "businessUnsubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessUnsubscribeTimeNotBetween(Date value1, Date value2) {
            addCriterion("business_unsubscribe_time not between", value1, value2, "businessUnsubscribeTime");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusIsNull() {
            addCriterion("business_status is null");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusIsNotNull() {
            addCriterion("business_status is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusEqualTo(Byte value) {
            addCriterion("business_status =", value, "businessStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusNotEqualTo(Byte value) {
            addCriterion("business_status <>", value, "businessStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusGreaterThan(Byte value) {
            addCriterion("business_status >", value, "businessStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("business_status >=", value, "businessStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusLessThan(Byte value) {
            addCriterion("business_status <", value, "businessStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusLessThanOrEqualTo(Byte value) {
            addCriterion("business_status <=", value, "businessStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusIn(List<Byte> values) {
            addCriterion("business_status in", values, "businessStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusNotIn(List<Byte> values) {
            addCriterion("business_status not in", values, "businessStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusBetween(Byte value1, Byte value2) {
            addCriterion("business_status between", value1, value2, "businessStatus");
            return (Criteria) this;
        }

        public Criteria andBusinessStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("business_status not between", value1, value2, "businessStatus");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeIsNull() {
            addCriterion("package_effect_time is null");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeIsNotNull() {
            addCriterion("package_effect_time is not null");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeEqualTo(Date value) {
            addCriterion("package_effect_time =", value, "packageEffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeNotEqualTo(Date value) {
            addCriterion("package_effect_time <>", value, "packageEffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeGreaterThan(Date value) {
            addCriterion("package_effect_time >", value, "packageEffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("package_effect_time >=", value, "packageEffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeLessThan(Date value) {
            addCriterion("package_effect_time <", value, "packageEffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeLessThanOrEqualTo(Date value) {
            addCriterion("package_effect_time <=", value, "packageEffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeIn(List<Date> values) {
            addCriterion("package_effect_time in", values, "packageEffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeNotIn(List<Date> values) {
            addCriterion("package_effect_time not in", values, "packageEffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeBetween(Date value1, Date value2) {
            addCriterion("package_effect_time between", value1, value2, "packageEffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageEffectTimeNotBetween(Date value1, Date value2) {
            addCriterion("package_effect_time not between", value1, value2, "packageEffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeIsNull() {
            addCriterion("package_uneffect_time is null");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeIsNotNull() {
            addCriterion("package_uneffect_time is not null");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeEqualTo(Date value) {
            addCriterion("package_uneffect_time =", value, "packageUneffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeNotEqualTo(Date value) {
            addCriterion("package_uneffect_time <>", value, "packageUneffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeGreaterThan(Date value) {
            addCriterion("package_uneffect_time >", value, "packageUneffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("package_uneffect_time >=", value, "packageUneffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeLessThan(Date value) {
            addCriterion("package_uneffect_time <", value, "packageUneffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeLessThanOrEqualTo(Date value) {
            addCriterion("package_uneffect_time <=", value, "packageUneffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeIn(List<Date> values) {
            addCriterion("package_uneffect_time in", values, "packageUneffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeNotIn(List<Date> values) {
            addCriterion("package_uneffect_time not in", values, "packageUneffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeBetween(Date value1, Date value2) {
            addCriterion("package_uneffect_time between", value1, value2, "packageUneffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageUneffectTimeNotBetween(Date value1, Date value2) {
            addCriterion("package_uneffect_time not between", value1, value2, "packageUneffectTime");
            return (Criteria) this;
        }

        public Criteria andPackageStatusIsNull() {
            addCriterion("package_status is null");
            return (Criteria) this;
        }

        public Criteria andPackageStatusIsNotNull() {
            addCriterion("package_status is not null");
            return (Criteria) this;
        }

        public Criteria andPackageStatusEqualTo(Byte value) {
            addCriterion("package_status =", value, "packageStatus");
            return (Criteria) this;
        }

        public Criteria andPackageStatusNotEqualTo(Byte value) {
            addCriterion("package_status <>", value, "packageStatus");
            return (Criteria) this;
        }

        public Criteria andPackageStatusGreaterThan(Byte value) {
            addCriterion("package_status >", value, "packageStatus");
            return (Criteria) this;
        }

        public Criteria andPackageStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("package_status >=", value, "packageStatus");
            return (Criteria) this;
        }

        public Criteria andPackageStatusLessThan(Byte value) {
            addCriterion("package_status <", value, "packageStatus");
            return (Criteria) this;
        }

        public Criteria andPackageStatusLessThanOrEqualTo(Byte value) {
            addCriterion("package_status <=", value, "packageStatus");
            return (Criteria) this;
        }

        public Criteria andPackageStatusIn(List<Byte> values) {
            addCriterion("package_status in", values, "packageStatus");
            return (Criteria) this;
        }

        public Criteria andPackageStatusNotIn(List<Byte> values) {
            addCriterion("package_status not in", values, "packageStatus");
            return (Criteria) this;
        }

        public Criteria andPackageStatusBetween(Byte value1, Byte value2) {
            addCriterion("package_status between", value1, value2, "packageStatus");
            return (Criteria) this;
        }

        public Criteria andPackageStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("package_status not between", value1, value2, "packageStatus");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeIsNull() {
            addCriterion("order_create_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeIsNotNull() {
            addCriterion("order_create_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeEqualTo(Date value) {
            addCriterion("order_create_time =", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeNotEqualTo(Date value) {
            addCriterion("order_create_time <>", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeGreaterThan(Date value) {
            addCriterion("order_create_time >", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_create_time >=", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeLessThan(Date value) {
            addCriterion("order_create_time <", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_create_time <=", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeIn(List<Date> values) {
            addCriterion("order_create_time in", values, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeNotIn(List<Date> values) {
            addCriterion("order_create_time not in", values, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeBetween(Date value1, Date value2) {
            addCriterion("order_create_time between", value1, value2, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_create_time not between", value1, value2, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeIsNull() {
            addCriterion("order_update_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeIsNotNull() {
            addCriterion("order_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeEqualTo(Date value) {
            addCriterion("order_update_time =", value, "orderUpdateTime");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeNotEqualTo(Date value) {
            addCriterion("order_update_time <>", value, "orderUpdateTime");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeGreaterThan(Date value) {
            addCriterion("order_update_time >", value, "orderUpdateTime");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_update_time >=", value, "orderUpdateTime");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeLessThan(Date value) {
            addCriterion("order_update_time <", value, "orderUpdateTime");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_update_time <=", value, "orderUpdateTime");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeIn(List<Date> values) {
            addCriterion("order_update_time in", values, "orderUpdateTime");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeNotIn(List<Date> values) {
            addCriterion("order_update_time not in", values, "orderUpdateTime");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("order_update_time between", value1, value2, "orderUpdateTime");
            return (Criteria) this;
        }

        public Criteria andOrderUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_update_time not between", value1, value2, "orderUpdateTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}