package com.cmiot.report.bean;


import java.util.Objects;

public class GatewayOnOfflineCount {

    private String factoryCode;

    private String provinceCode;

    private String cityCode;

    // 网关类型,1-在线网关,0-离线网关
    private Byte gtype;

    private Long count;


    public String getFactoryCode() {
        return factoryCode;
    }

    public void setFactoryCode(String factoryCode) {
        this.factoryCode = factoryCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }


    public Byte getGtype() {
        return gtype;
    }

    public void setGtype(Byte gtype) {
        this.gtype = gtype;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GatewayOnOfflineCount that = (GatewayOnOfflineCount) o;
        return Objects.equals(factoryCode, that.factoryCode) &&
                Objects.equals(provinceCode, that.provinceCode) &&
                Objects.equals(cityCode, that.cityCode) &&
                Objects.equals(gtype, that.gtype) &&
                Objects.equals(count, that.count);
    }

    @Override
    public int hashCode() {
        return Objects.hash(factoryCode, provinceCode, cityCode, gtype, count);
    }

    @Override
    public String toString() {
        return "GatewayOnOfflineCount{" +
                "factoryCode='" + factoryCode + '\'' +
                ", provinceCode='" + provinceCode + '\'' +
                ", cityCode='" + cityCode + '\'' +
                ", gtype=" + gtype +
                ", count=" + count +
                '}';
    }
}
