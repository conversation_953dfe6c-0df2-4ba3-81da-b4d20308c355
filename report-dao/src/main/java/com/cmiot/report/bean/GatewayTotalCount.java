package com.cmiot.report.bean;

import java.util.Date;

public class GatewayTotalCount {
    private String provinceCode;

    private String cityCode;

    private Long factoryId;

    private Long factoryModelId;

    private Long count;

    private Date sampleTime;

    private Long gdate;

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public Long getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Long factoryId) {
        this.factoryId = factoryId;
    }

    public Long getFactoryModelId() {
        return factoryModelId;
    }

    public void setFactoryModelId(Long factoryModelId) {
        this.factoryModelId = factoryModelId;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }

    public Long getGdate() {
        return gdate;
    }

    public void setGdate(Long gdate) {
        this.gdate = gdate;
    }
}