package com.cmiot.report.bean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GatewayPeriodAllExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GatewayPeriodAllExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andGatewaySnIsNull() {
            addCriterion("gateway_sn is null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIsNotNull() {
            addCriterion("gateway_sn is not null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnEqualTo(String value) {
            addCriterion("gateway_sn =", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotEqualTo(String value) {
            addCriterion("gateway_sn <>", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThan(String value) {
            addCriterion("gateway_sn >", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_sn >=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThan(String value) {
            addCriterion("gateway_sn <", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThanOrEqualTo(String value) {
            addCriterion("gateway_sn <=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLike(String value) {
            addCriterion("gateway_sn like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotLike(String value) {
            addCriterion("gateway_sn not like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIn(List<String> values) {
            addCriterion("gateway_sn in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotIn(List<String> values) {
            addCriterion("gateway_sn not in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnBetween(String value1, String value2) {
            addCriterion("gateway_sn between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotBetween(String value1, String value2) {
            addCriterion("gateway_sn not between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewayNameIsNull() {
            addCriterion("gateway_name is null");
            return (Criteria) this;
        }

        public Criteria andGatewayNameIsNotNull() {
            addCriterion("gateway_name is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayNameEqualTo(String value) {
            addCriterion("gateway_name =", value, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameNotEqualTo(String value) {
            addCriterion("gateway_name <>", value, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameGreaterThan(String value) {
            addCriterion("gateway_name >", value, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_name >=", value, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameLessThan(String value) {
            addCriterion("gateway_name <", value, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameLessThanOrEqualTo(String value) {
            addCriterion("gateway_name <=", value, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameLike(String value) {
            addCriterion("gateway_name like", value, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameNotLike(String value) {
            addCriterion("gateway_name not like", value, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameIn(List<String> values) {
            addCriterion("gateway_name in", values, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameNotIn(List<String> values) {
            addCriterion("gateway_name not in", values, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameBetween(String value1, String value2) {
            addCriterion("gateway_name between", value1, value2, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andGatewayNameNotBetween(String value1, String value2) {
            addCriterion("gateway_name not between", value1, value2, "gatewayName");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIsNull() {
            addCriterion("factory_id is null");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIsNotNull() {
            addCriterion("factory_id is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryIdEqualTo(Long value) {
            addCriterion("factory_id =", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotEqualTo(Long value) {
            addCriterion("factory_id <>", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdGreaterThan(Long value) {
            addCriterion("factory_id >", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("factory_id >=", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdLessThan(Long value) {
            addCriterion("factory_id <", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdLessThanOrEqualTo(Long value) {
            addCriterion("factory_id <=", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIn(List<Long> values) {
            addCriterion("factory_id in", values, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotIn(List<Long> values) {
            addCriterion("factory_id not in", values, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdBetween(Long value1, Long value2) {
            addCriterion("factory_id between", value1, value2, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotBetween(Long value1, Long value2) {
            addCriterion("factory_id not between", value1, value2, "factoryId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNull() {
            addCriterion("gateway_vendor is null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNotNull() {
            addCriterion("gateway_vendor is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorEqualTo(String value) {
            addCriterion("gateway_vendor =", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotEqualTo(String value) {
            addCriterion("gateway_vendor <>", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThan(String value) {
            addCriterion("gateway_vendor >", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_vendor >=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThan(String value) {
            addCriterion("gateway_vendor <", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThanOrEqualTo(String value) {
            addCriterion("gateway_vendor <=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLike(String value) {
            addCriterion("gateway_vendor like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotLike(String value) {
            addCriterion("gateway_vendor not like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIn(List<String> values) {
            addCriterion("gateway_vendor in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotIn(List<String> values) {
            addCriterion("gateway_vendor not in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorBetween(String value1, String value2) {
            addCriterion("gateway_vendor between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotBetween(String value1, String value2) {
            addCriterion("gateway_vendor not between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuIsNull() {
            addCriterion("gateway_cpu is null");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuIsNotNull() {
            addCriterion("gateway_cpu is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuEqualTo(String value) {
            addCriterion("gateway_cpu =", value, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuNotEqualTo(String value) {
            addCriterion("gateway_cpu <>", value, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuGreaterThan(String value) {
            addCriterion("gateway_cpu >", value, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_cpu >=", value, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuLessThan(String value) {
            addCriterion("gateway_cpu <", value, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuLessThanOrEqualTo(String value) {
            addCriterion("gateway_cpu <=", value, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuLike(String value) {
            addCriterion("gateway_cpu like", value, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuNotLike(String value) {
            addCriterion("gateway_cpu not like", value, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuIn(List<String> values) {
            addCriterion("gateway_cpu in", values, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuNotIn(List<String> values) {
            addCriterion("gateway_cpu not in", values, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuBetween(String value1, String value2) {
            addCriterion("gateway_cpu between", value1, value2, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayCpuNotBetween(String value1, String value2) {
            addCriterion("gateway_cpu not between", value1, value2, "gatewayCpu");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerIsNull() {
            addCriterion("gateway_hardware_ver is null");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerIsNotNull() {
            addCriterion("gateway_hardware_ver is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerEqualTo(String value) {
            addCriterion("gateway_hardware_ver =", value, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerNotEqualTo(String value) {
            addCriterion("gateway_hardware_ver <>", value, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerGreaterThan(String value) {
            addCriterion("gateway_hardware_ver >", value, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_hardware_ver >=", value, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerLessThan(String value) {
            addCriterion("gateway_hardware_ver <", value, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerLessThanOrEqualTo(String value) {
            addCriterion("gateway_hardware_ver <=", value, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerLike(String value) {
            addCriterion("gateway_hardware_ver like", value, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerNotLike(String value) {
            addCriterion("gateway_hardware_ver not like", value, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerIn(List<String> values) {
            addCriterion("gateway_hardware_ver in", values, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerNotIn(List<String> values) {
            addCriterion("gateway_hardware_ver not in", values, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerBetween(String value1, String value2) {
            addCriterion("gateway_hardware_ver between", value1, value2, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayHardwareVerNotBetween(String value1, String value2) {
            addCriterion("gateway_hardware_ver not between", value1, value2, "gatewayHardwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerIsNull() {
            addCriterion("gateway_firmware_ver is null");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerIsNotNull() {
            addCriterion("gateway_firmware_ver is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerEqualTo(String value) {
            addCriterion("gateway_firmware_ver =", value, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerNotEqualTo(String value) {
            addCriterion("gateway_firmware_ver <>", value, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerGreaterThan(String value) {
            addCriterion("gateway_firmware_ver >", value, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_firmware_ver >=", value, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerLessThan(String value) {
            addCriterion("gateway_firmware_ver <", value, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerLessThanOrEqualTo(String value) {
            addCriterion("gateway_firmware_ver <=", value, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerLike(String value) {
            addCriterion("gateway_firmware_ver like", value, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerNotLike(String value) {
            addCriterion("gateway_firmware_ver not like", value, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerIn(List<String> values) {
            addCriterion("gateway_firmware_ver in", values, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerNotIn(List<String> values) {
            addCriterion("gateway_firmware_ver not in", values, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerBetween(String value1, String value2) {
            addCriterion("gateway_firmware_ver between", value1, value2, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andGatewayFirmwareVerNotBetween(String value1, String value2) {
            addCriterion("gateway_firmware_ver not between", value1, value2, "gatewayFirmwareVer");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIsNull() {
            addCriterion("device_model_id is null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIsNotNull() {
            addCriterion("device_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdEqualTo(Long value) {
            addCriterion("device_model_id =", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotEqualTo(Long value) {
            addCriterion("device_model_id <>", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdGreaterThan(Long value) {
            addCriterion("device_model_id >", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("device_model_id >=", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdLessThan(Long value) {
            addCriterion("device_model_id <", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdLessThanOrEqualTo(Long value) {
            addCriterion("device_model_id <=", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIn(List<Long> values) {
            addCriterion("device_model_id in", values, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotIn(List<Long> values) {
            addCriterion("device_model_id not in", values, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdBetween(Long value1, Long value2) {
            addCriterion("device_model_id between", value1, value2, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotBetween(Long value1, Long value2) {
            addCriterion("device_model_id not between", value1, value2, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIsNull() {
            addCriterion("gateway_productclass is null");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIsNotNull() {
            addCriterion("gateway_productclass is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassEqualTo(String value) {
            addCriterion("gateway_productclass =", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotEqualTo(String value) {
            addCriterion("gateway_productclass <>", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassGreaterThan(String value) {
            addCriterion("gateway_productclass >", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_productclass >=", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLessThan(String value) {
            addCriterion("gateway_productclass <", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLessThanOrEqualTo(String value) {
            addCriterion("gateway_productclass <=", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLike(String value) {
            addCriterion("gateway_productclass like", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotLike(String value) {
            addCriterion("gateway_productclass not like", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIn(List<String> values) {
            addCriterion("gateway_productclass in", values, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotIn(List<String> values) {
            addCriterion("gateway_productclass not in", values, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassBetween(String value1, String value2) {
            addCriterion("gateway_productclass between", value1, value2, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotBetween(String value1, String value2) {
            addCriterion("gateway_productclass not between", value1, value2, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIsNull() {
            addCriterion("gateway_mac is null");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIsNotNull() {
            addCriterion("gateway_mac is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayMacEqualTo(String value) {
            addCriterion("gateway_mac =", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotEqualTo(String value) {
            addCriterion("gateway_mac <>", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacGreaterThan(String value) {
            addCriterion("gateway_mac >", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_mac >=", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLessThan(String value) {
            addCriterion("gateway_mac <", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLessThanOrEqualTo(String value) {
            addCriterion("gateway_mac <=", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLike(String value) {
            addCriterion("gateway_mac like", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotLike(String value) {
            addCriterion("gateway_mac not like", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIn(List<String> values) {
            addCriterion("gateway_mac in", values, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotIn(List<String> values) {
            addCriterion("gateway_mac not in", values, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacBetween(String value1, String value2) {
            addCriterion("gateway_mac between", value1, value2, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotBetween(String value1, String value2) {
            addCriterion("gateway_mac not between", value1, value2, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeIsNull() {
            addCriterion("gateway_flash_size is null");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeIsNotNull() {
            addCriterion("gateway_flash_size is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeEqualTo(Integer value) {
            addCriterion("gateway_flash_size =", value, "gatewayFlashSize");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeNotEqualTo(Integer value) {
            addCriterion("gateway_flash_size <>", value, "gatewayFlashSize");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeGreaterThan(Integer value) {
            addCriterion("gateway_flash_size >", value, "gatewayFlashSize");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("gateway_flash_size >=", value, "gatewayFlashSize");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeLessThan(Integer value) {
            addCriterion("gateway_flash_size <", value, "gatewayFlashSize");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeLessThanOrEqualTo(Integer value) {
            addCriterion("gateway_flash_size <=", value, "gatewayFlashSize");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeIn(List<Integer> values) {
            addCriterion("gateway_flash_size in", values, "gatewayFlashSize");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeNotIn(List<Integer> values) {
            addCriterion("gateway_flash_size not in", values, "gatewayFlashSize");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeBetween(Integer value1, Integer value2) {
            addCriterion("gateway_flash_size between", value1, value2, "gatewayFlashSize");
            return (Criteria) this;
        }

        public Criteria andGatewayFlashSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("gateway_flash_size not between", value1, value2, "gatewayFlashSize");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeIsNull() {
            addCriterion("gateway_ram_size is null");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeIsNotNull() {
            addCriterion("gateway_ram_size is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeEqualTo(Integer value) {
            addCriterion("gateway_ram_size =", value, "gatewayRamSize");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeNotEqualTo(Integer value) {
            addCriterion("gateway_ram_size <>", value, "gatewayRamSize");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeGreaterThan(Integer value) {
            addCriterion("gateway_ram_size >", value, "gatewayRamSize");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("gateway_ram_size >=", value, "gatewayRamSize");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeLessThan(Integer value) {
            addCriterion("gateway_ram_size <", value, "gatewayRamSize");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeLessThanOrEqualTo(Integer value) {
            addCriterion("gateway_ram_size <=", value, "gatewayRamSize");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeIn(List<Integer> values) {
            addCriterion("gateway_ram_size in", values, "gatewayRamSize");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeNotIn(List<Integer> values) {
            addCriterion("gateway_ram_size not in", values, "gatewayRamSize");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeBetween(Integer value1, Integer value2) {
            addCriterion("gateway_ram_size between", value1, value2, "gatewayRamSize");
            return (Criteria) this;
        }

        public Criteria andGatewayRamSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("gateway_ram_size not between", value1, value2, "gatewayRamSize");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcIsNull() {
            addCriterion("gateway_nfc is null");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcIsNotNull() {
            addCriterion("gateway_nfc is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcEqualTo(String value) {
            addCriterion("gateway_nfc =", value, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcNotEqualTo(String value) {
            addCriterion("gateway_nfc <>", value, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcGreaterThan(String value) {
            addCriterion("gateway_nfc >", value, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_nfc >=", value, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcLessThan(String value) {
            addCriterion("gateway_nfc <", value, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcLessThanOrEqualTo(String value) {
            addCriterion("gateway_nfc <=", value, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcLike(String value) {
            addCriterion("gateway_nfc like", value, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcNotLike(String value) {
            addCriterion("gateway_nfc not like", value, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcIn(List<String> values) {
            addCriterion("gateway_nfc in", values, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcNotIn(List<String> values) {
            addCriterion("gateway_nfc not in", values, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcBetween(String value1, String value2) {
            addCriterion("gateway_nfc between", value1, value2, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andGatewayNfcNotBetween(String value1, String value2) {
            addCriterion("gateway_nfc not between", value1, value2, "gatewayNfc");
            return (Criteria) this;
        }

        public Criteria andOsgiNameIsNull() {
            addCriterion("osgi_name is null");
            return (Criteria) this;
        }

        public Criteria andOsgiNameIsNotNull() {
            addCriterion("osgi_name is not null");
            return (Criteria) this;
        }

        public Criteria andOsgiNameEqualTo(String value) {
            addCriterion("osgi_name =", value, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameNotEqualTo(String value) {
            addCriterion("osgi_name <>", value, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameGreaterThan(String value) {
            addCriterion("osgi_name >", value, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameGreaterThanOrEqualTo(String value) {
            addCriterion("osgi_name >=", value, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameLessThan(String value) {
            addCriterion("osgi_name <", value, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameLessThanOrEqualTo(String value) {
            addCriterion("osgi_name <=", value, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameLike(String value) {
            addCriterion("osgi_name like", value, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameNotLike(String value) {
            addCriterion("osgi_name not like", value, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameIn(List<String> values) {
            addCriterion("osgi_name in", values, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameNotIn(List<String> values) {
            addCriterion("osgi_name not in", values, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameBetween(String value1, String value2) {
            addCriterion("osgi_name between", value1, value2, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiNameNotBetween(String value1, String value2) {
            addCriterion("osgi_name not between", value1, value2, "osgiName");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorIsNull() {
            addCriterion("osgi_vendor is null");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorIsNotNull() {
            addCriterion("osgi_vendor is not null");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorEqualTo(String value) {
            addCriterion("osgi_vendor =", value, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorNotEqualTo(String value) {
            addCriterion("osgi_vendor <>", value, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorGreaterThan(String value) {
            addCriterion("osgi_vendor >", value, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorGreaterThanOrEqualTo(String value) {
            addCriterion("osgi_vendor >=", value, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorLessThan(String value) {
            addCriterion("osgi_vendor <", value, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorLessThanOrEqualTo(String value) {
            addCriterion("osgi_vendor <=", value, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorLike(String value) {
            addCriterion("osgi_vendor like", value, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorNotLike(String value) {
            addCriterion("osgi_vendor not like", value, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorIn(List<String> values) {
            addCriterion("osgi_vendor in", values, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorNotIn(List<String> values) {
            addCriterion("osgi_vendor not in", values, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorBetween(String value1, String value2) {
            addCriterion("osgi_vendor between", value1, value2, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVendorNotBetween(String value1, String value2) {
            addCriterion("osgi_vendor not between", value1, value2, "osgiVendor");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionIsNull() {
            addCriterion("osgi_version is null");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionIsNotNull() {
            addCriterion("osgi_version is not null");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionEqualTo(String value) {
            addCriterion("osgi_version =", value, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionNotEqualTo(String value) {
            addCriterion("osgi_version <>", value, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionGreaterThan(String value) {
            addCriterion("osgi_version >", value, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionGreaterThanOrEqualTo(String value) {
            addCriterion("osgi_version >=", value, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionLessThan(String value) {
            addCriterion("osgi_version <", value, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionLessThanOrEqualTo(String value) {
            addCriterion("osgi_version <=", value, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionLike(String value) {
            addCriterion("osgi_version like", value, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionNotLike(String value) {
            addCriterion("osgi_version not like", value, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionIn(List<String> values) {
            addCriterion("osgi_version in", values, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionNotIn(List<String> values) {
            addCriterion("osgi_version not in", values, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionBetween(String value1, String value2) {
            addCriterion("osgi_version between", value1, value2, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andOsgiVersionNotBetween(String value1, String value2) {
            addCriterion("osgi_version not between", value1, value2, "osgiVersion");
            return (Criteria) this;
        }

        public Criteria andJvmNameIsNull() {
            addCriterion("jvm_name is null");
            return (Criteria) this;
        }

        public Criteria andJvmNameIsNotNull() {
            addCriterion("jvm_name is not null");
            return (Criteria) this;
        }

        public Criteria andJvmNameEqualTo(String value) {
            addCriterion("jvm_name =", value, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameNotEqualTo(String value) {
            addCriterion("jvm_name <>", value, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameGreaterThan(String value) {
            addCriterion("jvm_name >", value, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameGreaterThanOrEqualTo(String value) {
            addCriterion("jvm_name >=", value, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameLessThan(String value) {
            addCriterion("jvm_name <", value, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameLessThanOrEqualTo(String value) {
            addCriterion("jvm_name <=", value, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameLike(String value) {
            addCriterion("jvm_name like", value, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameNotLike(String value) {
            addCriterion("jvm_name not like", value, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameIn(List<String> values) {
            addCriterion("jvm_name in", values, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameNotIn(List<String> values) {
            addCriterion("jvm_name not in", values, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameBetween(String value1, String value2) {
            addCriterion("jvm_name between", value1, value2, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmNameNotBetween(String value1, String value2) {
            addCriterion("jvm_name not between", value1, value2, "jvmName");
            return (Criteria) this;
        }

        public Criteria andJvmVendorIsNull() {
            addCriterion("jvm_vendor is null");
            return (Criteria) this;
        }

        public Criteria andJvmVendorIsNotNull() {
            addCriterion("jvm_vendor is not null");
            return (Criteria) this;
        }

        public Criteria andJvmVendorEqualTo(String value) {
            addCriterion("jvm_vendor =", value, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorNotEqualTo(String value) {
            addCriterion("jvm_vendor <>", value, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorGreaterThan(String value) {
            addCriterion("jvm_vendor >", value, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorGreaterThanOrEqualTo(String value) {
            addCriterion("jvm_vendor >=", value, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorLessThan(String value) {
            addCriterion("jvm_vendor <", value, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorLessThanOrEqualTo(String value) {
            addCriterion("jvm_vendor <=", value, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorLike(String value) {
            addCriterion("jvm_vendor like", value, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorNotLike(String value) {
            addCriterion("jvm_vendor not like", value, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorIn(List<String> values) {
            addCriterion("jvm_vendor in", values, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorNotIn(List<String> values) {
            addCriterion("jvm_vendor not in", values, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorBetween(String value1, String value2) {
            addCriterion("jvm_vendor between", value1, value2, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVendorNotBetween(String value1, String value2) {
            addCriterion("jvm_vendor not between", value1, value2, "jvmVendor");
            return (Criteria) this;
        }

        public Criteria andJvmVersionIsNull() {
            addCriterion("jvm_version is null");
            return (Criteria) this;
        }

        public Criteria andJvmVersionIsNotNull() {
            addCriterion("jvm_version is not null");
            return (Criteria) this;
        }

        public Criteria andJvmVersionEqualTo(String value) {
            addCriterion("jvm_version =", value, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionNotEqualTo(String value) {
            addCriterion("jvm_version <>", value, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionGreaterThan(String value) {
            addCriterion("jvm_version >", value, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionGreaterThanOrEqualTo(String value) {
            addCriterion("jvm_version >=", value, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionLessThan(String value) {
            addCriterion("jvm_version <", value, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionLessThanOrEqualTo(String value) {
            addCriterion("jvm_version <=", value, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionLike(String value) {
            addCriterion("jvm_version like", value, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionNotLike(String value) {
            addCriterion("jvm_version not like", value, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionIn(List<String> values) {
            addCriterion("jvm_version in", values, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionNotIn(List<String> values) {
            addCriterion("jvm_version not in", values, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionBetween(String value1, String value2) {
            addCriterion("jvm_version between", value1, value2, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andJvmVersionNotBetween(String value1, String value2) {
            addCriterion("jvm_version not between", value1, value2, "jvmVersion");
            return (Criteria) this;
        }

        public Criteria andAccountIsNull() {
            addCriterion("account is null");
            return (Criteria) this;
        }

        public Criteria andAccountIsNotNull() {
            addCriterion("account is not null");
            return (Criteria) this;
        }

        public Criteria andAccountEqualTo(String value) {
            addCriterion("account =", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualTo(String value) {
            addCriterion("account <>", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThan(String value) {
            addCriterion("account >", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualTo(String value) {
            addCriterion("account >=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThan(String value) {
            addCriterion("account <", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualTo(String value) {
            addCriterion("account <=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLike(String value) {
            addCriterion("account like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotLike(String value) {
            addCriterion("account not like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountIn(List<String> values) {
            addCriterion("account in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotIn(List<String> values) {
            addCriterion("account not in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountBetween(String value1, String value2) {
            addCriterion("account between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotBetween(String value1, String value2) {
            addCriterion("account not between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andBundleVersionIsNull() {
            addCriterion("bundle_version is null");
            return (Criteria) this;
        }

        public Criteria andBundleVersionIsNotNull() {
            addCriterion("bundle_version is not null");
            return (Criteria) this;
        }

        public Criteria andBundleVersionEqualTo(String value) {
            addCriterion("bundle_version =", value, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionNotEqualTo(String value) {
            addCriterion("bundle_version <>", value, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionGreaterThan(String value) {
            addCriterion("bundle_version >", value, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionGreaterThanOrEqualTo(String value) {
            addCriterion("bundle_version >=", value, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionLessThan(String value) {
            addCriterion("bundle_version <", value, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionLessThanOrEqualTo(String value) {
            addCriterion("bundle_version <=", value, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionLike(String value) {
            addCriterion("bundle_version like", value, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionNotLike(String value) {
            addCriterion("bundle_version not like", value, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionIn(List<String> values) {
            addCriterion("bundle_version in", values, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionNotIn(List<String> values) {
            addCriterion("bundle_version not in", values, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionBetween(String value1, String value2) {
            addCriterion("bundle_version between", value1, value2, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andBundleVersionNotBetween(String value1, String value2) {
            addCriterion("bundle_version not between", value1, value2, "bundleVersion");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNull() {
            addCriterion("enterprise_id is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNotNull() {
            addCriterion("enterprise_id is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdEqualTo(Long value) {
            addCriterion("enterprise_id =", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotEqualTo(Long value) {
            addCriterion("enterprise_id <>", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThan(Long value) {
            addCriterion("enterprise_id >", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("enterprise_id >=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThan(Long value) {
            addCriterion("enterprise_id <", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThanOrEqualTo(Long value) {
            addCriterion("enterprise_id <=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIn(List<Long> values) {
            addCriterion("enterprise_id in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotIn(List<Long> values) {
            addCriterion("enterprise_id not in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdBetween(Long value1, Long value2) {
            addCriterion("enterprise_id between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotBetween(Long value1, Long value2) {
            addCriterion("enterprise_id not between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(String value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(String value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(String value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(String value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(String value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(String value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLike(String value) {
            addCriterion("customer_id like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotLike(String value) {
            addCriterion("customer_id not like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<String> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<String> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(String value1, String value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(String value1, String value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andBootTimeIsNull() {
            addCriterion("boot_time is null");
            return (Criteria) this;
        }

        public Criteria andBootTimeIsNotNull() {
            addCriterion("boot_time is not null");
            return (Criteria) this;
        }

        public Criteria andBootTimeEqualTo(Date value) {
            addCriterion("boot_time =", value, "bootTime");
            return (Criteria) this;
        }

        public Criteria andBootTimeNotEqualTo(Date value) {
            addCriterion("boot_time <>", value, "bootTime");
            return (Criteria) this;
        }

        public Criteria andBootTimeGreaterThan(Date value) {
            addCriterion("boot_time >", value, "bootTime");
            return (Criteria) this;
        }

        public Criteria andBootTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("boot_time >=", value, "bootTime");
            return (Criteria) this;
        }

        public Criteria andBootTimeLessThan(Date value) {
            addCriterion("boot_time <", value, "bootTime");
            return (Criteria) this;
        }

        public Criteria andBootTimeLessThanOrEqualTo(Date value) {
            addCriterion("boot_time <=", value, "bootTime");
            return (Criteria) this;
        }

        public Criteria andBootTimeIn(List<Date> values) {
            addCriterion("boot_time in", values, "bootTime");
            return (Criteria) this;
        }

        public Criteria andBootTimeNotIn(List<Date> values) {
            addCriterion("boot_time not in", values, "bootTime");
            return (Criteria) this;
        }

        public Criteria andBootTimeBetween(Date value1, Date value2) {
            addCriterion("boot_time between", value1, value2, "bootTime");
            return (Criteria) this;
        }

        public Criteria andBootTimeNotBetween(Date value1, Date value2) {
            addCriterion("boot_time not between", value1, value2, "bootTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeIsNull() {
            addCriterion("runing_time is null");
            return (Criteria) this;
        }

        public Criteria andRuningTimeIsNotNull() {
            addCriterion("runing_time is not null");
            return (Criteria) this;
        }

        public Criteria andRuningTimeEqualTo(Integer value) {
            addCriterion("runing_time =", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeNotEqualTo(Integer value) {
            addCriterion("runing_time <>", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeGreaterThan(Integer value) {
            addCriterion("runing_time >", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("runing_time >=", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeLessThan(Integer value) {
            addCriterion("runing_time <", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeLessThanOrEqualTo(Integer value) {
            addCriterion("runing_time <=", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeIn(List<Integer> values) {
            addCriterion("runing_time in", values, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeNotIn(List<Integer> values) {
            addCriterion("runing_time not in", values, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeBetween(Integer value1, Integer value2) {
            addCriterion("runing_time between", value1, value2, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("runing_time not between", value1, value2, "runingTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andCpuIsNull() {
            addCriterion("cpu is null");
            return (Criteria) this;
        }

        public Criteria andCpuIsNotNull() {
            addCriterion("cpu is not null");
            return (Criteria) this;
        }

        public Criteria andCpuEqualTo(Byte value) {
            addCriterion("cpu =", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuNotEqualTo(Byte value) {
            addCriterion("cpu <>", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuGreaterThan(Byte value) {
            addCriterion("cpu >", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuGreaterThanOrEqualTo(Byte value) {
            addCriterion("cpu >=", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuLessThan(Byte value) {
            addCriterion("cpu <", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuLessThanOrEqualTo(Byte value) {
            addCriterion("cpu <=", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuIn(List<Byte> values) {
            addCriterion("cpu in", values, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuNotIn(List<Byte> values) {
            addCriterion("cpu not in", values, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuBetween(Byte value1, Byte value2) {
            addCriterion("cpu between", value1, value2, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuNotBetween(Byte value1, Byte value2) {
            addCriterion("cpu not between", value1, value2, "cpu");
            return (Criteria) this;
        }

        public Criteria andRamIsNull() {
            addCriterion("ram is null");
            return (Criteria) this;
        }

        public Criteria andRamIsNotNull() {
            addCriterion("ram is not null");
            return (Criteria) this;
        }

        public Criteria andRamEqualTo(Byte value) {
            addCriterion("ram =", value, "ram");
            return (Criteria) this;
        }

        public Criteria andRamNotEqualTo(Byte value) {
            addCriterion("ram <>", value, "ram");
            return (Criteria) this;
        }

        public Criteria andRamGreaterThan(Byte value) {
            addCriterion("ram >", value, "ram");
            return (Criteria) this;
        }

        public Criteria andRamGreaterThanOrEqualTo(Byte value) {
            addCriterion("ram >=", value, "ram");
            return (Criteria) this;
        }

        public Criteria andRamLessThan(Byte value) {
            addCriterion("ram <", value, "ram");
            return (Criteria) this;
        }

        public Criteria andRamLessThanOrEqualTo(Byte value) {
            addCriterion("ram <=", value, "ram");
            return (Criteria) this;
        }

        public Criteria andRamIn(List<Byte> values) {
            addCriterion("ram in", values, "ram");
            return (Criteria) this;
        }

        public Criteria andRamNotIn(List<Byte> values) {
            addCriterion("ram not in", values, "ram");
            return (Criteria) this;
        }

        public Criteria andRamBetween(Byte value1, Byte value2) {
            addCriterion("ram between", value1, value2, "ram");
            return (Criteria) this;
        }

        public Criteria andRamNotBetween(Byte value1, Byte value2) {
            addCriterion("ram not between", value1, value2, "ram");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureIsNull() {
            addCriterion("main_chip_temperature is null");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureIsNotNull() {
            addCriterion("main_chip_temperature is not null");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureEqualTo(String value) {
            addCriterion("main_chip_temperature =", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureNotEqualTo(String value) {
            addCriterion("main_chip_temperature <>", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureGreaterThan(String value) {
            addCriterion("main_chip_temperature >", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureGreaterThanOrEqualTo(String value) {
            addCriterion("main_chip_temperature >=", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureLessThan(String value) {
            addCriterion("main_chip_temperature <", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureLessThanOrEqualTo(String value) {
            addCriterion("main_chip_temperature <=", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureLike(String value) {
            addCriterion("main_chip_temperature like", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureNotLike(String value) {
            addCriterion("main_chip_temperature not like", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureIn(List<String> values) {
            addCriterion("main_chip_temperature in", values, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureNotIn(List<String> values) {
            addCriterion("main_chip_temperature not in", values, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureBetween(String value1, String value2) {
            addCriterion("main_chip_temperature between", value1, value2, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureNotBetween(String value1, String value2) {
            addCriterion("main_chip_temperature not between", value1, value2, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andLanIpIsNull() {
            addCriterion("lan_ip is null");
            return (Criteria) this;
        }

        public Criteria andLanIpIsNotNull() {
            addCriterion("lan_ip is not null");
            return (Criteria) this;
        }

        public Criteria andLanIpEqualTo(String value) {
            addCriterion("lan_ip =", value, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpNotEqualTo(String value) {
            addCriterion("lan_ip <>", value, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpGreaterThan(String value) {
            addCriterion("lan_ip >", value, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpGreaterThanOrEqualTo(String value) {
            addCriterion("lan_ip >=", value, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpLessThan(String value) {
            addCriterion("lan_ip <", value, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpLessThanOrEqualTo(String value) {
            addCriterion("lan_ip <=", value, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpLike(String value) {
            addCriterion("lan_ip like", value, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpNotLike(String value) {
            addCriterion("lan_ip not like", value, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpIn(List<String> values) {
            addCriterion("lan_ip in", values, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpNotIn(List<String> values) {
            addCriterion("lan_ip not in", values, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpBetween(String value1, String value2) {
            addCriterion("lan_ip between", value1, value2, "lanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpNotBetween(String value1, String value2) {
            addCriterion("lan_ip not between", value1, value2, "lanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpIsNull() {
            addCriterion("wan_ip is null");
            return (Criteria) this;
        }

        public Criteria andWanIpIsNotNull() {
            addCriterion("wan_ip is not null");
            return (Criteria) this;
        }

        public Criteria andWanIpEqualTo(String value) {
            addCriterion("wan_ip =", value, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpNotEqualTo(String value) {
            addCriterion("wan_ip <>", value, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpGreaterThan(String value) {
            addCriterion("wan_ip >", value, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpGreaterThanOrEqualTo(String value) {
            addCriterion("wan_ip >=", value, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpLessThan(String value) {
            addCriterion("wan_ip <", value, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpLessThanOrEqualTo(String value) {
            addCriterion("wan_ip <=", value, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpLike(String value) {
            addCriterion("wan_ip like", value, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpNotLike(String value) {
            addCriterion("wan_ip not like", value, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpIn(List<String> values) {
            addCriterion("wan_ip in", values, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpNotIn(List<String> values) {
            addCriterion("wan_ip not in", values, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpBetween(String value1, String value2) {
            addCriterion("wan_ip between", value1, value2, "wanIp");
            return (Criteria) this;
        }

        public Criteria andWanIpNotBetween(String value1, String value2) {
            addCriterion("wan_ip not between", value1, value2, "wanIp");
            return (Criteria) this;
        }

        public Criteria andLanIpv6IsNull() {
            addCriterion("lan_ipv6 is null");
            return (Criteria) this;
        }

        public Criteria andLanIpv6IsNotNull() {
            addCriterion("lan_ipv6 is not null");
            return (Criteria) this;
        }

        public Criteria andLanIpv6EqualTo(String value) {
            addCriterion("lan_ipv6 =", value, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6NotEqualTo(String value) {
            addCriterion("lan_ipv6 <>", value, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6GreaterThan(String value) {
            addCriterion("lan_ipv6 >", value, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6GreaterThanOrEqualTo(String value) {
            addCriterion("lan_ipv6 >=", value, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6LessThan(String value) {
            addCriterion("lan_ipv6 <", value, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6LessThanOrEqualTo(String value) {
            addCriterion("lan_ipv6 <=", value, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6Like(String value) {
            addCriterion("lan_ipv6 like", value, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6NotLike(String value) {
            addCriterion("lan_ipv6 not like", value, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6In(List<String> values) {
            addCriterion("lan_ipv6 in", values, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6NotIn(List<String> values) {
            addCriterion("lan_ipv6 not in", values, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6Between(String value1, String value2) {
            addCriterion("lan_ipv6 between", value1, value2, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andLanIpv6NotBetween(String value1, String value2) {
            addCriterion("lan_ipv6 not between", value1, value2, "lanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6IsNull() {
            addCriterion("wan_ipv6 is null");
            return (Criteria) this;
        }

        public Criteria andWanIpv6IsNotNull() {
            addCriterion("wan_ipv6 is not null");
            return (Criteria) this;
        }

        public Criteria andWanIpv6EqualTo(String value) {
            addCriterion("wan_ipv6 =", value, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6NotEqualTo(String value) {
            addCriterion("wan_ipv6 <>", value, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6GreaterThan(String value) {
            addCriterion("wan_ipv6 >", value, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6GreaterThanOrEqualTo(String value) {
            addCriterion("wan_ipv6 >=", value, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6LessThan(String value) {
            addCriterion("wan_ipv6 <", value, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6LessThanOrEqualTo(String value) {
            addCriterion("wan_ipv6 <=", value, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6Like(String value) {
            addCriterion("wan_ipv6 like", value, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6NotLike(String value) {
            addCriterion("wan_ipv6 not like", value, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6In(List<String> values) {
            addCriterion("wan_ipv6 in", values, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6NotIn(List<String> values) {
            addCriterion("wan_ipv6 not in", values, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6Between(String value1, String value2) {
            addCriterion("wan_ipv6 between", value1, value2, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andWanIpv6NotBetween(String value1, String value2) {
            addCriterion("wan_ipv6 not between", value1, value2, "wanIpv6");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusIsNull() {
            addCriterion("lan1ConnectStatus is null");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusIsNotNull() {
            addCriterion("lan1ConnectStatus is not null");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusEqualTo(String value) {
            addCriterion("lan1ConnectStatus =", value, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusNotEqualTo(String value) {
            addCriterion("lan1ConnectStatus <>", value, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusGreaterThan(String value) {
            addCriterion("lan1ConnectStatus >", value, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusGreaterThanOrEqualTo(String value) {
            addCriterion("lan1ConnectStatus >=", value, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusLessThan(String value) {
            addCriterion("lan1ConnectStatus <", value, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusLessThanOrEqualTo(String value) {
            addCriterion("lan1ConnectStatus <=", value, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusLike(String value) {
            addCriterion("lan1ConnectStatus like", value, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusNotLike(String value) {
            addCriterion("lan1ConnectStatus not like", value, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusIn(List<String> values) {
            addCriterion("lan1ConnectStatus in", values, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusNotIn(List<String> values) {
            addCriterion("lan1ConnectStatus not in", values, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusBetween(String value1, String value2) {
            addCriterion("lan1ConnectStatus between", value1, value2, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan1connectstatusNotBetween(String value1, String value2) {
            addCriterion("lan1ConnectStatus not between", value1, value2, "lan1connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusIsNull() {
            addCriterion("lan2ConnectStatus is null");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusIsNotNull() {
            addCriterion("lan2ConnectStatus is not null");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusEqualTo(String value) {
            addCriterion("lan2ConnectStatus =", value, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusNotEqualTo(String value) {
            addCriterion("lan2ConnectStatus <>", value, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusGreaterThan(String value) {
            addCriterion("lan2ConnectStatus >", value, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusGreaterThanOrEqualTo(String value) {
            addCriterion("lan2ConnectStatus >=", value, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusLessThan(String value) {
            addCriterion("lan2ConnectStatus <", value, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusLessThanOrEqualTo(String value) {
            addCriterion("lan2ConnectStatus <=", value, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusLike(String value) {
            addCriterion("lan2ConnectStatus like", value, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusNotLike(String value) {
            addCriterion("lan2ConnectStatus not like", value, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusIn(List<String> values) {
            addCriterion("lan2ConnectStatus in", values, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusNotIn(List<String> values) {
            addCriterion("lan2ConnectStatus not in", values, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusBetween(String value1, String value2) {
            addCriterion("lan2ConnectStatus between", value1, value2, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan2connectstatusNotBetween(String value1, String value2) {
            addCriterion("lan2ConnectStatus not between", value1, value2, "lan2connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusIsNull() {
            addCriterion("lan3ConnectStatus is null");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusIsNotNull() {
            addCriterion("lan3ConnectStatus is not null");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusEqualTo(String value) {
            addCriterion("lan3ConnectStatus =", value, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusNotEqualTo(String value) {
            addCriterion("lan3ConnectStatus <>", value, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusGreaterThan(String value) {
            addCriterion("lan3ConnectStatus >", value, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusGreaterThanOrEqualTo(String value) {
            addCriterion("lan3ConnectStatus >=", value, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusLessThan(String value) {
            addCriterion("lan3ConnectStatus <", value, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusLessThanOrEqualTo(String value) {
            addCriterion("lan3ConnectStatus <=", value, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusLike(String value) {
            addCriterion("lan3ConnectStatus like", value, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusNotLike(String value) {
            addCriterion("lan3ConnectStatus not like", value, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusIn(List<String> values) {
            addCriterion("lan3ConnectStatus in", values, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusNotIn(List<String> values) {
            addCriterion("lan3ConnectStatus not in", values, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusBetween(String value1, String value2) {
            addCriterion("lan3ConnectStatus between", value1, value2, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan3connectstatusNotBetween(String value1, String value2) {
            addCriterion("lan3ConnectStatus not between", value1, value2, "lan3connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusIsNull() {
            addCriterion("lan4ConnectStatus is null");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusIsNotNull() {
            addCriterion("lan4ConnectStatus is not null");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusEqualTo(String value) {
            addCriterion("lan4ConnectStatus =", value, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusNotEqualTo(String value) {
            addCriterion("lan4ConnectStatus <>", value, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusGreaterThan(String value) {
            addCriterion("lan4ConnectStatus >", value, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusGreaterThanOrEqualTo(String value) {
            addCriterion("lan4ConnectStatus >=", value, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusLessThan(String value) {
            addCriterion("lan4ConnectStatus <", value, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusLessThanOrEqualTo(String value) {
            addCriterion("lan4ConnectStatus <=", value, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusLike(String value) {
            addCriterion("lan4ConnectStatus like", value, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusNotLike(String value) {
            addCriterion("lan4ConnectStatus not like", value, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusIn(List<String> values) {
            addCriterion("lan4ConnectStatus in", values, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusNotIn(List<String> values) {
            addCriterion("lan4ConnectStatus not in", values, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusBetween(String value1, String value2) {
            addCriterion("lan4ConnectStatus between", value1, value2, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan4connectstatusNotBetween(String value1, String value2) {
            addCriterion("lan4ConnectStatus not between", value1, value2, "lan4connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusIsNull() {
            addCriterion("lan5ConnectStatus is null");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusIsNotNull() {
            addCriterion("lan5ConnectStatus is not null");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusEqualTo(String value) {
            addCriterion("lan5ConnectStatus =", value, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusNotEqualTo(String value) {
            addCriterion("lan5ConnectStatus <>", value, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusGreaterThan(String value) {
            addCriterion("lan5ConnectStatus >", value, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusGreaterThanOrEqualTo(String value) {
            addCriterion("lan5ConnectStatus >=", value, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusLessThan(String value) {
            addCriterion("lan5ConnectStatus <", value, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusLessThanOrEqualTo(String value) {
            addCriterion("lan5ConnectStatus <=", value, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusLike(String value) {
            addCriterion("lan5ConnectStatus like", value, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusNotLike(String value) {
            addCriterion("lan5ConnectStatus not like", value, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusIn(List<String> values) {
            addCriterion("lan5ConnectStatus in", values, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusNotIn(List<String> values) {
            addCriterion("lan5ConnectStatus not in", values, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusBetween(String value1, String value2) {
            addCriterion("lan5ConnectStatus between", value1, value2, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan5connectstatusNotBetween(String value1, String value2) {
            addCriterion("lan5ConnectStatus not between", value1, value2, "lan5connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusIsNull() {
            addCriterion("lan6ConnectStatus is null");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusIsNotNull() {
            addCriterion("lan6ConnectStatus is not null");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusEqualTo(String value) {
            addCriterion("lan6ConnectStatus =", value, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusNotEqualTo(String value) {
            addCriterion("lan6ConnectStatus <>", value, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusGreaterThan(String value) {
            addCriterion("lan6ConnectStatus >", value, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusGreaterThanOrEqualTo(String value) {
            addCriterion("lan6ConnectStatus >=", value, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusLessThan(String value) {
            addCriterion("lan6ConnectStatus <", value, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusLessThanOrEqualTo(String value) {
            addCriterion("lan6ConnectStatus <=", value, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusLike(String value) {
            addCriterion("lan6ConnectStatus like", value, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusNotLike(String value) {
            addCriterion("lan6ConnectStatus not like", value, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusIn(List<String> values) {
            addCriterion("lan6ConnectStatus in", values, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusNotIn(List<String> values) {
            addCriterion("lan6ConnectStatus not in", values, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusBetween(String value1, String value2) {
            addCriterion("lan6ConnectStatus between", value1, value2, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan6connectstatusNotBetween(String value1, String value2) {
            addCriterion("lan6ConnectStatus not between", value1, value2, "lan6connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusIsNull() {
            addCriterion("lan7ConnectStatus is null");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusIsNotNull() {
            addCriterion("lan7ConnectStatus is not null");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusEqualTo(String value) {
            addCriterion("lan7ConnectStatus =", value, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusNotEqualTo(String value) {
            addCriterion("lan7ConnectStatus <>", value, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusGreaterThan(String value) {
            addCriterion("lan7ConnectStatus >", value, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusGreaterThanOrEqualTo(String value) {
            addCriterion("lan7ConnectStatus >=", value, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusLessThan(String value) {
            addCriterion("lan7ConnectStatus <", value, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusLessThanOrEqualTo(String value) {
            addCriterion("lan7ConnectStatus <=", value, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusLike(String value) {
            addCriterion("lan7ConnectStatus like", value, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusNotLike(String value) {
            addCriterion("lan7ConnectStatus not like", value, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusIn(List<String> values) {
            addCriterion("lan7ConnectStatus in", values, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusNotIn(List<String> values) {
            addCriterion("lan7ConnectStatus not in", values, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusBetween(String value1, String value2) {
            addCriterion("lan7ConnectStatus between", value1, value2, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan7connectstatusNotBetween(String value1, String value2) {
            addCriterion("lan7ConnectStatus not between", value1, value2, "lan7connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusIsNull() {
            addCriterion("lan8ConnectStatus is null");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusIsNotNull() {
            addCriterion("lan8ConnectStatus is not null");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusEqualTo(String value) {
            addCriterion("lan8ConnectStatus =", value, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusNotEqualTo(String value) {
            addCriterion("lan8ConnectStatus <>", value, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusGreaterThan(String value) {
            addCriterion("lan8ConnectStatus >", value, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusGreaterThanOrEqualTo(String value) {
            addCriterion("lan8ConnectStatus >=", value, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusLessThan(String value) {
            addCriterion("lan8ConnectStatus <", value, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusLessThanOrEqualTo(String value) {
            addCriterion("lan8ConnectStatus <=", value, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusLike(String value) {
            addCriterion("lan8ConnectStatus like", value, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusNotLike(String value) {
            addCriterion("lan8ConnectStatus not like", value, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusIn(List<String> values) {
            addCriterion("lan8ConnectStatus in", values, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusNotIn(List<String> values) {
            addCriterion("lan8ConnectStatus not in", values, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusBetween(String value1, String value2) {
            addCriterion("lan8ConnectStatus between", value1, value2, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andLan8connectstatusNotBetween(String value1, String value2) {
            addCriterion("lan8ConnectStatus not between", value1, value2, "lan8connectstatus");
            return (Criteria) this;
        }

        public Criteria andWanIsNull() {
            addCriterion("wan is null");
            return (Criteria) this;
        }

        public Criteria andWanIsNotNull() {
            addCriterion("wan is not null");
            return (Criteria) this;
        }

        public Criteria andWanEqualTo(String value) {
            addCriterion("wan =", value, "wan");
            return (Criteria) this;
        }

        public Criteria andWanNotEqualTo(String value) {
            addCriterion("wan <>", value, "wan");
            return (Criteria) this;
        }

        public Criteria andWanGreaterThan(String value) {
            addCriterion("wan >", value, "wan");
            return (Criteria) this;
        }

        public Criteria andWanGreaterThanOrEqualTo(String value) {
            addCriterion("wan >=", value, "wan");
            return (Criteria) this;
        }

        public Criteria andWanLessThan(String value) {
            addCriterion("wan <", value, "wan");
            return (Criteria) this;
        }

        public Criteria andWanLessThanOrEqualTo(String value) {
            addCriterion("wan <=", value, "wan");
            return (Criteria) this;
        }

        public Criteria andWanLike(String value) {
            addCriterion("wan like", value, "wan");
            return (Criteria) this;
        }

        public Criteria andWanNotLike(String value) {
            addCriterion("wan not like", value, "wan");
            return (Criteria) this;
        }

        public Criteria andWanIn(List<String> values) {
            addCriterion("wan in", values, "wan");
            return (Criteria) this;
        }

        public Criteria andWanNotIn(List<String> values) {
            addCriterion("wan not in", values, "wan");
            return (Criteria) this;
        }

        public Criteria andWanBetween(String value1, String value2) {
            addCriterion("wan between", value1, value2, "wan");
            return (Criteria) this;
        }

        public Criteria andWanNotBetween(String value1, String value2) {
            addCriterion("wan not between", value1, value2, "wan");
            return (Criteria) this;
        }

        public Criteria andWifiIsNull() {
            addCriterion("wifi is null");
            return (Criteria) this;
        }

        public Criteria andWifiIsNotNull() {
            addCriterion("wifi is not null");
            return (Criteria) this;
        }

        public Criteria andWifiEqualTo(String value) {
            addCriterion("wifi =", value, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiNotEqualTo(String value) {
            addCriterion("wifi <>", value, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiGreaterThan(String value) {
            addCriterion("wifi >", value, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiGreaterThanOrEqualTo(String value) {
            addCriterion("wifi >=", value, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiLessThan(String value) {
            addCriterion("wifi <", value, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiLessThanOrEqualTo(String value) {
            addCriterion("wifi <=", value, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiLike(String value) {
            addCriterion("wifi like", value, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiNotLike(String value) {
            addCriterion("wifi not like", value, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiIn(List<String> values) {
            addCriterion("wifi in", values, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiNotIn(List<String> values) {
            addCriterion("wifi not in", values, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiBetween(String value1, String value2) {
            addCriterion("wifi between", value1, value2, "wifi");
            return (Criteria) this;
        }

        public Criteria andWifiNotBetween(String value1, String value2) {
            addCriterion("wifi not between", value1, value2, "wifi");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeIsNull() {
            addCriterion("pppoe_up_time is null");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeIsNotNull() {
            addCriterion("pppoe_up_time is not null");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeEqualTo(String value) {
            addCriterion("pppoe_up_time =", value, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeNotEqualTo(String value) {
            addCriterion("pppoe_up_time <>", value, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeGreaterThan(String value) {
            addCriterion("pppoe_up_time >", value, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeGreaterThanOrEqualTo(String value) {
            addCriterion("pppoe_up_time >=", value, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeLessThan(String value) {
            addCriterion("pppoe_up_time <", value, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeLessThanOrEqualTo(String value) {
            addCriterion("pppoe_up_time <=", value, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeLike(String value) {
            addCriterion("pppoe_up_time like", value, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeNotLike(String value) {
            addCriterion("pppoe_up_time not like", value, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeIn(List<String> values) {
            addCriterion("pppoe_up_time in", values, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeNotIn(List<String> values) {
            addCriterion("pppoe_up_time not in", values, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeBetween(String value1, String value2) {
            addCriterion("pppoe_up_time between", value1, value2, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeUpTimeNotBetween(String value1, String value2) {
            addCriterion("pppoe_up_time not between", value1, value2, "pppoeUpTime");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorIsNull() {
            addCriterion("pppoe_error is null");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorIsNotNull() {
            addCriterion("pppoe_error is not null");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorEqualTo(String value) {
            addCriterion("pppoe_error =", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorNotEqualTo(String value) {
            addCriterion("pppoe_error <>", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorGreaterThan(String value) {
            addCriterion("pppoe_error >", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorGreaterThanOrEqualTo(String value) {
            addCriterion("pppoe_error >=", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorLessThan(String value) {
            addCriterion("pppoe_error <", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorLessThanOrEqualTo(String value) {
            addCriterion("pppoe_error <=", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorLike(String value) {
            addCriterion("pppoe_error like", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorNotLike(String value) {
            addCriterion("pppoe_error not like", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorIn(List<String> values) {
            addCriterion("pppoe_error in", values, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorNotIn(List<String> values) {
            addCriterion("pppoe_error not in", values, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorBetween(String value1, String value2) {
            addCriterion("pppoe_error between", value1, value2, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorNotBetween(String value1, String value2) {
            addCriterion("pppoe_error not between", value1, value2, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusIsNull() {
            addCriterion("pppoe_status is null");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusIsNotNull() {
            addCriterion("pppoe_status is not null");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusEqualTo(String value) {
            addCriterion("pppoe_status =", value, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusNotEqualTo(String value) {
            addCriterion("pppoe_status <>", value, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusGreaterThan(String value) {
            addCriterion("pppoe_status >", value, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusGreaterThanOrEqualTo(String value) {
            addCriterion("pppoe_status >=", value, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusLessThan(String value) {
            addCriterion("pppoe_status <", value, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusLessThanOrEqualTo(String value) {
            addCriterion("pppoe_status <=", value, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusLike(String value) {
            addCriterion("pppoe_status like", value, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusNotLike(String value) {
            addCriterion("pppoe_status not like", value, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusIn(List<String> values) {
            addCriterion("pppoe_status in", values, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusNotIn(List<String> values) {
            addCriterion("pppoe_status not in", values, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusBetween(String value1, String value2) {
            addCriterion("pppoe_status between", value1, value2, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeStatusNotBetween(String value1, String value2) {
            addCriterion("pppoe_status not between", value1, value2, "pppoeStatus");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerIsNull() {
            addCriterion("pon_tx_power is null");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerIsNotNull() {
            addCriterion("pon_tx_power is not null");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerEqualTo(Integer value) {
            addCriterion("pon_tx_power =", value, "ponTxPower");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerNotEqualTo(Integer value) {
            addCriterion("pon_tx_power <>", value, "ponTxPower");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerGreaterThan(Integer value) {
            addCriterion("pon_tx_power >", value, "ponTxPower");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerGreaterThanOrEqualTo(Integer value) {
            addCriterion("pon_tx_power >=", value, "ponTxPower");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerLessThan(Integer value) {
            addCriterion("pon_tx_power <", value, "ponTxPower");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerLessThanOrEqualTo(Integer value) {
            addCriterion("pon_tx_power <=", value, "ponTxPower");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerIn(List<Integer> values) {
            addCriterion("pon_tx_power in", values, "ponTxPower");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerNotIn(List<Integer> values) {
            addCriterion("pon_tx_power not in", values, "ponTxPower");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerBetween(Integer value1, Integer value2) {
            addCriterion("pon_tx_power between", value1, value2, "ponTxPower");
            return (Criteria) this;
        }

        public Criteria andPonTxPowerNotBetween(Integer value1, Integer value2) {
            addCriterion("pon_tx_power not between", value1, value2, "ponTxPower");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerIsNull() {
            addCriterion("pon_rx_power is null");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerIsNotNull() {
            addCriterion("pon_rx_power is not null");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerEqualTo(Integer value) {
            addCriterion("pon_rx_power =", value, "ponRxPower");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerNotEqualTo(Integer value) {
            addCriterion("pon_rx_power <>", value, "ponRxPower");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerGreaterThan(Integer value) {
            addCriterion("pon_rx_power >", value, "ponRxPower");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerGreaterThanOrEqualTo(Integer value) {
            addCriterion("pon_rx_power >=", value, "ponRxPower");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerLessThan(Integer value) {
            addCriterion("pon_rx_power <", value, "ponRxPower");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerLessThanOrEqualTo(Integer value) {
            addCriterion("pon_rx_power <=", value, "ponRxPower");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerIn(List<Integer> values) {
            addCriterion("pon_rx_power in", values, "ponRxPower");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerNotIn(List<Integer> values) {
            addCriterion("pon_rx_power not in", values, "ponRxPower");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerBetween(Integer value1, Integer value2) {
            addCriterion("pon_rx_power between", value1, value2, "ponRxPower");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerNotBetween(Integer value1, Integer value2) {
            addCriterion("pon_rx_power not between", value1, value2, "ponRxPower");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureIsNull() {
            addCriterion("transceiver_temperature is null");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureIsNotNull() {
            addCriterion("transceiver_temperature is not null");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureEqualTo(Integer value) {
            addCriterion("transceiver_temperature =", value, "transceiverTemperature");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureNotEqualTo(Integer value) {
            addCriterion("transceiver_temperature <>", value, "transceiverTemperature");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureGreaterThan(Integer value) {
            addCriterion("transceiver_temperature >", value, "transceiverTemperature");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureGreaterThanOrEqualTo(Integer value) {
            addCriterion("transceiver_temperature >=", value, "transceiverTemperature");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureLessThan(Integer value) {
            addCriterion("transceiver_temperature <", value, "transceiverTemperature");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureLessThanOrEqualTo(Integer value) {
            addCriterion("transceiver_temperature <=", value, "transceiverTemperature");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureIn(List<Integer> values) {
            addCriterion("transceiver_temperature in", values, "transceiverTemperature");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureNotIn(List<Integer> values) {
            addCriterion("transceiver_temperature not in", values, "transceiverTemperature");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureBetween(Integer value1, Integer value2) {
            addCriterion("transceiver_temperature between", value1, value2, "transceiverTemperature");
            return (Criteria) this;
        }

        public Criteria andTransceiverTemperatureNotBetween(Integer value1, Integer value2) {
            addCriterion("transceiver_temperature not between", value1, value2, "transceiverTemperature");
            return (Criteria) this;
        }

        public Criteria andWanIndexIsNull() {
            addCriterion("wan_index is null");
            return (Criteria) this;
        }

        public Criteria andWanIndexIsNotNull() {
            addCriterion("wan_index is not null");
            return (Criteria) this;
        }

        public Criteria andWanIndexEqualTo(String value) {
            addCriterion("wan_index =", value, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexNotEqualTo(String value) {
            addCriterion("wan_index <>", value, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexGreaterThan(String value) {
            addCriterion("wan_index >", value, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexGreaterThanOrEqualTo(String value) {
            addCriterion("wan_index >=", value, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexLessThan(String value) {
            addCriterion("wan_index <", value, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexLessThanOrEqualTo(String value) {
            addCriterion("wan_index <=", value, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexLike(String value) {
            addCriterion("wan_index like", value, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexNotLike(String value) {
            addCriterion("wan_index not like", value, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexIn(List<String> values) {
            addCriterion("wan_index in", values, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexNotIn(List<String> values) {
            addCriterion("wan_index not in", values, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexBetween(String value1, String value2) {
            addCriterion("wan_index between", value1, value2, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanIndexNotBetween(String value1, String value2) {
            addCriterion("wan_index not between", value1, value2, "wanIndex");
            return (Criteria) this;
        }

        public Criteria andWanNameIsNull() {
            addCriterion("wan_name is null");
            return (Criteria) this;
        }

        public Criteria andWanNameIsNotNull() {
            addCriterion("wan_name is not null");
            return (Criteria) this;
        }

        public Criteria andWanNameEqualTo(String value) {
            addCriterion("wan_name =", value, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameNotEqualTo(String value) {
            addCriterion("wan_name <>", value, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameGreaterThan(String value) {
            addCriterion("wan_name >", value, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameGreaterThanOrEqualTo(String value) {
            addCriterion("wan_name >=", value, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameLessThan(String value) {
            addCriterion("wan_name <", value, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameLessThanOrEqualTo(String value) {
            addCriterion("wan_name <=", value, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameLike(String value) {
            addCriterion("wan_name like", value, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameNotLike(String value) {
            addCriterion("wan_name not like", value, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameIn(List<String> values) {
            addCriterion("wan_name in", values, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameNotIn(List<String> values) {
            addCriterion("wan_name not in", values, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameBetween(String value1, String value2) {
            addCriterion("wan_name between", value1, value2, "wanName");
            return (Criteria) this;
        }

        public Criteria andWanNameNotBetween(String value1, String value2) {
            addCriterion("wan_name not between", value1, value2, "wanName");
            return (Criteria) this;
        }

        public Criteria andAverTxrateIsNull() {
            addCriterion("aver_txrate is null");
            return (Criteria) this;
        }

        public Criteria andAverTxrateIsNotNull() {
            addCriterion("aver_txrate is not null");
            return (Criteria) this;
        }

        public Criteria andAverTxrateEqualTo(BigDecimal value) {
            addCriterion("aver_txrate =", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateNotEqualTo(BigDecimal value) {
            addCriterion("aver_txrate <>", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateGreaterThan(BigDecimal value) {
            addCriterion("aver_txrate >", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("aver_txrate >=", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateLessThan(BigDecimal value) {
            addCriterion("aver_txrate <", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("aver_txrate <=", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateIn(List<BigDecimal> values) {
            addCriterion("aver_txrate in", values, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateNotIn(List<BigDecimal> values) {
            addCriterion("aver_txrate not in", values, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("aver_txrate between", value1, value2, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("aver_txrate not between", value1, value2, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateIsNull() {
            addCriterion("aver_rxrate is null");
            return (Criteria) this;
        }

        public Criteria andAverRxrateIsNotNull() {
            addCriterion("aver_rxrate is not null");
            return (Criteria) this;
        }

        public Criteria andAverRxrateEqualTo(BigDecimal value) {
            addCriterion("aver_rxrate =", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateNotEqualTo(BigDecimal value) {
            addCriterion("aver_rxrate <>", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateGreaterThan(BigDecimal value) {
            addCriterion("aver_rxrate >", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("aver_rxrate >=", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateLessThan(BigDecimal value) {
            addCriterion("aver_rxrate <", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("aver_rxrate <=", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateIn(List<BigDecimal> values) {
            addCriterion("aver_rxrate in", values, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateNotIn(List<BigDecimal> values) {
            addCriterion("aver_rxrate not in", values, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("aver_rxrate between", value1, value2, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("aver_rxrate not between", value1, value2, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateIsNull() {
            addCriterion("max_txrate is null");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateIsNotNull() {
            addCriterion("max_txrate is not null");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateEqualTo(BigDecimal value) {
            addCriterion("max_txrate =", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateNotEqualTo(BigDecimal value) {
            addCriterion("max_txrate <>", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateGreaterThan(BigDecimal value) {
            addCriterion("max_txrate >", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("max_txrate >=", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateLessThan(BigDecimal value) {
            addCriterion("max_txrate <", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("max_txrate <=", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateIn(List<BigDecimal> values) {
            addCriterion("max_txrate in", values, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateNotIn(List<BigDecimal> values) {
            addCriterion("max_txrate not in", values, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_txrate between", value1, value2, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_txrate not between", value1, value2, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateIsNull() {
            addCriterion("max_rxrate is null");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateIsNotNull() {
            addCriterion("max_rxrate is not null");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateEqualTo(BigDecimal value) {
            addCriterion("max_rxrate =", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateNotEqualTo(BigDecimal value) {
            addCriterion("max_rxrate <>", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateGreaterThan(BigDecimal value) {
            addCriterion("max_rxrate >", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("max_rxrate >=", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateLessThan(BigDecimal value) {
            addCriterion("max_rxrate <", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("max_rxrate <=", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateIn(List<BigDecimal> values) {
            addCriterion("max_rxrate in", values, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateNotIn(List<BigDecimal> values) {
            addCriterion("max_rxrate not in", values, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_rxrate between", value1, value2, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_rxrate not between", value1, value2, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsIsNull() {
            addCriterion("up_staticstics is null");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsIsNotNull() {
            addCriterion("up_staticstics is not null");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsEqualTo(BigDecimal value) {
            addCriterion("up_staticstics =", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsNotEqualTo(BigDecimal value) {
            addCriterion("up_staticstics <>", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsGreaterThan(BigDecimal value) {
            addCriterion("up_staticstics >", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("up_staticstics >=", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsLessThan(BigDecimal value) {
            addCriterion("up_staticstics <", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsLessThanOrEqualTo(BigDecimal value) {
            addCriterion("up_staticstics <=", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsIn(List<BigDecimal> values) {
            addCriterion("up_staticstics in", values, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsNotIn(List<BigDecimal> values) {
            addCriterion("up_staticstics not in", values, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_staticstics between", value1, value2, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_staticstics not between", value1, value2, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsIsNull() {
            addCriterion("down_staticstics is null");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsIsNotNull() {
            addCriterion("down_staticstics is not null");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsEqualTo(BigDecimal value) {
            addCriterion("down_staticstics =", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsNotEqualTo(BigDecimal value) {
            addCriterion("down_staticstics <>", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsGreaterThan(BigDecimal value) {
            addCriterion("down_staticstics >", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("down_staticstics >=", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsLessThan(BigDecimal value) {
            addCriterion("down_staticstics <", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsLessThanOrEqualTo(BigDecimal value) {
            addCriterion("down_staticstics <=", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsIn(List<BigDecimal> values) {
            addCriterion("down_staticstics in", values, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsNotIn(List<BigDecimal> values) {
            addCriterion("down_staticstics not in", values, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("down_staticstics between", value1, value2, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("down_staticstics not between", value1, value2, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andSupportIsNull() {
            addCriterion("support is null");
            return (Criteria) this;
        }

        public Criteria andSupportIsNotNull() {
            addCriterion("support is not null");
            return (Criteria) this;
        }

        public Criteria andSupportEqualTo(String value) {
            addCriterion("support =", value, "support");
            return (Criteria) this;
        }

        public Criteria andSupportNotEqualTo(String value) {
            addCriterion("support <>", value, "support");
            return (Criteria) this;
        }

        public Criteria andSupportGreaterThan(String value) {
            addCriterion("support >", value, "support");
            return (Criteria) this;
        }

        public Criteria andSupportGreaterThanOrEqualTo(String value) {
            addCriterion("support >=", value, "support");
            return (Criteria) this;
        }

        public Criteria andSupportLessThan(String value) {
            addCriterion("support <", value, "support");
            return (Criteria) this;
        }

        public Criteria andSupportLessThanOrEqualTo(String value) {
            addCriterion("support <=", value, "support");
            return (Criteria) this;
        }

        public Criteria andSupportLike(String value) {
            addCriterion("support like", value, "support");
            return (Criteria) this;
        }

        public Criteria andSupportNotLike(String value) {
            addCriterion("support not like", value, "support");
            return (Criteria) this;
        }

        public Criteria andSupportIn(List<String> values) {
            addCriterion("support in", values, "support");
            return (Criteria) this;
        }

        public Criteria andSupportNotIn(List<String> values) {
            addCriterion("support not in", values, "support");
            return (Criteria) this;
        }

        public Criteria andSupportBetween(String value1, String value2) {
            addCriterion("support between", value1, value2, "support");
            return (Criteria) this;
        }

        public Criteria andSupportNotBetween(String value1, String value2) {
            addCriterion("support not between", value1, value2, "support");
            return (Criteria) this;
        }

        public Criteria andPotsIndexIsNull() {
            addCriterion("pots_index is null");
            return (Criteria) this;
        }

        public Criteria andPotsIndexIsNotNull() {
            addCriterion("pots_index is not null");
            return (Criteria) this;
        }

        public Criteria andPotsIndexEqualTo(String value) {
            addCriterion("pots_index =", value, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexNotEqualTo(String value) {
            addCriterion("pots_index <>", value, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexGreaterThan(String value) {
            addCriterion("pots_index >", value, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexGreaterThanOrEqualTo(String value) {
            addCriterion("pots_index >=", value, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexLessThan(String value) {
            addCriterion("pots_index <", value, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexLessThanOrEqualTo(String value) {
            addCriterion("pots_index <=", value, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexLike(String value) {
            addCriterion("pots_index like", value, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexNotLike(String value) {
            addCriterion("pots_index not like", value, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexIn(List<String> values) {
            addCriterion("pots_index in", values, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexNotIn(List<String> values) {
            addCriterion("pots_index not in", values, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexBetween(String value1, String value2) {
            addCriterion("pots_index between", value1, value2, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPotsIndexNotBetween(String value1, String value2) {
            addCriterion("pots_index not between", value1, value2, "potsIndex");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNull() {
            addCriterion("phone_number is null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNotNull() {
            addCriterion("phone_number is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberEqualTo(String value) {
            addCriterion("phone_number =", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotEqualTo(String value) {
            addCriterion("phone_number <>", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThan(String value) {
            addCriterion("phone_number >", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThanOrEqualTo(String value) {
            addCriterion("phone_number >=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThan(String value) {
            addCriterion("phone_number <", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThanOrEqualTo(String value) {
            addCriterion("phone_number <=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLike(String value) {
            addCriterion("phone_number like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotLike(String value) {
            addCriterion("phone_number not like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIn(List<String> values) {
            addCriterion("phone_number in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotIn(List<String> values) {
            addCriterion("phone_number not in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberBetween(String value1, String value2) {
            addCriterion("phone_number between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotBetween(String value1, String value2) {
            addCriterion("phone_number not between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}