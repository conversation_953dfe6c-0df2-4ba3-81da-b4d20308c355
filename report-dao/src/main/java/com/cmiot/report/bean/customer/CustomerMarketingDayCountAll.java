package com.cmiot.report.bean.customer;

public class CustomerMarketingDayCountAll {
    private String provinceCode;

    private String cityCode;

    private String areaName;

    private Long totalNum;

    private Long lastDayIncr;

    private Long lastDayDecr;

    private Long lastWeekIncr;

    private Long lastWeekDecr;

    private Long lastMonthIncr;

    private Long lastMonthDecr;

    private Integer gdate;

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }

    public Long getLastDayIncr() {
        return lastDayIncr;
    }

    public void setLastDayIncr(Long lastDayIncr) {
        this.lastDayIncr = lastDayIncr;
    }

    public Long getLastDayDecr() {
        return lastDayDecr;
    }

    public void setLastDayDecr(Long lastDayDecr) {
        this.lastDayDecr = lastDayDecr;
    }

    public Long getLastWeekIncr() {
        return lastWeekIncr;
    }

    public void setLastWeekIncr(Long lastWeekIncr) {
        this.lastWeekIncr = lastWeekIncr;
    }

    public Long getLastWeekDecr() {
        return lastWeekDecr;
    }

    public void setLastWeekDecr(Long lastWeekDecr) {
        this.lastWeekDecr = lastWeekDecr;
    }

    public Long getLastMonthIncr() {
        return lastMonthIncr;
    }

    public void setLastMonthIncr(Long lastMonthIncr) {
        this.lastMonthIncr = lastMonthIncr;
    }

    public Long getLastMonthDecr() {
        return lastMonthDecr;
    }

    public void setLastMonthDecr(Long lastMonthDecr) {
        this.lastMonthDecr = lastMonthDecr;
    }

    public Integer getGdate() {
        return gdate;
    }

    public void setGdate(Integer gdate) {
        this.gdate = gdate;
    }
}