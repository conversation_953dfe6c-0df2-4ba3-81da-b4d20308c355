package com.cmiot.report.bean;

import java.math.BigDecimal;
import java.util.Date;

public class GatewayPeriodAggrAll {
    private Date recordTime;

    private Long enterpriseId;

    private String customerId;

    private String customerName;

    private String gatewaySn;

    private String gatewayMac;

    private Long gatewayProductclassId;

    private String gatewayProductclass;

    private Long gatewayVendorId;

    private String gatewayVendor;

    private String provinceCode;

    private String cityCode;

    private BigDecimal maxTxrate;

    private BigDecimal averTxrate;

    private BigDecimal maxRxrate;

    private BigDecimal averRxrate;

    private BigDecimal downStaticstics;

    private BigDecimal upStaticstics;

    private BigDecimal runingTime;

    private BigDecimal cpuRateMax;

    private Date cpuRateMaxTime;

    private BigDecimal cpuRateAvg;

    private BigDecimal ramRateMax;

    private Date ramRateMaxTime;

    private BigDecimal ramRateAvg;

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getGatewaySn() {
        return gatewaySn;
    }

    public void setGatewaySn(String gatewaySn) {
        this.gatewaySn = gatewaySn == null ? null : gatewaySn.trim();
    }

    public String getGatewayMac() {
        return gatewayMac;
    }

    public void setGatewayMac(String gatewayMac) {
        this.gatewayMac = gatewayMac == null ? null : gatewayMac.trim();
    }

    public Long getGatewayProductclassId() {
        return gatewayProductclassId;
    }

    public void setGatewayProductclassId(Long gatewayProductclassId) {
        this.gatewayProductclassId = gatewayProductclassId;
    }

    public String getGatewayProductclass() {
        return gatewayProductclass;
    }

    public void setGatewayProductclass(String gatewayProductclass) {
        this.gatewayProductclass = gatewayProductclass == null ? null : gatewayProductclass.trim();
    }

    public Long getGatewayVendorId() {
        return gatewayVendorId;
    }

    public void setGatewayVendorId(Long gatewayVendorId) {
        this.gatewayVendorId = gatewayVendorId;
    }

    public String getGatewayVendor() {
        return gatewayVendor;
    }

    public void setGatewayVendor(String gatewayVendor) {
        this.gatewayVendor = gatewayVendor == null ? null : gatewayVendor.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public BigDecimal getMaxTxrate() {
        return maxTxrate;
    }

    public void setMaxTxrate(BigDecimal maxTxrate) {
        this.maxTxrate = maxTxrate;
    }

    public BigDecimal getAverTxrate() {
        return averTxrate;
    }

    public void setAverTxrate(BigDecimal averTxrate) {
        this.averTxrate = averTxrate;
    }

    public BigDecimal getMaxRxrate() {
        return maxRxrate;
    }

    public void setMaxRxrate(BigDecimal maxRxrate) {
        this.maxRxrate = maxRxrate;
    }

    public BigDecimal getAverRxrate() {
        return averRxrate;
    }

    public void setAverRxrate(BigDecimal averRxrate) {
        this.averRxrate = averRxrate;
    }

    public BigDecimal getDownStaticstics() {
        return downStaticstics;
    }

    public void setDownStaticstics(BigDecimal downStaticstics) {
        this.downStaticstics = downStaticstics;
    }

    public BigDecimal getUpStaticstics() {
        return upStaticstics;
    }

    public void setUpStaticstics(BigDecimal upStaticstics) {
        this.upStaticstics = upStaticstics;
    }

    public BigDecimal getRuningTime() {
        return runingTime;
    }

    public void setRuningTime(BigDecimal runingTime) {
        this.runingTime = runingTime;
    }

    public BigDecimal getCpuRateMax() {
        return cpuRateMax;
    }

    public void setCpuRateMax(BigDecimal cpuRateMax) {
        this.cpuRateMax = cpuRateMax;
    }

    public Date getCpuRateMaxTime() {
        return cpuRateMaxTime;
    }

    public void setCpuRateMaxTime(Date cpuRateMaxTime) {
        this.cpuRateMaxTime = cpuRateMaxTime;
    }

    public BigDecimal getCpuRateAvg() {
        return cpuRateAvg;
    }

    public void setCpuRateAvg(BigDecimal cpuRateAvg) {
        this.cpuRateAvg = cpuRateAvg;
    }

    public BigDecimal getRamRateMax() {
        return ramRateMax;
    }

    public void setRamRateMax(BigDecimal ramRateMax) {
        this.ramRateMax = ramRateMax;
    }

    public Date getRamRateMaxTime() {
        return ramRateMaxTime;
    }

    public void setRamRateMaxTime(Date ramRateMaxTime) {
        this.ramRateMaxTime = ramRateMaxTime;
    }

    public BigDecimal getRamRateAvg() {
        return ramRateAvg;
    }

    public void setRamRateAvg(BigDecimal ramRateAvg) {
        this.ramRateAvg = ramRateAvg;
    }
}