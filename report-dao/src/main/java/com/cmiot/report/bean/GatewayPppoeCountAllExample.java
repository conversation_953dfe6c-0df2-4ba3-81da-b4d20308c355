package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.List;

public class GatewayPppoeCountAllExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GatewayPppoeCountAllExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPdateIsNull() {
            addCriterion("pdate is null");
            return (Criteria) this;
        }

        public Criteria andPdateIsNotNull() {
            addCriterion("pdate is not null");
            return (Criteria) this;
        }

        public Criteria andPdateEqualTo(Long value) {
            addCriterion("pdate =", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateNotEqualTo(Long value) {
            addCriterion("pdate <>", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateGreaterThan(Long value) {
            addCriterion("pdate >", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateGreaterThanOrEqualTo(Long value) {
            addCriterion("pdate >=", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateLessThan(Long value) {
            addCriterion("pdate <", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateLessThanOrEqualTo(Long value) {
            addCriterion("pdate <=", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateIn(List<Long> values) {
            addCriterion("pdate in", values, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateNotIn(List<Long> values) {
            addCriterion("pdate not in", values, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateBetween(Long value1, Long value2) {
            addCriterion("pdate between", value1, value2, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateNotBetween(Long value1, Long value2) {
            addCriterion("pdate not between", value1, value2, "pdate");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNull() {
            addCriterion("gateway_vendor is null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNotNull() {
            addCriterion("gateway_vendor is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorEqualTo(String value) {
            addCriterion("gateway_vendor =", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotEqualTo(String value) {
            addCriterion("gateway_vendor <>", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThan(String value) {
            addCriterion("gateway_vendor >", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_vendor >=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThan(String value) {
            addCriterion("gateway_vendor <", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThanOrEqualTo(String value) {
            addCriterion("gateway_vendor <=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLike(String value) {
            addCriterion("gateway_vendor like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotLike(String value) {
            addCriterion("gateway_vendor not like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIn(List<String> values) {
            addCriterion("gateway_vendor in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotIn(List<String> values) {
            addCriterion("gateway_vendor not in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorBetween(String value1, String value2) {
            addCriterion("gateway_vendor between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotBetween(String value1, String value2) {
            addCriterion("gateway_vendor not between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusIsNull() {
            addCriterion("pppoeerror_status is null");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusIsNotNull() {
            addCriterion("pppoeerror_status is not null");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusEqualTo(String value) {
            addCriterion("pppoeerror_status =", value, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusNotEqualTo(String value) {
            addCriterion("pppoeerror_status <>", value, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusGreaterThan(String value) {
            addCriterion("pppoeerror_status >", value, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusGreaterThanOrEqualTo(String value) {
            addCriterion("pppoeerror_status >=", value, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusLessThan(String value) {
            addCriterion("pppoeerror_status <", value, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusLessThanOrEqualTo(String value) {
            addCriterion("pppoeerror_status <=", value, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusLike(String value) {
            addCriterion("pppoeerror_status like", value, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusNotLike(String value) {
            addCriterion("pppoeerror_status not like", value, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusIn(List<String> values) {
            addCriterion("pppoeerror_status in", values, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusNotIn(List<String> values) {
            addCriterion("pppoeerror_status not in", values, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusBetween(String value1, String value2) {
            addCriterion("pppoeerror_status between", value1, value2, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorStatusNotBetween(String value1, String value2) {
            addCriterion("pppoeerror_status not between", value1, value2, "pppoeerrorStatus");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountIsNull() {
            addCriterion("pppoeerror_count is null");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountIsNotNull() {
            addCriterion("pppoeerror_count is not null");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountEqualTo(Long value) {
            addCriterion("pppoeerror_count =", value, "pppoeerrorCount");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountNotEqualTo(Long value) {
            addCriterion("pppoeerror_count <>", value, "pppoeerrorCount");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountGreaterThan(Long value) {
            addCriterion("pppoeerror_count >", value, "pppoeerrorCount");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountGreaterThanOrEqualTo(Long value) {
            addCriterion("pppoeerror_count >=", value, "pppoeerrorCount");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountLessThan(Long value) {
            addCriterion("pppoeerror_count <", value, "pppoeerrorCount");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountLessThanOrEqualTo(Long value) {
            addCriterion("pppoeerror_count <=", value, "pppoeerrorCount");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountIn(List<Long> values) {
            addCriterion("pppoeerror_count in", values, "pppoeerrorCount");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountNotIn(List<Long> values) {
            addCriterion("pppoeerror_count not in", values, "pppoeerrorCount");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountBetween(Long value1, Long value2) {
            addCriterion("pppoeerror_count between", value1, value2, "pppoeerrorCount");
            return (Criteria) this;
        }

        public Criteria andPppoeerrorCountNotBetween(Long value1, Long value2) {
            addCriterion("pppoeerror_count not between", value1, value2, "pppoeerrorCount");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}