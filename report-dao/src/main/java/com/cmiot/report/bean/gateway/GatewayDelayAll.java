package com.cmiot.report.bean.gateway;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class GatewayDelayAll implements Serializable {
    private LocalDateTime recordTime;
    private String provinceCode;
    private String cityCode;
    private Long factoryId;
    private String gatewayVendor;
    private Long deviceModelId;
    private String gatewayProductclass;
    private String gatewayMac;
    private String gatewaySn;
    private Long enterpriseId;
    private Long minTotalDelay;
}
