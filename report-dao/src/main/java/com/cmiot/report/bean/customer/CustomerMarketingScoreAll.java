package com.cmiot.report.bean.customer;

import com.cmiot.report.poi.ExcelColumn;

import java.util.Date;

public class CustomerMarketingScoreAll {
    //企业名称,归属地区,集团客户标识,企业价值分类,客户状态,营销指数

    private Long enterpriseId;

    private String provinceCode;

    private String cityCode;

    @ExcelColumn(value = "归属地区", col = 3)
    private String areaName;

    @ExcelColumn(value = "集团客户标识", col = 2)
    private String customerId;

    @ExcelColumn(value = "企业名称", col = 1)
    private String customerName;

    @ExcelColumn(value = "客户状态", col = 5)
    private String customerStatus;

    @ExcelColumn(value = "企业价值分类", col = 4)
    private String valueCategory;

    @ExcelColumn(value = "营销指数", col = 6)
    private Long score;

    private Date sampleTime;

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getCustomerStatus() {
        return customerStatus;
    }

    public void setCustomerStatus(String customerStatus) {
        this.customerStatus = customerStatus == null ? null : customerStatus.trim();
    }

    public String getValueCategory() {
        return valueCategory;
    }

    public void setValueCategory(String valueCategory) {
        this.valueCategory = valueCategory == null ? null : valueCategory.trim();
    }

    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }
}