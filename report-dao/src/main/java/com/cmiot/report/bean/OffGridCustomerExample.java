package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OffGridCustomerExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public OffGridCustomerExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andEnterpriseIdIsNull() {
            addCriterion("enterprise_id is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNotNull() {
            addCriterion("enterprise_id is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdEqualTo(Long value) {
            addCriterion("enterprise_id =", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotEqualTo(Long value) {
            addCriterion("enterprise_id <>", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThan(Long value) {
            addCriterion("enterprise_id >", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("enterprise_id >=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThan(Long value) {
            addCriterion("enterprise_id <", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThanOrEqualTo(Long value) {
            addCriterion("enterprise_id <=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIn(List<Long> values) {
            addCriterion("enterprise_id in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotIn(List<Long> values) {
            addCriterion("enterprise_id not in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdBetween(Long value1, Long value2) {
            addCriterion("enterprise_id between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotBetween(Long value1, Long value2) {
            addCriterion("enterprise_id not between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(String value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(String value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(String value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(String value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(String value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(String value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLike(String value) {
            addCriterion("customer_id like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotLike(String value) {
            addCriterion("customer_id not like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<String> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<String> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(String value1, String value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(String value1, String value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNull() {
            addCriterion("province_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNotNull() {
            addCriterion("province_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualTo(String value) {
            addCriterion("province_name =", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualTo(String value) {
            addCriterion("province_name <>", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThan(String value) {
            addCriterion("province_name >", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_name >=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThan(String value) {
            addCriterion("province_name <", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("province_name <=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLike(String value) {
            addCriterion("province_name like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotLike(String value) {
            addCriterion("province_name not like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIn(List<String> values) {
            addCriterion("province_name in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotIn(List<String> values) {
            addCriterion("province_name not in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameBetween(String value1, String value2) {
            addCriterion("province_name between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotBetween(String value1, String value2) {
            addCriterion("province_name not between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIsNull() {
            addCriterion("customer_status is null");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIsNotNull() {
            addCriterion("customer_status is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusEqualTo(Integer value) {
            addCriterion("customer_status =", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotEqualTo(Integer value) {
            addCriterion("customer_status <>", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusGreaterThan(Integer value) {
            addCriterion("customer_status >", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_status >=", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusLessThan(Integer value) {
            addCriterion("customer_status <", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusLessThanOrEqualTo(Integer value) {
            addCriterion("customer_status <=", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIn(List<Integer> values) {
            addCriterion("customer_status in", values, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotIn(List<Integer> values) {
            addCriterion("customer_status not in", values, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusBetween(Integer value1, Integer value2) {
            addCriterion("customer_status between", value1, value2, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_status not between", value1, value2, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andValueCategoryIsNull() {
            addCriterion("value_category is null");
            return (Criteria) this;
        }

        public Criteria andValueCategoryIsNotNull() {
            addCriterion("value_category is not null");
            return (Criteria) this;
        }

        public Criteria andValueCategoryEqualTo(String value) {
            addCriterion("value_category =", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotEqualTo(String value) {
            addCriterion("value_category <>", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryGreaterThan(String value) {
            addCriterion("value_category >", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("value_category >=", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryLessThan(String value) {
            addCriterion("value_category <", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryLessThanOrEqualTo(String value) {
            addCriterion("value_category <=", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryLike(String value) {
            addCriterion("value_category like", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotLike(String value) {
            addCriterion("value_category not like", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryIn(List<String> values) {
            addCriterion("value_category in", values, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotIn(List<String> values) {
            addCriterion("value_category not in", values, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryBetween(String value1, String value2) {
            addCriterion("value_category between", value1, value2, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotBetween(String value1, String value2) {
            addCriterion("value_category not between", value1, value2, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreIsNull() {
            addCriterion("off_grid_score is null");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreIsNotNull() {
            addCriterion("off_grid_score is not null");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreEqualTo(Integer value) {
            addCriterion("off_grid_score =", value, "offGridScore");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreNotEqualTo(Integer value) {
            addCriterion("off_grid_score <>", value, "offGridScore");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreGreaterThan(Integer value) {
            addCriterion("off_grid_score >", value, "offGridScore");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("off_grid_score >=", value, "offGridScore");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreLessThan(Integer value) {
            addCriterion("off_grid_score <", value, "offGridScore");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreLessThanOrEqualTo(Integer value) {
            addCriterion("off_grid_score <=", value, "offGridScore");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreIn(List<Integer> values) {
            addCriterion("off_grid_score in", values, "offGridScore");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreNotIn(List<Integer> values) {
            addCriterion("off_grid_score not in", values, "offGridScore");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreBetween(Integer value1, Integer value2) {
            addCriterion("off_grid_score between", value1, value2, "offGridScore");
            return (Criteria) this;
        }

        public Criteria andOffGridScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("off_grid_score not between", value1, value2, "offGridScore");
            return (Criteria) this;
        }

        public Criteria andOffGridTagIsNull() {
            addCriterion("off_grid_tag is null");
            return (Criteria) this;
        }

        public Criteria andOffGridTagIsNotNull() {
            addCriterion("off_grid_tag is not null");
            return (Criteria) this;
        }

        public Criteria andOffGridTagEqualTo(Integer value) {
            addCriterion("off_grid_tag =", value, "offGridTag");
            return (Criteria) this;
        }

        public Criteria andOffGridTagNotEqualTo(Integer value) {
            addCriterion("off_grid_tag <>", value, "offGridTag");
            return (Criteria) this;
        }

        public Criteria andOffGridTagGreaterThan(Integer value) {
            addCriterion("off_grid_tag >", value, "offGridTag");
            return (Criteria) this;
        }

        public Criteria andOffGridTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("off_grid_tag >=", value, "offGridTag");
            return (Criteria) this;
        }

        public Criteria andOffGridTagLessThan(Integer value) {
            addCriterion("off_grid_tag <", value, "offGridTag");
            return (Criteria) this;
        }

        public Criteria andOffGridTagLessThanOrEqualTo(Integer value) {
            addCriterion("off_grid_tag <=", value, "offGridTag");
            return (Criteria) this;
        }

        public Criteria andOffGridTagIn(List<Integer> values) {
            addCriterion("off_grid_tag in", values, "offGridTag");
            return (Criteria) this;
        }

        public Criteria andOffGridTagNotIn(List<Integer> values) {
            addCriterion("off_grid_tag not in", values, "offGridTag");
            return (Criteria) this;
        }

        public Criteria andOffGridTagBetween(Integer value1, Integer value2) {
            addCriterion("off_grid_tag between", value1, value2, "offGridTag");
            return (Criteria) this;
        }

        public Criteria andOffGridTagNotBetween(Integer value1, Integer value2) {
            addCriterion("off_grid_tag not between", value1, value2, "offGridTag");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreIsNull() {
            addCriterion("terminal_score is null");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreIsNotNull() {
            addCriterion("terminal_score is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreEqualTo(Integer value) {
            addCriterion("terminal_score =", value, "terminalScore");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreNotEqualTo(Integer value) {
            addCriterion("terminal_score <>", value, "terminalScore");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreGreaterThan(Integer value) {
            addCriterion("terminal_score >", value, "terminalScore");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("terminal_score >=", value, "terminalScore");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreLessThan(Integer value) {
            addCriterion("terminal_score <", value, "terminalScore");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreLessThanOrEqualTo(Integer value) {
            addCriterion("terminal_score <=", value, "terminalScore");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreIn(List<Integer> values) {
            addCriterion("terminal_score in", values, "terminalScore");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreNotIn(List<Integer> values) {
            addCriterion("terminal_score not in", values, "terminalScore");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreBetween(Integer value1, Integer value2) {
            addCriterion("terminal_score between", value1, value2, "terminalScore");
            return (Criteria) this;
        }

        public Criteria andTerminalScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("terminal_score not between", value1, value2, "terminalScore");
            return (Criteria) this;
        }

        public Criteria andTerminalTagIsNull() {
            addCriterion("terminal_tag is null");
            return (Criteria) this;
        }

        public Criteria andTerminalTagIsNotNull() {
            addCriterion("terminal_tag is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalTagEqualTo(Integer value) {
            addCriterion("terminal_tag =", value, "terminalTag");
            return (Criteria) this;
        }

        public Criteria andTerminalTagNotEqualTo(Integer value) {
            addCriterion("terminal_tag <>", value, "terminalTag");
            return (Criteria) this;
        }

        public Criteria andTerminalTagGreaterThan(Integer value) {
            addCriterion("terminal_tag >", value, "terminalTag");
            return (Criteria) this;
        }

        public Criteria andTerminalTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("terminal_tag >=", value, "terminalTag");
            return (Criteria) this;
        }

        public Criteria andTerminalTagLessThan(Integer value) {
            addCriterion("terminal_tag <", value, "terminalTag");
            return (Criteria) this;
        }

        public Criteria andTerminalTagLessThanOrEqualTo(Integer value) {
            addCriterion("terminal_tag <=", value, "terminalTag");
            return (Criteria) this;
        }

        public Criteria andTerminalTagIn(List<Integer> values) {
            addCriterion("terminal_tag in", values, "terminalTag");
            return (Criteria) this;
        }

        public Criteria andTerminalTagNotIn(List<Integer> values) {
            addCriterion("terminal_tag not in", values, "terminalTag");
            return (Criteria) this;
        }

        public Criteria andTerminalTagBetween(Integer value1, Integer value2) {
            addCriterion("terminal_tag between", value1, value2, "terminalTag");
            return (Criteria) this;
        }

        public Criteria andTerminalTagNotBetween(Integer value1, Integer value2) {
            addCriterion("terminal_tag not between", value1, value2, "terminalTag");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreIsNull() {
            addCriterion("network_score is null");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreIsNotNull() {
            addCriterion("network_score is not null");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreEqualTo(Integer value) {
            addCriterion("network_score =", value, "networkScore");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreNotEqualTo(Integer value) {
            addCriterion("network_score <>", value, "networkScore");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreGreaterThan(Integer value) {
            addCriterion("network_score >", value, "networkScore");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("network_score >=", value, "networkScore");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreLessThan(Integer value) {
            addCriterion("network_score <", value, "networkScore");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreLessThanOrEqualTo(Integer value) {
            addCriterion("network_score <=", value, "networkScore");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreIn(List<Integer> values) {
            addCriterion("network_score in", values, "networkScore");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreNotIn(List<Integer> values) {
            addCriterion("network_score not in", values, "networkScore");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreBetween(Integer value1, Integer value2) {
            addCriterion("network_score between", value1, value2, "networkScore");
            return (Criteria) this;
        }

        public Criteria andNetworkScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("network_score not between", value1, value2, "networkScore");
            return (Criteria) this;
        }

        public Criteria andNetworkTagIsNull() {
            addCriterion("network_tag is null");
            return (Criteria) this;
        }

        public Criteria andNetworkTagIsNotNull() {
            addCriterion("network_tag is not null");
            return (Criteria) this;
        }

        public Criteria andNetworkTagEqualTo(Integer value) {
            addCriterion("network_tag =", value, "networkTag");
            return (Criteria) this;
        }

        public Criteria andNetworkTagNotEqualTo(Integer value) {
            addCriterion("network_tag <>", value, "networkTag");
            return (Criteria) this;
        }

        public Criteria andNetworkTagGreaterThan(Integer value) {
            addCriterion("network_tag >", value, "networkTag");
            return (Criteria) this;
        }

        public Criteria andNetworkTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("network_tag >=", value, "networkTag");
            return (Criteria) this;
        }

        public Criteria andNetworkTagLessThan(Integer value) {
            addCriterion("network_tag <", value, "networkTag");
            return (Criteria) this;
        }

        public Criteria andNetworkTagLessThanOrEqualTo(Integer value) {
            addCriterion("network_tag <=", value, "networkTag");
            return (Criteria) this;
        }

        public Criteria andNetworkTagIn(List<Integer> values) {
            addCriterion("network_tag in", values, "networkTag");
            return (Criteria) this;
        }

        public Criteria andNetworkTagNotIn(List<Integer> values) {
            addCriterion("network_tag not in", values, "networkTag");
            return (Criteria) this;
        }

        public Criteria andNetworkTagBetween(Integer value1, Integer value2) {
            addCriterion("network_tag between", value1, value2, "networkTag");
            return (Criteria) this;
        }

        public Criteria andNetworkTagNotBetween(Integer value1, Integer value2) {
            addCriterion("network_tag not between", value1, value2, "networkTag");
            return (Criteria) this;
        }

        public Criteria andServiceScoreIsNull() {
            addCriterion("service_score is null");
            return (Criteria) this;
        }

        public Criteria andServiceScoreIsNotNull() {
            addCriterion("service_score is not null");
            return (Criteria) this;
        }

        public Criteria andServiceScoreEqualTo(Integer value) {
            addCriterion("service_score =", value, "serviceScore");
            return (Criteria) this;
        }

        public Criteria andServiceScoreNotEqualTo(Integer value) {
            addCriterion("service_score <>", value, "serviceScore");
            return (Criteria) this;
        }

        public Criteria andServiceScoreGreaterThan(Integer value) {
            addCriterion("service_score >", value, "serviceScore");
            return (Criteria) this;
        }

        public Criteria andServiceScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_score >=", value, "serviceScore");
            return (Criteria) this;
        }

        public Criteria andServiceScoreLessThan(Integer value) {
            addCriterion("service_score <", value, "serviceScore");
            return (Criteria) this;
        }

        public Criteria andServiceScoreLessThanOrEqualTo(Integer value) {
            addCriterion("service_score <=", value, "serviceScore");
            return (Criteria) this;
        }

        public Criteria andServiceScoreIn(List<Integer> values) {
            addCriterion("service_score in", values, "serviceScore");
            return (Criteria) this;
        }

        public Criteria andServiceScoreNotIn(List<Integer> values) {
            addCriterion("service_score not in", values, "serviceScore");
            return (Criteria) this;
        }

        public Criteria andServiceScoreBetween(Integer value1, Integer value2) {
            addCriterion("service_score between", value1, value2, "serviceScore");
            return (Criteria) this;
        }

        public Criteria andServiceScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("service_score not between", value1, value2, "serviceScore");
            return (Criteria) this;
        }

        public Criteria andServiceTagIsNull() {
            addCriterion("service_tag is null");
            return (Criteria) this;
        }

        public Criteria andServiceTagIsNotNull() {
            addCriterion("service_tag is not null");
            return (Criteria) this;
        }

        public Criteria andServiceTagEqualTo(Integer value) {
            addCriterion("service_tag =", value, "serviceTag");
            return (Criteria) this;
        }

        public Criteria andServiceTagNotEqualTo(Integer value) {
            addCriterion("service_tag <>", value, "serviceTag");
            return (Criteria) this;
        }

        public Criteria andServiceTagGreaterThan(Integer value) {
            addCriterion("service_tag >", value, "serviceTag");
            return (Criteria) this;
        }

        public Criteria andServiceTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_tag >=", value, "serviceTag");
            return (Criteria) this;
        }

        public Criteria andServiceTagLessThan(Integer value) {
            addCriterion("service_tag <", value, "serviceTag");
            return (Criteria) this;
        }

        public Criteria andServiceTagLessThanOrEqualTo(Integer value) {
            addCriterion("service_tag <=", value, "serviceTag");
            return (Criteria) this;
        }

        public Criteria andServiceTagIn(List<Integer> values) {
            addCriterion("service_tag in", values, "serviceTag");
            return (Criteria) this;
        }

        public Criteria andServiceTagNotIn(List<Integer> values) {
            addCriterion("service_tag not in", values, "serviceTag");
            return (Criteria) this;
        }

        public Criteria andServiceTagBetween(Integer value1, Integer value2) {
            addCriterion("service_tag between", value1, value2, "serviceTag");
            return (Criteria) this;
        }

        public Criteria andServiceTagNotBetween(Integer value1, Integer value2) {
            addCriterion("service_tag not between", value1, value2, "serviceTag");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreIsNull() {
            addCriterion("business_score is null");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreIsNotNull() {
            addCriterion("business_score is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreEqualTo(Integer value) {
            addCriterion("business_score =", value, "businessScore");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreNotEqualTo(Integer value) {
            addCriterion("business_score <>", value, "businessScore");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreGreaterThan(Integer value) {
            addCriterion("business_score >", value, "businessScore");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_score >=", value, "businessScore");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreLessThan(Integer value) {
            addCriterion("business_score <", value, "businessScore");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreLessThanOrEqualTo(Integer value) {
            addCriterion("business_score <=", value, "businessScore");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreIn(List<Integer> values) {
            addCriterion("business_score in", values, "businessScore");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreNotIn(List<Integer> values) {
            addCriterion("business_score not in", values, "businessScore");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreBetween(Integer value1, Integer value2) {
            addCriterion("business_score between", value1, value2, "businessScore");
            return (Criteria) this;
        }

        public Criteria andBusinessScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("business_score not between", value1, value2, "businessScore");
            return (Criteria) this;
        }

        public Criteria andBusinessTagIsNull() {
            addCriterion("business_tag is null");
            return (Criteria) this;
        }

        public Criteria andBusinessTagIsNotNull() {
            addCriterion("business_tag is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessTagEqualTo(Integer value) {
            addCriterion("business_tag =", value, "businessTag");
            return (Criteria) this;
        }

        public Criteria andBusinessTagNotEqualTo(Integer value) {
            addCriterion("business_tag <>", value, "businessTag");
            return (Criteria) this;
        }

        public Criteria andBusinessTagGreaterThan(Integer value) {
            addCriterion("business_tag >", value, "businessTag");
            return (Criteria) this;
        }

        public Criteria andBusinessTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_tag >=", value, "businessTag");
            return (Criteria) this;
        }

        public Criteria andBusinessTagLessThan(Integer value) {
            addCriterion("business_tag <", value, "businessTag");
            return (Criteria) this;
        }

        public Criteria andBusinessTagLessThanOrEqualTo(Integer value) {
            addCriterion("business_tag <=", value, "businessTag");
            return (Criteria) this;
        }

        public Criteria andBusinessTagIn(List<Integer> values) {
            addCriterion("business_tag in", values, "businessTag");
            return (Criteria) this;
        }

        public Criteria andBusinessTagNotIn(List<Integer> values) {
            addCriterion("business_tag not in", values, "businessTag");
            return (Criteria) this;
        }

        public Criteria andBusinessTagBetween(Integer value1, Integer value2) {
            addCriterion("business_tag between", value1, value2, "businessTag");
            return (Criteria) this;
        }

        public Criteria andBusinessTagNotBetween(Integer value1, Integer value2) {
            addCriterion("business_tag not between", value1, value2, "businessTag");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreIsNull() {
            addCriterion("maintenance_score is null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreIsNotNull() {
            addCriterion("maintenance_score is not null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreEqualTo(Integer value) {
            addCriterion("maintenance_score =", value, "maintenanceScore");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreNotEqualTo(Integer value) {
            addCriterion("maintenance_score <>", value, "maintenanceScore");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreGreaterThan(Integer value) {
            addCriterion("maintenance_score >", value, "maintenanceScore");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("maintenance_score >=", value, "maintenanceScore");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreLessThan(Integer value) {
            addCriterion("maintenance_score <", value, "maintenanceScore");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreLessThanOrEqualTo(Integer value) {
            addCriterion("maintenance_score <=", value, "maintenanceScore");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreIn(List<Integer> values) {
            addCriterion("maintenance_score in", values, "maintenanceScore");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreNotIn(List<Integer> values) {
            addCriterion("maintenance_score not in", values, "maintenanceScore");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_score between", value1, value2, "maintenanceScore");
            return (Criteria) this;
        }

        public Criteria andMaintenanceScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_score not between", value1, value2, "maintenanceScore");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagIsNull() {
            addCriterion("maintenance_tag is null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagIsNotNull() {
            addCriterion("maintenance_tag is not null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagEqualTo(Integer value) {
            addCriterion("maintenance_tag =", value, "maintenanceTag");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagNotEqualTo(Integer value) {
            addCriterion("maintenance_tag <>", value, "maintenanceTag");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagGreaterThan(Integer value) {
            addCriterion("maintenance_tag >", value, "maintenanceTag");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("maintenance_tag >=", value, "maintenanceTag");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagLessThan(Integer value) {
            addCriterion("maintenance_tag <", value, "maintenanceTag");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagLessThanOrEqualTo(Integer value) {
            addCriterion("maintenance_tag <=", value, "maintenanceTag");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagIn(List<Integer> values) {
            addCriterion("maintenance_tag in", values, "maintenanceTag");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagNotIn(List<Integer> values) {
            addCriterion("maintenance_tag not in", values, "maintenanceTag");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_tag between", value1, value2, "maintenanceTag");
            return (Criteria) this;
        }

        public Criteria andMaintenanceTagNotBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_tag not between", value1, value2, "maintenanceTag");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}