package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CustomerAllExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CustomerAllExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andEnterpriseIdIsNull() {
            addCriterion("enterprise_id is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNotNull() {
            addCriterion("enterprise_id is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdEqualTo(Long value) {
            addCriterion("enterprise_id =", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotEqualTo(Long value) {
            addCriterion("enterprise_id <>", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThan(Long value) {
            addCriterion("enterprise_id >", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("enterprise_id >=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThan(Long value) {
            addCriterion("enterprise_id <", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThanOrEqualTo(Long value) {
            addCriterion("enterprise_id <=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIn(List<Long> values) {
            addCriterion("enterprise_id in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotIn(List<Long> values) {
            addCriterion("enterprise_id not in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdBetween(Long value1, Long value2) {
            addCriterion("enterprise_id between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotBetween(Long value1, Long value2) {
            addCriterion("enterprise_id not between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(String value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(String value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(String value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(String value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(String value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(String value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLike(String value) {
            addCriterion("customer_id like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotLike(String value) {
            addCriterion("customer_id not like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<String> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<String> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(String value1, String value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(String value1, String value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andAddressIsNull() {
            addCriterion("address is null");
            return (Criteria) this;
        }

        public Criteria andAddressIsNotNull() {
            addCriterion("address is not null");
            return (Criteria) this;
        }

        public Criteria andAddressEqualTo(String value) {
            addCriterion("address =", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotEqualTo(String value) {
            addCriterion("address <>", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThan(String value) {
            addCriterion("address >", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThanOrEqualTo(String value) {
            addCriterion("address >=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThan(String value) {
            addCriterion("address <", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThanOrEqualTo(String value) {
            addCriterion("address <=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLike(String value) {
            addCriterion("address like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotLike(String value) {
            addCriterion("address not like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressIn(List<String> values) {
            addCriterion("address in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotIn(List<String> values) {
            addCriterion("address not in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressBetween(String value1, String value2) {
            addCriterion("address between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotBetween(String value1, String value2) {
            addCriterion("address not between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andIdTypeIsNull() {
            addCriterion("id_type is null");
            return (Criteria) this;
        }

        public Criteria andIdTypeIsNotNull() {
            addCriterion("id_type is not null");
            return (Criteria) this;
        }

        public Criteria andIdTypeEqualTo(Integer value) {
            addCriterion("id_type =", value, "idType");
            return (Criteria) this;
        }

        public Criteria andIdTypeNotEqualTo(Integer value) {
            addCriterion("id_type <>", value, "idType");
            return (Criteria) this;
        }

        public Criteria andIdTypeGreaterThan(Integer value) {
            addCriterion("id_type >", value, "idType");
            return (Criteria) this;
        }

        public Criteria andIdTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("id_type >=", value, "idType");
            return (Criteria) this;
        }

        public Criteria andIdTypeLessThan(Integer value) {
            addCriterion("id_type <", value, "idType");
            return (Criteria) this;
        }

        public Criteria andIdTypeLessThanOrEqualTo(Integer value) {
            addCriterion("id_type <=", value, "idType");
            return (Criteria) this;
        }

        public Criteria andIdTypeIn(List<Integer> values) {
            addCriterion("id_type in", values, "idType");
            return (Criteria) this;
        }

        public Criteria andIdTypeNotIn(List<Integer> values) {
            addCriterion("id_type not in", values, "idType");
            return (Criteria) this;
        }

        public Criteria andIdTypeBetween(Integer value1, Integer value2) {
            addCriterion("id_type between", value1, value2, "idType");
            return (Criteria) this;
        }

        public Criteria andIdTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("id_type not between", value1, value2, "idType");
            return (Criteria) this;
        }

        public Criteria andIdNumberIsNull() {
            addCriterion("id_number is null");
            return (Criteria) this;
        }

        public Criteria andIdNumberIsNotNull() {
            addCriterion("id_number is not null");
            return (Criteria) this;
        }

        public Criteria andIdNumberEqualTo(String value) {
            addCriterion("id_number =", value, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberNotEqualTo(String value) {
            addCriterion("id_number <>", value, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberGreaterThan(String value) {
            addCriterion("id_number >", value, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberGreaterThanOrEqualTo(String value) {
            addCriterion("id_number >=", value, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberLessThan(String value) {
            addCriterion("id_number <", value, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberLessThanOrEqualTo(String value) {
            addCriterion("id_number <=", value, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberLike(String value) {
            addCriterion("id_number like", value, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberNotLike(String value) {
            addCriterion("id_number not like", value, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberIn(List<String> values) {
            addCriterion("id_number in", values, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberNotIn(List<String> values) {
            addCriterion("id_number not in", values, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberBetween(String value1, String value2) {
            addCriterion("id_number between", value1, value2, "idNumber");
            return (Criteria) this;
        }

        public Criteria andIdNumberNotBetween(String value1, String value2) {
            addCriterion("id_number not between", value1, value2, "idNumber");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdIsNull() {
            addCriterion("parent_customer_id is null");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdIsNotNull() {
            addCriterion("parent_customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdEqualTo(String value) {
            addCriterion("parent_customer_id =", value, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdNotEqualTo(String value) {
            addCriterion("parent_customer_id <>", value, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdGreaterThan(String value) {
            addCriterion("parent_customer_id >", value, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdGreaterThanOrEqualTo(String value) {
            addCriterion("parent_customer_id >=", value, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdLessThan(String value) {
            addCriterion("parent_customer_id <", value, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdLessThanOrEqualTo(String value) {
            addCriterion("parent_customer_id <=", value, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdLike(String value) {
            addCriterion("parent_customer_id like", value, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdNotLike(String value) {
            addCriterion("parent_customer_id not like", value, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdIn(List<String> values) {
            addCriterion("parent_customer_id in", values, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdNotIn(List<String> values) {
            addCriterion("parent_customer_id not in", values, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdBetween(String value1, String value2) {
            addCriterion("parent_customer_id between", value1, value2, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andParentCustomerIdNotBetween(String value1, String value2) {
            addCriterion("parent_customer_id not between", value1, value2, "parentCustomerId");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNull() {
            addCriterion("contact_phone is null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNotNull() {
            addCriterion("contact_phone is not null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneEqualTo(String value) {
            addCriterion("contact_phone =", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotEqualTo(String value) {
            addCriterion("contact_phone <>", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThan(String value) {
            addCriterion("contact_phone >", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("contact_phone >=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThan(String value) {
            addCriterion("contact_phone <", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("contact_phone <=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLike(String value) {
            addCriterion("contact_phone like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotLike(String value) {
            addCriterion("contact_phone not like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIn(List<String> values) {
            addCriterion("contact_phone in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotIn(List<String> values) {
            addCriterion("contact_phone not in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneBetween(String value1, String value2) {
            addCriterion("contact_phone between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotBetween(String value1, String value2) {
            addCriterion("contact_phone not between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactEmailIsNull() {
            addCriterion("contact_email is null");
            return (Criteria) this;
        }

        public Criteria andContactEmailIsNotNull() {
            addCriterion("contact_email is not null");
            return (Criteria) this;
        }

        public Criteria andContactEmailEqualTo(String value) {
            addCriterion("contact_email =", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailNotEqualTo(String value) {
            addCriterion("contact_email <>", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailGreaterThan(String value) {
            addCriterion("contact_email >", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailGreaterThanOrEqualTo(String value) {
            addCriterion("contact_email >=", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailLessThan(String value) {
            addCriterion("contact_email <", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailLessThanOrEqualTo(String value) {
            addCriterion("contact_email <=", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailLike(String value) {
            addCriterion("contact_email like", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailNotLike(String value) {
            addCriterion("contact_email not like", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailIn(List<String> values) {
            addCriterion("contact_email in", values, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailNotIn(List<String> values) {
            addCriterion("contact_email not in", values, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailBetween(String value1, String value2) {
            addCriterion("contact_email between", value1, value2, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailNotBetween(String value1, String value2) {
            addCriterion("contact_email not between", value1, value2, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeIsNull() {
            addCriterion("county_code is null");
            return (Criteria) this;
        }

        public Criteria andCountyCodeIsNotNull() {
            addCriterion("county_code is not null");
            return (Criteria) this;
        }

        public Criteria andCountyCodeEqualTo(String value) {
            addCriterion("county_code =", value, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeNotEqualTo(String value) {
            addCriterion("county_code <>", value, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeGreaterThan(String value) {
            addCriterion("county_code >", value, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("county_code >=", value, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeLessThan(String value) {
            addCriterion("county_code <", value, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeLessThanOrEqualTo(String value) {
            addCriterion("county_code <=", value, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeLike(String value) {
            addCriterion("county_code like", value, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeNotLike(String value) {
            addCriterion("county_code not like", value, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeIn(List<String> values) {
            addCriterion("county_code in", values, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeNotIn(List<String> values) {
            addCriterion("county_code not in", values, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeBetween(String value1, String value2) {
            addCriterion("county_code between", value1, value2, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCountyCodeNotBetween(String value1, String value2) {
            addCriterion("county_code not between", value1, value2, "countyCode");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNull() {
            addCriterion("industry is null");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNotNull() {
            addCriterion("industry is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryEqualTo(Long value) {
            addCriterion("industry =", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotEqualTo(Long value) {
            addCriterion("industry <>", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThan(Long value) {
            addCriterion("industry >", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThanOrEqualTo(Long value) {
            addCriterion("industry >=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThan(Long value) {
            addCriterion("industry <", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThanOrEqualTo(Long value) {
            addCriterion("industry <=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryIn(List<Long> values) {
            addCriterion("industry in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotIn(List<Long> values) {
            addCriterion("industry not in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryBetween(Long value1, Long value2) {
            addCriterion("industry between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotBetween(Long value1, Long value2) {
            addCriterion("industry not between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryIsNull() {
            addCriterion("industry_category is null");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryIsNotNull() {
            addCriterion("industry_category is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryEqualTo(String value) {
            addCriterion("industry_category =", value, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryNotEqualTo(String value) {
            addCriterion("industry_category <>", value, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryGreaterThan(String value) {
            addCriterion("industry_category >", value, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("industry_category >=", value, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryLessThan(String value) {
            addCriterion("industry_category <", value, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryLessThanOrEqualTo(String value) {
            addCriterion("industry_category <=", value, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryLike(String value) {
            addCriterion("industry_category like", value, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryNotLike(String value) {
            addCriterion("industry_category not like", value, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryIn(List<String> values) {
            addCriterion("industry_category in", values, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryNotIn(List<String> values) {
            addCriterion("industry_category not in", values, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryBetween(String value1, String value2) {
            addCriterion("industry_category between", value1, value2, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andIndustryCategoryNotBetween(String value1, String value2) {
            addCriterion("industry_category not between", value1, value2, "industryCategory");
            return (Criteria) this;
        }

        public Criteria andAliasIsNull() {
            addCriterion("alias is null");
            return (Criteria) this;
        }

        public Criteria andAliasIsNotNull() {
            addCriterion("alias is not null");
            return (Criteria) this;
        }

        public Criteria andAliasEqualTo(String value) {
            addCriterion("alias =", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotEqualTo(String value) {
            addCriterion("alias <>", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasGreaterThan(String value) {
            addCriterion("alias >", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasGreaterThanOrEqualTo(String value) {
            addCriterion("alias >=", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasLessThan(String value) {
            addCriterion("alias <", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasLessThanOrEqualTo(String value) {
            addCriterion("alias <=", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasLike(String value) {
            addCriterion("alias like", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotLike(String value) {
            addCriterion("alias not like", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasIn(List<String> values) {
            addCriterion("alias in", values, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotIn(List<String> values) {
            addCriterion("alias not in", values, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasBetween(String value1, String value2) {
            addCriterion("alias between", value1, value2, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotBetween(String value1, String value2) {
            addCriterion("alias not between", value1, value2, "alias");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkIsNull() {
            addCriterion("enterprise_mark is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkIsNotNull() {
            addCriterion("enterprise_mark is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkEqualTo(String value) {
            addCriterion("enterprise_mark =", value, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkNotEqualTo(String value) {
            addCriterion("enterprise_mark <>", value, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkGreaterThan(String value) {
            addCriterion("enterprise_mark >", value, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkGreaterThanOrEqualTo(String value) {
            addCriterion("enterprise_mark >=", value, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkLessThan(String value) {
            addCriterion("enterprise_mark <", value, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkLessThanOrEqualTo(String value) {
            addCriterion("enterprise_mark <=", value, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkLike(String value) {
            addCriterion("enterprise_mark like", value, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkNotLike(String value) {
            addCriterion("enterprise_mark not like", value, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkIn(List<String> values) {
            addCriterion("enterprise_mark in", values, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkNotIn(List<String> values) {
            addCriterion("enterprise_mark not in", values, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkBetween(String value1, String value2) {
            addCriterion("enterprise_mark between", value1, value2, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andEnterpriseMarkNotBetween(String value1, String value2) {
            addCriterion("enterprise_mark not between", value1, value2, "enterpriseMark");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIsNull() {
            addCriterion("customer_status is null");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIsNotNull() {
            addCriterion("customer_status is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusEqualTo(Integer value) {
            addCriterion("customer_status =", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotEqualTo(Integer value) {
            addCriterion("customer_status <>", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusGreaterThan(Integer value) {
            addCriterion("customer_status >", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_status >=", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusLessThan(Integer value) {
            addCriterion("customer_status <", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusLessThanOrEqualTo(Integer value) {
            addCriterion("customer_status <=", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIn(List<Integer> values) {
            addCriterion("customer_status in", values, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotIn(List<Integer> values) {
            addCriterion("customer_status not in", values, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusBetween(Integer value1, Integer value2) {
            addCriterion("customer_status between", value1, value2, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_status not between", value1, value2, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossIsNull() {
            addCriterion("customer_code_boss is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossIsNotNull() {
            addCriterion("customer_code_boss is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossEqualTo(String value) {
            addCriterion("customer_code_boss =", value, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossNotEqualTo(String value) {
            addCriterion("customer_code_boss <>", value, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossGreaterThan(String value) {
            addCriterion("customer_code_boss >", value, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code_boss >=", value, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossLessThan(String value) {
            addCriterion("customer_code_boss <", value, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossLessThanOrEqualTo(String value) {
            addCriterion("customer_code_boss <=", value, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossLike(String value) {
            addCriterion("customer_code_boss like", value, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossNotLike(String value) {
            addCriterion("customer_code_boss not like", value, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossIn(List<String> values) {
            addCriterion("customer_code_boss in", values, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossNotIn(List<String> values) {
            addCriterion("customer_code_boss not in", values, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossBetween(String value1, String value2) {
            addCriterion("customer_code_boss between", value1, value2, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBossNotBetween(String value1, String value2) {
            addCriterion("customer_code_boss not between", value1, value2, "customerCodeBoss");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeIsNull() {
            addCriterion("customer_size is null");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeIsNotNull() {
            addCriterion("customer_size is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeEqualTo(String value) {
            addCriterion("customer_size =", value, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeNotEqualTo(String value) {
            addCriterion("customer_size <>", value, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeGreaterThan(String value) {
            addCriterion("customer_size >", value, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_size >=", value, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeLessThan(String value) {
            addCriterion("customer_size <", value, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeLessThanOrEqualTo(String value) {
            addCriterion("customer_size <=", value, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeLike(String value) {
            addCriterion("customer_size like", value, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeNotLike(String value) {
            addCriterion("customer_size not like", value, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeIn(List<String> values) {
            addCriterion("customer_size in", values, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeNotIn(List<String> values) {
            addCriterion("customer_size not in", values, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeBetween(String value1, String value2) {
            addCriterion("customer_size between", value1, value2, "customerSize");
            return (Criteria) this;
        }

        public Criteria andCustomerSizeNotBetween(String value1, String value2) {
            addCriterion("customer_size not between", value1, value2, "customerSize");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeIsNull() {
            addCriterion("last_business_time is null");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeIsNotNull() {
            addCriterion("last_business_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeEqualTo(Date value) {
            addCriterion("last_business_time =", value, "lastBusinessTime");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeNotEqualTo(Date value) {
            addCriterion("last_business_time <>", value, "lastBusinessTime");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeGreaterThan(Date value) {
            addCriterion("last_business_time >", value, "lastBusinessTime");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("last_business_time >=", value, "lastBusinessTime");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeLessThan(Date value) {
            addCriterion("last_business_time <", value, "lastBusinessTime");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeLessThanOrEqualTo(Date value) {
            addCriterion("last_business_time <=", value, "lastBusinessTime");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeIn(List<Date> values) {
            addCriterion("last_business_time in", values, "lastBusinessTime");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeNotIn(List<Date> values) {
            addCriterion("last_business_time not in", values, "lastBusinessTime");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeBetween(Date value1, Date value2) {
            addCriterion("last_business_time between", value1, value2, "lastBusinessTime");
            return (Criteria) this;
        }

        public Criteria andLastBusinessTimeNotBetween(Date value1, Date value2) {
            addCriterion("last_business_time not between", value1, value2, "lastBusinessTime");
            return (Criteria) this;
        }

        public Criteria andBusinessCountIsNull() {
            addCriterion("business_count is null");
            return (Criteria) this;
        }

        public Criteria andBusinessCountIsNotNull() {
            addCriterion("business_count is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessCountEqualTo(Integer value) {
            addCriterion("business_count =", value, "businessCount");
            return (Criteria) this;
        }

        public Criteria andBusinessCountNotEqualTo(Integer value) {
            addCriterion("business_count <>", value, "businessCount");
            return (Criteria) this;
        }

        public Criteria andBusinessCountGreaterThan(Integer value) {
            addCriterion("business_count >", value, "businessCount");
            return (Criteria) this;
        }

        public Criteria andBusinessCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_count >=", value, "businessCount");
            return (Criteria) this;
        }

        public Criteria andBusinessCountLessThan(Integer value) {
            addCriterion("business_count <", value, "businessCount");
            return (Criteria) this;
        }

        public Criteria andBusinessCountLessThanOrEqualTo(Integer value) {
            addCriterion("business_count <=", value, "businessCount");
            return (Criteria) this;
        }

        public Criteria andBusinessCountIn(List<Integer> values) {
            addCriterion("business_count in", values, "businessCount");
            return (Criteria) this;
        }

        public Criteria andBusinessCountNotIn(List<Integer> values) {
            addCriterion("business_count not in", values, "businessCount");
            return (Criteria) this;
        }

        public Criteria andBusinessCountBetween(Integer value1, Integer value2) {
            addCriterion("business_count between", value1, value2, "businessCount");
            return (Criteria) this;
        }

        public Criteria andBusinessCountNotBetween(Integer value1, Integer value2) {
            addCriterion("business_count not between", value1, value2, "businessCount");
            return (Criteria) this;
        }

        public Criteria andValueCategoryIsNull() {
            addCriterion("value_category is null");
            return (Criteria) this;
        }

        public Criteria andValueCategoryIsNotNull() {
            addCriterion("value_category is not null");
            return (Criteria) this;
        }

        public Criteria andValueCategoryEqualTo(String value) {
            addCriterion("value_category =", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotEqualTo(String value) {
            addCriterion("value_category <>", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryGreaterThan(String value) {
            addCriterion("value_category >", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("value_category >=", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryLessThan(String value) {
            addCriterion("value_category <", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryLessThanOrEqualTo(String value) {
            addCriterion("value_category <=", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryLike(String value) {
            addCriterion("value_category like", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotLike(String value) {
            addCriterion("value_category not like", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryIn(List<String> values) {
            addCriterion("value_category in", values, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotIn(List<String> values) {
            addCriterion("value_category not in", values, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryBetween(String value1, String value2) {
            addCriterion("value_category between", value1, value2, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotBetween(String value1, String value2) {
            addCriterion("value_category not between", value1, value2, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andManagerNumberIsNull() {
            addCriterion("manager_number is null");
            return (Criteria) this;
        }

        public Criteria andManagerNumberIsNotNull() {
            addCriterion("manager_number is not null");
            return (Criteria) this;
        }

        public Criteria andManagerNumberEqualTo(String value) {
            addCriterion("manager_number =", value, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberNotEqualTo(String value) {
            addCriterion("manager_number <>", value, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberGreaterThan(String value) {
            addCriterion("manager_number >", value, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberGreaterThanOrEqualTo(String value) {
            addCriterion("manager_number >=", value, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberLessThan(String value) {
            addCriterion("manager_number <", value, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberLessThanOrEqualTo(String value) {
            addCriterion("manager_number <=", value, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberLike(String value) {
            addCriterion("manager_number like", value, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberNotLike(String value) {
            addCriterion("manager_number not like", value, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberIn(List<String> values) {
            addCriterion("manager_number in", values, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberNotIn(List<String> values) {
            addCriterion("manager_number not in", values, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberBetween(String value1, String value2) {
            addCriterion("manager_number between", value1, value2, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andManagerNumberNotBetween(String value1, String value2) {
            addCriterion("manager_number not between", value1, value2, "managerNumber");
            return (Criteria) this;
        }

        public Criteria andWebsiteIsNull() {
            addCriterion("website is null");
            return (Criteria) this;
        }

        public Criteria andWebsiteIsNotNull() {
            addCriterion("website is not null");
            return (Criteria) this;
        }

        public Criteria andWebsiteEqualTo(String value) {
            addCriterion("website =", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteNotEqualTo(String value) {
            addCriterion("website <>", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteGreaterThan(String value) {
            addCriterion("website >", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteGreaterThanOrEqualTo(String value) {
            addCriterion("website >=", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteLessThan(String value) {
            addCriterion("website <", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteLessThanOrEqualTo(String value) {
            addCriterion("website <=", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteLike(String value) {
            addCriterion("website like", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteNotLike(String value) {
            addCriterion("website not like", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteIn(List<String> values) {
            addCriterion("website in", values, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteNotIn(List<String> values) {
            addCriterion("website not in", values, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteBetween(String value1, String value2) {
            addCriterion("website between", value1, value2, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteNotBetween(String value1, String value2) {
            addCriterion("website not between", value1, value2, "website");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureIsNull() {
            addCriterion("region_feature is null");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureIsNotNull() {
            addCriterion("region_feature is not null");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureEqualTo(String value) {
            addCriterion("region_feature =", value, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureNotEqualTo(String value) {
            addCriterion("region_feature <>", value, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureGreaterThan(String value) {
            addCriterion("region_feature >", value, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureGreaterThanOrEqualTo(String value) {
            addCriterion("region_feature >=", value, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureLessThan(String value) {
            addCriterion("region_feature <", value, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureLessThanOrEqualTo(String value) {
            addCriterion("region_feature <=", value, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureLike(String value) {
            addCriterion("region_feature like", value, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureNotLike(String value) {
            addCriterion("region_feature not like", value, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureIn(List<String> values) {
            addCriterion("region_feature in", values, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureNotIn(List<String> values) {
            addCriterion("region_feature not in", values, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureBetween(String value1, String value2) {
            addCriterion("region_feature between", value1, value2, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andRegionFeatureNotBetween(String value1, String value2) {
            addCriterion("region_feature not between", value1, value2, "regionFeature");
            return (Criteria) this;
        }

        public Criteria andOwnershipIsNull() {
            addCriterion("ownership is null");
            return (Criteria) this;
        }

        public Criteria andOwnershipIsNotNull() {
            addCriterion("ownership is not null");
            return (Criteria) this;
        }

        public Criteria andOwnershipEqualTo(String value) {
            addCriterion("ownership =", value, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipNotEqualTo(String value) {
            addCriterion("ownership <>", value, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipGreaterThan(String value) {
            addCriterion("ownership >", value, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipGreaterThanOrEqualTo(String value) {
            addCriterion("ownership >=", value, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipLessThan(String value) {
            addCriterion("ownership <", value, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipLessThanOrEqualTo(String value) {
            addCriterion("ownership <=", value, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipLike(String value) {
            addCriterion("ownership like", value, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipNotLike(String value) {
            addCriterion("ownership not like", value, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipIn(List<String> values) {
            addCriterion("ownership in", values, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipNotIn(List<String> values) {
            addCriterion("ownership not in", values, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipBetween(String value1, String value2) {
            addCriterion("ownership between", value1, value2, "ownership");
            return (Criteria) this;
        }

        public Criteria andOwnershipNotBetween(String value1, String value2) {
            addCriterion("ownership not between", value1, value2, "ownership");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeIsNull() {
            addCriterion("organization_code is null");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeIsNotNull() {
            addCriterion("organization_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeEqualTo(String value) {
            addCriterion("organization_code =", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeNotEqualTo(String value) {
            addCriterion("organization_code <>", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeGreaterThan(String value) {
            addCriterion("organization_code >", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeGreaterThanOrEqualTo(String value) {
            addCriterion("organization_code >=", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeLessThan(String value) {
            addCriterion("organization_code <", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeLessThanOrEqualTo(String value) {
            addCriterion("organization_code <=", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeLike(String value) {
            addCriterion("organization_code like", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeNotLike(String value) {
            addCriterion("organization_code not like", value, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeIn(List<String> values) {
            addCriterion("organization_code in", values, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeNotIn(List<String> values) {
            addCriterion("organization_code not in", values, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeBetween(String value1, String value2) {
            addCriterion("organization_code between", value1, value2, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andOrganizationCodeNotBetween(String value1, String value2) {
            addCriterion("organization_code not between", value1, value2, "organizationCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeIsNull() {
            addCriterion("post_code is null");
            return (Criteria) this;
        }

        public Criteria andPostCodeIsNotNull() {
            addCriterion("post_code is not null");
            return (Criteria) this;
        }

        public Criteria andPostCodeEqualTo(String value) {
            addCriterion("post_code =", value, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeNotEqualTo(String value) {
            addCriterion("post_code <>", value, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeGreaterThan(String value) {
            addCriterion("post_code >", value, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeGreaterThanOrEqualTo(String value) {
            addCriterion("post_code >=", value, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeLessThan(String value) {
            addCriterion("post_code <", value, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeLessThanOrEqualTo(String value) {
            addCriterion("post_code <=", value, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeLike(String value) {
            addCriterion("post_code like", value, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeNotLike(String value) {
            addCriterion("post_code not like", value, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeIn(List<String> values) {
            addCriterion("post_code in", values, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeNotIn(List<String> values) {
            addCriterion("post_code not in", values, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeBetween(String value1, String value2) {
            addCriterion("post_code between", value1, value2, "postCode");
            return (Criteria) this;
        }

        public Criteria andPostCodeNotBetween(String value1, String value2) {
            addCriterion("post_code not between", value1, value2, "postCode");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(Date value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(Date value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(Date value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(Date value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<Date> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<Date> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeToYYYYMMDDEqualTo(String value) {
            addCriterion("toYYYYMMDD(sample_time) =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}