package com.cmiot.report.bean;


public class GatewayOnOfflineMiniCount {
    private long gdate;

    private String gtype;

    private long count;


    public long getGdate() {
        return gdate;
    }

    public void setGdate(long gdate) {
        this.gdate = gdate;
    }

    public String getGtype() {
        return gtype;
    }

    public void setGtype(String gtype) {
        this.gtype = gtype;
    }

    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }
}
