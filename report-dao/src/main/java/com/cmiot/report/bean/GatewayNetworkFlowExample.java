package com.cmiot.report.bean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GatewayNetworkFlowExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GatewayNetworkFlowExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNull() {
            addCriterion("industry is null");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNotNull() {
            addCriterion("industry is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryEqualTo(String value) {
            addCriterion("industry =", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotEqualTo(String value) {
            addCriterion("industry <>", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThan(String value) {
            addCriterion("industry >", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThanOrEqualTo(String value) {
            addCriterion("industry >=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThan(String value) {
            addCriterion("industry <", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThanOrEqualTo(String value) {
            addCriterion("industry <=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLike(String value) {
            addCriterion("industry like", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotLike(String value) {
            addCriterion("industry not like", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryIn(List<String> values) {
            addCriterion("industry in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotIn(List<String> values) {
            addCriterion("industry not in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryBetween(String value1, String value2) {
            addCriterion("industry between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotBetween(String value1, String value2) {
            addCriterion("industry not between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andValueCategoryIsNull() {
            addCriterion("value_category is null");
            return (Criteria) this;
        }

        public Criteria andValueCategoryIsNotNull() {
            addCriterion("value_category is not null");
            return (Criteria) this;
        }

        public Criteria andValueCategoryEqualTo(String value) {
            addCriterion("value_category =", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotEqualTo(String value) {
            addCriterion("value_category <>", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryGreaterThan(String value) {
            addCriterion("value_category >", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("value_category >=", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryLessThan(String value) {
            addCriterion("value_category <", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryLessThanOrEqualTo(String value) {
            addCriterion("value_category <=", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryLike(String value) {
            addCriterion("value_category like", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotLike(String value) {
            addCriterion("value_category not like", value, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryIn(List<String> values) {
            addCriterion("value_category in", values, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotIn(List<String> values) {
            addCriterion("value_category not in", values, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryBetween(String value1, String value2) {
            addCriterion("value_category between", value1, value2, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andValueCategoryNotBetween(String value1, String value2) {
            addCriterion("value_category not between", value1, value2, "valueCategory");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeIsNull() {
            addCriterion("bussiness_type is null");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeIsNotNull() {
            addCriterion("bussiness_type is not null");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeEqualTo(String value) {
            addCriterion("bussiness_type =", value, "bussinessType");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeNotEqualTo(String value) {
            addCriterion("bussiness_type <>", value, "bussinessType");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeGreaterThan(String value) {
            addCriterion("bussiness_type >", value, "bussinessType");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bussiness_type >=", value, "bussinessType");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeLessThan(String value) {
            addCriterion("bussiness_type <", value, "bussinessType");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeLessThanOrEqualTo(String value) {
            addCriterion("bussiness_type <=", value, "bussinessType");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeIn(List<String> values) {
            addCriterion("bussiness_type in", values, "bussinessType");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeNotIn(List<String> values) {
            addCriterion("bussiness_type not in", values, "bussinessType");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeBetween(String value1, String value2) {
            addCriterion("bussiness_type between", value1, value2, "bussinessType");
            return (Criteria) this;
        }

        public Criteria andBussinessTypeNotBetween(String value1, String value2) {
            addCriterion("bussiness_type not between", value1, value2, "bussinessType");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllIsNull() {
            addCriterion("bandwidth_all is null");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllIsNotNull() {
            addCriterion("bandwidth_all is not null");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllEqualTo(BigDecimal value) {
            addCriterion("bandwidth_all =", value, "bandwidthAll");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllNotEqualTo(BigDecimal value) {
            addCriterion("bandwidth_all <>", value, "bandwidthAll");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllGreaterThan(BigDecimal value) {
            addCriterion("bandwidth_all >", value, "bandwidthAll");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bandwidth_all >=", value, "bandwidthAll");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllLessThan(BigDecimal value) {
            addCriterion("bandwidth_all <", value, "bandwidthAll");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bandwidth_all <=", value, "bandwidthAll");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllIn(List<BigDecimal> values) {
            addCriterion("bandwidth_all in", values, "bandwidthAll");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllNotIn(List<BigDecimal> values) {
            addCriterion("bandwidth_all not in", values, "bandwidthAll");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bandwidth_all between", value1, value2, "bandwidthAll");
            return (Criteria) this;
        }

        public Criteria andBandwidthAllNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bandwidth_all not between", value1, value2, "bandwidthAll");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateIsNull() {
            addCriterion("total_txrate is null");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateIsNotNull() {
            addCriterion("total_txrate is not null");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateEqualTo(BigDecimal value) {
            addCriterion("total_txrate =", value, "totalTxrate");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateNotEqualTo(BigDecimal value) {
            addCriterion("total_txrate <>", value, "totalTxrate");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateGreaterThan(BigDecimal value) {
            addCriterion("total_txrate >", value, "totalTxrate");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_txrate >=", value, "totalTxrate");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateLessThan(BigDecimal value) {
            addCriterion("total_txrate <", value, "totalTxrate");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_txrate <=", value, "totalTxrate");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateIn(List<BigDecimal> values) {
            addCriterion("total_txrate in", values, "totalTxrate");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateNotIn(List<BigDecimal> values) {
            addCriterion("total_txrate not in", values, "totalTxrate");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_txrate between", value1, value2, "totalTxrate");
            return (Criteria) this;
        }

        public Criteria andTotalTxrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_txrate not between", value1, value2, "totalTxrate");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateIsNull() {
            addCriterion("total_rxrate is null");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateIsNotNull() {
            addCriterion("total_rxrate is not null");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateEqualTo(BigDecimal value) {
            addCriterion("total_rxrate =", value, "totalRxrate");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateNotEqualTo(BigDecimal value) {
            addCriterion("total_rxrate <>", value, "totalRxrate");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateGreaterThan(BigDecimal value) {
            addCriterion("total_rxrate >", value, "totalRxrate");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_rxrate >=", value, "totalRxrate");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateLessThan(BigDecimal value) {
            addCriterion("total_rxrate <", value, "totalRxrate");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_rxrate <=", value, "totalRxrate");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateIn(List<BigDecimal> values) {
            addCriterion("total_rxrate in", values, "totalRxrate");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateNotIn(List<BigDecimal> values) {
            addCriterion("total_rxrate not in", values, "totalRxrate");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_rxrate between", value1, value2, "totalRxrate");
            return (Criteria) this;
        }

        public Criteria andTotalRxrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_rxrate not between", value1, value2, "totalRxrate");
            return (Criteria) this;
        }

        public Criteria andTotalFlowIsNull() {
            addCriterion("total_flow is null");
            return (Criteria) this;
        }

        public Criteria andTotalFlowIsNotNull() {
            addCriterion("total_flow is not null");
            return (Criteria) this;
        }

        public Criteria andTotalFlowEqualTo(BigDecimal value) {
            addCriterion("total_flow =", value, "totalFlow");
            return (Criteria) this;
        }

        public Criteria andTotalFlowNotEqualTo(BigDecimal value) {
            addCriterion("total_flow <>", value, "totalFlow");
            return (Criteria) this;
        }

        public Criteria andTotalFlowGreaterThan(BigDecimal value) {
            addCriterion("total_flow >", value, "totalFlow");
            return (Criteria) this;
        }

        public Criteria andTotalFlowGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_flow >=", value, "totalFlow");
            return (Criteria) this;
        }

        public Criteria andTotalFlowLessThan(BigDecimal value) {
            addCriterion("total_flow <", value, "totalFlow");
            return (Criteria) this;
        }

        public Criteria andTotalFlowLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_flow <=", value, "totalFlow");
            return (Criteria) this;
        }

        public Criteria andTotalFlowIn(List<BigDecimal> values) {
            addCriterion("total_flow in", values, "totalFlow");
            return (Criteria) this;
        }

        public Criteria andTotalFlowNotIn(List<BigDecimal> values) {
            addCriterion("total_flow not in", values, "totalFlow");
            return (Criteria) this;
        }

        public Criteria andTotalFlowBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_flow between", value1, value2, "totalFlow");
            return (Criteria) this;
        }

        public Criteria andTotalFlowNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_flow not between", value1, value2, "totalFlow");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andGdateIsNull() {
            addCriterion("gdate is null");
            return (Criteria) this;
        }

        public Criteria andGdateIsNotNull() {
            addCriterion("gdate is not null");
            return (Criteria) this;
        }

        public Criteria andGdateEqualTo(Long value) {
            addCriterion("gdate =", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotEqualTo(Long value) {
            addCriterion("gdate <>", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThan(Long value) {
            addCriterion("gdate >", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThanOrEqualTo(Long value) {
            addCriterion("gdate >=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThan(Long value) {
            addCriterion("gdate <", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThanOrEqualTo(Long value) {
            addCriterion("gdate <=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateIn(List<Long> values) {
            addCriterion("gdate in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotIn(List<Long> values) {
            addCriterion("gdate not in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateBetween(Long value1, Long value2) {
            addCriterion("gdate between", value1, value2, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotBetween(Long value1, Long value2) {
            addCriterion("gdate not between", value1, value2, "gdate");
            return (Criteria) this;
        }

        public Criteria andTimeHourEqualTo(Long value) {
            addCriterion("time_hour =", value, "time_hour");
            return (Criteria) this;
        }
    }



    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}