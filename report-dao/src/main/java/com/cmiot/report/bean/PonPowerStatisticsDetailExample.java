package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PonPowerStatisticsDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PonPowerStatisticsDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIsNull() {
            addCriterion("gateway_sn is null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIsNotNull() {
            addCriterion("gateway_sn is not null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnEqualTo(String value) {
            addCriterion("gateway_sn =", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotEqualTo(String value) {
            addCriterion("gateway_sn <>", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThan(String value) {
            addCriterion("gateway_sn >", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_sn >=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThan(String value) {
            addCriterion("gateway_sn <", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThanOrEqualTo(String value) {
            addCriterion("gateway_sn <=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLike(String value) {
            addCriterion("gateway_sn like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotLike(String value) {
            addCriterion("gateway_sn not like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIn(List<String> values) {
            addCriterion("gateway_sn in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotIn(List<String> values) {
            addCriterion("gateway_sn not in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnBetween(String value1, String value2) {
            addCriterion("gateway_sn between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotBetween(String value1, String value2) {
            addCriterion("gateway_sn not between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIsNull() {
            addCriterion("factory_id is null");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIsNotNull() {
            addCriterion("factory_id is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryIdEqualTo(Long value) {
            addCriterion("factory_id =", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotEqualTo(Long value) {
            addCriterion("factory_id <>", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdGreaterThan(Long value) {
            addCriterion("factory_id >", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("factory_id >=", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdLessThan(Long value) {
            addCriterion("factory_id <", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdLessThanOrEqualTo(Long value) {
            addCriterion("factory_id <=", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIn(List<Long> values) {
            addCriterion("factory_id in", values, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotIn(List<Long> values) {
            addCriterion("factory_id not in", values, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdBetween(Long value1, Long value2) {
            addCriterion("factory_id between", value1, value2, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotBetween(Long value1, Long value2) {
            addCriterion("factory_id not between", value1, value2, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIsNull() {
            addCriterion("factory_name is null");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIsNotNull() {
            addCriterion("factory_name is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryNameEqualTo(String value) {
            addCriterion("factory_name =", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotEqualTo(String value) {
            addCriterion("factory_name <>", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameGreaterThan(String value) {
            addCriterion("factory_name >", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("factory_name >=", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLessThan(String value) {
            addCriterion("factory_name <", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLessThanOrEqualTo(String value) {
            addCriterion("factory_name <=", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLike(String value) {
            addCriterion("factory_name like", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotLike(String value) {
            addCriterion("factory_name not like", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIn(List<String> values) {
            addCriterion("factory_name in", values, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotIn(List<String> values) {
            addCriterion("factory_name not in", values, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameBetween(String value1, String value2) {
            addCriterion("factory_name between", value1, value2, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotBetween(String value1, String value2) {
            addCriterion("factory_name not between", value1, value2, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdIsNull() {
            addCriterion("factory_model_id is null");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdIsNotNull() {
            addCriterion("factory_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdEqualTo(Long value) {
            addCriterion("factory_model_id =", value, "factoryModelId");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdNotEqualTo(Long value) {
            addCriterion("factory_model_id <>", value, "factoryModelId");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdGreaterThan(Long value) {
            addCriterion("factory_model_id >", value, "factoryModelId");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("factory_model_id >=", value, "factoryModelId");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdLessThan(Long value) {
            addCriterion("factory_model_id <", value, "factoryModelId");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdLessThanOrEqualTo(Long value) {
            addCriterion("factory_model_id <=", value, "factoryModelId");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdIn(List<Long> values) {
            addCriterion("factory_model_id in", values, "factoryModelId");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdNotIn(List<Long> values) {
            addCriterion("factory_model_id not in", values, "factoryModelId");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdBetween(Long value1, Long value2) {
            addCriterion("factory_model_id between", value1, value2, "factoryModelId");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIdNotBetween(Long value1, Long value2) {
            addCriterion("factory_model_id not between", value1, value2, "factoryModelId");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIsNull() {
            addCriterion("factory_model is null");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIsNotNull() {
            addCriterion("factory_model is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryModelEqualTo(String value) {
            addCriterion("factory_model =", value, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelNotEqualTo(String value) {
            addCriterion("factory_model <>", value, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelGreaterThan(String value) {
            addCriterion("factory_model >", value, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelGreaterThanOrEqualTo(String value) {
            addCriterion("factory_model >=", value, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelLessThan(String value) {
            addCriterion("factory_model <", value, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelLessThanOrEqualTo(String value) {
            addCriterion("factory_model <=", value, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelLike(String value) {
            addCriterion("factory_model like", value, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelNotLike(String value) {
            addCriterion("factory_model not like", value, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelIn(List<String> values) {
            addCriterion("factory_model in", values, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelNotIn(List<String> values) {
            addCriterion("factory_model not in", values, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelBetween(String value1, String value2) {
            addCriterion("factory_model between", value1, value2, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andFactoryModelNotBetween(String value1, String value2) {
            addCriterion("factory_model not between", value1, value2, "factoryModel");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrIsNull() {
            addCriterion("runing_time_curr is null");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrIsNotNull() {
            addCriterion("runing_time_curr is not null");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrEqualTo(Long value) {
            addCriterion("runing_time_curr =", value, "runingTimeCurr");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrNotEqualTo(Long value) {
            addCriterion("runing_time_curr <>", value, "runingTimeCurr");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrGreaterThan(Long value) {
            addCriterion("runing_time_curr >", value, "runingTimeCurr");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrGreaterThanOrEqualTo(Long value) {
            addCriterion("runing_time_curr >=", value, "runingTimeCurr");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrLessThan(Long value) {
            addCriterion("runing_time_curr <", value, "runingTimeCurr");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrLessThanOrEqualTo(Long value) {
            addCriterion("runing_time_curr <=", value, "runingTimeCurr");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrIn(List<Long> values) {
            addCriterion("runing_time_curr in", values, "runingTimeCurr");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrNotIn(List<Long> values) {
            addCriterion("runing_time_curr not in", values, "runingTimeCurr");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrBetween(Long value1, Long value2) {
            addCriterion("runing_time_curr between", value1, value2, "runingTimeCurr");
            return (Criteria) this;
        }

        public Criteria andRuningTimeCurrNotBetween(Long value1, Long value2) {
            addCriterion("runing_time_curr not between", value1, value2, "runingTimeCurr");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumIsNull() {
            addCriterion("pon_rx_power_curr_sum is null");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumIsNotNull() {
            addCriterion("pon_rx_power_curr_sum is not null");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumEqualTo(Integer value) {
            addCriterion("pon_rx_power_curr_sum =", value, "ponRxPowerCurrSum");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumNotEqualTo(Integer value) {
            addCriterion("pon_rx_power_curr_sum <>", value, "ponRxPowerCurrSum");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumGreaterThan(Integer value) {
            addCriterion("pon_rx_power_curr_sum >", value, "ponRxPowerCurrSum");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumGreaterThanOrEqualTo(Integer value) {
            addCriterion("pon_rx_power_curr_sum >=", value, "ponRxPowerCurrSum");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumLessThan(Integer value) {
            addCriterion("pon_rx_power_curr_sum <", value, "ponRxPowerCurrSum");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumLessThanOrEqualTo(Integer value) {
            addCriterion("pon_rx_power_curr_sum <=", value, "ponRxPowerCurrSum");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumIn(List<Integer> values) {
            addCriterion("pon_rx_power_curr_sum in", values, "ponRxPowerCurrSum");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumNotIn(List<Integer> values) {
            addCriterion("pon_rx_power_curr_sum not in", values, "ponRxPowerCurrSum");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumBetween(Integer value1, Integer value2) {
            addCriterion("pon_rx_power_curr_sum between", value1, value2, "ponRxPowerCurrSum");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrSumNotBetween(Integer value1, Integer value2) {
            addCriterion("pon_rx_power_curr_sum not between", value1, value2, "ponRxPowerCurrSum");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountIsNull() {
            addCriterion("pon_rx_power_curr_count is null");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountIsNotNull() {
            addCriterion("pon_rx_power_curr_count is not null");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountEqualTo(Integer value) {
            addCriterion("pon_rx_power_curr_count =", value, "ponRxPowerCurrCount");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountNotEqualTo(Integer value) {
            addCriterion("pon_rx_power_curr_count <>", value, "ponRxPowerCurrCount");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountGreaterThan(Integer value) {
            addCriterion("pon_rx_power_curr_count >", value, "ponRxPowerCurrCount");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("pon_rx_power_curr_count >=", value, "ponRxPowerCurrCount");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountLessThan(Integer value) {
            addCriterion("pon_rx_power_curr_count <", value, "ponRxPowerCurrCount");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountLessThanOrEqualTo(Integer value) {
            addCriterion("pon_rx_power_curr_count <=", value, "ponRxPowerCurrCount");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountIn(List<Integer> values) {
            addCriterion("pon_rx_power_curr_count in", values, "ponRxPowerCurrCount");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountNotIn(List<Integer> values) {
            addCriterion("pon_rx_power_curr_count not in", values, "ponRxPowerCurrCount");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountBetween(Integer value1, Integer value2) {
            addCriterion("pon_rx_power_curr_count between", value1, value2, "ponRxPowerCurrCount");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrCountNotBetween(Integer value1, Integer value2) {
            addCriterion("pon_rx_power_curr_count not between", value1, value2, "ponRxPowerCurrCount");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeIsNull() {
            addCriterion("pon_rx_power_curr_type is null");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeIsNotNull() {
            addCriterion("pon_rx_power_curr_type is not null");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeEqualTo(Byte value) {
            addCriterion("pon_rx_power_curr_type =", value, "ponRxPowerCurrType");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeNotEqualTo(Byte value) {
            addCriterion("pon_rx_power_curr_type <>", value, "ponRxPowerCurrType");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeGreaterThan(Byte value) {
            addCriterion("pon_rx_power_curr_type >", value, "ponRxPowerCurrType");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("pon_rx_power_curr_type >=", value, "ponRxPowerCurrType");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeLessThan(Byte value) {
            addCriterion("pon_rx_power_curr_type <", value, "ponRxPowerCurrType");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeLessThanOrEqualTo(Byte value) {
            addCriterion("pon_rx_power_curr_type <=", value, "ponRxPowerCurrType");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeIn(List<Byte> values) {
            addCriterion("pon_rx_power_curr_type in", values, "ponRxPowerCurrType");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeNotIn(List<Byte> values) {
            addCriterion("pon_rx_power_curr_type not in", values, "ponRxPowerCurrType");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeBetween(Byte value1, Byte value2) {
            addCriterion("pon_rx_power_curr_type between", value1, value2, "ponRxPowerCurrType");
            return (Criteria) this;
        }

        public Criteria andPonRxPowerCurrTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("pon_rx_power_curr_type not between", value1, value2, "ponRxPowerCurrType");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andGdateIsNull() {
            addCriterion("gdate is null");
            return (Criteria) this;
        }

        public Criteria andGdateIsNotNull() {
            addCriterion("gdate is not null");
            return (Criteria) this;
        }

        public Criteria andGdateEqualTo(Long value) {
            addCriterion("gdate =", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotEqualTo(Long value) {
            addCriterion("gdate <>", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThan(Long value) {
            addCriterion("gdate >", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThanOrEqualTo(Long value) {
            addCriterion("gdate >=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThan(Long value) {
            addCriterion("gdate <", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThanOrEqualTo(Long value) {
            addCriterion("gdate <=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateIn(List<Long> values) {
            addCriterion("gdate in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotIn(List<Long> values) {
            addCriterion("gdate not in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateBetween(Long value1, Long value2) {
            addCriterion("gdate between", value1, value2, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotBetween(Long value1, Long value2) {
            addCriterion("gdate not between", value1, value2, "gdate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}