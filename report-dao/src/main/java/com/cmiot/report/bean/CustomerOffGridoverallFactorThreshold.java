package com.cmiot.report.bean;

import lombok.ToString;

import java.util.Date;

@ToString
public class CustomerOffGridoverallFactorThreshold {
    private Long timestamp;

    private Integer factorThreshold;

    private Date sampleTime;

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Integer getFactorThreshold() {
        return factorThreshold;
    }

    public void setFactorThreshold(Integer factorThreshold) {
        this.factorThreshold = factorThreshold;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }
}