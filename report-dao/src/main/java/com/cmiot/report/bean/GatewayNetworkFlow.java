package com.cmiot.report.bean;

import java.math.BigDecimal;
import java.util.Date;

public class GatewayNetworkFlow {
    private String provinceCode;

    private String cityCode;

    private String industry;

    private String valueCategory;

    private String bussinessType;

    private BigDecimal bandwidthAll;

    private BigDecimal totalTxrate;

    private BigDecimal totalRxrate;

    private BigDecimal totalFlow;

    private Date sampleTime;

    private Long gdate;

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry == null ? null : industry.trim();
    }

    public String getValueCategory() {
        return valueCategory;
    }

    public void setValueCategory(String valueCategory) {
        this.valueCategory = valueCategory == null ? null : valueCategory.trim();
    }

    public String getBussinessType() {
        return bussinessType;
    }

    public void setBussinessType(String bussinessType) {
        this.bussinessType = bussinessType;
    }

    public BigDecimal getBandwidthAll() {
        return bandwidthAll;
    }

    public void setBandwidthAll(BigDecimal bandwidthAll) {
        this.bandwidthAll = bandwidthAll;
    }

    public BigDecimal getTotalTxrate() {
        return totalTxrate;
    }

    public void setTotalTxrate(BigDecimal totalTxrate) {
        this.totalTxrate = totalTxrate;
    }

    public BigDecimal getTotalRxrate() {
        return totalRxrate;
    }

    public void setTotalRxrate(BigDecimal totalRxrate) {
        this.totalRxrate = totalRxrate;
    }

    public BigDecimal getTotalFlow() {
        return totalFlow;
    }

    public void setTotalFlow(BigDecimal totalFlow) {
        this.totalFlow = totalFlow;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }

    public Long getGdate() {
        return gdate;
    }

    public void setGdate(Long gdate) {
        this.gdate = gdate;
    }
}