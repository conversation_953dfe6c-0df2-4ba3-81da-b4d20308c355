package com.cmiot.report.bean;

import java.util.Date;

public class GatewayRebootDetail {
    private Date sampleDate;

    private String gatewaySn;

    private String gatewayMac;

    private Long factoryId;

    private Long deviceModelId;

    private Short gatewayModelInt;

    private String gatewayModel;

    private String gatewayVendor;

    private String gatewayProductclass;

    private String provinceCode;

    private String cityCode;

    private Long enterpriseId;

    private String customerId;

    private Date rebootTime;

    public Date getSampleDate() {
        return sampleDate;
    }

    public void setSampleDate(Date sampleDate) {
        this.sampleDate = sampleDate;
    }

    public String getGatewaySn() {
        return gatewaySn;
    }

    public void setGatewaySn(String gatewaySn) {
        this.gatewaySn = gatewaySn == null ? null : gatewaySn.trim();
    }

    public String getGatewayMac() {
        return gatewayMac;
    }

    public void setGatewayMac(String gatewayMac) {
        this.gatewayMac = gatewayMac == null ? null : gatewayMac.trim();
    }

    public Long getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Long factoryId) {
        this.factoryId = factoryId;
    }

    public Long getDeviceModelId() {
        return deviceModelId;
    }

    public void setDeviceModelId(Long deviceModelId) {
        this.deviceModelId = deviceModelId;
    }

    public Short getGatewayModelInt() {
        return gatewayModelInt;
    }

    public void setGatewayModelInt(Short gatewayModelInt) {
        this.gatewayModelInt = gatewayModelInt;
    }

    public String getGatewayModel() {
        return gatewayModel;
    }

    public void setGatewayModel(String gatewayModel) {
        this.gatewayModel = gatewayModel == null ? null : gatewayModel.trim();
    }

    public String getGatewayVendor() {
        return gatewayVendor;
    }

    public void setGatewayVendor(String gatewayVendor) {
        this.gatewayVendor = gatewayVendor == null ? null : gatewayVendor.trim();
    }

    public String getGatewayProductclass() {
        return gatewayProductclass;
    }

    public void setGatewayProductclass(String gatewayProductclass) {
        this.gatewayProductclass = gatewayProductclass == null ? null : gatewayProductclass.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public Date getRebootTime() {
        return rebootTime;
    }

    public void setRebootTime(Date rebootTime) {
        this.rebootTime = rebootTime;
    }
}