package com.cmiot.report.bean;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

public class CustomerUneffect {
    private String provinceCode;
    private String cityCode;
    private String areaName;
    private String customerName;
    private String contactPhone;
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date packageUneffectTime;
    private Integer uneffectDays;

    //订购实例、集团客户标识、企业联系人电话、客户经理联系电话

    private String businessProductId;
    private String customerId;
    private String managerNumber;
    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public Date getPackageUneffectTime() {
        return packageUneffectTime;
    }

    public void setPackageUneffectTime(Date packageUneffectTime) {
        this.packageUneffectTime = packageUneffectTime;
    }

    public Integer getUneffectDays() {
        return uneffectDays;
    }

    public void setUneffectDays(Integer uneffectDays) {
        this.uneffectDays = uneffectDays;
    }

    public String getBusinessProductId() {
        return businessProductId;
    }

    public void setBusinessProductId(String businessProductId) {
        this.businessProductId = businessProductId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getManagerNumber() {
        return managerNumber;
    }

    public void setManagerNumber(String managerNumber) {
        this.managerNumber = managerNumber;
    }
}
