package com.cmiot.report.bean;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

@ToString
@Data
public class CustomerOffGridFactorThreshold {
    private Long timestamp;

    private Byte factorType;

    private String factorName;

    private Double factorWeight;

    private Integer factorThreshold;

    private Byte factorIndicateType;

    private String factorIndicateDesc;

    private Double factorIndicateWeight;

    private Date sampleTime;

    private Long gdate;
}