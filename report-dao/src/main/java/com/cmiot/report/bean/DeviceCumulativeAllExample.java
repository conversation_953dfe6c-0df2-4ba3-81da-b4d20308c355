package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DeviceCumulativeAllExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public DeviceCumulativeAllExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }


    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }


    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andGatewaySnIsNull() {
            addCriterion("gateway_sn is null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIsNotNull() {
            addCriterion("gateway_sn is not null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnEqualTo(String value) {
            addCriterion("gateway_sn =", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotEqualTo(String value) {
            addCriterion("gateway_sn <>", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThan(String value) {
            addCriterion("gateway_sn >", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_sn >=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThan(String value) {
            addCriterion("gateway_sn <", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThanOrEqualTo(String value) {
            addCriterion("gateway_sn <=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLike(String value) {
            addCriterion("gateway_sn like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotLike(String value) {
            addCriterion("gateway_sn not like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIn(List<String> values) {
            addCriterion("gateway_sn in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotIn(List<String> values) {
            addCriterion("gateway_sn not in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnBetween(String value1, String value2) {
            addCriterion("gateway_sn between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotBetween(String value1, String value2) {
            addCriterion("gateway_sn not between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIsNull() {
            addCriterion("gateway_mac is null");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIsNotNull() {
            addCriterion("gateway_mac is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayMacEqualTo(String value) {
            addCriterion("gateway_mac =", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotEqualTo(String value) {
            addCriterion("gateway_mac <>", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacGreaterThan(String value) {
            addCriterion("gateway_mac >", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_mac >=", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLessThan(String value) {
            addCriterion("gateway_mac <", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLessThanOrEqualTo(String value) {
            addCriterion("gateway_mac <=", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLike(String value) {
            addCriterion("gateway_mac like", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotLike(String value) {
            addCriterion("gateway_mac not like", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIn(List<String> values) {
            addCriterion("gateway_mac in", values, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotIn(List<String> values) {
            addCriterion("gateway_mac not in", values, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacBetween(String value1, String value2) {
            addCriterion("gateway_mac between", value1, value2, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotBetween(String value1, String value2) {
            addCriterion("gateway_mac not between", value1, value2, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIsNull() {
            addCriterion("factory_id is null");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIsNotNull() {
            addCriterion("factory_id is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryIdEqualTo(Long value) {
            addCriterion("factory_id =", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotEqualTo(Long value) {
            addCriterion("factory_id <>", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdGreaterThan(Long value) {
            addCriterion("factory_id >", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("factory_id >=", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdLessThan(Long value) {
            addCriterion("factory_id <", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdLessThanOrEqualTo(Long value) {
            addCriterion("factory_id <=", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIn(List<Long> values) {
            addCriterion("factory_id in", values, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotIn(List<Long> values) {
            addCriterion("factory_id not in", values, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdBetween(Long value1, Long value2) {
            addCriterion("factory_id between", value1, value2, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotBetween(Long value1, Long value2) {
            addCriterion("factory_id not between", value1, value2, "factoryId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNull() {
            addCriterion("gateway_vendor is null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNotNull() {
            addCriterion("gateway_vendor is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorEqualTo(String value) {
            addCriterion("gateway_vendor =", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotEqualTo(String value) {
            addCriterion("gateway_vendor <>", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThan(String value) {
            addCriterion("gateway_vendor >", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_vendor >=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThan(String value) {
            addCriterion("gateway_vendor <", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThanOrEqualTo(String value) {
            addCriterion("gateway_vendor <=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLike(String value) {
            addCriterion("gateway_vendor like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotLike(String value) {
            addCriterion("gateway_vendor not like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIn(List<String> values) {
            addCriterion("gateway_vendor in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotIn(List<String> values) {
            addCriterion("gateway_vendor not in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorBetween(String value1, String value2) {
            addCriterion("gateway_vendor between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotBetween(String value1, String value2) {
            addCriterion("gateway_vendor not between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIsNull() {
            addCriterion("device_model_id is null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIsNotNull() {
            addCriterion("device_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdEqualTo(Long value) {
            addCriterion("device_model_id =", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotEqualTo(Long value) {
            addCriterion("device_model_id <>", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdGreaterThan(Long value) {
            addCriterion("device_model_id >", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("device_model_id >=", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdLessThan(Long value) {
            addCriterion("device_model_id <", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdLessThanOrEqualTo(Long value) {
            addCriterion("device_model_id <=", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIn(List<Long> values) {
            addCriterion("device_model_id in", values, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotIn(List<Long> values) {
            addCriterion("device_model_id not in", values, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdBetween(Long value1, Long value2) {
            addCriterion("device_model_id between", value1, value2, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotBetween(Long value1, Long value2) {
            addCriterion("device_model_id not between", value1, value2, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIsNull() {
            addCriterion("gateway_productclass is null");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIsNotNull() {
            addCriterion("gateway_productclass is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassEqualTo(String value) {
            addCriterion("gateway_productclass =", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotEqualTo(String value) {
            addCriterion("gateway_productclass <>", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassGreaterThan(String value) {
            addCriterion("gateway_productclass >", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_productclass >=", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLessThan(String value) {
            addCriterion("gateway_productclass <", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLessThanOrEqualTo(String value) {
            addCriterion("gateway_productclass <=", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLike(String value) {
            addCriterion("gateway_productclass like", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotLike(String value) {
            addCriterion("gateway_productclass not like", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIn(List<String> values) {
            addCriterion("gateway_productclass in", values, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotIn(List<String> values) {
            addCriterion("gateway_productclass not in", values, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassBetween(String value1, String value2) {
            addCriterion("gateway_productclass between", value1, value2, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotBetween(String value1, String value2) {
            addCriterion("gateway_productclass not between", value1, value2, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNull() {
            addCriterion("enterprise_id is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNotNull() {
            addCriterion("enterprise_id is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdEqualTo(Long value) {
            addCriterion("enterprise_id =", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotEqualTo(Long value) {
            addCriterion("enterprise_id <>", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThan(Long value) {
            addCriterion("enterprise_id >", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("enterprise_id >=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThan(Long value) {
            addCriterion("enterprise_id <", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThanOrEqualTo(Long value) {
            addCriterion("enterprise_id <=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIn(List<Long> values) {
            addCriterion("enterprise_id in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotIn(List<Long> values) {
            addCriterion("enterprise_id not in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdBetween(Long value1, Long value2) {
            addCriterion("enterprise_id between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotBetween(Long value1, Long value2) {
            addCriterion("enterprise_id not between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(String value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(String value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(String value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(String value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(String value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(String value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLike(String value) {
            addCriterion("customer_id like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotLike(String value) {
            addCriterion("customer_id not like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<String> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<String> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(String value1, String value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(String value1, String value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andAccountIsNull() {
            addCriterion("account is null");
            return (Criteria) this;
        }

        public Criteria andAccountIsNotNull() {
            addCriterion("account is not null");
            return (Criteria) this;
        }

        public Criteria andAccountEqualTo(String value) {
            addCriterion("account =", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualTo(String value) {
            addCriterion("account <>", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThan(String value) {
            addCriterion("account >", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualTo(String value) {
            addCriterion("account >=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThan(String value) {
            addCriterion("account <", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualTo(String value) {
            addCriterion("account <=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLike(String value) {
            addCriterion("account like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotLike(String value) {
            addCriterion("account not like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountIn(List<String> values) {
            addCriterion("account in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotIn(List<String> values) {
            addCriterion("account not in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountBetween(String value1, String value2) {
            addCriterion("account between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotBetween(String value1, String value2) {
            addCriterion("account not between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andDeviceNameIsNull() {
            addCriterion("device_name is null");
            return (Criteria) this;
        }

        public Criteria andDeviceNameIsNotNull() {
            addCriterion("device_name is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceNameEqualTo(String value) {
            addCriterion("device_name =", value, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameNotEqualTo(String value) {
            addCriterion("device_name <>", value, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameGreaterThan(String value) {
            addCriterion("device_name >", value, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameGreaterThanOrEqualTo(String value) {
            addCriterion("device_name >=", value, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameLessThan(String value) {
            addCriterion("device_name <", value, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameLessThanOrEqualTo(String value) {
            addCriterion("device_name <=", value, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameLike(String value) {
            addCriterion("device_name like", value, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameNotLike(String value) {
            addCriterion("device_name not like", value, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameIn(List<String> values) {
            addCriterion("device_name in", values, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameNotIn(List<String> values) {
            addCriterion("device_name not in", values, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameBetween(String value1, String value2) {
            addCriterion("device_name between", value1, value2, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDeviceNameNotBetween(String value1, String value2) {
            addCriterion("device_name not between", value1, value2, "deviceName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameIsNull() {
            addCriterion("dhcp_name is null");
            return (Criteria) this;
        }

        public Criteria andDhcpNameIsNotNull() {
            addCriterion("dhcp_name is not null");
            return (Criteria) this;
        }

        public Criteria andDhcpNameEqualTo(String value) {
            addCriterion("dhcp_name =", value, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameNotEqualTo(String value) {
            addCriterion("dhcp_name <>", value, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameGreaterThan(String value) {
            addCriterion("dhcp_name >", value, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameGreaterThanOrEqualTo(String value) {
            addCriterion("dhcp_name >=", value, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameLessThan(String value) {
            addCriterion("dhcp_name <", value, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameLessThanOrEqualTo(String value) {
            addCriterion("dhcp_name <=", value, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameLike(String value) {
            addCriterion("dhcp_name like", value, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameNotLike(String value) {
            addCriterion("dhcp_name not like", value, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameIn(List<String> values) {
            addCriterion("dhcp_name in", values, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameNotIn(List<String> values) {
            addCriterion("dhcp_name not in", values, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameBetween(String value1, String value2) {
            addCriterion("dhcp_name between", value1, value2, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andDhcpNameNotBetween(String value1, String value2) {
            addCriterion("dhcp_name not between", value1, value2, "dhcpName");
            return (Criteria) this;
        }

        public Criteria andMacIsNull() {
            addCriterion("mac is null");
            return (Criteria) this;
        }

        public Criteria andMacIsNotNull() {
            addCriterion("mac is not null");
            return (Criteria) this;
        }

        public Criteria andMacEqualTo(String value) {
            addCriterion("mac =", value, "mac");
            return (Criteria) this;
        }

        public Criteria andMacNotEqualTo(String value) {
            addCriterion("mac <>", value, "mac");
            return (Criteria) this;
        }

        public Criteria andMacGreaterThan(String value) {
            addCriterion("mac >", value, "mac");
            return (Criteria) this;
        }

        public Criteria andMacGreaterThanOrEqualTo(String value) {
            addCriterion("mac >=", value, "mac");
            return (Criteria) this;
        }

        public Criteria andMacLessThan(String value) {
            addCriterion("mac <", value, "mac");
            return (Criteria) this;
        }

        public Criteria andMacLessThanOrEqualTo(String value) {
            addCriterion("mac <=", value, "mac");
            return (Criteria) this;
        }

        public Criteria andMacLike(String value) {
            addCriterion("mac like", value, "mac");
            return (Criteria) this;
        }

        public Criteria andMacNotLike(String value) {
            addCriterion("mac not like", value, "mac");
            return (Criteria) this;
        }

        public Criteria andMacIn(List<String> values) {
            addCriterion("mac in", values, "mac");
            return (Criteria) this;
        }

        public Criteria andMacNotIn(List<String> values) {
            addCriterion("mac not in", values, "mac");
            return (Criteria) this;
        }

        public Criteria andMacBetween(String value1, String value2) {
            addCriterion("mac between", value1, value2, "mac");
            return (Criteria) this;
        }

        public Criteria andMacNotBetween(String value1, String value2) {
            addCriterion("mac not between", value1, value2, "mac");
            return (Criteria) this;
        }

        public Criteria andWifiNameIsNull() {
            addCriterion("wifi_name is null");
            return (Criteria) this;
        }

        public Criteria andWifiNameIsNotNull() {
            addCriterion("wifi_name is not null");
            return (Criteria) this;
        }

        public Criteria andWifiNameEqualTo(String value) {
            addCriterion("wifi_name =", value, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameNotEqualTo(String value) {
            addCriterion("wifi_name <>", value, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameGreaterThan(String value) {
            addCriterion("wifi_name >", value, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameGreaterThanOrEqualTo(String value) {
            addCriterion("wifi_name >=", value, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameLessThan(String value) {
            addCriterion("wifi_name <", value, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameLessThanOrEqualTo(String value) {
            addCriterion("wifi_name <=", value, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameLike(String value) {
            addCriterion("wifi_name like", value, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameNotLike(String value) {
            addCriterion("wifi_name not like", value, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameIn(List<String> values) {
            addCriterion("wifi_name in", values, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameNotIn(List<String> values) {
            addCriterion("wifi_name not in", values, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameBetween(String value1, String value2) {
            addCriterion("wifi_name between", value1, value2, "wifiName");
            return (Criteria) this;
        }

        public Criteria andWifiNameNotBetween(String value1, String value2) {
            addCriterion("wifi_name not between", value1, value2, "wifiName");
            return (Criteria) this;
        }

        public Criteria andIpIsNull() {
            addCriterion("ip is null");
            return (Criteria) this;
        }

        public Criteria andIpIsNotNull() {
            addCriterion("ip is not null");
            return (Criteria) this;
        }

        public Criteria andIpEqualTo(String value) {
            addCriterion("ip =", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotEqualTo(String value) {
            addCriterion("ip <>", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpGreaterThan(String value) {
            addCriterion("ip >", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpGreaterThanOrEqualTo(String value) {
            addCriterion("ip >=", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpLessThan(String value) {
            addCriterion("ip <", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpLessThanOrEqualTo(String value) {
            addCriterion("ip <=", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpLike(String value) {
            addCriterion("ip like", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotLike(String value) {
            addCriterion("ip not like", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpIn(List<String> values) {
            addCriterion("ip in", values, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotIn(List<String> values) {
            addCriterion("ip not in", values, "ip");
            return (Criteria) this;
        }

        public Criteria andIpBetween(String value1, String value2) {
            addCriterion("ip between", value1, value2, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotBetween(String value1, String value2) {
            addCriterion("ip not between", value1, value2, "ip");
            return (Criteria) this;
        }

        public Criteria andIpv6IsNull() {
            addCriterion("ipv6 is null");
            return (Criteria) this;
        }

        public Criteria andIpv6IsNotNull() {
            addCriterion("ipv6 is not null");
            return (Criteria) this;
        }

        public Criteria andIpv6EqualTo(String value) {
            addCriterion("ipv6 =", value, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6NotEqualTo(String value) {
            addCriterion("ipv6 <>", value, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6GreaterThan(String value) {
            addCriterion("ipv6 >", value, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6GreaterThanOrEqualTo(String value) {
            addCriterion("ipv6 >=", value, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6LessThan(String value) {
            addCriterion("ipv6 <", value, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6LessThanOrEqualTo(String value) {
            addCriterion("ipv6 <=", value, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6Like(String value) {
            addCriterion("ipv6 like", value, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6NotLike(String value) {
            addCriterion("ipv6 not like", value, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6In(List<String> values) {
            addCriterion("ipv6 in", values, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6NotIn(List<String> values) {
            addCriterion("ipv6 not in", values, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6Between(String value1, String value2) {
            addCriterion("ipv6 between", value1, value2, "ipv6");
            return (Criteria) this;
        }

        public Criteria andIpv6NotBetween(String value1, String value2) {
            addCriterion("ipv6 not between", value1, value2, "ipv6");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeIsNull() {
            addCriterion("wlan_radio_type is null");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeIsNotNull() {
            addCriterion("wlan_radio_type is not null");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeEqualTo(String value) {
            addCriterion("wlan_radio_type =", value, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeNotEqualTo(String value) {
            addCriterion("wlan_radio_type <>", value, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeGreaterThan(String value) {
            addCriterion("wlan_radio_type >", value, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeGreaterThanOrEqualTo(String value) {
            addCriterion("wlan_radio_type >=", value, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeLessThan(String value) {
            addCriterion("wlan_radio_type <", value, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeLessThanOrEqualTo(String value) {
            addCriterion("wlan_radio_type <=", value, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeLike(String value) {
            addCriterion("wlan_radio_type like", value, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeNotLike(String value) {
            addCriterion("wlan_radio_type not like", value, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeIn(List<String> values) {
            addCriterion("wlan_radio_type in", values, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeNotIn(List<String> values) {
            addCriterion("wlan_radio_type not in", values, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeBetween(String value1, String value2) {
            addCriterion("wlan_radio_type between", value1, value2, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioTypeNotBetween(String value1, String value2) {
            addCriterion("wlan_radio_type not between", value1, value2, "wlanRadioType");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerIsNull() {
            addCriterion("wlan_radio_power is null");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerIsNotNull() {
            addCriterion("wlan_radio_power is not null");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerEqualTo(Integer value) {
            addCriterion("wlan_radio_power =", value, "wlanRadioPower");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerNotEqualTo(Integer value) {
            addCriterion("wlan_radio_power <>", value, "wlanRadioPower");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerGreaterThan(Integer value) {
            addCriterion("wlan_radio_power >", value, "wlanRadioPower");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerGreaterThanOrEqualTo(Integer value) {
            addCriterion("wlan_radio_power >=", value, "wlanRadioPower");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerLessThan(Integer value) {
            addCriterion("wlan_radio_power <", value, "wlanRadioPower");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerLessThanOrEqualTo(Integer value) {
            addCriterion("wlan_radio_power <=", value, "wlanRadioPower");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerIn(List<Integer> values) {
            addCriterion("wlan_radio_power in", values, "wlanRadioPower");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerNotIn(List<Integer> values) {
            addCriterion("wlan_radio_power not in", values, "wlanRadioPower");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerBetween(Integer value1, Integer value2) {
            addCriterion("wlan_radio_power between", value1, value2, "wlanRadioPower");
            return (Criteria) this;
        }

        public Criteria andWlanRadioPowerNotBetween(Integer value1, Integer value2) {
            addCriterion("wlan_radio_power not between", value1, value2, "wlanRadioPower");
            return (Criteria) this;
        }

        public Criteria andLanPortIsNull() {
            addCriterion("lan_port is null");
            return (Criteria) this;
        }

        public Criteria andLanPortIsNotNull() {
            addCriterion("lan_port is not null");
            return (Criteria) this;
        }

        public Criteria andLanPortEqualTo(String value) {
            addCriterion("lan_port =", value, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortNotEqualTo(String value) {
            addCriterion("lan_port <>", value, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortGreaterThan(String value) {
            addCriterion("lan_port >", value, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortGreaterThanOrEqualTo(String value) {
            addCriterion("lan_port >=", value, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortLessThan(String value) {
            addCriterion("lan_port <", value, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortLessThanOrEqualTo(String value) {
            addCriterion("lan_port <=", value, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortLike(String value) {
            addCriterion("lan_port like", value, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortNotLike(String value) {
            addCriterion("lan_port not like", value, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortIn(List<String> values) {
            addCriterion("lan_port in", values, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortNotIn(List<String> values) {
            addCriterion("lan_port not in", values, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortBetween(String value1, String value2) {
            addCriterion("lan_port between", value1, value2, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanPortNotBetween(String value1, String value2) {
            addCriterion("lan_port not between", value1, value2, "lanPort");
            return (Criteria) this;
        }

        public Criteria andLanBitRateIsNull() {
            addCriterion("lan_bit_rate is null");
            return (Criteria) this;
        }

        public Criteria andLanBitRateIsNotNull() {
            addCriterion("lan_bit_rate is not null");
            return (Criteria) this;
        }

        public Criteria andLanBitRateEqualTo(Long value) {
            addCriterion("lan_bit_rate =", value, "lanBitRate");
            return (Criteria) this;
        }

        public Criteria andLanBitRateNotEqualTo(Long value) {
            addCriterion("lan_bit_rate <>", value, "lanBitRate");
            return (Criteria) this;
        }

        public Criteria andLanBitRateGreaterThan(Long value) {
            addCriterion("lan_bit_rate >", value, "lanBitRate");
            return (Criteria) this;
        }

        public Criteria andLanBitRateGreaterThanOrEqualTo(Long value) {
            addCriterion("lan_bit_rate >=", value, "lanBitRate");
            return (Criteria) this;
        }

        public Criteria andLanBitRateLessThan(Long value) {
            addCriterion("lan_bit_rate <", value, "lanBitRate");
            return (Criteria) this;
        }

        public Criteria andLanBitRateLessThanOrEqualTo(Long value) {
            addCriterion("lan_bit_rate <=", value, "lanBitRate");
            return (Criteria) this;
        }

        public Criteria andLanBitRateIn(List<Long> values) {
            addCriterion("lan_bit_rate in", values, "lanBitRate");
            return (Criteria) this;
        }

        public Criteria andLanBitRateNotIn(List<Long> values) {
            addCriterion("lan_bit_rate not in", values, "lanBitRate");
            return (Criteria) this;
        }

        public Criteria andLanBitRateBetween(Long value1, Long value2) {
            addCriterion("lan_bit_rate between", value1, value2, "lanBitRate");
            return (Criteria) this;
        }

        public Criteria andLanBitRateNotBetween(Long value1, Long value2) {
            addCriterion("lan_bit_rate not between", value1, value2, "lanBitRate");
            return (Criteria) this;
        }

        public Criteria andFreqIsNull() {
            addCriterion("freq is null");
            return (Criteria) this;
        }

        public Criteria andFreqIsNotNull() {
            addCriterion("freq is not null");
            return (Criteria) this;
        }

        public Criteria andFreqEqualTo(Long value) {
            addCriterion("freq =", value, "freq");
            return (Criteria) this;
        }

        public Criteria andFreqNotEqualTo(Long value) {
            addCriterion("freq <>", value, "freq");
            return (Criteria) this;
        }

        public Criteria andFreqGreaterThan(Long value) {
            addCriterion("freq >", value, "freq");
            return (Criteria) this;
        }

        public Criteria andFreqGreaterThanOrEqualTo(Long value) {
            addCriterion("freq >=", value, "freq");
            return (Criteria) this;
        }

        public Criteria andFreqLessThan(Long value) {
            addCriterion("freq <", value, "freq");
            return (Criteria) this;
        }

        public Criteria andFreqLessThanOrEqualTo(Long value) {
            addCriterion("freq <=", value, "freq");
            return (Criteria) this;
        }

        public Criteria andFreqIn(List<Long> values) {
            addCriterion("freq in", values, "freq");
            return (Criteria) this;
        }

        public Criteria andFreqNotIn(List<Long> values) {
            addCriterion("freq not in", values, "freq");
            return (Criteria) this;
        }

        public Criteria andFreqBetween(Long value1, Long value2) {
            addCriterion("freq between", value1, value2, "freq");
            return (Criteria) this;
        }

        public Criteria andFreqNotBetween(Long value1, Long value2) {
            addCriterion("freq not between", value1, value2, "freq");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andLastTimeIsNull() {
            addCriterion("last_time is null");
            return (Criteria) this;
        }

        public Criteria andLastTimeIsNotNull() {
            addCriterion("last_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastTimeEqualTo(Date value) {
            addCriterion("last_time =", value, "lastTime");
            return (Criteria) this;
        }

        public Criteria andLastTimeNotEqualTo(Date value) {
            addCriterion("last_time <>", value, "lastTime");
            return (Criteria) this;
        }

        public Criteria andLastTimeGreaterThan(Date value) {
            addCriterion("last_time >", value, "lastTime");
            return (Criteria) this;
        }

        public Criteria andLastTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("last_time >=", value, "lastTime");
            return (Criteria) this;
        }

        public Criteria andLastTimeLessThan(Date value) {
            addCriterion("last_time <", value, "lastTime");
            return (Criteria) this;
        }

        public Criteria andLastTimeLessThanOrEqualTo(Date value) {
            addCriterion("last_time <=", value, "lastTime");
            return (Criteria) this;
        }

        public Criteria andLastTimeIn(List<Date> values) {
            addCriterion("last_time in", values, "lastTime");
            return (Criteria) this;
        }

        public Criteria andLastTimeNotIn(List<Date> values) {
            addCriterion("last_time not in", values, "lastTime");
            return (Criteria) this;
        }

        public Criteria andLastTimeBetween(Date value1, Date value2) {
            addCriterion("last_time between", value1, value2, "lastTime");
            return (Criteria) this;
        }

        public Criteria andLastTimeNotBetween(Date value1, Date value2) {
            addCriterion("last_time not between", value1, value2, "lastTime");
            return (Criteria) this;
        }

        public Criteria andPdateIsNull() {
            addCriterion("pdate is null");
            return (Criteria) this;
        }

        public Criteria andPdateIsNotNull() {
            addCriterion("pdate is not null");
            return (Criteria) this;
        }

        public Criteria andPdateEqualTo(Date value) {
            addCriterion("pdate =", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateNotEqualTo(Date value) {
            addCriterion("pdate <>", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateGreaterThan(Date value) {
            addCriterion("pdate >", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateGreaterThanOrEqualTo(Date value) {
            addCriterion("pdate >=", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateLessThan(Date value) {
            addCriterion("pdate <", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateLessThanOrEqualTo(Date value) {
            addCriterion("pdate <=", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateIn(List<Date> values) {
            addCriterion("pdate in", values, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateNotIn(List<Date> values) {
            addCriterion("pdate not in", values, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateBetween(Date value1, Date value2) {
            addCriterion("pdate between", value1, value2, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateNotBetween(Date value1, Date value2) {
            addCriterion("pdate not between", value1, value2, "pdate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}