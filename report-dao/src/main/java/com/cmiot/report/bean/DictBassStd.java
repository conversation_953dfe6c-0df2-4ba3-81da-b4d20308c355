package com.cmiot.report.bean;

public class DictBassStd {
    private Long id;

    private String bassType;

    private String code;

    private String fullCode;

    private String name;

    private String description;

    private Long cType;

    private String pCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBassType() {
        return bassType;
    }

    public void setBassType(String bassType) {
        this.bassType = bassType == null ? null : bassType.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getFullCode() {
        return fullCode;
    }

    public void setFullCode(String fullCode) {
        this.fullCode = fullCode == null ? null : fullCode.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Long getcType() {
        return cType;
    }

    public void setcType(Long cType) {
        this.cType = cType;
    }

    public String getpCode() {
        return pCode;
    }

    public void setpCode(String pCode) {
        this.pCode = pCode == null ? null : pCode.trim();
    }
}