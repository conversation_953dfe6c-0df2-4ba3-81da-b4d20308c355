package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.List;

public class DeviceCumCountExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DeviceCumCountExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPdateIsNull() {
            addCriterion("pdate is null");
            return (Criteria) this;
        }

        public Criteria andPdateIsNotNull() {
            addCriterion("pdate is not null");
            return (Criteria) this;
        }

        public Criteria andPdateEqualTo(String value) {
            addCriterion("pdate =", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateNotEqualTo(String value) {
            addCriterion("pdate <>", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateGreaterThan(String value) {
            addCriterion("pdate >", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateGreaterThanOrEqualTo(String value) {
            addCriterion("pdate >=", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateLessThan(String value) {
            addCriterion("pdate <", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateLessThanOrEqualTo(String value) {
            addCriterion("pdate <=", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateLike(String value) {
            addCriterion("pdate like", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateNotLike(String value) {
            addCriterion("pdate not like", value, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateIn(List<String> values) {
            addCriterion("pdate in", values, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateNotIn(List<String> values) {
            addCriterion("pdate not in", values, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateBetween(String value1, String value2) {
            addCriterion("pdate between", value1, value2, "pdate");
            return (Criteria) this;
        }

        public Criteria andPdateNotBetween(String value1, String value2) {
            addCriterion("pdate not between", value1, value2, "pdate");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNull() {
            addCriterion("gateway_vendor is null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNotNull() {
            addCriterion("gateway_vendor is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorEqualTo(String value) {
            addCriterion("gateway_vendor =", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotEqualTo(String value) {
            addCriterion("gateway_vendor <>", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThan(String value) {
            addCriterion("gateway_vendor >", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_vendor >=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThan(String value) {
            addCriterion("gateway_vendor <", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThanOrEqualTo(String value) {
            addCriterion("gateway_vendor <=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLike(String value) {
            addCriterion("gateway_vendor like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotLike(String value) {
            addCriterion("gateway_vendor not like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIn(List<String> values) {
            addCriterion("gateway_vendor in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotIn(List<String> values) {
            addCriterion("gateway_vendor not in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorBetween(String value1, String value2) {
            addCriterion("gateway_vendor between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotBetween(String value1, String value2) {
            addCriterion("gateway_vendor not between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountIsNull() {
            addCriterion("device_all_count is null");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountIsNotNull() {
            addCriterion("device_all_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountEqualTo(Long value) {
            addCriterion("device_all_count =", value, "deviceAllCount");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountNotEqualTo(Long value) {
            addCriterion("device_all_count <>", value, "deviceAllCount");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountGreaterThan(Long value) {
            addCriterion("device_all_count >", value, "deviceAllCount");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountGreaterThanOrEqualTo(Long value) {
            addCriterion("device_all_count >=", value, "deviceAllCount");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountLessThan(Long value) {
            addCriterion("device_all_count <", value, "deviceAllCount");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountLessThanOrEqualTo(Long value) {
            addCriterion("device_all_count <=", value, "deviceAllCount");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountIn(List<Long> values) {
            addCriterion("device_all_count in", values, "deviceAllCount");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountNotIn(List<Long> values) {
            addCriterion("device_all_count not in", values, "deviceAllCount");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountBetween(Long value1, Long value2) {
            addCriterion("device_all_count between", value1, value2, "deviceAllCount");
            return (Criteria) this;
        }

        public Criteria andDeviceAllCountNotBetween(Long value1, Long value2) {
            addCriterion("device_all_count not between", value1, value2, "deviceAllCount");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveIsNull() {
            addCriterion("device_day_alive is null");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveIsNotNull() {
            addCriterion("device_day_alive is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveEqualTo(Long value) {
            addCriterion("device_day_alive =", value, "deviceDayAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveNotEqualTo(Long value) {
            addCriterion("device_day_alive <>", value, "deviceDayAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveGreaterThan(Long value) {
            addCriterion("device_day_alive >", value, "deviceDayAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveGreaterThanOrEqualTo(Long value) {
            addCriterion("device_day_alive >=", value, "deviceDayAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveLessThan(Long value) {
            addCriterion("device_day_alive <", value, "deviceDayAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveLessThanOrEqualTo(Long value) {
            addCriterion("device_day_alive <=", value, "deviceDayAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveIn(List<Long> values) {
            addCriterion("device_day_alive in", values, "deviceDayAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveNotIn(List<Long> values) {
            addCriterion("device_day_alive not in", values, "deviceDayAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveBetween(Long value1, Long value2) {
            addCriterion("device_day_alive between", value1, value2, "deviceDayAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceDayAliveNotBetween(Long value1, Long value2) {
            addCriterion("device_day_alive not between", value1, value2, "deviceDayAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveIsNull() {
            addCriterion("device_week_alive is null");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveIsNotNull() {
            addCriterion("device_week_alive is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveEqualTo(Long value) {
            addCriterion("device_week_alive =", value, "deviceWeekAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveNotEqualTo(Long value) {
            addCriterion("device_week_alive <>", value, "deviceWeekAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveGreaterThan(Long value) {
            addCriterion("device_week_alive >", value, "deviceWeekAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveGreaterThanOrEqualTo(Long value) {
            addCriterion("device_week_alive >=", value, "deviceWeekAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveLessThan(Long value) {
            addCriterion("device_week_alive <", value, "deviceWeekAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveLessThanOrEqualTo(Long value) {
            addCriterion("device_week_alive <=", value, "deviceWeekAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveIn(List<Long> values) {
            addCriterion("device_week_alive in", values, "deviceWeekAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveNotIn(List<Long> values) {
            addCriterion("device_week_alive not in", values, "deviceWeekAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveBetween(Long value1, Long value2) {
            addCriterion("device_week_alive between", value1, value2, "deviceWeekAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceWeekAliveNotBetween(Long value1, Long value2) {
            addCriterion("device_week_alive not between", value1, value2, "deviceWeekAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveIsNull() {
            addCriterion("device_month_alive is null");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveIsNotNull() {
            addCriterion("device_month_alive is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveEqualTo(Long value) {
            addCriterion("device_month_alive =", value, "deviceMonthAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveNotEqualTo(Long value) {
            addCriterion("device_month_alive <>", value, "deviceMonthAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveGreaterThan(Long value) {
            addCriterion("device_month_alive >", value, "deviceMonthAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveGreaterThanOrEqualTo(Long value) {
            addCriterion("device_month_alive >=", value, "deviceMonthAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveLessThan(Long value) {
            addCriterion("device_month_alive <", value, "deviceMonthAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveLessThanOrEqualTo(Long value) {
            addCriterion("device_month_alive <=", value, "deviceMonthAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveIn(List<Long> values) {
            addCriterion("device_month_alive in", values, "deviceMonthAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveNotIn(List<Long> values) {
            addCriterion("device_month_alive not in", values, "deviceMonthAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveBetween(Long value1, Long value2) {
            addCriterion("device_month_alive between", value1, value2, "deviceMonthAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceMonthAliveNotBetween(Long value1, Long value2) {
            addCriterion("device_month_alive not between", value1, value2, "deviceMonthAlive");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredIsNull() {
            addCriterion("device_all_wired is null");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredIsNotNull() {
            addCriterion("device_all_wired is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredEqualTo(Long value) {
            addCriterion("device_all_wired =", value, "deviceAllWired");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredNotEqualTo(Long value) {
            addCriterion("device_all_wired <>", value, "deviceAllWired");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredGreaterThan(Long value) {
            addCriterion("device_all_wired >", value, "deviceAllWired");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredGreaterThanOrEqualTo(Long value) {
            addCriterion("device_all_wired >=", value, "deviceAllWired");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredLessThan(Long value) {
            addCriterion("device_all_wired <", value, "deviceAllWired");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredLessThanOrEqualTo(Long value) {
            addCriterion("device_all_wired <=", value, "deviceAllWired");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredIn(List<Long> values) {
            addCriterion("device_all_wired in", values, "deviceAllWired");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredNotIn(List<Long> values) {
            addCriterion("device_all_wired not in", values, "deviceAllWired");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredBetween(Long value1, Long value2) {
            addCriterion("device_all_wired between", value1, value2, "deviceAllWired");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWiredNotBetween(Long value1, Long value2) {
            addCriterion("device_all_wired not between", value1, value2, "deviceAllWired");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessIsNull() {
            addCriterion("device_all_wireless is null");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessIsNotNull() {
            addCriterion("device_all_wireless is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessEqualTo(Long value) {
            addCriterion("device_all_wireless =", value, "deviceAllWireless");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessNotEqualTo(Long value) {
            addCriterion("device_all_wireless <>", value, "deviceAllWireless");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessGreaterThan(Long value) {
            addCriterion("device_all_wireless >", value, "deviceAllWireless");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessGreaterThanOrEqualTo(Long value) {
            addCriterion("device_all_wireless >=", value, "deviceAllWireless");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessLessThan(Long value) {
            addCriterion("device_all_wireless <", value, "deviceAllWireless");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessLessThanOrEqualTo(Long value) {
            addCriterion("device_all_wireless <=", value, "deviceAllWireless");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessIn(List<Long> values) {
            addCriterion("device_all_wireless in", values, "deviceAllWireless");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessNotIn(List<Long> values) {
            addCriterion("device_all_wireless not in", values, "deviceAllWireless");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessBetween(Long value1, Long value2) {
            addCriterion("device_all_wireless between", value1, value2, "deviceAllWireless");
            return (Criteria) this;
        }

        public Criteria andDeviceAllWirelessNotBetween(Long value1, Long value2) {
            addCriterion("device_all_wireless not between", value1, value2, "deviceAllWireless");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}