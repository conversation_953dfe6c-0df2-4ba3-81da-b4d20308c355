package com.cmiot.report.bean.customer;

import java.util.List;

/**
 * @Classname CustomerMarketingScoreQueryParam
 * @Description
 * @Date 2022/8/29 9:15
 * @Created by lei
 */
public class CustomerMarketingScoreQueryParam {

    private String time;
    private List<String> provList;
    private List<String> cityList;
    private String customerName;
    private String customerId;
    private List<String> valueCategoryList;
    private List<String> customerStatusList;
    private String scoreStart;
    private String scoreEnd;
    private Long offset;
    private Integer pageSize;

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public List<String> getProvList() {
        return provList;
    }

    public void setProvList(List<String> provList) {
        this.provList = provList;
    }

    public List<String> getCityList() {
        return cityList;
    }

    public void setCityList(List<String> cityList) {
        this.cityList = cityList;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public List<String> getValueCategoryList() {
        return valueCategoryList;
    }

    public void setValueCategoryList(List<String> valueCategoryList) {
        this.valueCategoryList = valueCategoryList;
    }

    public List<String> getCustomerStatusList() {
        return customerStatusList;
    }

    public void setCustomerStatusList(List<String> customerStatusList) {
        this.customerStatusList = customerStatusList;
    }

    public String getScoreStart() {
        return scoreStart;
    }

    public void setScoreStart(String scoreStart) {
        this.scoreStart = scoreStart;
    }

    public String getScoreEnd() {
        return scoreEnd;
    }

    public void setScoreEnd(String scoreEnd) {
        this.scoreEnd = scoreEnd;
    }

    public Long getOffset() {
        return offset;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
