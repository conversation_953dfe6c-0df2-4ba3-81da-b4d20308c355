package com.cmiot.report.bean;

import java.util.Date;

public class PonPowerStatisticsDetail {
    private String provinceCode;

    private String cityCode;

    private String gatewaySn;

    private Long factoryId;

    private String factoryName;

    private Long factoryModelId;

    private String factoryModel;

    private Long runingTimeCurr;

    private Integer ponRxPowerCurrSum;

    private Integer ponRxPowerCurrCount;

    private Byte ponRxPowerCurrType;

    private Date sampleTime;

    private Long gdate;

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getGatewaySn() {
        return gatewaySn;
    }

    public void setGatewaySn(String gatewaySn) {
        this.gatewaySn = gatewaySn == null ? null : gatewaySn.trim();
    }

    public Long getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Long factoryId) {
        this.factoryId = factoryId;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName == null ? null : factoryName.trim();
    }

    public Long getFactoryModelId() {
        return factoryModelId;
    }

    public void setFactoryModelId(Long factoryModelId) {
        this.factoryModelId = factoryModelId;
    }

    public String getFactoryModel() {
        return factoryModel;
    }

    public void setFactoryModel(String factoryModel) {
        this.factoryModel = factoryModel == null ? null : factoryModel.trim();
    }

    public Long getRuningTimeCurr() {
        return runingTimeCurr;
    }

    public void setRuningTimeCurr(Long runingTimeCurr) {
        this.runingTimeCurr = runingTimeCurr;
    }

    public Integer getPonRxPowerCurrSum() {
        return ponRxPowerCurrSum;
    }

    public void setPonRxPowerCurrSum(Integer ponRxPowerCurrSum) {
        this.ponRxPowerCurrSum = ponRxPowerCurrSum;
    }

    public Integer getPonRxPowerCurrCount() {
        return ponRxPowerCurrCount;
    }

    public void setPonRxPowerCurrCount(Integer ponRxPowerCurrCount) {
        this.ponRxPowerCurrCount = ponRxPowerCurrCount;
    }

    public Byte getPonRxPowerCurrType() {
        return ponRxPowerCurrType;
    }

    public void setPonRxPowerCurrType(Byte ponRxPowerCurrType) {
        this.ponRxPowerCurrType = ponRxPowerCurrType;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }

    public Long getGdate() {
        return gdate;
    }

    public void setGdate(Long gdate) {
        this.gdate = gdate;
    }
}