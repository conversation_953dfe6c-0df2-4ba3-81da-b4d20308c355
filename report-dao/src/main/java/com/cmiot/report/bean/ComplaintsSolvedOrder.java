package com.cmiot.report.bean;

public class ComplaintsSolvedOrder {

    // 集团客户标识
    private String customerId;

    // 省份code
    private String provinceCode;

    // 地市code
    private String cityCode;

    // 装移机及时处理工单量
    private Integer installOrders;

    // 故障解决工单量
    private Integer maintenanceSolvedOrders;

    // 故障维修及时处理工单量
    private Integer maintenanceSolvedExtOrders;

    // 投诉解决工单量
    private Integer complaintsSolvedOrders;

    // 投诉处理及时工单量
    private Integer complaintsSolvedExtOrders;

    // 客服准确工单量
    private Integer serviceOrders;

    // 总工单量
    private Integer totalOrders;

    private Integer gdate;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public Integer getInstallOrders() {
        return installOrders;
    }

    public void setInstallOrders(Integer installOrders) {
        this.installOrders = installOrders;
    }

    public Integer getMaintenanceSolvedOrders() {
        return maintenanceSolvedOrders;
    }

    public void setMaintenanceSolvedOrders(Integer maintenanceSolvedOrders) {
        this.maintenanceSolvedOrders = maintenanceSolvedOrders;
    }

    public Integer getMaintenanceSolvedExtOrders() {
        return maintenanceSolvedExtOrders;
    }

    public void setMaintenanceSolvedExtOrders(Integer maintenanceSolvedExtOrders) {
        this.maintenanceSolvedExtOrders = maintenanceSolvedExtOrders;
    }

    public Integer getComplaintsSolvedOrders() {
        return complaintsSolvedOrders;
    }

    public void setComplaintsSolvedOrders(Integer complaintsSolvedOrders) {
        this.complaintsSolvedOrders = complaintsSolvedOrders;
    }

    public Integer getComplaintsSolvedExtOrders() {
        return complaintsSolvedExtOrders;
    }

    public void setComplaintsSolvedExtOrders(Integer complaintsSolvedExtOrders) {
        this.complaintsSolvedExtOrders = complaintsSolvedExtOrders;
    }

    public Integer getServiceOrders() {
        return serviceOrders;
    }

    public void setServiceOrders(Integer serviceOrders) {
        this.serviceOrders = serviceOrders;
    }

    public Integer getTotalOrders() {
        return totalOrders;
    }

    public void setTotalOrders(Integer totalOrders) {
        this.totalOrders = totalOrders;
    }

    public Integer getGdate() {
        return gdate;
    }

    public void setGdate(Integer gdate) {
        this.gdate = gdate;
    }
}