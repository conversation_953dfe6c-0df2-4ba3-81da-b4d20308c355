package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.List;

public class DictBassTypeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DictBassTypeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBassTypeIsNull() {
            addCriterion("bass_type is null");
            return (Criteria) this;
        }

        public Criteria andBassTypeIsNotNull() {
            addCriterion("bass_type is not null");
            return (Criteria) this;
        }

        public Criteria andBassTypeEqualTo(String value) {
            addCriterion("bass_type =", value, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeNotEqualTo(String value) {
            addCriterion("bass_type <>", value, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeGreaterThan(String value) {
            addCriterion("bass_type >", value, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bass_type >=", value, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeLessThan(String value) {
            addCriterion("bass_type <", value, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeLessThanOrEqualTo(String value) {
            addCriterion("bass_type <=", value, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeLike(String value) {
            addCriterion("bass_type like", value, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeNotLike(String value) {
            addCriterion("bass_type not like", value, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeIn(List<String> values) {
            addCriterion("bass_type in", values, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeNotIn(List<String> values) {
            addCriterion("bass_type not in", values, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeBetween(String value1, String value2) {
            addCriterion("bass_type between", value1, value2, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassTypeNotBetween(String value1, String value2) {
            addCriterion("bass_type not between", value1, value2, "bassType");
            return (Criteria) this;
        }

        public Criteria andBassNameIsNull() {
            addCriterion("bass_name is null");
            return (Criteria) this;
        }

        public Criteria andBassNameIsNotNull() {
            addCriterion("bass_name is not null");
            return (Criteria) this;
        }

        public Criteria andBassNameEqualTo(String value) {
            addCriterion("bass_name =", value, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameNotEqualTo(String value) {
            addCriterion("bass_name <>", value, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameGreaterThan(String value) {
            addCriterion("bass_name >", value, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameGreaterThanOrEqualTo(String value) {
            addCriterion("bass_name >=", value, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameLessThan(String value) {
            addCriterion("bass_name <", value, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameLessThanOrEqualTo(String value) {
            addCriterion("bass_name <=", value, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameLike(String value) {
            addCriterion("bass_name like", value, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameNotLike(String value) {
            addCriterion("bass_name not like", value, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameIn(List<String> values) {
            addCriterion("bass_name in", values, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameNotIn(List<String> values) {
            addCriterion("bass_name not in", values, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameBetween(String value1, String value2) {
            addCriterion("bass_name between", value1, value2, "bassName");
            return (Criteria) this;
        }

        public Criteria andBassNameNotBetween(String value1, String value2) {
            addCriterion("bass_name not between", value1, value2, "bassName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}