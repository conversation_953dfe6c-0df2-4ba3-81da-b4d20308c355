package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PluginInstallExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PluginInstallExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andGatewaySnIsNull() {
            addCriterion("gateway_sn is null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIsNotNull() {
            addCriterion("gateway_sn is not null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnEqualTo(String value) {
            addCriterion("gateway_sn =", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotEqualTo(String value) {
            addCriterion("gateway_sn <>", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThan(String value) {
            addCriterion("gateway_sn >", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_sn >=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThan(String value) {
            addCriterion("gateway_sn <", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThanOrEqualTo(String value) {
            addCriterion("gateway_sn <=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLike(String value) {
            addCriterion("gateway_sn like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotLike(String value) {
            addCriterion("gateway_sn not like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIn(List<String> values) {
            addCriterion("gateway_sn in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotIn(List<String> values) {
            addCriterion("gateway_sn not in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnBetween(String value1, String value2) {
            addCriterion("gateway_sn between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotBetween(String value1, String value2) {
            addCriterion("gateway_sn not between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIsNull() {
            addCriterion("gateway_mac is null");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIsNotNull() {
            addCriterion("gateway_mac is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayMacEqualTo(String value) {
            addCriterion("gateway_mac =", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotEqualTo(String value) {
            addCriterion("gateway_mac <>", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacGreaterThan(String value) {
            addCriterion("gateway_mac >", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_mac >=", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLessThan(String value) {
            addCriterion("gateway_mac <", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLessThanOrEqualTo(String value) {
            addCriterion("gateway_mac <=", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLike(String value) {
            addCriterion("gateway_mac like", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotLike(String value) {
            addCriterion("gateway_mac not like", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIn(List<String> values) {
            addCriterion("gateway_mac in", values, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotIn(List<String> values) {
            addCriterion("gateway_mac not in", values, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacBetween(String value1, String value2) {
            addCriterion("gateway_mac between", value1, value2, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotBetween(String value1, String value2) {
            addCriterion("gateway_mac not between", value1, value2, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIsNull() {
            addCriterion("factory_id is null");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIsNotNull() {
            addCriterion("factory_id is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryIdEqualTo(Long value) {
            addCriterion("factory_id =", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotEqualTo(Long value) {
            addCriterion("factory_id <>", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdGreaterThan(Long value) {
            addCriterion("factory_id >", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("factory_id >=", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdLessThan(Long value) {
            addCriterion("factory_id <", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdLessThanOrEqualTo(Long value) {
            addCriterion("factory_id <=", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIn(List<Long> values) {
            addCriterion("factory_id in", values, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotIn(List<Long> values) {
            addCriterion("factory_id not in", values, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdBetween(Long value1, Long value2) {
            addCriterion("factory_id between", value1, value2, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotBetween(Long value1, Long value2) {
            addCriterion("factory_id not between", value1, value2, "factoryId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIsNull() {
            addCriterion("device_model_id is null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIsNotNull() {
            addCriterion("device_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdEqualTo(Long value) {
            addCriterion("device_model_id =", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotEqualTo(Long value) {
            addCriterion("device_model_id <>", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdGreaterThan(Long value) {
            addCriterion("device_model_id >", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("device_model_id >=", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdLessThan(Long value) {
            addCriterion("device_model_id <", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdLessThanOrEqualTo(Long value) {
            addCriterion("device_model_id <=", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIn(List<Long> values) {
            addCriterion("device_model_id in", values, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotIn(List<Long> values) {
            addCriterion("device_model_id not in", values, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdBetween(Long value1, Long value2) {
            addCriterion("device_model_id between", value1, value2, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotBetween(Long value1, Long value2) {
            addCriterion("device_model_id not between", value1, value2, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntIsNull() {
            addCriterion("gateway_model_int is null");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntIsNotNull() {
            addCriterion("gateway_model_int is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntEqualTo(Short value) {
            addCriterion("gateway_model_int =", value, "gatewayModelInt");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntNotEqualTo(Short value) {
            addCriterion("gateway_model_int <>", value, "gatewayModelInt");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntGreaterThan(Short value) {
            addCriterion("gateway_model_int >", value, "gatewayModelInt");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntGreaterThanOrEqualTo(Short value) {
            addCriterion("gateway_model_int >=", value, "gatewayModelInt");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntLessThan(Short value) {
            addCriterion("gateway_model_int <", value, "gatewayModelInt");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntLessThanOrEqualTo(Short value) {
            addCriterion("gateway_model_int <=", value, "gatewayModelInt");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntIn(List<Short> values) {
            addCriterion("gateway_model_int in", values, "gatewayModelInt");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntNotIn(List<Short> values) {
            addCriterion("gateway_model_int not in", values, "gatewayModelInt");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntBetween(Short value1, Short value2) {
            addCriterion("gateway_model_int between", value1, value2, "gatewayModelInt");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIntNotBetween(Short value1, Short value2) {
            addCriterion("gateway_model_int not between", value1, value2, "gatewayModelInt");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIsNull() {
            addCriterion("gateway_model is null");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIsNotNull() {
            addCriterion("gateway_model is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayModelEqualTo(String value) {
            addCriterion("gateway_model =", value, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelNotEqualTo(String value) {
            addCriterion("gateway_model <>", value, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelGreaterThan(String value) {
            addCriterion("gateway_model >", value, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_model >=", value, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelLessThan(String value) {
            addCriterion("gateway_model <", value, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelLessThanOrEqualTo(String value) {
            addCriterion("gateway_model <=", value, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelLike(String value) {
            addCriterion("gateway_model like", value, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelNotLike(String value) {
            addCriterion("gateway_model not like", value, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelIn(List<String> values) {
            addCriterion("gateway_model in", values, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelNotIn(List<String> values) {
            addCriterion("gateway_model not in", values, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelBetween(String value1, String value2) {
            addCriterion("gateway_model between", value1, value2, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayModelNotBetween(String value1, String value2) {
            addCriterion("gateway_model not between", value1, value2, "gatewayModel");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNull() {
            addCriterion("gateway_vendor is null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNotNull() {
            addCriterion("gateway_vendor is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorEqualTo(String value) {
            addCriterion("gateway_vendor =", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotEqualTo(String value) {
            addCriterion("gateway_vendor <>", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThan(String value) {
            addCriterion("gateway_vendor >", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_vendor >=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThan(String value) {
            addCriterion("gateway_vendor <", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThanOrEqualTo(String value) {
            addCriterion("gateway_vendor <=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLike(String value) {
            addCriterion("gateway_vendor like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotLike(String value) {
            addCriterion("gateway_vendor not like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIn(List<String> values) {
            addCriterion("gateway_vendor in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotIn(List<String> values) {
            addCriterion("gateway_vendor not in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorBetween(String value1, String value2) {
            addCriterion("gateway_vendor between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotBetween(String value1, String value2) {
            addCriterion("gateway_vendor not between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIsNull() {
            addCriterion("gateway_productclass is null");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIsNotNull() {
            addCriterion("gateway_productclass is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassEqualTo(String value) {
            addCriterion("gateway_productclass =", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotEqualTo(String value) {
            addCriterion("gateway_productclass <>", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassGreaterThan(String value) {
            addCriterion("gateway_productclass >", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_productclass >=", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLessThan(String value) {
            addCriterion("gateway_productclass <", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLessThanOrEqualTo(String value) {
            addCriterion("gateway_productclass <=", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLike(String value) {
            addCriterion("gateway_productclass like", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotLike(String value) {
            addCriterion("gateway_productclass not like", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIn(List<String> values) {
            addCriterion("gateway_productclass in", values, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotIn(List<String> values) {
            addCriterion("gateway_productclass not in", values, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassBetween(String value1, String value2) {
            addCriterion("gateway_productclass between", value1, value2, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotBetween(String value1, String value2) {
            addCriterion("gateway_productclass not between", value1, value2, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNull() {
            addCriterion("enterprise_id is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNotNull() {
            addCriterion("enterprise_id is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdEqualTo(Long value) {
            addCriterion("enterprise_id =", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotEqualTo(Long value) {
            addCriterion("enterprise_id <>", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThan(Long value) {
            addCriterion("enterprise_id >", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("enterprise_id >=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThan(Long value) {
            addCriterion("enterprise_id <", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThanOrEqualTo(Long value) {
            addCriterion("enterprise_id <=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIn(List<Long> values) {
            addCriterion("enterprise_id in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotIn(List<Long> values) {
            addCriterion("enterprise_id not in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdBetween(Long value1, Long value2) {
            addCriterion("enterprise_id between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotBetween(Long value1, Long value2) {
            addCriterion("enterprise_id not between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(String value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(String value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(String value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(String value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(String value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(String value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLike(String value) {
            addCriterion("customer_id like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotLike(String value) {
            addCriterion("customer_id not like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<String> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<String> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(String value1, String value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(String value1, String value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andPluginNameIsNull() {
            addCriterion("plugin_name is null");
            return (Criteria) this;
        }

        public Criteria andPluginNameIsNotNull() {
            addCriterion("plugin_name is not null");
            return (Criteria) this;
        }

        public Criteria andPluginNameEqualTo(String value) {
            addCriterion("plugin_name =", value, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameNotEqualTo(String value) {
            addCriterion("plugin_name <>", value, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameGreaterThan(String value) {
            addCriterion("plugin_name >", value, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameGreaterThanOrEqualTo(String value) {
            addCriterion("plugin_name >=", value, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameLessThan(String value) {
            addCriterion("plugin_name <", value, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameLessThanOrEqualTo(String value) {
            addCriterion("plugin_name <=", value, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameLike(String value) {
            addCriterion("plugin_name like", value, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameNotLike(String value) {
            addCriterion("plugin_name not like", value, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameIn(List<String> values) {
            addCriterion("plugin_name in", values, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameNotIn(List<String> values) {
            addCriterion("plugin_name not in", values, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameBetween(String value1, String value2) {
            addCriterion("plugin_name between", value1, value2, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginNameNotBetween(String value1, String value2) {
            addCriterion("plugin_name not between", value1, value2, "pluginName");
            return (Criteria) this;
        }

        public Criteria andPluginVersionIsNull() {
            addCriterion("plugin_version is null");
            return (Criteria) this;
        }

        public Criteria andPluginVersionIsNotNull() {
            addCriterion("plugin_version is not null");
            return (Criteria) this;
        }

        public Criteria andPluginVersionEqualTo(String value) {
            addCriterion("plugin_version =", value, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionNotEqualTo(String value) {
            addCriterion("plugin_version <>", value, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionGreaterThan(String value) {
            addCriterion("plugin_version >", value, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionGreaterThanOrEqualTo(String value) {
            addCriterion("plugin_version >=", value, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionLessThan(String value) {
            addCriterion("plugin_version <", value, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionLessThanOrEqualTo(String value) {
            addCriterion("plugin_version <=", value, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionLike(String value) {
            addCriterion("plugin_version like", value, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionNotLike(String value) {
            addCriterion("plugin_version not like", value, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionIn(List<String> values) {
            addCriterion("plugin_version in", values, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionNotIn(List<String> values) {
            addCriterion("plugin_version not in", values, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionBetween(String value1, String value2) {
            addCriterion("plugin_version between", value1, value2, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginVersionNotBetween(String value1, String value2) {
            addCriterion("plugin_version not between", value1, value2, "pluginVersion");
            return (Criteria) this;
        }

        public Criteria andPluginStatusIsNull() {
            addCriterion("plugin_status is null");
            return (Criteria) this;
        }

        public Criteria andPluginStatusIsNotNull() {
            addCriterion("plugin_status is not null");
            return (Criteria) this;
        }

        public Criteria andPluginStatusEqualTo(String value) {
            addCriterion("plugin_status =", value, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusNotEqualTo(String value) {
            addCriterion("plugin_status <>", value, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusGreaterThan(String value) {
            addCriterion("plugin_status >", value, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusGreaterThanOrEqualTo(String value) {
            addCriterion("plugin_status >=", value, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusLessThan(String value) {
            addCriterion("plugin_status <", value, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusLessThanOrEqualTo(String value) {
            addCriterion("plugin_status <=", value, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusLike(String value) {
            addCriterion("plugin_status like", value, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusNotLike(String value) {
            addCriterion("plugin_status not like", value, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusIn(List<String> values) {
            addCriterion("plugin_status in", values, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusNotIn(List<String> values) {
            addCriterion("plugin_status not in", values, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusBetween(String value1, String value2) {
            addCriterion("plugin_status between", value1, value2, "pluginStatus");
            return (Criteria) this;
        }

        public Criteria andPluginStatusNotBetween(String value1, String value2) {
            addCriterion("plugin_status not between", value1, value2, "pluginStatus");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}