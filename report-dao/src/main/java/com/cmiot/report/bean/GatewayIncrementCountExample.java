package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.List;

public class GatewayIncrementCountExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GatewayIncrementCountExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andFactoryCodeIsNull() {
            addCriterion("factory_code is null");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeIsNotNull() {
            addCriterion("factory_code is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeEqualTo(String value) {
            addCriterion("factory_code =", value, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeNotEqualTo(String value) {
            addCriterion("factory_code <>", value, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeGreaterThan(String value) {
            addCriterion("factory_code >", value, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("factory_code >=", value, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeLessThan(String value) {
            addCriterion("factory_code <", value, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeLessThanOrEqualTo(String value) {
            addCriterion("factory_code <=", value, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeLike(String value) {
            addCriterion("factory_code like", value, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeNotLike(String value) {
            addCriterion("factory_code not like", value, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeIn(List<String> values) {
            addCriterion("factory_code in", values, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeNotIn(List<String> values) {
            addCriterion("factory_code not in", values, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeBetween(String value1, String value2) {
            addCriterion("factory_code between", value1, value2, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andFactoryCodeNotBetween(String value1, String value2) {
            addCriterion("factory_code not between", value1, value2, "factoryCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNull() {
            addCriterion("industry is null");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNotNull() {
            addCriterion("industry is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryEqualTo(String value) {
            addCriterion("industry =", value, "industry");
            return (Criteria) this;
        }

        public Criteria andConnectTypeEqualTo(Integer value) {
            addCriterion("product_type = ", value, "connectType");
            return (Criteria) this;
        }

        public Criteria andIndustryNotEqualTo(String value) {
            addCriterion("industry <>", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThan(String value) {
            addCriterion("industry >", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThanOrEqualTo(String value) {
            addCriterion("industry >=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThan(String value) {
            addCriterion("industry <", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThanOrEqualTo(String value) {
            addCriterion("industry <=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLike(String value) {
            addCriterion("industry like", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotLike(String value) {
            addCriterion("industry not like", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryIn(List<String> values) {
            addCriterion("industry in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotIn(List<String> values) {
            addCriterion("industry not in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryBetween(String value1, String value2) {
            addCriterion("industry between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotBetween(String value1, String value2) {
            addCriterion("industry not between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountIsNull() {
            addCriterion("gateway_inc_count is null");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountIsNotNull() {
            addCriterion("gateway_inc_count is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountEqualTo(Long value) {
            addCriterion("gateway_inc_count =", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountNotEqualTo(Long value) {
            addCriterion("gateway_inc_count <>", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountGreaterThan(Long value) {
            addCriterion("gateway_inc_count >", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountGreaterThanOrEqualTo(Long value) {
            addCriterion("gateway_inc_count >=", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountLessThan(Long value) {
            addCriterion("gateway_inc_count <", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountLessThanOrEqualTo(Long value) {
            addCriterion("gateway_inc_count <=", value, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountIn(List<Long> values) {
            addCriterion("gateway_inc_count in", values, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountNotIn(List<Long> values) {
            addCriterion("gateway_inc_count not in", values, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountBetween(Long value1, Long value2) {
            addCriterion("gateway_inc_count between", value1, value2, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGatewayIncCountNotBetween(Long value1, Long value2) {
            addCriterion("gateway_inc_count not between", value1, value2, "gatewayIncCount");
            return (Criteria) this;
        }

        public Criteria andGdateIsNull() {
            addCriterion("gdate is null");
            return (Criteria) this;
        }

        public Criteria andGdateIsNotNull() {
            addCriterion("gdate is not null");
            return (Criteria) this;
        }

        public Criteria andGdateEqualTo(Long value) {
            addCriterion("gdate =", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotEqualTo(Long value) {
            addCriterion("gdate <>", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThan(Long value) {
            addCriterion("gdate >", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThanOrEqualTo(Long value) {
            addCriterion("gdate >=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThan(Long value) {
            addCriterion("gdate <", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThanOrEqualTo(Long value) {
            addCriterion("gdate <=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateIn(List<Long> values) {
            addCriterion("gdate in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotIn(List<Long> values) {
            addCriterion("gdate not in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateBetween(Long value1, Long value2) {
            addCriterion("gdate between", value1, value2, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotBetween(Long value1, Long value2) {
            addCriterion("gdate not between", value1, value2, "gdate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}