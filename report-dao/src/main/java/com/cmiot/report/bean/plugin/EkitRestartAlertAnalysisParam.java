package com.cmiot.report.bean.plugin;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EkitRestartAlertAnalysisParam implements Serializable {

    /**
     * 告警等级 1普通 2重要 3严重
     */
    private Integer alarmLevel;

    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private List<Long> vendor;

    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private List<Long> model;

    private String sn;

    private List<String> province;

    private String businessName;

    private Integer deviceType;

    //@JsonSerialize(using = ToStringSerializer.class)
    private Integer page;
   // @JsonSerialize(using = ToStringSerializer.class)
    private Integer pageSize;

}
