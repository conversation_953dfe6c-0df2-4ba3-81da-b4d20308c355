package com.cmiot.report.bean;

import java.math.BigDecimal;
import java.util.Date;

public class GatewayAnomalyRecord {
    private String province;
    private String city;
    private String areaName;
    private String gatewaySn;
    private Long factoryId;
    private String factoryName;
    private Long deviceModelId;
    private String deviceModel;
    private String customerId;
    private String customerName;
    private String contactPhone;
    private BigDecimal ponMax;
    private BigDecimal ponMin;
    private BigDecimal ponAvg;
    private Integer pppoeErrorNum;
    private Integer pppoeErrorDaysLimit;
    private Integer pppoeErrorNumLimit;
    private BigDecimal cpuAvg;
    private BigDecimal cpuLimit;
    private BigDecimal ramAvg;
    private BigDecimal ramLimit;
    private BigDecimal netDelay;
    private Integer netDelayDaysLimit;
    private Integer netDelayLimit;
    private Integer subDeviceOver;
    private Integer subDeviceOverDaysLimit;
    private Integer subDeviceOverNumLimit;
    private BigDecimal wlanRadioPowerAvg;
    private BigDecimal wlanRadioPowerAvgLimit;
    private Integer errs;

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getGatewaySn() {
        return gatewaySn;
    }

    public void setGatewaySn(String gatewaySn) {
        this.gatewaySn = gatewaySn;
    }

    public Long getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Long factoryId) {
        this.factoryId = factoryId;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public Long getDeviceModelId() {
        return deviceModelId;
    }

    public void setDeviceModelId(Long deviceModelId) {
        this.deviceModelId = deviceModelId;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public BigDecimal getPonMax() {
        return ponMax;
    }

    public void setPonMax(BigDecimal ponMax) {
        this.ponMax = ponMax;
    }

    public BigDecimal getPonMin() {
        return ponMin;
    }

    public void setPonMin(BigDecimal ponMin) {
        this.ponMin = ponMin;
    }

    public BigDecimal getPonAvg() {
        return ponAvg;
    }

    public void setPonAvg(BigDecimal ponAvg) {
        this.ponAvg = ponAvg;
    }

    public Integer getPppoeErrorNum() {
        return pppoeErrorNum;
    }

    public void setPppoeErrorNum(Integer pppoeErrorNum) {
        this.pppoeErrorNum = pppoeErrorNum;
    }

    public Integer getPppoeErrorDaysLimit() {
        return pppoeErrorDaysLimit;
    }

    public void setPppoeErrorDaysLimit(Integer pppoeErrorDaysLimit) {
        this.pppoeErrorDaysLimit = pppoeErrorDaysLimit;
    }

    public Integer getPppoeErrorNumLimit() {
        return pppoeErrorNumLimit;
    }

    public void setPppoeErrorNumLimit(Integer pppoeErrorNumLimit) {
        this.pppoeErrorNumLimit = pppoeErrorNumLimit;
    }

    public BigDecimal getCpuAvg() {
        return cpuAvg;
    }

    public void setCpuAvg(BigDecimal cpuAvg) {
        this.cpuAvg = cpuAvg;
    }

    public BigDecimal getCpuLimit() {
        return cpuLimit;
    }

    public void setCpuLimit(BigDecimal cpuLimit) {
        this.cpuLimit = cpuLimit;
    }

    public BigDecimal getRamAvg() {
        return ramAvg;
    }

    public void setRamAvg(BigDecimal ramAvg) {
        this.ramAvg = ramAvg;
    }

    public BigDecimal getRamLimit() {
        return ramLimit;
    }

    public void setRamLimit(BigDecimal ramLimit) {
        this.ramLimit = ramLimit;
    }

    public BigDecimal getNetDelay() {
        return netDelay;
    }

    public void setNetDelay(BigDecimal netDelay) {
        this.netDelay = netDelay;
    }

    public Integer getNetDelayDaysLimit() {
        return netDelayDaysLimit;
    }

    public void setNetDelayDaysLimit(Integer netDelayDaysLimit) {
        this.netDelayDaysLimit = netDelayDaysLimit;
    }

    public Integer getNetDelayLimit() {
        return netDelayLimit;
    }

    public void setNetDelayLimit(Integer netDelayLimit) {
        this.netDelayLimit = netDelayLimit;
    }

    public Integer getSubDeviceOver() {
        return subDeviceOver;
    }

    public void setSubDeviceOver(Integer subDeviceOver) {
        this.subDeviceOver = subDeviceOver;
    }

    public Integer getSubDeviceOverDaysLimit() {
        return subDeviceOverDaysLimit;
    }

    public void setSubDeviceOverDaysLimit(Integer subDeviceOverDaysLimit) {
        this.subDeviceOverDaysLimit = subDeviceOverDaysLimit;
    }

    public Integer getSubDeviceOverNumLimit() {
        return subDeviceOverNumLimit;
    }

    public void setSubDeviceOverNumLimit(Integer subDeviceOverNumLimit) {
        this.subDeviceOverNumLimit = subDeviceOverNumLimit;
    }

    public BigDecimal getWlanRadioPowerAvg() {
        return wlanRadioPowerAvg;
    }

    public void setWlanRadioPowerAvg(BigDecimal wlanRadioPowerAvg) {
        this.wlanRadioPowerAvg = wlanRadioPowerAvg;
    }

    public BigDecimal getWlanRadioPowerAvgLimit() {
        return wlanRadioPowerAvgLimit;
    }

    public void setWlanRadioPowerAvgLimit(BigDecimal wlanRadioPowerAvgLimit) {
        this.wlanRadioPowerAvgLimit = wlanRadioPowerAvgLimit;
    }

    public Integer getErrs() {
        return errs;
    }

    public void setErrs(Integer errs) {
        this.errs = errs;
    }
}
