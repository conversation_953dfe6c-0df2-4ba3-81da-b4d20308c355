package com.cmiot.report.bean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GatewayPeriodAggrAllExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GatewayPeriodAggrAllExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andRecordTimeIsNull() {
            addCriterion("record_time is null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNotNull() {
            addCriterion("record_time is not null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeEqualTo(Date value) {
            addCriterion("record_time =", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotEqualTo(Date value) {
            addCriterion("record_time <>", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThan(Date value) {
            addCriterion("record_time >", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("record_time >=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThan(Date value) {
            addCriterion("record_time <", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThanOrEqualTo(Date value) {
            addCriterion("record_time <=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIn(List<Date> values) {
            addCriterion("record_time in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotIn(List<Date> values) {
            addCriterion("record_time not in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeBetween(Date value1, Date value2) {
            addCriterion("record_time between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotBetween(Date value1, Date value2) {
            addCriterion("record_time not between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNull() {
            addCriterion("enterprise_id is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNotNull() {
            addCriterion("enterprise_id is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdEqualTo(Long value) {
            addCriterion("enterprise_id =", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotEqualTo(Long value) {
            addCriterion("enterprise_id <>", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThan(Long value) {
            addCriterion("enterprise_id >", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("enterprise_id >=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThan(Long value) {
            addCriterion("enterprise_id <", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThanOrEqualTo(Long value) {
            addCriterion("enterprise_id <=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIn(List<Long> values) {
            addCriterion("enterprise_id in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotIn(List<Long> values) {
            addCriterion("enterprise_id not in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdBetween(Long value1, Long value2) {
            addCriterion("enterprise_id between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotBetween(Long value1, Long value2) {
            addCriterion("enterprise_id not between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(String value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(String value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(String value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(String value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(String value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(String value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLike(String value) {
            addCriterion("customer_id like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotLike(String value) {
            addCriterion("customer_id not like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<String> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<String> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(String value1, String value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(String value1, String value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIsNull() {
            addCriterion("gateway_sn is null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIsNotNull() {
            addCriterion("gateway_sn is not null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnEqualTo(String value) {
            addCriterion("gateway_sn =", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotEqualTo(String value) {
            addCriterion("gateway_sn <>", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThan(String value) {
            addCriterion("gateway_sn >", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_sn >=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThan(String value) {
            addCriterion("gateway_sn <", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThanOrEqualTo(String value) {
            addCriterion("gateway_sn <=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLike(String value) {
            addCriterion("gateway_sn like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotLike(String value) {
            addCriterion("gateway_sn not like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIn(List<String> values) {
            addCriterion("gateway_sn in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotIn(List<String> values) {
            addCriterion("gateway_sn not in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnBetween(String value1, String value2) {
            addCriterion("gateway_sn between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotBetween(String value1, String value2) {
            addCriterion("gateway_sn not between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIsNull() {
            addCriterion("gateway_mac is null");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIsNotNull() {
            addCriterion("gateway_mac is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayMacEqualTo(String value) {
            addCriterion("gateway_mac =", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotEqualTo(String value) {
            addCriterion("gateway_mac <>", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacGreaterThan(String value) {
            addCriterion("gateway_mac >", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_mac >=", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLessThan(String value) {
            addCriterion("gateway_mac <", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLessThanOrEqualTo(String value) {
            addCriterion("gateway_mac <=", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLike(String value) {
            addCriterion("gateway_mac like", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotLike(String value) {
            addCriterion("gateway_mac not like", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIn(List<String> values) {
            addCriterion("gateway_mac in", values, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotIn(List<String> values) {
            addCriterion("gateway_mac not in", values, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacBetween(String value1, String value2) {
            addCriterion("gateway_mac between", value1, value2, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotBetween(String value1, String value2) {
            addCriterion("gateway_mac not between", value1, value2, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdIsNull() {
            addCriterion("gateway_productclass_id is null");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdIsNotNull() {
            addCriterion("gateway_productclass_id is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdEqualTo(Long value) {
            addCriterion("gateway_productclass_id =", value, "gatewayProductclassId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdNotEqualTo(Long value) {
            addCriterion("gateway_productclass_id <>", value, "gatewayProductclassId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdGreaterThan(Long value) {
            addCriterion("gateway_productclass_id >", value, "gatewayProductclassId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdGreaterThanOrEqualTo(Long value) {
            addCriterion("gateway_productclass_id >=", value, "gatewayProductclassId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdLessThan(Long value) {
            addCriterion("gateway_productclass_id <", value, "gatewayProductclassId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdLessThanOrEqualTo(Long value) {
            addCriterion("gateway_productclass_id <=", value, "gatewayProductclassId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdIn(List<Long> values) {
            addCriterion("gateway_productclass_id in", values, "gatewayProductclassId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdNotIn(List<Long> values) {
            addCriterion("gateway_productclass_id not in", values, "gatewayProductclassId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdBetween(Long value1, Long value2) {
            addCriterion("gateway_productclass_id between", value1, value2, "gatewayProductclassId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIdNotBetween(Long value1, Long value2) {
            addCriterion("gateway_productclass_id not between", value1, value2, "gatewayProductclassId");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIsNull() {
            addCriterion("gateway_productclass is null");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIsNotNull() {
            addCriterion("gateway_productclass is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassEqualTo(String value) {
            addCriterion("gateway_productclass =", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotEqualTo(String value) {
            addCriterion("gateway_productclass <>", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassGreaterThan(String value) {
            addCriterion("gateway_productclass >", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_productclass >=", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLessThan(String value) {
            addCriterion("gateway_productclass <", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLessThanOrEqualTo(String value) {
            addCriterion("gateway_productclass <=", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassLike(String value) {
            addCriterion("gateway_productclass like", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotLike(String value) {
            addCriterion("gateway_productclass not like", value, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassIn(List<String> values) {
            addCriterion("gateway_productclass in", values, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotIn(List<String> values) {
            addCriterion("gateway_productclass not in", values, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassBetween(String value1, String value2) {
            addCriterion("gateway_productclass between", value1, value2, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayProductclassNotBetween(String value1, String value2) {
            addCriterion("gateway_productclass not between", value1, value2, "gatewayProductclass");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdIsNull() {
            addCriterion("gateway_vendor_id is null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdIsNotNull() {
            addCriterion("gateway_vendor_id is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdEqualTo(Long value) {
            addCriterion("gateway_vendor_id =", value, "gatewayVendorId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdNotEqualTo(Long value) {
            addCriterion("gateway_vendor_id <>", value, "gatewayVendorId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdGreaterThan(Long value) {
            addCriterion("gateway_vendor_id >", value, "gatewayVendorId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdGreaterThanOrEqualTo(Long value) {
            addCriterion("gateway_vendor_id >=", value, "gatewayVendorId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdLessThan(Long value) {
            addCriterion("gateway_vendor_id <", value, "gatewayVendorId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdLessThanOrEqualTo(Long value) {
            addCriterion("gateway_vendor_id <=", value, "gatewayVendorId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdIn(List<Long> values) {
            addCriterion("gateway_vendor_id in", values, "gatewayVendorId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdNotIn(List<Long> values) {
            addCriterion("gateway_vendor_id not in", values, "gatewayVendorId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdBetween(Long value1, Long value2) {
            addCriterion("gateway_vendor_id between", value1, value2, "gatewayVendorId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIdNotBetween(Long value1, Long value2) {
            addCriterion("gateway_vendor_id not between", value1, value2, "gatewayVendorId");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNull() {
            addCriterion("gateway_vendor is null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIsNotNull() {
            addCriterion("gateway_vendor is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorEqualTo(String value) {
            addCriterion("gateway_vendor =", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotEqualTo(String value) {
            addCriterion("gateway_vendor <>", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThan(String value) {
            addCriterion("gateway_vendor >", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_vendor >=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThan(String value) {
            addCriterion("gateway_vendor <", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLessThanOrEqualTo(String value) {
            addCriterion("gateway_vendor <=", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorLike(String value) {
            addCriterion("gateway_vendor like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotLike(String value) {
            addCriterion("gateway_vendor not like", value, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorIn(List<String> values) {
            addCriterion("gateway_vendor in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotIn(List<String> values) {
            addCriterion("gateway_vendor not in", values, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorBetween(String value1, String value2) {
            addCriterion("gateway_vendor between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andGatewayVendorNotBetween(String value1, String value2) {
            addCriterion("gateway_vendor not between", value1, value2, "gatewayVendor");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateIsNull() {
            addCriterion("max_txrate is null");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateIsNotNull() {
            addCriterion("max_txrate is not null");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateEqualTo(BigDecimal value) {
            addCriterion("max_txrate =", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateNotEqualTo(BigDecimal value) {
            addCriterion("max_txrate <>", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateGreaterThan(BigDecimal value) {
            addCriterion("max_txrate >", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("max_txrate >=", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateLessThan(BigDecimal value) {
            addCriterion("max_txrate <", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("max_txrate <=", value, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateIn(List<BigDecimal> values) {
            addCriterion("max_txrate in", values, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateNotIn(List<BigDecimal> values) {
            addCriterion("max_txrate not in", values, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_txrate between", value1, value2, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxTxrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_txrate not between", value1, value2, "maxTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateIsNull() {
            addCriterion("aver_txrate is null");
            return (Criteria) this;
        }

        public Criteria andAverTxrateIsNotNull() {
            addCriterion("aver_txrate is not null");
            return (Criteria) this;
        }

        public Criteria andAverTxrateEqualTo(BigDecimal value) {
            addCriterion("aver_txrate =", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateNotEqualTo(BigDecimal value) {
            addCriterion("aver_txrate <>", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateGreaterThan(BigDecimal value) {
            addCriterion("aver_txrate >", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("aver_txrate >=", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateLessThan(BigDecimal value) {
            addCriterion("aver_txrate <", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("aver_txrate <=", value, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateIn(List<BigDecimal> values) {
            addCriterion("aver_txrate in", values, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateNotIn(List<BigDecimal> values) {
            addCriterion("aver_txrate not in", values, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("aver_txrate between", value1, value2, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andAverTxrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("aver_txrate not between", value1, value2, "averTxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateIsNull() {
            addCriterion("max_rxrate is null");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateIsNotNull() {
            addCriterion("max_rxrate is not null");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateEqualTo(BigDecimal value) {
            addCriterion("max_rxrate =", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateNotEqualTo(BigDecimal value) {
            addCriterion("max_rxrate <>", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateGreaterThan(BigDecimal value) {
            addCriterion("max_rxrate >", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("max_rxrate >=", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateLessThan(BigDecimal value) {
            addCriterion("max_rxrate <", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("max_rxrate <=", value, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateIn(List<BigDecimal> values) {
            addCriterion("max_rxrate in", values, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateNotIn(List<BigDecimal> values) {
            addCriterion("max_rxrate not in", values, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_rxrate between", value1, value2, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andMaxRxrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_rxrate not between", value1, value2, "maxRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateIsNull() {
            addCriterion("aver_rxrate is null");
            return (Criteria) this;
        }

        public Criteria andAverRxrateIsNotNull() {
            addCriterion("aver_rxrate is not null");
            return (Criteria) this;
        }

        public Criteria andAverRxrateEqualTo(BigDecimal value) {
            addCriterion("aver_rxrate =", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateNotEqualTo(BigDecimal value) {
            addCriterion("aver_rxrate <>", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateGreaterThan(BigDecimal value) {
            addCriterion("aver_rxrate >", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("aver_rxrate >=", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateLessThan(BigDecimal value) {
            addCriterion("aver_rxrate <", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("aver_rxrate <=", value, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateIn(List<BigDecimal> values) {
            addCriterion("aver_rxrate in", values, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateNotIn(List<BigDecimal> values) {
            addCriterion("aver_rxrate not in", values, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("aver_rxrate between", value1, value2, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andAverRxrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("aver_rxrate not between", value1, value2, "averRxrate");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsIsNull() {
            addCriterion("down_staticstics is null");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsIsNotNull() {
            addCriterion("down_staticstics is not null");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsEqualTo(BigDecimal value) {
            addCriterion("down_staticstics =", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsNotEqualTo(BigDecimal value) {
            addCriterion("down_staticstics <>", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsGreaterThan(BigDecimal value) {
            addCriterion("down_staticstics >", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("down_staticstics >=", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsLessThan(BigDecimal value) {
            addCriterion("down_staticstics <", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsLessThanOrEqualTo(BigDecimal value) {
            addCriterion("down_staticstics <=", value, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsIn(List<BigDecimal> values) {
            addCriterion("down_staticstics in", values, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsNotIn(List<BigDecimal> values) {
            addCriterion("down_staticstics not in", values, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("down_staticstics between", value1, value2, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andDownStaticsticsNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("down_staticstics not between", value1, value2, "downStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsIsNull() {
            addCriterion("up_staticstics is null");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsIsNotNull() {
            addCriterion("up_staticstics is not null");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsEqualTo(BigDecimal value) {
            addCriterion("up_staticstics =", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsNotEqualTo(BigDecimal value) {
            addCriterion("up_staticstics <>", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsGreaterThan(BigDecimal value) {
            addCriterion("up_staticstics >", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("up_staticstics >=", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsLessThan(BigDecimal value) {
            addCriterion("up_staticstics <", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsLessThanOrEqualTo(BigDecimal value) {
            addCriterion("up_staticstics <=", value, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsIn(List<BigDecimal> values) {
            addCriterion("up_staticstics in", values, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsNotIn(List<BigDecimal> values) {
            addCriterion("up_staticstics not in", values, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_staticstics between", value1, value2, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andUpStaticsticsNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_staticstics not between", value1, value2, "upStaticstics");
            return (Criteria) this;
        }

        public Criteria andRuningTimeIsNull() {
            addCriterion("runing_time is null");
            return (Criteria) this;
        }

        public Criteria andRuningTimeIsNotNull() {
            addCriterion("runing_time is not null");
            return (Criteria) this;
        }

        public Criteria andRuningTimeEqualTo(BigDecimal value) {
            addCriterion("runing_time =", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeNotEqualTo(BigDecimal value) {
            addCriterion("runing_time <>", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeGreaterThan(BigDecimal value) {
            addCriterion("runing_time >", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("runing_time >=", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeLessThan(BigDecimal value) {
            addCriterion("runing_time <", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("runing_time <=", value, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeIn(List<BigDecimal> values) {
            addCriterion("runing_time in", values, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeNotIn(List<BigDecimal> values) {
            addCriterion("runing_time not in", values, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("runing_time between", value1, value2, "runingTime");
            return (Criteria) this;
        }

        public Criteria andRuningTimeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("runing_time not between", value1, value2, "runingTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxIsNull() {
            addCriterion("cpu_rate_max is null");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxIsNotNull() {
            addCriterion("cpu_rate_max is not null");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxEqualTo(BigDecimal value) {
            addCriterion("cpu_rate_max =", value, "cpuRateMax");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxNotEqualTo(BigDecimal value) {
            addCriterion("cpu_rate_max <>", value, "cpuRateMax");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxGreaterThan(BigDecimal value) {
            addCriterion("cpu_rate_max >", value, "cpuRateMax");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cpu_rate_max >=", value, "cpuRateMax");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxLessThan(BigDecimal value) {
            addCriterion("cpu_rate_max <", value, "cpuRateMax");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cpu_rate_max <=", value, "cpuRateMax");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxIn(List<BigDecimal> values) {
            addCriterion("cpu_rate_max in", values, "cpuRateMax");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxNotIn(List<BigDecimal> values) {
            addCriterion("cpu_rate_max not in", values, "cpuRateMax");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cpu_rate_max between", value1, value2, "cpuRateMax");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cpu_rate_max not between", value1, value2, "cpuRateMax");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeIsNull() {
            addCriterion("cpu_rate_max_time is null");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeIsNotNull() {
            addCriterion("cpu_rate_max_time is not null");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeEqualTo(Date value) {
            addCriterion("cpu_rate_max_time =", value, "cpuRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeNotEqualTo(Date value) {
            addCriterion("cpu_rate_max_time <>", value, "cpuRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeGreaterThan(Date value) {
            addCriterion("cpu_rate_max_time >", value, "cpuRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("cpu_rate_max_time >=", value, "cpuRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeLessThan(Date value) {
            addCriterion("cpu_rate_max_time <", value, "cpuRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeLessThanOrEqualTo(Date value) {
            addCriterion("cpu_rate_max_time <=", value, "cpuRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeIn(List<Date> values) {
            addCriterion("cpu_rate_max_time in", values, "cpuRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeNotIn(List<Date> values) {
            addCriterion("cpu_rate_max_time not in", values, "cpuRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeBetween(Date value1, Date value2) {
            addCriterion("cpu_rate_max_time between", value1, value2, "cpuRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateMaxTimeNotBetween(Date value1, Date value2) {
            addCriterion("cpu_rate_max_time not between", value1, value2, "cpuRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgIsNull() {
            addCriterion("cpu_rate_avg is null");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgIsNotNull() {
            addCriterion("cpu_rate_avg is not null");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgEqualTo(BigDecimal value) {
            addCriterion("cpu_rate_avg =", value, "cpuRateAvg");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgNotEqualTo(BigDecimal value) {
            addCriterion("cpu_rate_avg <>", value, "cpuRateAvg");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgGreaterThan(BigDecimal value) {
            addCriterion("cpu_rate_avg >", value, "cpuRateAvg");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cpu_rate_avg >=", value, "cpuRateAvg");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgLessThan(BigDecimal value) {
            addCriterion("cpu_rate_avg <", value, "cpuRateAvg");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cpu_rate_avg <=", value, "cpuRateAvg");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgIn(List<BigDecimal> values) {
            addCriterion("cpu_rate_avg in", values, "cpuRateAvg");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgNotIn(List<BigDecimal> values) {
            addCriterion("cpu_rate_avg not in", values, "cpuRateAvg");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cpu_rate_avg between", value1, value2, "cpuRateAvg");
            return (Criteria) this;
        }

        public Criteria andCpuRateAvgNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cpu_rate_avg not between", value1, value2, "cpuRateAvg");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxIsNull() {
            addCriterion("ram_rate_max is null");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxIsNotNull() {
            addCriterion("ram_rate_max is not null");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxEqualTo(BigDecimal value) {
            addCriterion("ram_rate_max =", value, "ramRateMax");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxNotEqualTo(BigDecimal value) {
            addCriterion("ram_rate_max <>", value, "ramRateMax");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxGreaterThan(BigDecimal value) {
            addCriterion("ram_rate_max >", value, "ramRateMax");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ram_rate_max >=", value, "ramRateMax");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxLessThan(BigDecimal value) {
            addCriterion("ram_rate_max <", value, "ramRateMax");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ram_rate_max <=", value, "ramRateMax");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxIn(List<BigDecimal> values) {
            addCriterion("ram_rate_max in", values, "ramRateMax");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxNotIn(List<BigDecimal> values) {
            addCriterion("ram_rate_max not in", values, "ramRateMax");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ram_rate_max between", value1, value2, "ramRateMax");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ram_rate_max not between", value1, value2, "ramRateMax");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeIsNull() {
            addCriterion("ram_rate_max_time is null");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeIsNotNull() {
            addCriterion("ram_rate_max_time is not null");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeEqualTo(Date value) {
            addCriterion("ram_rate_max_time =", value, "ramRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeNotEqualTo(Date value) {
            addCriterion("ram_rate_max_time <>", value, "ramRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeGreaterThan(Date value) {
            addCriterion("ram_rate_max_time >", value, "ramRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ram_rate_max_time >=", value, "ramRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeLessThan(Date value) {
            addCriterion("ram_rate_max_time <", value, "ramRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeLessThanOrEqualTo(Date value) {
            addCriterion("ram_rate_max_time <=", value, "ramRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeIn(List<Date> values) {
            addCriterion("ram_rate_max_time in", values, "ramRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeNotIn(List<Date> values) {
            addCriterion("ram_rate_max_time not in", values, "ramRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeBetween(Date value1, Date value2) {
            addCriterion("ram_rate_max_time between", value1, value2, "ramRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andRamRateMaxTimeNotBetween(Date value1, Date value2) {
            addCriterion("ram_rate_max_time not between", value1, value2, "ramRateMaxTime");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgIsNull() {
            addCriterion("ram_rate_avg is null");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgIsNotNull() {
            addCriterion("ram_rate_avg is not null");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgEqualTo(BigDecimal value) {
            addCriterion("ram_rate_avg =", value, "ramRateAvg");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgNotEqualTo(BigDecimal value) {
            addCriterion("ram_rate_avg <>", value, "ramRateAvg");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgGreaterThan(BigDecimal value) {
            addCriterion("ram_rate_avg >", value, "ramRateAvg");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ram_rate_avg >=", value, "ramRateAvg");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgLessThan(BigDecimal value) {
            addCriterion("ram_rate_avg <", value, "ramRateAvg");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ram_rate_avg <=", value, "ramRateAvg");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgIn(List<BigDecimal> values) {
            addCriterion("ram_rate_avg in", values, "ramRateAvg");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgNotIn(List<BigDecimal> values) {
            addCriterion("ram_rate_avg not in", values, "ramRateAvg");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ram_rate_avg between", value1, value2, "ramRateAvg");
            return (Criteria) this;
        }

        public Criteria andRamRateAvgNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ram_rate_avg not between", value1, value2, "ramRateAvg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}