package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GatewayAlarmDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GatewayAlarmDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNull() {
            addCriterion("province_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNotNull() {
            addCriterion("province_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualTo(String value) {
            addCriterion("province_name =", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualTo(String value) {
            addCriterion("province_name <>", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThan(String value) {
            addCriterion("province_name >", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_name >=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThan(String value) {
            addCriterion("province_name <", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("province_name <=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLike(String value) {
            addCriterion("province_name like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotLike(String value) {
            addCriterion("province_name not like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIn(List<String> values) {
            addCriterion("province_name in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotIn(List<String> values) {
            addCriterion("province_name not in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameBetween(String value1, String value2) {
            addCriterion("province_name between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotBetween(String value1, String value2) {
            addCriterion("province_name not between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andAdslAccountIsNull() {
            addCriterion("adsl_account is null");
            return (Criteria) this;
        }

        public Criteria andAdslAccountIsNotNull() {
            addCriterion("adsl_account is not null");
            return (Criteria) this;
        }

        public Criteria andAdslAccountEqualTo(String value) {
            addCriterion("adsl_account =", value, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountNotEqualTo(String value) {
            addCriterion("adsl_account <>", value, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountGreaterThan(String value) {
            addCriterion("adsl_account >", value, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountGreaterThanOrEqualTo(String value) {
            addCriterion("adsl_account >=", value, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountLessThan(String value) {
            addCriterion("adsl_account <", value, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountLessThanOrEqualTo(String value) {
            addCriterion("adsl_account <=", value, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountLike(String value) {
            addCriterion("adsl_account like", value, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountNotLike(String value) {
            addCriterion("adsl_account not like", value, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountIn(List<String> values) {
            addCriterion("adsl_account in", values, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountNotIn(List<String> values) {
            addCriterion("adsl_account not in", values, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountBetween(String value1, String value2) {
            addCriterion("adsl_account between", value1, value2, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andAdslAccountNotBetween(String value1, String value2) {
            addCriterion("adsl_account not between", value1, value2, "adslAccount");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIsNull() {
            addCriterion("gateway_sn is null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIsNotNull() {
            addCriterion("gateway_sn is not null");
            return (Criteria) this;
        }

        public Criteria andGatewaySnEqualTo(String value) {
            addCriterion("gateway_sn =", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotEqualTo(String value) {
            addCriterion("gateway_sn <>", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThan(String value) {
            addCriterion("gateway_sn >", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_sn >=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThan(String value) {
            addCriterion("gateway_sn <", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLessThanOrEqualTo(String value) {
            addCriterion("gateway_sn <=", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnLike(String value) {
            addCriterion("gateway_sn like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotLike(String value) {
            addCriterion("gateway_sn not like", value, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnIn(List<String> values) {
            addCriterion("gateway_sn in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotIn(List<String> values) {
            addCriterion("gateway_sn not in", values, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnBetween(String value1, String value2) {
            addCriterion("gateway_sn between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewaySnNotBetween(String value1, String value2) {
            addCriterion("gateway_sn not between", value1, value2, "gatewaySn");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIsNull() {
            addCriterion("gateway_mac is null");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIsNotNull() {
            addCriterion("gateway_mac is not null");
            return (Criteria) this;
        }

        public Criteria andGatewayMacEqualTo(String value) {
            addCriterion("gateway_mac =", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotEqualTo(String value) {
            addCriterion("gateway_mac <>", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacGreaterThan(String value) {
            addCriterion("gateway_mac >", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacGreaterThanOrEqualTo(String value) {
            addCriterion("gateway_mac >=", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLessThan(String value) {
            addCriterion("gateway_mac <", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLessThanOrEqualTo(String value) {
            addCriterion("gateway_mac <=", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacLike(String value) {
            addCriterion("gateway_mac like", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotLike(String value) {
            addCriterion("gateway_mac not like", value, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacIn(List<String> values) {
            addCriterion("gateway_mac in", values, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotIn(List<String> values) {
            addCriterion("gateway_mac not in", values, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacBetween(String value1, String value2) {
            addCriterion("gateway_mac between", value1, value2, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andGatewayMacNotBetween(String value1, String value2) {
            addCriterion("gateway_mac not between", value1, value2, "gatewayMac");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIsNull() {
            addCriterion("factory_id is null");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIsNotNull() {
            addCriterion("factory_id is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryIdEqualTo(Long value) {
            addCriterion("factory_id =", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotEqualTo(Long value) {
            addCriterion("factory_id <>", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdGreaterThan(Long value) {
            addCriterion("factory_id >", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("factory_id >=", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdLessThan(Long value) {
            addCriterion("factory_id <", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdLessThanOrEqualTo(Long value) {
            addCriterion("factory_id <=", value, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdIn(List<Long> values) {
            addCriterion("factory_id in", values, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotIn(List<Long> values) {
            addCriterion("factory_id not in", values, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdBetween(Long value1, Long value2) {
            addCriterion("factory_id between", value1, value2, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryIdNotBetween(Long value1, Long value2) {
            addCriterion("factory_id not between", value1, value2, "factoryId");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIsNull() {
            addCriterion("factory_name is null");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIsNotNull() {
            addCriterion("factory_name is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryNameEqualTo(String value) {
            addCriterion("factory_name =", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotEqualTo(String value) {
            addCriterion("factory_name <>", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameGreaterThan(String value) {
            addCriterion("factory_name >", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("factory_name >=", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLessThan(String value) {
            addCriterion("factory_name <", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLessThanOrEqualTo(String value) {
            addCriterion("factory_name <=", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLike(String value) {
            addCriterion("factory_name like", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotLike(String value) {
            addCriterion("factory_name not like", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIn(List<String> values) {
            addCriterion("factory_name in", values, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotIn(List<String> values) {
            addCriterion("factory_name not in", values, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameBetween(String value1, String value2) {
            addCriterion("factory_name between", value1, value2, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotBetween(String value1, String value2) {
            addCriterion("factory_name not between", value1, value2, "factoryName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIsNull() {
            addCriterion("device_model_id is null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIsNotNull() {
            addCriterion("device_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdEqualTo(Long value) {
            addCriterion("device_model_id =", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotEqualTo(Long value) {
            addCriterion("device_model_id <>", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdGreaterThan(Long value) {
            addCriterion("device_model_id >", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("device_model_id >=", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdLessThan(Long value) {
            addCriterion("device_model_id <", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdLessThanOrEqualTo(Long value) {
            addCriterion("device_model_id <=", value, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdIn(List<Long> values) {
            addCriterion("device_model_id in", values, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotIn(List<Long> values) {
            addCriterion("device_model_id not in", values, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdBetween(Long value1, Long value2) {
            addCriterion("device_model_id between", value1, value2, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIdNotBetween(Long value1, Long value2) {
            addCriterion("device_model_id not between", value1, value2, "deviceModelId");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIsNull() {
            addCriterion("device_model is null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIsNotNull() {
            addCriterion("device_model is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelEqualTo(String value) {
            addCriterion("device_model =", value, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelNotEqualTo(String value) {
            addCriterion("device_model <>", value, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelGreaterThan(String value) {
            addCriterion("device_model >", value, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelGreaterThanOrEqualTo(String value) {
            addCriterion("device_model >=", value, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelLessThan(String value) {
            addCriterion("device_model <", value, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelLessThanOrEqualTo(String value) {
            addCriterion("device_model <=", value, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelLike(String value) {
            addCriterion("device_model like", value, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelNotLike(String value) {
            addCriterion("device_model not like", value, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelIn(List<String> values) {
            addCriterion("device_model in", values, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelNotIn(List<String> values) {
            addCriterion("device_model not in", values, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelBetween(String value1, String value2) {
            addCriterion("device_model between", value1, value2, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andDeviceModelNotBetween(String value1, String value2) {
            addCriterion("device_model not between", value1, value2, "deviceModel");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeIsNull() {
            addCriterion("alarm_type is null");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeIsNotNull() {
            addCriterion("alarm_type is not null");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeEqualTo(Byte value) {
            addCriterion("alarm_type =", value, "alarmType");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeNotEqualTo(Byte value) {
            addCriterion("alarm_type <>", value, "alarmType");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeGreaterThan(Byte value) {
            addCriterion("alarm_type >", value, "alarmType");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("alarm_type >=", value, "alarmType");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeLessThan(Byte value) {
            addCriterion("alarm_type <", value, "alarmType");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeLessThanOrEqualTo(Byte value) {
            addCriterion("alarm_type <=", value, "alarmType");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeIn(List<Byte> values) {
            addCriterion("alarm_type in", values, "alarmType");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeNotIn(List<Byte> values) {
            addCriterion("alarm_type not in", values, "alarmType");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeBetween(Byte value1, Byte value2) {
            addCriterion("alarm_type between", value1, value2, "alarmType");
            return (Criteria) this;
        }

        public Criteria andAlarmTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("alarm_type not between", value1, value2, "alarmType");
            return (Criteria) this;
        }

        public Criteria andAlarmNameIsNull() {
            addCriterion("alarm_name is null");
            return (Criteria) this;
        }

        public Criteria andAlarmNameIsNotNull() {
            addCriterion("alarm_name is not null");
            return (Criteria) this;
        }

        public Criteria andAlarmNameEqualTo(String value) {
            addCriterion("alarm_name =", value, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameNotEqualTo(String value) {
            addCriterion("alarm_name <>", value, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameGreaterThan(String value) {
            addCriterion("alarm_name >", value, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameGreaterThanOrEqualTo(String value) {
            addCriterion("alarm_name >=", value, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameLessThan(String value) {
            addCriterion("alarm_name <", value, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameLessThanOrEqualTo(String value) {
            addCriterion("alarm_name <=", value, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameLike(String value) {
            addCriterion("alarm_name like", value, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameNotLike(String value) {
            addCriterion("alarm_name not like", value, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameIn(List<String> values) {
            addCriterion("alarm_name in", values, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameNotIn(List<String> values) {
            addCriterion("alarm_name not in", values, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameBetween(String value1, String value2) {
            addCriterion("alarm_name between", value1, value2, "alarmName");
            return (Criteria) this;
        }

        public Criteria andAlarmNameNotBetween(String value1, String value2) {
            addCriterion("alarm_name not between", value1, value2, "alarmName");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureIsNull() {
            addCriterion("main_chip_temperature is null");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureIsNotNull() {
            addCriterion("main_chip_temperature is not null");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureEqualTo(String value) {
            addCriterion("main_chip_temperature =", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureNotEqualTo(String value) {
            addCriterion("main_chip_temperature <>", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureGreaterThan(String value) {
            addCriterion("main_chip_temperature >", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureGreaterThanOrEqualTo(String value) {
            addCriterion("main_chip_temperature >=", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureLessThan(String value) {
            addCriterion("main_chip_temperature <", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureLessThanOrEqualTo(String value) {
            addCriterion("main_chip_temperature <=", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureLike(String value) {
            addCriterion("main_chip_temperature like", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureNotLike(String value) {
            addCriterion("main_chip_temperature not like", value, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureIn(List<String> values) {
            addCriterion("main_chip_temperature in", values, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureNotIn(List<String> values) {
            addCriterion("main_chip_temperature not in", values, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureBetween(String value1, String value2) {
            addCriterion("main_chip_temperature between", value1, value2, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andMainChipTemperatureNotBetween(String value1, String value2) {
            addCriterion("main_chip_temperature not between", value1, value2, "mainChipTemperature");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeIsNull() {
            addCriterion("alarm_time is null");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeIsNotNull() {
            addCriterion("alarm_time is not null");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeEqualTo(Date value) {
            addCriterion("alarm_time =", value, "alarmTime");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeNotEqualTo(Date value) {
            addCriterion("alarm_time <>", value, "alarmTime");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeGreaterThan(Date value) {
            addCriterion("alarm_time >", value, "alarmTime");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("alarm_time >=", value, "alarmTime");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeLessThan(Date value) {
            addCriterion("alarm_time <", value, "alarmTime");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeLessThanOrEqualTo(Date value) {
            addCriterion("alarm_time <=", value, "alarmTime");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeIn(List<Date> values) {
            addCriterion("alarm_time in", values, "alarmTime");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeNotIn(List<Date> values) {
            addCriterion("alarm_time not in", values, "alarmTime");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeBetween(Date value1, Date value2) {
            addCriterion("alarm_time between", value1, value2, "alarmTime");
            return (Criteria) this;
        }

        public Criteria andAlarmTimeNotBetween(Date value1, Date value2) {
            addCriterion("alarm_time not between", value1, value2, "alarmTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andGdateIsNull() {
            addCriterion("gdate is null");
            return (Criteria) this;
        }

        public Criteria andGdateIsNotNull() {
            addCriterion("gdate is not null");
            return (Criteria) this;
        }

        public Criteria andGdateEqualTo(Long value) {
            addCriterion("gdate =", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotEqualTo(Long value) {
            addCriterion("gdate <>", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThan(Long value) {
            addCriterion("gdate >", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThanOrEqualTo(Long value) {
            addCriterion("gdate >=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThan(Long value) {
            addCriterion("gdate <", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThanOrEqualTo(Long value) {
            addCriterion("gdate <=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateIn(List<Long> values) {
            addCriterion("gdate in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotIn(List<Long> values) {
            addCriterion("gdate not in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateBetween(Long value1, Long value2) {
            addCriterion("gdate between", value1, value2, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotBetween(Long value1, Long value2) {
            addCriterion("gdate not between", value1, value2, "gdate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}