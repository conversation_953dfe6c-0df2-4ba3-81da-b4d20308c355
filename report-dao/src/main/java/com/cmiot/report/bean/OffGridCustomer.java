package com.cmiot.report.bean;

import java.util.Date;

public class OffGridCustomer {
    private Long enterpriseId;

    private String customerId;

    private String customerName;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private Integer customerStatus;

    private String valueCategory;

    private Integer offGridScore;

    private Integer offGridTag;

    private Integer terminalScore;

    private Integer terminalTag;

    private Integer networkScore;

    private Integer networkTag;

    private Integer serviceScore;

    private Integer serviceTag;

    private Integer businessScore;

    private Integer businessTag;

    private Integer maintenanceScore;

    private Integer maintenanceTag;

    private Date sampleTime;

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    public Integer getCustomerStatus() {
        return customerStatus;
    }

    public void setCustomerStatus(Integer customerStatus) {
        this.customerStatus = customerStatus;
    }

    public String getValueCategory() {
        return valueCategory;
    }

    public void setValueCategory(String valueCategory) {
        this.valueCategory = valueCategory == null ? null : valueCategory.trim();
    }

    public Integer getOffGridScore() {
        return offGridScore;
    }

    public void setOffGridScore(Integer offGridScore) {
        this.offGridScore = offGridScore;
    }

    public Integer getOffGridTag() {
        return offGridTag;
    }

    public void setOffGridTag(Integer offGridTag) {
        this.offGridTag = offGridTag;
    }

    public Integer getTerminalScore() {
        return terminalScore;
    }

    public void setTerminalScore(Integer terminalScore) {
        this.terminalScore = terminalScore;
    }

    public Integer getTerminalTag() {
        return terminalTag;
    }

    public void setTerminalTag(Integer terminalTag) {
        this.terminalTag = terminalTag;
    }

    public Integer getNetworkScore() {
        return networkScore;
    }

    public void setNetworkScore(Integer networkScore) {
        this.networkScore = networkScore;
    }

    public Integer getNetworkTag() {
        return networkTag;
    }

    public void setNetworkTag(Integer networkTag) {
        this.networkTag = networkTag;
    }

    public Integer getServiceScore() {
        return serviceScore;
    }

    public void setServiceScore(Integer serviceScore) {
        this.serviceScore = serviceScore;
    }

    public Integer getServiceTag() {
        return serviceTag;
    }

    public void setServiceTag(Integer serviceTag) {
        this.serviceTag = serviceTag;
    }

    public Integer getBusinessScore() {
        return businessScore;
    }

    public void setBusinessScore(Integer businessScore) {
        this.businessScore = businessScore;
    }

    public Integer getBusinessTag() {
        return businessTag;
    }

    public void setBusinessTag(Integer businessTag) {
        this.businessTag = businessTag;
    }

    public Integer getMaintenanceScore() {
        return maintenanceScore;
    }

    public void setMaintenanceScore(Integer maintenanceScore) {
        this.maintenanceScore = maintenanceScore;
    }

    public Integer getMaintenanceTag() {
        return maintenanceTag;
    }

    public void setMaintenanceTag(Integer maintenanceTag) {
        this.maintenanceTag = maintenanceTag;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }
}