package com.cmiot.report.bean;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@EqualsAndHashCode
public class GatewayOverviewCount implements Serializable {
    private static final long serialVersionUID = 2073384727310117759L;

    private Long gatewayCount;

    private Long gatewayIncCount;

    private Long gatewayActiveCount;

    private Long gatewayLostCount;

    private Double gatewayYoyRatio;

    private Double gatewayMomRatio;

    private Long connectFamilyGatewayNum;

    private Double connectFamilyGatewayRate;

    private Long gdate;

}