package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.List;

public class DicPppoeErrorTableExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DicPppoeErrorTableExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPppoeErrorIsNull() {
            addCriterion("pppoe_error is null");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorIsNotNull() {
            addCriterion("pppoe_error is not null");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorEqualTo(String value) {
            addCriterion("pppoe_error =", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorNotEqualTo(String value) {
            addCriterion("pppoe_error <>", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorGreaterThan(String value) {
            addCriterion("pppoe_error >", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorGreaterThanOrEqualTo(String value) {
            addCriterion("pppoe_error >=", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorLessThan(String value) {
            addCriterion("pppoe_error <", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorLessThanOrEqualTo(String value) {
            addCriterion("pppoe_error <=", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorLike(String value) {
            addCriterion("pppoe_error like", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorNotLike(String value) {
            addCriterion("pppoe_error not like", value, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorIn(List<String> values) {
            addCriterion("pppoe_error in", values, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorNotIn(List<String> values) {
            addCriterion("pppoe_error not in", values, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorBetween(String value1, String value2) {
            addCriterion("pppoe_error between", value1, value2, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorNotBetween(String value1, String value2) {
            addCriterion("pppoe_error not between", value1, value2, "pppoeError");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescIsNull() {
            addCriterion("pppoe_error_desc is null");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescIsNotNull() {
            addCriterion("pppoe_error_desc is not null");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescEqualTo(String value) {
            addCriterion("pppoe_error_desc =", value, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescNotEqualTo(String value) {
            addCriterion("pppoe_error_desc <>", value, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescGreaterThan(String value) {
            addCriterion("pppoe_error_desc >", value, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescGreaterThanOrEqualTo(String value) {
            addCriterion("pppoe_error_desc >=", value, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescLessThan(String value) {
            addCriterion("pppoe_error_desc <", value, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescLessThanOrEqualTo(String value) {
            addCriterion("pppoe_error_desc <=", value, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescLike(String value) {
            addCriterion("pppoe_error_desc like", value, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescNotLike(String value) {
            addCriterion("pppoe_error_desc not like", value, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescIn(List<String> values) {
            addCriterion("pppoe_error_desc in", values, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescNotIn(List<String> values) {
            addCriterion("pppoe_error_desc not in", values, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescBetween(String value1, String value2) {
            addCriterion("pppoe_error_desc between", value1, value2, "pppoeErrorDesc");
            return (Criteria) this;
        }

        public Criteria andPppoeErrorDescNotBetween(String value1, String value2) {
            addCriterion("pppoe_error_desc not between", value1, value2, "pppoeErrorDesc");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}