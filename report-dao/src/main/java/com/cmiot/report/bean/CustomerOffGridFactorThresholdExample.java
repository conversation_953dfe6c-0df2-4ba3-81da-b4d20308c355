package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CustomerOffGridFactorThresholdExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CustomerOffGridFactorThresholdExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andTimestampIsNull() {
            addCriterion("timestamp is null");
            return (Criteria) this;
        }

        public Criteria andTimestampIsNotNull() {
            addCriterion("timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andTimestampEqualTo(Long value) {
            addCriterion("timestamp =", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampNotEqualTo(Long value) {
            addCriterion("timestamp <>", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampGreaterThan(Long value) {
            addCriterion("timestamp >", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampGreaterThanOrEqualTo(Long value) {
            addCriterion("timestamp >=", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampLessThan(Long value) {
            addCriterion("timestamp <", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampLessThanOrEqualTo(Long value) {
            addCriterion("timestamp <=", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampIn(List<Long> values) {
            addCriterion("timestamp in", values, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampNotIn(List<Long> values) {
            addCriterion("timestamp not in", values, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampBetween(Long value1, Long value2) {
            addCriterion("timestamp between", value1, value2, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampNotBetween(Long value1, Long value2) {
            addCriterion("timestamp not between", value1, value2, "timestamp");
            return (Criteria) this;
        }

        public Criteria andFactorTypeIsNull() {
            addCriterion("factor_type is null");
            return (Criteria) this;
        }

        public Criteria andFactorTypeIsNotNull() {
            addCriterion("factor_type is not null");
            return (Criteria) this;
        }

        public Criteria andFactorTypeEqualTo(Byte value) {
            addCriterion("factor_type =", value, "factorType");
            return (Criteria) this;
        }

        public Criteria andFactorTypeNotEqualTo(Byte value) {
            addCriterion("factor_type <>", value, "factorType");
            return (Criteria) this;
        }

        public Criteria andFactorTypeGreaterThan(Byte value) {
            addCriterion("factor_type >", value, "factorType");
            return (Criteria) this;
        }

        public Criteria andFactorTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("factor_type >=", value, "factorType");
            return (Criteria) this;
        }

        public Criteria andFactorTypeLessThan(Byte value) {
            addCriterion("factor_type <", value, "factorType");
            return (Criteria) this;
        }

        public Criteria andFactorTypeLessThanOrEqualTo(Byte value) {
            addCriterion("factor_type <=", value, "factorType");
            return (Criteria) this;
        }

        public Criteria andFactorTypeIn(List<Byte> values) {
            addCriterion("factor_type in", values, "factorType");
            return (Criteria) this;
        }

        public Criteria andFactorTypeNotIn(List<Byte> values) {
            addCriterion("factor_type not in", values, "factorType");
            return (Criteria) this;
        }

        public Criteria andFactorTypeBetween(Byte value1, Byte value2) {
            addCriterion("factor_type between", value1, value2, "factorType");
            return (Criteria) this;
        }

        public Criteria andFactorTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("factor_type not between", value1, value2, "factorType");
            return (Criteria) this;
        }

        public Criteria andFactorNameIsNull() {
            addCriterion("factor_name is null");
            return (Criteria) this;
        }

        public Criteria andFactorNameIsNotNull() {
            addCriterion("factor_name is not null");
            return (Criteria) this;
        }

        public Criteria andFactorNameEqualTo(String value) {
            addCriterion("factor_name =", value, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameNotEqualTo(String value) {
            addCriterion("factor_name <>", value, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameGreaterThan(String value) {
            addCriterion("factor_name >", value, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameGreaterThanOrEqualTo(String value) {
            addCriterion("factor_name >=", value, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameLessThan(String value) {
            addCriterion("factor_name <", value, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameLessThanOrEqualTo(String value) {
            addCriterion("factor_name <=", value, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameLike(String value) {
            addCriterion("factor_name like", value, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameNotLike(String value) {
            addCriterion("factor_name not like", value, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameIn(List<String> values) {
            addCriterion("factor_name in", values, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameNotIn(List<String> values) {
            addCriterion("factor_name not in", values, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameBetween(String value1, String value2) {
            addCriterion("factor_name between", value1, value2, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorNameNotBetween(String value1, String value2) {
            addCriterion("factor_name not between", value1, value2, "factorName");
            return (Criteria) this;
        }

        public Criteria andFactorWeightIsNull() {
            addCriterion("factor_weight is null");
            return (Criteria) this;
        }

        public Criteria andFactorWeightIsNotNull() {
            addCriterion("factor_weight is not null");
            return (Criteria) this;
        }

        public Criteria andFactorWeightEqualTo(Double value) {
            addCriterion("factor_weight =", value, "factorWeight");
            return (Criteria) this;
        }

        public Criteria andFactorWeightNotEqualTo(Double value) {
            addCriterion("factor_weight <>", value, "factorWeight");
            return (Criteria) this;
        }

        public Criteria andFactorWeightGreaterThan(Double value) {
            addCriterion("factor_weight >", value, "factorWeight");
            return (Criteria) this;
        }

        public Criteria andFactorWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("factor_weight >=", value, "factorWeight");
            return (Criteria) this;
        }

        public Criteria andFactorWeightLessThan(Double value) {
            addCriterion("factor_weight <", value, "factorWeight");
            return (Criteria) this;
        }

        public Criteria andFactorWeightLessThanOrEqualTo(Double value) {
            addCriterion("factor_weight <=", value, "factorWeight");
            return (Criteria) this;
        }

        public Criteria andFactorWeightIn(List<Double> values) {
            addCriterion("factor_weight in", values, "factorWeight");
            return (Criteria) this;
        }

        public Criteria andFactorWeightNotIn(List<Double> values) {
            addCriterion("factor_weight not in", values, "factorWeight");
            return (Criteria) this;
        }

        public Criteria andFactorWeightBetween(Double value1, Double value2) {
            addCriterion("factor_weight between", value1, value2, "factorWeight");
            return (Criteria) this;
        }

        public Criteria andFactorWeightNotBetween(Double value1, Double value2) {
            addCriterion("factor_weight not between", value1, value2, "factorWeight");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdIsNull() {
            addCriterion("factor_threshold is null");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdIsNotNull() {
            addCriterion("factor_threshold is not null");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdEqualTo(Integer value) {
            addCriterion("factor_threshold =", value, "factorThreshold");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdNotEqualTo(Integer value) {
            addCriterion("factor_threshold <>", value, "factorThreshold");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdGreaterThan(Integer value) {
            addCriterion("factor_threshold >", value, "factorThreshold");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdGreaterThanOrEqualTo(Integer value) {
            addCriterion("factor_threshold >=", value, "factorThreshold");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdLessThan(Integer value) {
            addCriterion("factor_threshold <", value, "factorThreshold");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdLessThanOrEqualTo(Integer value) {
            addCriterion("factor_threshold <=", value, "factorThreshold");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdIn(List<Integer> values) {
            addCriterion("factor_threshold in", values, "factorThreshold");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdNotIn(List<Integer> values) {
            addCriterion("factor_threshold not in", values, "factorThreshold");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdBetween(Integer value1, Integer value2) {
            addCriterion("factor_threshold between", value1, value2, "factorThreshold");
            return (Criteria) this;
        }

        public Criteria andFactorThresholdNotBetween(Integer value1, Integer value2) {
            addCriterion("factor_threshold not between", value1, value2, "factorThreshold");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeIsNull() {
            addCriterion("factor_indicate_type is null");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeIsNotNull() {
            addCriterion("factor_indicate_type is not null");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeEqualTo(Byte value) {
            addCriterion("factor_indicate_type =", value, "factorIndicateType");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeNotEqualTo(Byte value) {
            addCriterion("factor_indicate_type <>", value, "factorIndicateType");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeGreaterThan(Byte value) {
            addCriterion("factor_indicate_type >", value, "factorIndicateType");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("factor_indicate_type >=", value, "factorIndicateType");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeLessThan(Byte value) {
            addCriterion("factor_indicate_type <", value, "factorIndicateType");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeLessThanOrEqualTo(Byte value) {
            addCriterion("factor_indicate_type <=", value, "factorIndicateType");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeIn(List<Byte> values) {
            addCriterion("factor_indicate_type in", values, "factorIndicateType");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeNotIn(List<Byte> values) {
            addCriterion("factor_indicate_type not in", values, "factorIndicateType");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeBetween(Byte value1, Byte value2) {
            addCriterion("factor_indicate_type between", value1, value2, "factorIndicateType");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("factor_indicate_type not between", value1, value2, "factorIndicateType");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescIsNull() {
            addCriterion("factor_indicate_desc is null");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescIsNotNull() {
            addCriterion("factor_indicate_desc is not null");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescEqualTo(String value) {
            addCriterion("factor_indicate_desc =", value, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescNotEqualTo(String value) {
            addCriterion("factor_indicate_desc <>", value, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescGreaterThan(String value) {
            addCriterion("factor_indicate_desc >", value, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescGreaterThanOrEqualTo(String value) {
            addCriterion("factor_indicate_desc >=", value, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescLessThan(String value) {
            addCriterion("factor_indicate_desc <", value, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescLessThanOrEqualTo(String value) {
            addCriterion("factor_indicate_desc <=", value, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescLike(String value) {
            addCriterion("factor_indicate_desc like", value, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescNotLike(String value) {
            addCriterion("factor_indicate_desc not like", value, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescIn(List<String> values) {
            addCriterion("factor_indicate_desc in", values, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescNotIn(List<String> values) {
            addCriterion("factor_indicate_desc not in", values, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescBetween(String value1, String value2) {
            addCriterion("factor_indicate_desc between", value1, value2, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateDescNotBetween(String value1, String value2) {
            addCriterion("factor_indicate_desc not between", value1, value2, "factorIndicateDesc");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightIsNull() {
            addCriterion("factor_indicate_weight is null");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightIsNotNull() {
            addCriterion("factor_indicate_weight is not null");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightEqualTo(Double value) {
            addCriterion("factor_indicate_weight =", value, "factorIndicateWeight");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightNotEqualTo(Double value) {
            addCriterion("factor_indicate_weight <>", value, "factorIndicateWeight");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightGreaterThan(Double value) {
            addCriterion("factor_indicate_weight >", value, "factorIndicateWeight");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("factor_indicate_weight >=", value, "factorIndicateWeight");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightLessThan(Double value) {
            addCriterion("factor_indicate_weight <", value, "factorIndicateWeight");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightLessThanOrEqualTo(Double value) {
            addCriterion("factor_indicate_weight <=", value, "factorIndicateWeight");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightIn(List<Double> values) {
            addCriterion("factor_indicate_weight in", values, "factorIndicateWeight");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightNotIn(List<Double> values) {
            addCriterion("factor_indicate_weight not in", values, "factorIndicateWeight");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightBetween(Double value1, Double value2) {
            addCriterion("factor_indicate_weight between", value1, value2, "factorIndicateWeight");
            return (Criteria) this;
        }

        public Criteria andFactorIndicateWeightNotBetween(Double value1, Double value2) {
            addCriterion("factor_indicate_weight not between", value1, value2, "factorIndicateWeight");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andGdateIsNull() {
            addCriterion("gdate is null");
            return (Criteria) this;
        }

        public Criteria andGdateIsNotNull() {
            addCriterion("gdate is not null");
            return (Criteria) this;
        }

        public Criteria andGdateEqualTo(Long value) {
            addCriterion("gdate =", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotEqualTo(Long value) {
            addCriterion("gdate <>", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThan(Long value) {
            addCriterion("gdate >", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThanOrEqualTo(Long value) {
            addCriterion("gdate >=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThan(Long value) {
            addCriterion("gdate <", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThanOrEqualTo(Long value) {
            addCriterion("gdate <=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateIn(List<Long> values) {
            addCriterion("gdate in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotIn(List<Long> values) {
            addCriterion("gdate not in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateBetween(Long value1, Long value2) {
            addCriterion("gdate between", value1, value2, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotBetween(Long value1, Long value2) {
            addCriterion("gdate not between", value1, value2, "gdate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}