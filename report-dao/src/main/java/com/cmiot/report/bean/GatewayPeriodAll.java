package com.cmiot.report.bean;

import java.math.BigDecimal;
import java.util.Date;

public class GatewayPeriodAll {
    private String gatewaySn;

    private String gatewayName;

    private Long factoryId;

    private String gatewayVendor;

    private String gatewayCpu;

    private String gatewayHardwareVer;

    private String gatewayFirmwareVer;

    private Long deviceModelId;

    private String gatewayProductclass;

    private String gatewayMac;

    private Integer gatewayFlashSize;

    private Integer gatewayRamSize;

    private String gatewayNfc;

    private String osgiName;

    private String osgiVendor;

    private String osgiVersion;

    private String jvmName;

    private String jvmVendor;

    private String jvmVersion;

    private String account;

    private String provinceCode;

    private String cityCode;

    private String bundleVersion;

    private Long enterpriseId;

    private String customerId;

    private Date bootTime;

    private Integer runingTime;

    private Date sampleTime;

    private Byte cpu;

    private Byte ram;

    private String mainChipTemperature;

    private String lanIp;

    private String wanIp;

    private String lanIpv6;

    private String wanIpv6;

    private String lan1connectstatus;

    private String lan2connectstatus;

    private String lan3connectstatus;

    private String lan4connectstatus;

    private String lan5connectstatus;

    private String lan6connectstatus;

    private String lan7connectstatus;

    private String lan8connectstatus;

    private String wan;

    private String wifi;

    private String pppoeUpTime;

    private String pppoeError;

    private String pppoeStatus;

    private Integer ponTxPower;

    private Integer ponRxPower;

    private Integer transceiverTemperature;

    private String wanIndex;

    private String wanName;

    private BigDecimal averTxrate;

    private BigDecimal averRxrate;

    private BigDecimal maxTxrate;

    private BigDecimal maxRxrate;

    private BigDecimal upStaticstics;

    private BigDecimal downStaticstics;

    private String support;

    private String potsIndex;

    private String phoneNumber;

    private String status;

    private Integer deviceNum;
    private Integer onlineDeviceNum;

    public String getGatewaySn() {
        return gatewaySn;
    }

    public void setGatewaySn(String gatewaySn) {
        this.gatewaySn = gatewaySn == null ? null : gatewaySn.trim();
    }

    public String getGatewayName() {
        return gatewayName;
    }

    public void setGatewayName(String gatewayName) {
        this.gatewayName = gatewayName == null ? null : gatewayName.trim();
    }

    public Long getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Long factoryId) {
        this.factoryId = factoryId;
    }

    public String getGatewayVendor() {
        return gatewayVendor;
    }

    public void setGatewayVendor(String gatewayVendor) {
        this.gatewayVendor = gatewayVendor == null ? null : gatewayVendor.trim();
    }

    public String getGatewayCpu() {
        return gatewayCpu;
    }

    public void setGatewayCpu(String gatewayCpu) {
        this.gatewayCpu = gatewayCpu == null ? null : gatewayCpu.trim();
    }

    public String getGatewayHardwareVer() {
        return gatewayHardwareVer;
    }

    public void setGatewayHardwareVer(String gatewayHardwareVer) {
        this.gatewayHardwareVer = gatewayHardwareVer == null ? null : gatewayHardwareVer.trim();
    }

    public String getGatewayFirmwareVer() {
        return gatewayFirmwareVer;
    }

    public void setGatewayFirmwareVer(String gatewayFirmwareVer) {
        this.gatewayFirmwareVer = gatewayFirmwareVer == null ? null : gatewayFirmwareVer.trim();
    }

    public Long getDeviceModelId() {
        return deviceModelId;
    }

    public void setDeviceModelId(Long deviceModelId) {
        this.deviceModelId = deviceModelId;
    }

    public String getGatewayProductclass() {
        return gatewayProductclass;
    }

    public void setGatewayProductclass(String gatewayProductclass) {
        this.gatewayProductclass = gatewayProductclass == null ? null : gatewayProductclass.trim();
    }

    public String getGatewayMac() {
        return gatewayMac;
    }

    public void setGatewayMac(String gatewayMac) {
        this.gatewayMac = gatewayMac == null ? null : gatewayMac.trim();
    }

    public Integer getGatewayFlashSize() {
        return gatewayFlashSize;
    }

    public void setGatewayFlashSize(Integer gatewayFlashSize) {
        this.gatewayFlashSize = gatewayFlashSize;
    }

    public Integer getGatewayRamSize() {
        return gatewayRamSize;
    }

    public void setGatewayRamSize(Integer gatewayRamSize) {
        this.gatewayRamSize = gatewayRamSize;
    }

    public String getGatewayNfc() {
        return gatewayNfc;
    }

    public void setGatewayNfc(String gatewayNfc) {
        this.gatewayNfc = gatewayNfc == null ? null : gatewayNfc.trim();
    }

    public String getOsgiName() {
        return osgiName;
    }

    public void setOsgiName(String osgiName) {
        this.osgiName = osgiName == null ? null : osgiName.trim();
    }

    public String getOsgiVendor() {
        return osgiVendor;
    }

    public void setOsgiVendor(String osgiVendor) {
        this.osgiVendor = osgiVendor == null ? null : osgiVendor.trim();
    }

    public String getOsgiVersion() {
        return osgiVersion;
    }

    public void setOsgiVersion(String osgiVersion) {
        this.osgiVersion = osgiVersion == null ? null : osgiVersion.trim();
    }

    public String getJvmName() {
        return jvmName;
    }

    public void setJvmName(String jvmName) {
        this.jvmName = jvmName == null ? null : jvmName.trim();
    }

    public String getJvmVendor() {
        return jvmVendor;
    }

    public void setJvmVendor(String jvmVendor) {
        this.jvmVendor = jvmVendor == null ? null : jvmVendor.trim();
    }

    public String getJvmVersion() {
        return jvmVersion;
    }

    public void setJvmVersion(String jvmVersion) {
        this.jvmVersion = jvmVersion == null ? null : jvmVersion.trim();
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account == null ? null : account.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getBundleVersion() {
        return bundleVersion;
    }

    public void setBundleVersion(String bundleVersion) {
        this.bundleVersion = bundleVersion == null ? null : bundleVersion.trim();
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public Date getBootTime() {
        return bootTime;
    }

    public void setBootTime(Date bootTime) {
        this.bootTime = bootTime;
    }

    public Integer getRuningTime() {
        return runingTime;
    }

    public void setRuningTime(Integer runingTime) {
        this.runingTime = runingTime;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }

    public Byte getCpu() {
        return cpu;
    }

    public void setCpu(Byte cpu) {
        this.cpu = cpu;
    }

    public Byte getRam() {
        return ram;
    }

    public void setRam(Byte ram) {
        this.ram = ram;
    }

    public String getMainChipTemperature() {
        return mainChipTemperature;
    }

    public void setMainChipTemperature(String mainChipTemperature) {
        this.mainChipTemperature = mainChipTemperature == null ? null : mainChipTemperature.trim();
    }

    public String getLanIp() {
        return lanIp;
    }

    public void setLanIp(String lanIp) {
        this.lanIp = lanIp == null ? null : lanIp.trim();
    }

    public String getWanIp() {
        return wanIp;
    }

    public void setWanIp(String wanIp) {
        this.wanIp = wanIp == null ? null : wanIp.trim();
    }

    public String getLanIpv6() {
        return lanIpv6;
    }

    public void setLanIpv6(String lanIpv6) {
        this.lanIpv6 = lanIpv6 == null ? null : lanIpv6.trim();
    }

    public String getWanIpv6() {
        return wanIpv6;
    }

    public void setWanIpv6(String wanIpv6) {
        this.wanIpv6 = wanIpv6 == null ? null : wanIpv6.trim();
    }

    public String getLan1connectstatus() {
        return lan1connectstatus;
    }

    public void setLan1connectstatus(String lan1connectstatus) {
        this.lan1connectstatus = lan1connectstatus == null ? null : lan1connectstatus.trim();
    }

    public String getLan2connectstatus() {
        return lan2connectstatus;
    }

    public void setLan2connectstatus(String lan2connectstatus) {
        this.lan2connectstatus = lan2connectstatus == null ? null : lan2connectstatus.trim();
    }

    public String getLan3connectstatus() {
        return lan3connectstatus;
    }

    public void setLan3connectstatus(String lan3connectstatus) {
        this.lan3connectstatus = lan3connectstatus == null ? null : lan3connectstatus.trim();
    }

    public String getLan4connectstatus() {
        return lan4connectstatus;
    }

    public void setLan4connectstatus(String lan4connectstatus) {
        this.lan4connectstatus = lan4connectstatus == null ? null : lan4connectstatus.trim();
    }

    public String getLan5connectstatus() {
        return lan5connectstatus;
    }

    public void setLan5connectstatus(String lan5connectstatus) {
        this.lan5connectstatus = lan5connectstatus == null ? null : lan5connectstatus.trim();
    }

    public String getLan6connectstatus() {
        return lan6connectstatus;
    }

    public void setLan6connectstatus(String lan6connectstatus) {
        this.lan6connectstatus = lan6connectstatus == null ? null : lan6connectstatus.trim();
    }

    public String getLan7connectstatus() {
        return lan7connectstatus;
    }

    public void setLan7connectstatus(String lan7connectstatus) {
        this.lan7connectstatus = lan7connectstatus == null ? null : lan7connectstatus.trim();
    }

    public String getLan8connectstatus() {
        return lan8connectstatus;
    }

    public void setLan8connectstatus(String lan8connectstatus) {
        this.lan8connectstatus = lan8connectstatus == null ? null : lan8connectstatus.trim();
    }

    public String getWan() {
        return wan;
    }

    public void setWan(String wan) {
        this.wan = wan == null ? null : wan.trim();
    }

    public String getWifi() {
        return wifi;
    }

    public void setWifi(String wifi) {
        this.wifi = wifi == null ? null : wifi.trim();
    }

    public String getPppoeUpTime() {
        return pppoeUpTime;
    }

    public void setPppoeUpTime(String pppoeUpTime) {
        this.pppoeUpTime = pppoeUpTime == null ? null : pppoeUpTime.trim();
    }

    public String getPppoeError() {
        return pppoeError;
    }

    public void setPppoeError(String pppoeError) {
        this.pppoeError = pppoeError == null ? null : pppoeError.trim();
    }

    public String getPppoeStatus() {
        return pppoeStatus;
    }

    public void setPppoeStatus(String pppoeStatus) {
        this.pppoeStatus = pppoeStatus == null ? null : pppoeStatus.trim();
    }

    public Integer getPonTxPower() {
        return ponTxPower;
    }

    public void setPonTxPower(Integer ponTxPower) {
        this.ponTxPower = ponTxPower;
    }

    public Integer getPonRxPower() {
        return ponRxPower;
    }

    public void setPonRxPower(Integer ponRxPower) {
        this.ponRxPower = ponRxPower;
    }

    public Integer getTransceiverTemperature() {
        return transceiverTemperature;
    }

    public void setTransceiverTemperature(Integer transceiverTemperature) {
        this.transceiverTemperature = transceiverTemperature;
    }

    public String getWanIndex() {
        return wanIndex;
    }

    public void setWanIndex(String wanIndex) {
        this.wanIndex = wanIndex == null ? null : wanIndex.trim();
    }

    public String getWanName() {
        return wanName;
    }

    public void setWanName(String wanName) {
        this.wanName = wanName == null ? null : wanName.trim();
    }

    public BigDecimal getAverTxrate() {
        return averTxrate;
    }

    public void setAverTxrate(BigDecimal averTxrate) {
        this.averTxrate = averTxrate;
    }

    public BigDecimal getAverRxrate() {
        return averRxrate;
    }

    public void setAverRxrate(BigDecimal averRxrate) {
        this.averRxrate = averRxrate;
    }

    public BigDecimal getMaxTxrate() {
        return maxTxrate;
    }

    public void setMaxTxrate(BigDecimal maxTxrate) {
        this.maxTxrate = maxTxrate;
    }

    public BigDecimal getMaxRxrate() {
        return maxRxrate;
    }

    public void setMaxRxrate(BigDecimal maxRxrate) {
        this.maxRxrate = maxRxrate;
    }

    public BigDecimal getUpStaticstics() {
        return upStaticstics;
    }

    public void setUpStaticstics(BigDecimal upStaticstics) {
        this.upStaticstics = upStaticstics;
    }

    public BigDecimal getDownStaticstics() {
        return downStaticstics;
    }

    public void setDownStaticstics(BigDecimal downStaticstics) {
        this.downStaticstics = downStaticstics;
    }

    public String getSupport() {
        return support;
    }

    public void setSupport(String support) {
        this.support = support == null ? null : support.trim();
    }

    public String getPotsIndex() {
        return potsIndex;
    }

    public void setPotsIndex(String potsIndex) {
        this.potsIndex = potsIndex == null ? null : potsIndex.trim();
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber == null ? null : phoneNumber.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Integer getDeviceNum() {
        return deviceNum;
    }

    public void setDeviceNum(Integer deviceNum) {
        this.deviceNum = deviceNum;
    }

    public Integer getOnlineDeviceNum() {
        return onlineDeviceNum;
    }

    public void setOnlineDeviceNum(Integer onlineDeviceNum) {
        this.onlineDeviceNum = onlineDeviceNum;
    }
}