package com.cmiot.report.bean;

import java.util.Objects;

public class GatewayIncrementCount {
    private String factoryCode;

    private String provinceCode;

    private String industry;

    private Long gatewayIncCount;

    private Long gdate;

    private Integer connectType;

    public String getFactoryCode() {
        return factoryCode;
    }

    public void setFactoryCode(String factoryCode) {
        this.factoryCode = factoryCode == null ? null : factoryCode.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry == null ? null : industry.trim();
    }

    public Long getGatewayIncCount() {
        return gatewayIncCount;
    }

    public void setGatewayIncCount(Long gatewayIncCount) {
        this.gatewayIncCount = gatewayIncCount;
    }

    public Long getGdate() {
        return gdate;
    }

    public void setGdate(Long gdate) {
        this.gdate = gdate;
    }

    public Integer getConnectType() {
        return connectType;
    }

    public void setConnectType(Integer connectType) {
        this.connectType = connectType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GatewayIncrementCount that = (GatewayIncrementCount) o;
        return Objects.equals(factoryCode, that.factoryCode) &&
                Objects.equals(provinceCode, that.provinceCode) &&
                Objects.equals(industry, that.industry) &&
                Objects.equals(connectType, that.connectType) &&
                Objects.equals(gatewayIncCount, that.gatewayIncCount) &&
                Objects.equals(gdate, that.gdate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(factoryCode, provinceCode, industry, gatewayIncCount, gdate, connectType);
    }
}