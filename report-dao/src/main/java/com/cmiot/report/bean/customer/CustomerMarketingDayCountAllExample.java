package com.cmiot.report.bean.customer;

import java.util.ArrayList;
import java.util.List;

public class CustomerMarketingDayCountAllExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CustomerMarketingDayCountAllExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andTotalNumIsNull() {
            addCriterion("total_num is null");
            return (Criteria) this;
        }

        public Criteria andTotalNumIsNotNull() {
            addCriterion("total_num is not null");
            return (Criteria) this;
        }

        public Criteria andTotalNumEqualTo(Long value) {
            addCriterion("total_num =", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumNotEqualTo(Long value) {
            addCriterion("total_num <>", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumGreaterThan(Long value) {
            addCriterion("total_num >", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumGreaterThanOrEqualTo(Long value) {
            addCriterion("total_num >=", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumLessThan(Long value) {
            addCriterion("total_num <", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumLessThanOrEqualTo(Long value) {
            addCriterion("total_num <=", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumIn(List<Long> values) {
            addCriterion("total_num in", values, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumNotIn(List<Long> values) {
            addCriterion("total_num not in", values, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumBetween(Long value1, Long value2) {
            addCriterion("total_num between", value1, value2, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumNotBetween(Long value1, Long value2) {
            addCriterion("total_num not between", value1, value2, "totalNum");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrIsNull() {
            addCriterion("last_day_incr is null");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrIsNotNull() {
            addCriterion("last_day_incr is not null");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrEqualTo(Long value) {
            addCriterion("last_day_incr =", value, "lastDayIncr");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrNotEqualTo(Long value) {
            addCriterion("last_day_incr <>", value, "lastDayIncr");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrGreaterThan(Long value) {
            addCriterion("last_day_incr >", value, "lastDayIncr");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrGreaterThanOrEqualTo(Long value) {
            addCriterion("last_day_incr >=", value, "lastDayIncr");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrLessThan(Long value) {
            addCriterion("last_day_incr <", value, "lastDayIncr");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrLessThanOrEqualTo(Long value) {
            addCriterion("last_day_incr <=", value, "lastDayIncr");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrIn(List<Long> values) {
            addCriterion("last_day_incr in", values, "lastDayIncr");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrNotIn(List<Long> values) {
            addCriterion("last_day_incr not in", values, "lastDayIncr");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrBetween(Long value1, Long value2) {
            addCriterion("last_day_incr between", value1, value2, "lastDayIncr");
            return (Criteria) this;
        }

        public Criteria andLastDayIncrNotBetween(Long value1, Long value2) {
            addCriterion("last_day_incr not between", value1, value2, "lastDayIncr");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrIsNull() {
            addCriterion("last_day_decr is null");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrIsNotNull() {
            addCriterion("last_day_decr is not null");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrEqualTo(Long value) {
            addCriterion("last_day_decr =", value, "lastDayDecr");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrNotEqualTo(Long value) {
            addCriterion("last_day_decr <>", value, "lastDayDecr");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrGreaterThan(Long value) {
            addCriterion("last_day_decr >", value, "lastDayDecr");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrGreaterThanOrEqualTo(Long value) {
            addCriterion("last_day_decr >=", value, "lastDayDecr");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrLessThan(Long value) {
            addCriterion("last_day_decr <", value, "lastDayDecr");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrLessThanOrEqualTo(Long value) {
            addCriterion("last_day_decr <=", value, "lastDayDecr");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrIn(List<Long> values) {
            addCriterion("last_day_decr in", values, "lastDayDecr");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrNotIn(List<Long> values) {
            addCriterion("last_day_decr not in", values, "lastDayDecr");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrBetween(Long value1, Long value2) {
            addCriterion("last_day_decr between", value1, value2, "lastDayDecr");
            return (Criteria) this;
        }

        public Criteria andLastDayDecrNotBetween(Long value1, Long value2) {
            addCriterion("last_day_decr not between", value1, value2, "lastDayDecr");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrIsNull() {
            addCriterion("last_week_incr is null");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrIsNotNull() {
            addCriterion("last_week_incr is not null");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrEqualTo(Long value) {
            addCriterion("last_week_incr =", value, "lastWeekIncr");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrNotEqualTo(Long value) {
            addCriterion("last_week_incr <>", value, "lastWeekIncr");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrGreaterThan(Long value) {
            addCriterion("last_week_incr >", value, "lastWeekIncr");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrGreaterThanOrEqualTo(Long value) {
            addCriterion("last_week_incr >=", value, "lastWeekIncr");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrLessThan(Long value) {
            addCriterion("last_week_incr <", value, "lastWeekIncr");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrLessThanOrEqualTo(Long value) {
            addCriterion("last_week_incr <=", value, "lastWeekIncr");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrIn(List<Long> values) {
            addCriterion("last_week_incr in", values, "lastWeekIncr");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrNotIn(List<Long> values) {
            addCriterion("last_week_incr not in", values, "lastWeekIncr");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrBetween(Long value1, Long value2) {
            addCriterion("last_week_incr between", value1, value2, "lastWeekIncr");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncrNotBetween(Long value1, Long value2) {
            addCriterion("last_week_incr not between", value1, value2, "lastWeekIncr");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrIsNull() {
            addCriterion("last_week_decr is null");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrIsNotNull() {
            addCriterion("last_week_decr is not null");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrEqualTo(Long value) {
            addCriterion("last_week_decr =", value, "lastWeekDecr");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrNotEqualTo(Long value) {
            addCriterion("last_week_decr <>", value, "lastWeekDecr");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrGreaterThan(Long value) {
            addCriterion("last_week_decr >", value, "lastWeekDecr");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrGreaterThanOrEqualTo(Long value) {
            addCriterion("last_week_decr >=", value, "lastWeekDecr");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrLessThan(Long value) {
            addCriterion("last_week_decr <", value, "lastWeekDecr");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrLessThanOrEqualTo(Long value) {
            addCriterion("last_week_decr <=", value, "lastWeekDecr");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrIn(List<Long> values) {
            addCriterion("last_week_decr in", values, "lastWeekDecr");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrNotIn(List<Long> values) {
            addCriterion("last_week_decr not in", values, "lastWeekDecr");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrBetween(Long value1, Long value2) {
            addCriterion("last_week_decr between", value1, value2, "lastWeekDecr");
            return (Criteria) this;
        }

        public Criteria andLastWeekDecrNotBetween(Long value1, Long value2) {
            addCriterion("last_week_decr not between", value1, value2, "lastWeekDecr");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrIsNull() {
            addCriterion("last_month_incr is null");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrIsNotNull() {
            addCriterion("last_month_incr is not null");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrEqualTo(Long value) {
            addCriterion("last_month_incr =", value, "lastMonthIncr");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrNotEqualTo(Long value) {
            addCriterion("last_month_incr <>", value, "lastMonthIncr");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrGreaterThan(Long value) {
            addCriterion("last_month_incr >", value, "lastMonthIncr");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrGreaterThanOrEqualTo(Long value) {
            addCriterion("last_month_incr >=", value, "lastMonthIncr");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrLessThan(Long value) {
            addCriterion("last_month_incr <", value, "lastMonthIncr");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrLessThanOrEqualTo(Long value) {
            addCriterion("last_month_incr <=", value, "lastMonthIncr");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrIn(List<Long> values) {
            addCriterion("last_month_incr in", values, "lastMonthIncr");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrNotIn(List<Long> values) {
            addCriterion("last_month_incr not in", values, "lastMonthIncr");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrBetween(Long value1, Long value2) {
            addCriterion("last_month_incr between", value1, value2, "lastMonthIncr");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncrNotBetween(Long value1, Long value2) {
            addCriterion("last_month_incr not between", value1, value2, "lastMonthIncr");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrIsNull() {
            addCriterion("last_month_decr is null");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrIsNotNull() {
            addCriterion("last_month_decr is not null");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrEqualTo(Long value) {
            addCriterion("last_month_decr =", value, "lastMonthDecr");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrNotEqualTo(Long value) {
            addCriterion("last_month_decr <>", value, "lastMonthDecr");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrGreaterThan(Long value) {
            addCriterion("last_month_decr >", value, "lastMonthDecr");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrGreaterThanOrEqualTo(Long value) {
            addCriterion("last_month_decr >=", value, "lastMonthDecr");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrLessThan(Long value) {
            addCriterion("last_month_decr <", value, "lastMonthDecr");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrLessThanOrEqualTo(Long value) {
            addCriterion("last_month_decr <=", value, "lastMonthDecr");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrIn(List<Long> values) {
            addCriterion("last_month_decr in", values, "lastMonthDecr");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrNotIn(List<Long> values) {
            addCriterion("last_month_decr not in", values, "lastMonthDecr");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrBetween(Long value1, Long value2) {
            addCriterion("last_month_decr between", value1, value2, "lastMonthDecr");
            return (Criteria) this;
        }

        public Criteria andLastMonthDecrNotBetween(Long value1, Long value2) {
            addCriterion("last_month_decr not between", value1, value2, "lastMonthDecr");
            return (Criteria) this;
        }

        public Criteria andGdateIsNull() {
            addCriterion("gdate is null");
            return (Criteria) this;
        }

        public Criteria andGdateIsNotNull() {
            addCriterion("gdate is not null");
            return (Criteria) this;
        }

        public Criteria andGdateEqualTo(Integer value) {
            addCriterion("gdate =", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotEqualTo(Integer value) {
            addCriterion("gdate <>", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThan(Integer value) {
            addCriterion("gdate >", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThanOrEqualTo(Integer value) {
            addCriterion("gdate >=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThan(Integer value) {
            addCriterion("gdate <", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThanOrEqualTo(Integer value) {
            addCriterion("gdate <=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateIn(List<Integer> values) {
            addCriterion("gdate in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotIn(List<Integer> values) {
            addCriterion("gdate not in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateBetween(Integer value1, Integer value2) {
            addCriterion("gdate between", value1, value2, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotBetween(Integer value1, Integer value2) {
            addCriterion("gdate not between", value1, value2, "gdate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}