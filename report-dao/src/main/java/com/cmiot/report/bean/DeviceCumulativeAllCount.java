package com.cmiot.report.bean;

public class DeviceCumulativeAllCount {
	/**
	 * 数据日期。根据采样时间而来。
	 */
	private Long pdate;
	
	/**
	 * 数据省份编码
	 */
    private String provinceCode;
    
    /**
     * 数据地市编码
     */
    private String cityCode;
    
    /**
     * 网关厂商。
     */
    private String gatewayVendor;
    
    /**
     * 新增设备数量
     */
    private Long newMacCount;

	@Override
	public String toString() {
		return "DeviceCumulativeAllCount [pdate=" + pdate + ", provinceCode=" + provinceCode + ", cityCode=" + cityCode
				+ ", gatewayVendor=" + gatewayVendor + ", newMacCount=" + newMacCount + "]";
	}

	public Long getPdate() {
		return pdate;
	}

	public void setPdate(Long pdate) {
		this.pdate = pdate;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getGatewayVendor() {
		return gatewayVendor;
	}

	public void setGatewayVendor(String gatewayVendor) {
		this.gatewayVendor = gatewayVendor;
	}

	public Long getNewMacCount() {
		return newMacCount;
	}

	public void setNewMacCount(Long newMacCount) {
		this.newMacCount = newMacCount;
	}
    
    
}
