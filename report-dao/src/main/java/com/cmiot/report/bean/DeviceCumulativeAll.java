package com.cmiot.report.bean;

import java.util.Date;

public class DeviceCumulativeAll {
    private String gatewaySn;

    private String gatewayMac;

    private Long factoryId;

    private String gatewayVendor;

    private Long deviceModelId;

    private String gatewayProductclass;

    private String provinceCode;

    private String cityCode;

    private Long enterpriseId;

    private String customerId;

    private String account;

    private Date sampleTime;

    private String deviceName;

    private String dhcpName;

    private String mac;

    private String wifiName;

    private String ip;

    private String ipv6;

    private String wlanRadioType;

    private Integer wlanRadioPower;

    private String lanPort;

    private Long lanBitRate;

    private Long freq;

    private Date startTime;

    private Date lastTime;

    private Date pdate;

    public String getGatewaySn() {
        return gatewaySn;
    }

    public void setGatewaySn(String gatewaySn) {
        this.gatewaySn = gatewaySn == null ? null : gatewaySn.trim();
    }

    public String getGatewayMac() {
        return gatewayMac;
    }

    public void setGatewayMac(String gatewayMac) {
        this.gatewayMac = gatewayMac == null ? null : gatewayMac.trim();
    }

    public Long getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Long factoryId) {
        this.factoryId = factoryId;
    }

    public String getGatewayVendor() {
        return gatewayVendor;
    }

    public void setGatewayVendor(String gatewayVendor) {
        this.gatewayVendor = gatewayVendor == null ? null : gatewayVendor.trim();
    }

    public Long getDeviceModelId() {
        return deviceModelId;
    }

    public void setDeviceModelId(Long deviceModelId) {
        this.deviceModelId = deviceModelId;
    }

    public String getGatewayProductclass() {
        return gatewayProductclass;
    }

    public void setGatewayProductclass(String gatewayProductclass) {
        this.gatewayProductclass = gatewayProductclass == null ? null : gatewayProductclass.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account == null ? null : account.trim();
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName == null ? null : deviceName.trim();
    }

    public String getDhcpName() {
        return dhcpName;
    }

    public void setDhcpName(String dhcpName) {
        this.dhcpName = dhcpName == null ? null : dhcpName.trim();
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac == null ? null : mac.trim();
    }

    public String getWifiName() {
        return wifiName;
    }

    public void setWifiName(String wifiName) {
        this.wifiName = wifiName == null ? null : wifiName.trim();
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip == null ? null : ip.trim();
    }

    public String getIpv6() {
        return ipv6;
    }

    public void setIpv6(String ipv6) {
        this.ipv6 = ipv6 == null ? null : ipv6.trim();
    }

    public String getWlanRadioType() {
        return wlanRadioType;
    }

    public void setWlanRadioType(String wlanRadioType) {
        this.wlanRadioType = wlanRadioType == null ? null : wlanRadioType.trim();
    }

    public Integer getWlanRadioPower() {
        return wlanRadioPower;
    }

    public void setWlanRadioPower(Integer wlanRadioPower) {
        this.wlanRadioPower = wlanRadioPower;
    }

    public String getLanPort() {
        return lanPort;
    }

    public void setLanPort(String lanPort) {
        this.lanPort = lanPort == null ? null : lanPort.trim();
    }

    public Long getLanBitRate() {
        return lanBitRate;
    }

    public void setLanBitRate(Long lanBitRate) {
        this.lanBitRate = lanBitRate;
    }

    public Long getFreq() {
        return freq;
    }

    public void setFreq(Long freq) {
        this.freq = freq;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getLastTime() {
        return lastTime;
    }

    public void setLastTime(Date lastTime) {
        this.lastTime = lastTime;
    }

    public Date getPdate() {
        return pdate;
    }

    public void setPdate(Date pdate) {
        this.pdate = pdate;
    }
}