package com.cmiot.report.bean;

import java.util.Date;

public class MediaNetworkVisitCount {
    private String provinceCode;

    private String cityCode;

    private String mediaType;

    private Short visitType;

    private Long visitDuration;

    private Date sampleTime;

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getMediaType() {
        return mediaType;
    }

    public void setMediaType(String mediaType) {
        this.mediaType = mediaType == null ? null : mediaType.trim();
    }

    public Short getVisitType() {
        return visitType;
    }

    public void setVisitType(Short visitType) {
        this.visitType = visitType;
    }

    public Long getVisitDuration() {
        return visitDuration;
    }

    public void setVisitDuration(Long visitDuration) {
        this.visitDuration = visitDuration;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }
}