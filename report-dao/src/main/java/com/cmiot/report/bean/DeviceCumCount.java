package com.cmiot.report.bean;

public class DeviceCumCount {
    private String pdate;

    private String provinceCode;

    private String cityCode;

    private String gatewayVendor;

    private Long deviceAllCount;

    private Long deviceDayAlive;

    private Long deviceWeekAlive;

    private Long deviceMonthAlive;

    private Long deviceAllWired;

    private Long deviceAllWireless;

    public String getPdate() {
        return pdate;
    }

    public void setPdate(String pdate) {
        this.pdate = pdate == null ? null : pdate.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getGatewayVendor() {
        return gatewayVendor;
    }

    public void setGatewayVendor(String gatewayVendor) {
        this.gatewayVendor = gatewayVendor == null ? null : gatewayVendor.trim();
    }

    public Long getDeviceAllCount() {
        return deviceAllCount;
    }

    public void setDeviceAllCount(Long deviceAllCount) {
        this.deviceAllCount = deviceAllCount;
    }

    public Long getDeviceDayAlive() {
        return deviceDayAlive;
    }

    public void setDeviceDayAlive(Long deviceDayAlive) {
        this.deviceDayAlive = deviceDayAlive;
    }

    public Long getDeviceWeekAlive() {
        return deviceWeekAlive;
    }

    public void setDeviceWeekAlive(Long deviceWeekAlive) {
        this.deviceWeekAlive = deviceWeekAlive;
    }

    public Long getDeviceMonthAlive() {
        return deviceMonthAlive;
    }

    public void setDeviceMonthAlive(Long deviceMonthAlive) {
        this.deviceMonthAlive = deviceMonthAlive;
    }

    public Long getDeviceAllWired() {
        return deviceAllWired;
    }

    public void setDeviceAllWired(Long deviceAllWired) {
        this.deviceAllWired = deviceAllWired;
    }

    public Long getDeviceAllWireless() {
        return deviceAllWireless;
    }

    public void setDeviceAllWireless(Long deviceAllWireless) {
        this.deviceAllWireless = deviceAllWireless;
    }
}