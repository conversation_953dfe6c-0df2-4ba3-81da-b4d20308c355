package com.cmiot.report.bean;

import java.util.Date;

public class PonPowerListDetail {
    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String customerName;

    private String adslAccount;

    private String gatewaySn;

    private String gatewayMac;

    private Long factoryId;

    private String factoryName;

    private Long deviceModelId;

    private String deviceModel;

    private Long runingTimeTotal;

    private Integer ponRxPowerAvg;

    private Integer ponRxPowerMinValue;

    private Date ponRxPowerMinValueTime;

    private Date sampleTime;

    private Long gdate;

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getAdslAccount() {
        return adslAccount;
    }

    public void setAdslAccount(String adslAccount) {
        this.adslAccount = adslAccount == null ? null : adslAccount.trim();
    }

    public String getGatewaySn() {
        return gatewaySn;
    }

    public void setGatewaySn(String gatewaySn) {
        this.gatewaySn = gatewaySn == null ? null : gatewaySn.trim();
    }

    public String getGatewayMac() {
        return gatewayMac;
    }

    public void setGatewayMac(String gatewayMac) {
        this.gatewayMac = gatewayMac == null ? null : gatewayMac.trim();
    }

    public Long getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Long factoryId) {
        this.factoryId = factoryId;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName == null ? null : factoryName.trim();
    }

    public Long getDeviceModelId() {
        return deviceModelId;
    }

    public void setDeviceModelId(Long deviceModelId) {
        this.deviceModelId = deviceModelId;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel == null ? null : deviceModel.trim();
    }

    public Long getRuningTimeTotal() {
        return runingTimeTotal;
    }

    public void setRuningTimeTotal(Long runingTimeTotal) {
        this.runingTimeTotal = runingTimeTotal;
    }

    public Integer getPonRxPowerAvg() {
        return ponRxPowerAvg;
    }

    public void setPonRxPowerAvg(Integer ponRxPowerAvg) {
        this.ponRxPowerAvg = ponRxPowerAvg;
    }

    public Integer getPonRxPowerMinValue() {
        return ponRxPowerMinValue;
    }

    public void setPonRxPowerMinValue(Integer ponRxPowerMinValue) {
        this.ponRxPowerMinValue = ponRxPowerMinValue;
    }

    public Date getPonRxPowerMinValueTime() {
        return ponRxPowerMinValueTime;
    }

    public void setPonRxPowerMinValueTime(Date ponRxPowerMinValueTime) {
        this.ponRxPowerMinValueTime = ponRxPowerMinValueTime;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }

    public Long getGdate() {
        return gdate;
    }

    public void setGdate(Long gdate) {
        this.gdate = gdate;
    }
}