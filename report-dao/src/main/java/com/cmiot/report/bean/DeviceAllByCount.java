package com.cmiot.report.bean;

import java.io.Serializable;

/**
 * 设备统计对象。
 * <AUTHOR>
 */
public class DeviceAllByCount implements Serializable {
	private static final long serialVersionUID = -3193615872685201781L;

	/**
	 * 数据日期。根据采样时间而来。
	 */
	private Long pdate;
	
	/**
	 * 数据省份编码
	 */
    private String provinceCode;
    
    /**
     * 数据地市编码
     */
    private String cityCode;
    
    /**
     * 总设备量
     */
    private Long deviceAllCount;
    
    /**
     * 无线设备量
     */
    private Long deviceAllWireless;
    
    /**
     * 有线设备量
     */
    private Long deviceAllWired;

	@Override
	public String toString() {
		return "DeviceAllByCount [pdate=" + pdate + ", provinceCode=" + provinceCode + ", cityCode=" + cityCode
				+ ", deviceAllCount=" + deviceAllCount + ", deviceAllWireless=" + deviceAllWireless
				+ ", deviceAllWired=" + deviceAllWired + "]";
	}

	public Long getPdate() {
		return pdate;
	}

	public void setPdate(Long pdate) {
		this.pdate = pdate;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public Long getDeviceAllCount() {
		return deviceAllCount;
	}

	public void setDeviceAllCount(Long deviceAllCount) {
		this.deviceAllCount = deviceAllCount;
	}

	public Long getDeviceAllWireless() {
		return deviceAllWireless;
	}

	public void setDeviceAllWireless(Long deviceAllWireless) {
		this.deviceAllWireless = deviceAllWireless;
	}

	public Long getDeviceAllWired() {
		return deviceAllWired;
	}

	public void setDeviceAllWired(Long deviceAllWired) {
		this.deviceAllWired = deviceAllWired;
	}
    
    
}
