package com.cmiot.report.bean;

import java.math.BigDecimal;
import java.util.Date;

public class CustomerOrderAll {
    private Long id;

    private Long enterpriseId;

    private String customerId;

    private String businessType;

    private String businessProductId;

    private BigDecimal discountRate;

    private Integer portNum;

    private Integer bandwidth;

    private Integer lineTotal;

    private Date businessSubscribeTime;

    private Date businessUnsubscribeTime;

    private Integer businessStatus;

    private Date packageEffectTime;

    private Date packageUneffectTime;

    private Integer packageStatus;

    private String packageCode;

    private String provincePackageCode;

    private String provinceCode;

    private String userIdentify;

    private Integer userReal;

    private Date createTime;

    private Date updateTime;

    private Integer isDelete;

    private Date sampleTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType == null ? null : businessType.trim();
    }

    public String getBusinessProductId() {
        return businessProductId;
    }

    public void setBusinessProductId(String businessProductId) {
        this.businessProductId = businessProductId == null ? null : businessProductId.trim();
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public Integer getPortNum() {
        return portNum;
    }

    public void setPortNum(Integer portNum) {
        this.portNum = portNum;
    }

    public Integer getBandwidth() {
        return bandwidth;
    }

    public void setBandwidth(Integer bandwidth) {
        this.bandwidth = bandwidth;
    }

    public Integer getLineTotal() {
        return lineTotal;
    }

    public void setLineTotal(Integer lineTotal) {
        this.lineTotal = lineTotal;
    }

    public Date getBusinessSubscribeTime() {
        return businessSubscribeTime;
    }

    public void setBusinessSubscribeTime(Date businessSubscribeTime) {
        this.businessSubscribeTime = businessSubscribeTime;
    }

    public Date getBusinessUnsubscribeTime() {
        return businessUnsubscribeTime;
    }

    public void setBusinessUnsubscribeTime(Date businessUnsubscribeTime) {
        this.businessUnsubscribeTime = businessUnsubscribeTime;
    }

    public Integer getBusinessStatus() {
        return businessStatus;
    }

    public void setBusinessStatus(Integer businessStatus) {
        this.businessStatus = businessStatus;
    }

    public Date getPackageEffectTime() {
        return packageEffectTime;
    }

    public void setPackageEffectTime(Date packageEffectTime) {
        this.packageEffectTime = packageEffectTime;
    }

    public Date getPackageUneffectTime() {
        return packageUneffectTime;
    }

    public void setPackageUneffectTime(Date packageUneffectTime) {
        this.packageUneffectTime = packageUneffectTime;
    }

    public Integer getPackageStatus() {
        return packageStatus;
    }

    public void setPackageStatus(Integer packageStatus) {
        this.packageStatus = packageStatus;
    }

    public String getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(String packageCode) {
        this.packageCode = packageCode == null ? null : packageCode.trim();
    }

    public String getProvincePackageCode() {
        return provincePackageCode;
    }

    public void setProvincePackageCode(String provincePackageCode) {
        this.provincePackageCode = provincePackageCode == null ? null : provincePackageCode.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getUserIdentify() {
        return userIdentify;
    }

    public void setUserIdentify(String userIdentify) {
        this.userIdentify = userIdentify == null ? null : userIdentify.trim();
    }

    public Integer getUserReal() {
        return userReal;
    }

    public void setUserReal(Integer userReal) {
        this.userReal = userReal;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }
}