package com.cmiot.report.bean;


public class PonPowerStatisticsQ {

    // 省份&地市,厂商,型号
    private String name;

    // 当前选择条件网关数量
    private Integer gtwCount;

    // 当前选择条件功率平均值
    private Integer ponRxPowerCurrAvg;

    // 类型: 1:弱光,2:强光
    private Byte ponRxPowerCurrType;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getGtwCount() {
        return gtwCount;
    }

    public void setGtwCount(Integer gtwCount) {
        this.gtwCount = gtwCount;
    }

    public Byte getPonRxPowerCurrType() {
        return ponRxPowerCurrType;
    }

    public void setPonRxPowerCurrType(Byte ponRxPowerCurrType) {
        this.ponRxPowerCurrType = ponRxPowerCurrType;
    }

    public Integer getPonRxPowerCurrAvg() {
        return ponRxPowerCurrAvg;
    }

    public void setPonRxPowerCurrAvg(Integer ponRxPowerCurrAvg) {
        this.ponRxPowerCurrAvg = ponRxPowerCurrAvg;
    }
}
