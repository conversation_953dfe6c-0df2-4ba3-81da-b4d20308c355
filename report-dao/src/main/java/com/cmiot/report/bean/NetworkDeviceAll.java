package com.cmiot.report.bean;

import java.util.Date;

public class NetworkDeviceAll {
    private Long enterpriseId;

    private String deviceMac;

    private Long runningTime;

    private Integer upFreq;

    private Date sampleTime;

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getDeviceMac() {
        return deviceMac;
    }

    public void setDeviceMac(String deviceMac) {
        this.deviceMac = deviceMac == null ? null : deviceMac.trim();
    }

    public Long getRunningTime() {
        return runningTime;
    }

    public void setRunningTime(Long runningTime) {
        this.runningTime = runningTime;
    }

    public Integer getUpFreq() {
        return upFreq;
    }

    public void setUpFreq(Integer upFreq) {
        this.upFreq = upFreq;
    }

    public Date getSampleTime() {
        return sampleTime;
    }

    public void setSampleTime(Date sampleTime) {
        this.sampleTime = sampleTime;
    }
}