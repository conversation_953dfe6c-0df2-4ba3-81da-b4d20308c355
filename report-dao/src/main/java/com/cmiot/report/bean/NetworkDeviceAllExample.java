package com.cmiot.report.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NetworkDeviceAllExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public NetworkDeviceAllExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andEnterpriseIdIsNull() {
            addCriterion("enterprise_id is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNotNull() {
            addCriterion("enterprise_id is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdEqualTo(Long value) {
            addCriterion("enterprise_id =", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotEqualTo(Long value) {
            addCriterion("enterprise_id <>", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThan(Long value) {
            addCriterion("enterprise_id >", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("enterprise_id >=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThan(Long value) {
            addCriterion("enterprise_id <", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThanOrEqualTo(Long value) {
            addCriterion("enterprise_id <=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIn(List<Long> values) {
            addCriterion("enterprise_id in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotIn(List<Long> values) {
            addCriterion("enterprise_id not in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdBetween(Long value1, Long value2) {
            addCriterion("enterprise_id between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotBetween(Long value1, Long value2) {
            addCriterion("enterprise_id not between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andDeviceMacIsNull() {
            addCriterion("device_mac is null");
            return (Criteria) this;
        }

        public Criteria andDeviceMacIsNotNull() {
            addCriterion("device_mac is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceMacEqualTo(String value) {
            addCriterion("device_mac =", value, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacNotEqualTo(String value) {
            addCriterion("device_mac <>", value, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacGreaterThan(String value) {
            addCriterion("device_mac >", value, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacGreaterThanOrEqualTo(String value) {
            addCriterion("device_mac >=", value, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacLessThan(String value) {
            addCriterion("device_mac <", value, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacLessThanOrEqualTo(String value) {
            addCriterion("device_mac <=", value, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacLike(String value) {
            addCriterion("device_mac like", value, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacNotLike(String value) {
            addCriterion("device_mac not like", value, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacIn(List<String> values) {
            addCriterion("device_mac in", values, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacNotIn(List<String> values) {
            addCriterion("device_mac not in", values, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacBetween(String value1, String value2) {
            addCriterion("device_mac between", value1, value2, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andDeviceMacNotBetween(String value1, String value2) {
            addCriterion("device_mac not between", value1, value2, "deviceMac");
            return (Criteria) this;
        }

        public Criteria andRunningTimeIsNull() {
            addCriterion("running_time is null");
            return (Criteria) this;
        }

        public Criteria andRunningTimeIsNotNull() {
            addCriterion("running_time is not null");
            return (Criteria) this;
        }

        public Criteria andRunningTimeEqualTo(Long value) {
            addCriterion("running_time =", value, "runningTime");
            return (Criteria) this;
        }

        public Criteria andRunningTimeNotEqualTo(Long value) {
            addCriterion("running_time <>", value, "runningTime");
            return (Criteria) this;
        }

        public Criteria andRunningTimeGreaterThan(Long value) {
            addCriterion("running_time >", value, "runningTime");
            return (Criteria) this;
        }

        public Criteria andRunningTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("running_time >=", value, "runningTime");
            return (Criteria) this;
        }

        public Criteria andRunningTimeLessThan(Long value) {
            addCriterion("running_time <", value, "runningTime");
            return (Criteria) this;
        }

        public Criteria andRunningTimeLessThanOrEqualTo(Long value) {
            addCriterion("running_time <=", value, "runningTime");
            return (Criteria) this;
        }

        public Criteria andRunningTimeIn(List<Long> values) {
            addCriterion("running_time in", values, "runningTime");
            return (Criteria) this;
        }

        public Criteria andRunningTimeNotIn(List<Long> values) {
            addCriterion("running_time not in", values, "runningTime");
            return (Criteria) this;
        }

        public Criteria andRunningTimeBetween(Long value1, Long value2) {
            addCriterion("running_time between", value1, value2, "runningTime");
            return (Criteria) this;
        }

        public Criteria andRunningTimeNotBetween(Long value1, Long value2) {
            addCriterion("running_time not between", value1, value2, "runningTime");
            return (Criteria) this;
        }

        public Criteria andUpFreqIsNull() {
            addCriterion("up_freq is null");
            return (Criteria) this;
        }

        public Criteria andUpFreqIsNotNull() {
            addCriterion("up_freq is not null");
            return (Criteria) this;
        }

        public Criteria andUpFreqEqualTo(Integer value) {
            addCriterion("up_freq =", value, "upFreq");
            return (Criteria) this;
        }

        public Criteria andUpFreqNotEqualTo(Integer value) {
            addCriterion("up_freq <>", value, "upFreq");
            return (Criteria) this;
        }

        public Criteria andUpFreqGreaterThan(Integer value) {
            addCriterion("up_freq >", value, "upFreq");
            return (Criteria) this;
        }

        public Criteria andUpFreqGreaterThanOrEqualTo(Integer value) {
            addCriterion("up_freq >=", value, "upFreq");
            return (Criteria) this;
        }

        public Criteria andUpFreqLessThan(Integer value) {
            addCriterion("up_freq <", value, "upFreq");
            return (Criteria) this;
        }

        public Criteria andUpFreqLessThanOrEqualTo(Integer value) {
            addCriterion("up_freq <=", value, "upFreq");
            return (Criteria) this;
        }

        public Criteria andUpFreqIn(List<Integer> values) {
            addCriterion("up_freq in", values, "upFreq");
            return (Criteria) this;
        }

        public Criteria andUpFreqNotIn(List<Integer> values) {
            addCriterion("up_freq not in", values, "upFreq");
            return (Criteria) this;
        }

        public Criteria andUpFreqBetween(Integer value1, Integer value2) {
            addCriterion("up_freq between", value1, value2, "upFreq");
            return (Criteria) this;
        }

        public Criteria andUpFreqNotBetween(Integer value1, Integer value2) {
            addCriterion("up_freq not between", value1, value2, "upFreq");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNull() {
            addCriterion("sample_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIsNotNull() {
            addCriterion("sample_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTimeEqualTo(Date value) {
            addCriterion("sample_time =", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotEqualTo(Date value) {
            addCriterion("sample_time <>", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThan(Date value) {
            addCriterion("sample_time >", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_time >=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThan(Date value) {
            addCriterion("sample_time <", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_time <=", value, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeIn(List<Date> values) {
            addCriterion("sample_time in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotIn(List<Date> values) {
            addCriterion("sample_time not in", values, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeBetween(Date value1, Date value2) {
            addCriterion("sample_time between", value1, value2, "sampleTime");
            return (Criteria) this;
        }

        public Criteria andSampleTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_time not between", value1, value2, "sampleTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}