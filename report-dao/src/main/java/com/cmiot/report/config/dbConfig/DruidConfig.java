package com.cmiot.report.config.dbConfig;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;

@Configuration
public class DruidConfig {
    @Resource
    private JdbcConfig jdbcConfig;

    @Bean
    public DataSource dataSource() {
        DruidDataSource datasource = new DruidDataSource();
        datasource.setUrl(jdbcConfig.getUrl());
        datasource.setUsername(jdbcConfig.getUserName());
        datasource.setPassword(jdbcConfig.getPassword());
        datasource.setDriverClassName(jdbcConfig.getDriverClassName());
        datasource.setInitialSize(jdbcConfig.getInitialSize());
        datasource.setMinIdle(jdbcConfig.getMinIdle());
        datasource.setMaxActive(jdbcConfig.getMaxActive());
        datasource.setMaxWait(jdbcConfig.getMaxWait());
        datasource.setValidationQuery(jdbcConfig.getValidationQuery());
        return datasource;
    }
}