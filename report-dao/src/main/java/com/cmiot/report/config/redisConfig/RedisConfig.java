package com.cmiot.report.config.redisConfig;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * @Classname RedisConfig
 * @Date 2020/10/13 16:40
 * @Created by zhangxin1
 * @Description :
 **/
@Configuration
@AutoConfigureAfter(RedisAutoConfiguration.class)
public class RedisConfig {
	@Bean
	RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
		// 配置 json 序列化器 - Jackson2JsonRedisSerializer
		Jackson2JsonRedisSerializer<Object> jacksonSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
		// objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
		jacksonSerializer.setObjectMapper(objectMapper);
		// 创建并配置自定义 RedisTemplateRedisOperator
		RedisTemplate<String, Object> template = new RedisTemplate<>();
		template.setConnectionFactory(redisConnectionFactory);
		// 将 key 序列化成字符串
		template.setKeySerializer(new StringRedisSerializer());
		// 将 hash 的 key 序列化成字符串
		template.setHashKeySerializer(new StringRedisSerializer());
		// 将 value 序列化成 json
		template.setValueSerializer(jacksonSerializer);
		// 将 hash 的 value 序列化成 json
		template.setHashValueSerializer(jacksonSerializer);
		template.afterPropertiesSet();
		return template;
	}

	// @Bean
	// public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
	// RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
	// redisTemplate.setConnectionFactory(redisConnectionFactory);
	// // 使用 GenericFastJsonRedisSerializer 替换默认序列化
	// GenericFastJsonRedisSerializer genericFastJsonRedisSerializer = new GenericFastJsonRedisSerializer();
	// // 设置key和value的序列化规则
	// redisTemplate.setKeySerializer(new GenericToStringSerializer<>(String.class));
	// redisTemplate.setValueSerializer(genericFastJsonRedisSerializer);
	// // 设置hashKey和hashValue的序列化规则
	// redisTemplate.setHashKeySerializer(new GenericToStringSerializer<>(String.class));
	// redisTemplate.setHashValueSerializer(genericFastJsonRedisSerializer);
	// // 设置支持事物
	// redisTemplate.setEnableTransactionSupport(true);
	// redisTemplate.afterPropertiesSet();
	// return redisTemplate;
	// }
}
