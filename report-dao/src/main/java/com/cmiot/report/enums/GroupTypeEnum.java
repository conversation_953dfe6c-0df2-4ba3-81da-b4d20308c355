package com.cmiot.report.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GroupTypeEnum {
    unknow(-1, "", "其他"),
    province(1, "province", "省份"),
    vendor(2, "vendor", "厂商"),
    model(3, "model", "型号"),
    day(4, "day", "日期"),
    city(5, "city", "地市");


    private Integer typeValue;
    private String typeDesc;
    private String commont;

    public static GroupTypeEnum getByType(int typeValue) {
        for (GroupTypeEnum item : GroupTypeEnum.values()) {
            if (typeValue == item.typeValue) {
                return item;
            }
        }
        return unknow;
    }

    public static GroupTypeEnum getByDesc(String typeDesc) {
        for (GroupTypeEnum item : GroupTypeEnum.values()) {
            if (typeDesc.equals(item.getTypeDesc())) {
                return item;
            }
        }
        return unknow;
    }
}
