<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayAlarmDetailMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.GatewayAlarmDetail">
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="adsl_account" jdbcType="VARCHAR" property="adslAccount"/>
        <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn"/>
        <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac"/>
        <result column="factory_id" jdbcType="BIGINT" property="factoryId"/>
        <result column="factory_name" jdbcType="VARCHAR" property="factoryName"/>
        <result column="device_model_id" jdbcType="BIGINT" property="deviceModelId"/>
        <result column="device_model" jdbcType="VARCHAR" property="deviceModel"/>
        <result column="alarm_type" jdbcType="TINYINT" property="alarmType"/>
        <result column="alarm_name" jdbcType="VARCHAR" property="alarmName"/>
        <result column="main_chip_temperature" jdbcType="VARCHAR" property="mainChipTemperature"/>
        <result column="alarm_time" jdbcType="TIMESTAMP" property="alarmTime"/>
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime"/>
        <result column="gdate" jdbcType="BIGINT" property="gdate"/>
    </resultMap>

    <resultMap id="QueryResultMap" type="com.cmiot.report.dto.GatewayAlarmQueryCount">
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="count" jdbcType="BIGINT" property="count"/>
    </resultMap>

    <resultMap id="TypeQueryResultMap" type="com.cmiot.report.dto.GatewayAlarmTypeQueryCount">
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="alarm_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="count" jdbcType="BIGINT" property="count"/>
    </resultMap>


    <resultMap id="CountBaseResultMap" type="com.cmiot.report.dto.GatewayAlarmDetailCount">
        <result column="factory_code" jdbcType="VARCHAR" property="factoryCode"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="count" jdbcType="BIGINT" property="count"/>
    </resultMap>

    <resultMap id="ListResultMap" type="com.cmiot.report.dto.GatewayAlarmDetailVo">
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="adsl_account" jdbcType="VARCHAR" property="adslAccount"/>
        <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn"/>
        <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac"/>
        <result column="factory_name" jdbcType="VARCHAR" property="factoryName"/>
        <result column="device_model" jdbcType="VARCHAR" property="deviceModel"/>
        <result column="alarm_name" jdbcType="VARCHAR" property="alarmName"/>
        <result column="main_chip_temperature" jdbcType="VARCHAR" property="mainChipTemperature"/>
        <result column="alarm_times" jdbcType="VARCHAR" property="alarmTimes"/>
    </resultMap>


    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        province_code, province_name, city_code, city_name, customer_name, adsl_account,
        gateway_sn, gateway_mac, factory_id, factory_name, device_model_id, device_model,
        alarm_type, alarm_name, main_chip_temperature, alarm_time, sample_time, gdate
    </sql>

    <sql id="Base_Column_Detail_List">
        province_name,city_name, customer_name, adsl_account, gateway_sn, gateway_mac,factory_name,
        device_model,alarm_name, main_chip_temperature, alarm_time
    </sql>

    <select id="selectByExample" parameterType="com.cmiot.report.bean.GatewayAlarmDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>


    <!--省份维度-->
    <select id="selectCountByProvQuery" resultMap="QueryResultMap">
        select province_code as name,uniqExact(gateway_sn) as count from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        and concat(province_code,cast(alarm_type as String)) global in (select
        concat(t3.province_code,cast(t3.alarm_type as String)) from (select
        t1.province_code,t1.alarm_type,if(equals(t2.allCount,0),0,round(divide(t1.count,t2.allCount)*100,2)) as percent
        from (select province_code,uniqExact(gateway_sn) as count,alarm_type from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by province_code,alarm_type) t1 left join (
        select `province_code`,uniqExact(`gateway_sn`) as allCount from
        t_gateway_on_offline_detail_all where gdate=#{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.factCodeList !=null">
            and factory_code in
            <foreach item="item" collection="query.factCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by `province_code`) t2 on t1.province_code=t2.province_code) t3 left join
        (select at2.type,at2.value from (select at.*,row_number() over (partition by at.type order by
        at.sample_time desc) rank from (select type,cast(value as Float32) as value,sample_time from
        t_gateway_alarm_threshold_all where gdate=toYYYYMMDD(addDays(now(), -1))) at) at2 where
        at2.rank=1) t4 on t3.alarm_type=t4.type where t3.percent>=t4.value group by t3.province_code,t3.alarm_type)
        group by
        province_code
    </select>

    <!--省份下告警类型维度-->
    <select id="selectCountByProvTypeQuery" resultMap="TypeQueryResultMap">
        select province_code as name,alarm_name,uniqExact(gateway_sn) as count from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        and concat(province_code,cast(alarm_type as String)) global in (select
        concat(t3.province_code,cast(t3.alarm_type as String)) from (select
        t1.province_code,t1.alarm_type,if(equals(t2.allCount,0),0,round(divide(t1.count,t2.allCount)*100,2)) as percent
        from (select province_code,uniqExact(gateway_sn) as count,alarm_type from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by province_code,alarm_type) t1 left join (
        select `province_code`,uniqExact(`gateway_sn`) as allCount from
        t_gateway_on_offline_detail_all where gdate=#{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.factCodeList !=null">
            and factory_code in
            <foreach item="item" collection="query.factCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by `province_code`) t2 on t1.province_code=t2.province_code) t3 left join
        (select at2.type,at2.value from (select at.*,row_number() over (partition by at.type order by
        at.sample_time desc) rank from (select type,cast(value as Float32) as value,sample_time from
        t_gateway_alarm_threshold_all where gdate=toYYYYMMDD(addDays(now(), -1))) at) at2 where
        at2.rank=1) t4 on t3.alarm_type=t4.type where t3.percent>=t4.value group by t3.province_code,t3.alarm_type)
        group by province_code,alarm_name
    </select>

    <!--地市维度-->
    <select id="selectCountByCityQuery" resultMap="QueryResultMap">
        select city_code as name,uniqExact(gateway_sn) as count from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        and concat(city_code,cast(alarm_type as String)) global in (select concat(t3.city_code,cast(t3.alarm_type as
        String)) from (select
        t1.city_code,t1.alarm_type,if(equals(t2.allCount,0),0,round(divide(t1.count,t2.allCount)*100,2)) as percent from
        (select city_code,uniqExact(gateway_sn) as count,alarm_type from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by city_code,alarm_type) t1 left join (select `city_code`,uniqExact(`gateway_sn`) as allCount from
        t_gateway_on_offline_detail_all where gdate=#{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.factCodeList !=null">
            and factory_code in
            <foreach item="item" collection="query.factCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by `city_code`) t2 on t1.city_code=t2.city_code) t3 left
        join (select at2.type,at2.value from (select at.*,row_number() over (partition by at.type order by
        at.sample_time desc) rank from (select type,cast(value as Float32) as value,sample_time from
        t_gateway_alarm_threshold_all where gdate=toYYYYMMDD(addDays(now(), -1))) at) at2 where at2.rank=1) t4 on
        t3.alarm_type=t4.type where t3.percent>=t4.value
        group by t3.city_code,t3.alarm_type) group by city_code
    </select>

    <!--地市下告警类型维度-->
    <select id="selectCountByCityTypeQuery" resultMap="TypeQueryResultMap">
        select city_code as name,alarm_name,uniqExact(gateway_sn) as count from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        and concat(city_code,cast(alarm_type as String)) global in (select concat(t3.city_code,cast(t3.alarm_type as
        String)) from (select
        t1.city_code,t1.alarm_type,if(equals(t2.allCount,0),0,round(divide(t1.count,t2.allCount)*100,2)) as percent from
        (select city_code,uniqExact(gateway_sn) as count,alarm_type from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by city_code,alarm_type) t1 left join (select `city_code`,uniqExact(`gateway_sn`) as allCount from
        t_gateway_on_offline_detail_all where gdate=#{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.factCodeList !=null">
            and factory_code in
            <foreach item="item" collection="query.factCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by `city_code`) t2 on t1.city_code=t2.city_code) t3 left
        join (select at2.type,at2.value from (select at.*,row_number() over (partition by at.type order by
        at.sample_time desc) rank from (select type,cast(value as Float32) as value,sample_time from
        t_gateway_alarm_threshold_all where gdate=toYYYYMMDD(addDays(now(), -1))) at) at2 where at2.rank=1) t4 on
        t3.alarm_type=t4.type where t3.percent>=t4.value
        group by t3.city_code,t3.alarm_type) group by city_code,alarm_name
    </select>

    <!--厂商维度-->
    <select id="selectCountByVendorQuery" resultMap="QueryResultMap">
        select dictGetOrDefault('ge_report.t_dic_factory', 'factory_code', factory_id,'其他') as
        name,uniqExact(gateway_sn) as count from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        and concat(name,cast(alarm_type as String)) global in (select concat(t3.factory_code,cast(t3.alarm_type
        as String)) from (select
        t1.factory_code,t1.alarm_type,if(equals(t2.allCount,0),0,round(divide(t1.count,t2.allCount)*100,2)) as percent
        from (select uniqExact(gateway_sn) as count,dictGetOrDefault('ge_report.t_dic_factory', 'factory_code',
        factory_id,'其他') as factory_code,alarm_type from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by factory_code,alarm_type) t1 left join (select `factory_code`,uniqExact(`gateway_sn`) as allCount from
        t_gateway_on_offline_detail_all where gdate=#{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.factCodeList !=null">
            and factory_code in
            <foreach item="item" collection="query.factCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by `factory_code`) t2 on t1.factory_code=t2.factory_code) t3 left
        join (select at2.type,at2.value from (select at.*,row_number() over (partition by at.type order by
        at.sample_time desc) rank from (select type,cast(value as Float32) as value,sample_time from
        t_gateway_alarm_threshold_all where gdate=toYYYYMMDD(addDays(now(), -1))) at) at2 where
        at2.rank=1) t4 on t3.alarm_type=t4.type where t3.percent>=t4.value group by t3.factory_code,t3.alarm_type) group by name
    </select>

    <!--厂商下告警类型维度-->
    <select id="selectCountByVendorTypeQuery" resultMap="TypeQueryResultMap">
        select dictGetOrDefault('ge_report.t_dic_factory', 'factory_code', factory_id,'其他') as
        name,alarm_name,uniqExact(gateway_sn) as count from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        and concat(name,cast(alarm_type as String)) global in (select concat(t3.factory_code,cast(t3.alarm_type
        as String)) from (select
        t1.factory_code,t1.alarm_type,if(equals(t2.allCount,0),0,round(divide(t1.count,t2.allCount)*100,2)) as
        percent from (select uniqExact(gateway_sn) as count,dictGetOrDefault('ge_report.t_dic_factory', 'factory_code',
        factory_id,'其他') as factory_code,alarm_type from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by factory_code,alarm_type) t1 left join (
        select `factory_code`,uniqExact(`gateway_sn`) as allCount from
        t_gateway_on_offline_detail_all where gdate=#{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.factCodeList !=null">
            and factory_code in
            <foreach item="item" collection="query.factCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by `factory_code`) t2 on t1.factory_code=t2.factory_code) t3 left
        join (select at2.type,at2.value from (select at.*,row_number() over (partition by at.type order by
        at.sample_time desc) rank from (select type,cast(value as Float32) as value,sample_time from
        t_gateway_alarm_threshold_all where gdate=toYYYYMMDD(addDays(now(), -1))) at) at2 where at2.rank=1) t4 on
        t3.alarm_type=t4.type where t3.percent>=t4.value group by t3.factory_code,t3.alarm_type) group by name,alarm_name
    </select>

    <!--以省份,地市,厂商为查询条件的明细-->
    <select id="selectAlarmDetailListByQuery" resultMap="ListResultMap">
        select province_name,city_name, customer_name, adsl_account, gateway_sn, gateway_mac,factory_name,
        device_model,alarm_name, main_chip_temperature, cast(alarm_time as String) as alarm_times
        from t_gateway_alarm_detail_all where gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by province_name,city_name, customer_name, adsl_account, gateway_sn, gateway_mac,factory_name,
        device_model,alarm_name, main_chip_temperature, alarm_time
    </select>


    <select id="selectAlarmDetailListByQuery2" resultMap="ListResultMap">
        select province_name,city_name, customer_name, adsl_account, gateway_sn, gateway_mac,factory_name,
        device_model,alarm_name, main_chip_temperature, cast(alarm_time as String) as alarm_times
        from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        and alarm_type global in (select t3.alarm_type from (select
        t1.alarm_type,if(equals(t2.allCount,0),0,round(divide(t1.count,t2.allCount)*100,2)) as percent from
        (select province_code,city_code,uniqExact(gateway_sn) as
        count,dictGetOrDefault('ge_report.t_dic_factory', 'factory_code',
        factory_id,'其他') as factory_code,alarm_type from t_gateway_alarm_detail_all where
        gdate >= #{query.startTime} and gdate <![CDATA[ <= ]]> #{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendorIdList !=null">
            and factory_id in
            <foreach item="item" collection="query.vendorIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by province_code,city_code,factory_code,alarm_type) t1 left join (
        select `factory_code`,`province_code`,`city_code`,uniqExact(`gateway_sn`) as allCount from
        t_gateway_on_offline_detail_all where gdate=#{query.endTime}
        <if test="query.provList !=null">
            and province_code in
            <foreach item="item" collection="query.provList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.cityList !=null">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.factCodeList !=null">
            and factory_code in
            <foreach item="item" collection="query.factCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by `factory_code`,`province_code`,`city_code`) t2 on
        t1.province_code=t2.province_code and t1.city_code=t2.city_code and t1.factory_code=t2.factory_code) t3 left
        join (select at2.type,at2.value from (select at.*,row_number() over (partition by at.type order by
        at.sample_time desc) rank from (select type,cast(value as Float32) as value,sample_time from
        t_gateway_alarm_threshold_all where gdate=toYYYYMMDD(addDays(now(), -1))) at) at2 where
        at2.rank=1) t4 on t3.alarm_type=t4.type where t3.percent>=t4.value group by t3.alarm_type) group by
        province_name,city_name, customer_name, adsl_account, gateway_sn, gateway_mac,factory_name,
        device_model,alarm_name, main_chip_temperature, alarm_time
    </select>


    <insert id="insertBatch" parameterType="java.util.List">
        insert into t_gateway_alarm_detail_all (province_code, province_name, city_code, city_name, customer_name,
        adsl_account, gateway_sn, gateway_mac, factory_id, factory_name, device_model_id, device_model, alarm_type,
        alarm_name, main_chip_temperature, alarm_time, sample_time, gdate)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.province_code,jdbcType=VARCHAR},#{item.province_name,jdbcType=VARCHAR},#{item.city_code,jdbcType=VARCHAR},#{item.city_name,jdbcType=VARCHAR},#{item.customer_name,jdbcType=VARCHAR},#{item.adsl_account,jdbcType=VARCHAR},#{item.gateway_sn,jdbcType=VARCHAR},#{item.gateway_mac,jdbcType=VARCHAR},#{item.factory_id,jdbcType=BIGINT},#{item.factory_name,jdbcType=VARCHAR},#{item.device_model_id,jdbcType=BIGINT},#{item.device_model,jdbcType=VARCHAR},#{item.alarm_type,jdbcType=TINYINT},#{item.alarm_name,jdbcType=VARCHAR},#{item.main_chip_temperature,jdbcType=VARCHAR},#{item.alarm_time,jdbcType=TIMESTAMP},#{item.sample_time,jdbcType=TIMESTAMP},#{item.gdate,jdbcType=BIGINT})
        </foreach>
    </insert>


    <select id="selectCountByProvExample" parameterType="com.cmiot.report.bean.GatewayAlarmDetailExample"
            resultType="com.cmiot.report.dto.GatewayAlarmCount">
        select province_code as name,alarm_name as typeName,uniqExact(gateway_sn) as count
        from t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and gateway_sn global in (select gateway_sn from (select gateway_sn,alarm_type,count(1) as count from
        t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by gateway_sn,alarm_type) t1 where t1.count>=(select t2.value from
        (select t1.*,row_number() over (partition by t1.type order by t1.timestamp desc) rank from (select
        timestamp,type,cast(value as Float32) as value from t_gateway_alarm_threshold_all where
        gdate=toYYYYMMDD(addDays(now(), -1)) and
        type=5) t1) t2 where t2.rank=1) group by gateway_sn)
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by province_code,alarm_name
    </select>


    <select id="selectCountByCityExample" parameterType="com.cmiot.report.bean.GatewayAlarmDetailExample"
            resultType="com.cmiot.report.dto.GatewayAlarmCount">
        select city_code as name,alarm_name as typeName,uniqExact(gateway_sn) as count
        from t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and gateway_sn global in (select gateway_sn from (select gateway_sn,alarm_type,count(1) as count from
        t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by gateway_sn,alarm_type) t1 where t1.count>=(select t2.value from
        (select t1.*,row_number() over (partition by t1.type order by t1.timestamp desc) rank from (select
        timestamp,type,cast(value as Float32) as value from t_gateway_alarm_threshold_all where
        gdate=toYYYYMMDD(addDays(now(), -1)) and
        type=5) t1) t2 where t2.rank=1) group by gateway_sn)
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by city_code,alarm_name
    </select>


    <select id="selectCountByVendorExample" parameterType="com.cmiot.report.bean.GatewayAlarmDetailExample"
            resultType="com.cmiot.report.dto.GatewayAlarmCount">
        select factory_name as name,alarm_name as typeName,uniqExact(gateway_sn) as count
        from t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and gateway_sn global in (select gateway_sn from (select gateway_sn,alarm_type,count(1) as count from
        t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by gateway_sn,alarm_type) t1 where t1.count>=(select t2.value from
        (select t1.*,row_number() over (partition by t1.type order by t1.timestamp desc) rank from (select
        timestamp,type,cast(value as Float32) as value from t_gateway_alarm_threshold_all where
        gdate=toYYYYMMDD(addDays(now(), -1)) and
        type=5) t1) t2 where t2.rank=1) group by gateway_sn)
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by factory_name,alarm_name
    </select>


    <select id="selectDetailCountByExample" parameterType="com.cmiot.report.bean.GatewayAlarmDetailExample"
            resultType="com.cmiot.report.dto.GatewayAlarmDetailCount">
        select province_code,city_code,dictGet('ge_report.t_dic_factory', 'factory_code', factory_id) as
        factory_code,alarm_type,uniqExact(gateway_sn) as count
        from t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and gateway_sn global in (select gateway_sn from (select gateway_sn,alarm_type,count(1) as count from
        t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by gateway_sn,alarm_type) t1 where t1.count>(select t2.value from
        (select t1.*,row_number() over (partition by t1.type order by t1.timestamp desc) rank from (select
        timestamp,type,cast(value as Float32) as value from t_gateway_alarm_threshold_all where
        gdate=toYYYYMMDD(addDays(now(), -1)) and
        type=5) t1) t2 where t2.rank=1) group by gateway_sn)
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by province_code,alarm_name
    </select>


    <select id="selectAlarmDetailListByExample" parameterType="com.cmiot.report.bean.GatewayAlarmDetailExample"
            resultMap="ListResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_Detail_List"/>
        from t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.GatewayAlarmDetailExample">
        delete from t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.GatewayAlarmDetail">
        insert into t_gateway_alarm_detail_all (province_code, province_name, city_code,
        city_name, customer_name, adsl_account,
        gateway_sn, gateway_mac, factory_id,
        factory_name, device_model_id, device_model,
        alarm_type, alarm_name, main_chip_temperature,
        alarm_time, sample_time, gdate
        )
        values (#{provinceCode,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR},
        #{cityName,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{adslAccount,jdbcType=VARCHAR},
        #{gatewaySn,jdbcType=VARCHAR}, #{gatewayMac,jdbcType=VARCHAR}, #{factoryId,jdbcType=BIGINT},
        #{factoryName,jdbcType=VARCHAR}, #{deviceModelId,jdbcType=BIGINT}, #{deviceModel,jdbcType=VARCHAR},
        #{alarmType,jdbcType=TINYINT}, #{alarmName,jdbcType=VARCHAR}, #{mainChipTemperature,jdbcType=VARCHAR},
        #{alarmTime,jdbcType=TIMESTAMP}, #{sampleTime,jdbcType=TIMESTAMP}, #{gdate,jdbcType=BIGINT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.GatewayAlarmDetail">
        insert into t_gateway_alarm_detail_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="provinceName != null">
                province_name,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="cityName != null">
                city_name,
            </if>
            <if test="customerName != null">
                customer_name,
            </if>
            <if test="adslAccount != null">
                adsl_account,
            </if>
            <if test="gatewaySn != null">
                gateway_sn,
            </if>
            <if test="gatewayMac != null">
                gateway_mac,
            </if>
            <if test="factoryId != null">
                factory_id,
            </if>
            <if test="factoryName != null">
                factory_name,
            </if>
            <if test="deviceModelId != null">
                device_model_id,
            </if>
            <if test="deviceModel != null">
                device_model,
            </if>
            <if test="alarmType != null">
                alarm_type,
            </if>
            <if test="alarmName != null">
                alarm_name,
            </if>
            <if test="mainChipTemperature != null">
                main_chip_temperature,
            </if>
            <if test="alarmTime != null">
                alarm_time,
            </if>
            <if test="sampleTime != null">
                sample_time,
            </if>
            <if test="gdate != null">
                gdate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="adslAccount != null">
                #{adslAccount,jdbcType=VARCHAR},
            </if>
            <if test="gatewaySn != null">
                #{gatewaySn,jdbcType=VARCHAR},
            </if>
            <if test="gatewayMac != null">
                #{gatewayMac,jdbcType=VARCHAR},
            </if>
            <if test="factoryId != null">
                #{factoryId,jdbcType=BIGINT},
            </if>
            <if test="factoryName != null">
                #{factoryName,jdbcType=VARCHAR},
            </if>
            <if test="deviceModelId != null">
                #{deviceModelId,jdbcType=BIGINT},
            </if>
            <if test="deviceModel != null">
                #{deviceModel,jdbcType=VARCHAR},
            </if>
            <if test="alarmType != null">
                #{alarmType,jdbcType=TINYINT},
            </if>
            <if test="alarmName != null">
                #{alarmName,jdbcType=VARCHAR},
            </if>
            <if test="mainChipTemperature != null">
                #{mainChipTemperature,jdbcType=VARCHAR},
            </if>
            <if test="alarmTime != null">
                #{alarmTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sampleTime != null">
                #{sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="gdate != null">
                #{gdate,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.GatewayAlarmDetailExample"
            resultType="java.lang.Long">
        select count(*) from t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_gateway_alarm_detail_all
        <set>
            <if test="record.provinceCode != null">
                province_code = #{record.provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="record.provinceName != null">
                province_name = #{record.provinceName,jdbcType=VARCHAR},
            </if>
            <if test="record.cityCode != null">
                city_code = #{record.cityCode,jdbcType=VARCHAR},
            </if>
            <if test="record.cityName != null">
                city_name = #{record.cityName,jdbcType=VARCHAR},
            </if>
            <if test="record.customerName != null">
                customer_name = #{record.customerName,jdbcType=VARCHAR},
            </if>
            <if test="record.adslAccount != null">
                adsl_account = #{record.adslAccount,jdbcType=VARCHAR},
            </if>
            <if test="record.gatewaySn != null">
                gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
            </if>
            <if test="record.gatewayMac != null">
                gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
            </if>
            <if test="record.factoryId != null">
                factory_id = #{record.factoryId,jdbcType=BIGINT},
            </if>
            <if test="record.factoryName != null">
                factory_name = #{record.factoryName,jdbcType=VARCHAR},
            </if>
            <if test="record.deviceModelId != null">
                device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
            </if>
            <if test="record.deviceModel != null">
                device_model = #{record.deviceModel,jdbcType=VARCHAR},
            </if>
            <if test="record.alarmType != null">
                alarm_type = #{record.alarmType,jdbcType=TINYINT},
            </if>
            <if test="record.alarmName != null">
                alarm_name = #{record.alarmName,jdbcType=VARCHAR},
            </if>
            <if test="record.mainChipTemperature != null">
                main_chip_temperature = #{record.mainChipTemperature,jdbcType=VARCHAR},
            </if>
            <if test="record.alarmTime != null">
                alarm_time = #{record.alarmTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.sampleTime != null">
                sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gdate != null">
                gdate = #{record.gdate,jdbcType=BIGINT},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_gateway_alarm_detail_all
        set province_code = #{record.provinceCode,jdbcType=VARCHAR},
        province_name = #{record.provinceName,jdbcType=VARCHAR},
        city_code = #{record.cityCode,jdbcType=VARCHAR},
        city_name = #{record.cityName,jdbcType=VARCHAR},
        customer_name = #{record.customerName,jdbcType=VARCHAR},
        adsl_account = #{record.adslAccount,jdbcType=VARCHAR},
        gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
        gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
        factory_id = #{record.factoryId,jdbcType=BIGINT},
        factory_name = #{record.factoryName,jdbcType=VARCHAR},
        device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
        device_model = #{record.deviceModel,jdbcType=VARCHAR},
        alarm_type = #{record.alarmType,jdbcType=TINYINT},
        alarm_name = #{record.alarmName,jdbcType=VARCHAR},
        main_chip_temperature = #{record.mainChipTemperature,jdbcType=VARCHAR},
        alarm_time = #{record.alarmTime,jdbcType=TIMESTAMP},
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
        gdate = #{record.gdate,jdbcType=BIGINT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.GatewayAlarmDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_alarm_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <!-- 小程序运行报告告警网关数统计 -->
    <select id="appReportCount" resultType="java.lang.Integer">
        select uniqExact(gateway_sn) as count from t_gateway_alarm_detail_all where
        toDate(alarm_time) >= toDate(#{startTime})
        and toDate(alarm_time) &lt;= toDate(#{endTime})
        and customer_name global in
        (select argMax(customer_name, sample_time) as customer_name from t_customer_all
        where toDate(sample_time) = toDate(#{endTime})
        and enterprise_id = ${eid} having notEquals(customer_name,''))
    </select>
</mapper>