<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DeviceAllMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.DeviceAll">
    <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn" />
    <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac" />
    <result column="factory_id" jdbcType="BIGINT" property="factoryId" />
    <result column="gateway_vendor" jdbcType="VARCHAR" property="gatewayVendor" />
    <result column="device_model_id" jdbcType="BIGINT" property="deviceModelId" />
    <result column="gateway_productclass" jdbcType="VARCHAR" property="gatewayProductclass" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="dhcp_name" jdbcType="VARCHAR" property="dhcpName" />
    <result column="mac" jdbcType="VARCHAR" property="mac" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="ipv6" jdbcType="VARCHAR" property="ipv6" />
    <result column="lan_port" jdbcType="VARCHAR" property="lanPort" />
    <result column="online_class" jdbcType="VARCHAR" property="onlineClass" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="wlan_radio_type" jdbcType="VARCHAR" property="wlanRadioType" />
    <result column="wlan_radio_power" jdbcType="INTEGER" property="wlanRadioPower" />
    <result column="aver_txrate" jdbcType="DECIMAL" property="averTxrate" />
    <result column="aver_rxrate" jdbcType="DECIMAL" property="averRxrate" />
    <result column="max_txrate" jdbcType="DECIMAL" property="maxTxrate" />
    <result column="max_rxrate" jdbcType="DECIMAL" property="maxRxrate" />
    <result column="up_staticstics" jdbcType="DECIMAL" property="upStaticstics" />
    <result column="down_staticstics" jdbcType="DECIMAL" property="downStaticstics" />
    <result column="lan_bit_rate" jdbcType="BIGINT" property="lanBitRate" />
    <result column="lan_duplex_mode" jdbcType="VARCHAR" property="lanDuplexMode" />
    <result column="wlan_rxrate" jdbcType="BIGINT" property="wlanRxrate" />
    <result column="wlan_txrate" jdbcType="BIGINT" property="wlanTxrate" />
    <result column="online_time" jdbcType="BIGINT" property="onlineTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    gateway_sn, gateway_mac, factory_id, gateway_vendor, device_model_id, gateway_productclass, 
    province_code, city_code, enterprise_id, customer_id, account, sample_time, device_name, 
    dhcp_name, mac, ip, ipv6, lan_port, online_class, type, wlan_radio_type, wlan_radio_power, 
    aver_txrate, aver_rxrate, max_txrate, max_rxrate, up_staticstics, down_staticstics, 
    lan_bit_rate, lan_duplex_mode, wlan_rxrate, wlan_txrate, online_time
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.DeviceAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_device_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.DeviceAllExample">
    delete from t_device_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.DeviceAll">
    insert into t_device_all (gateway_sn, gateway_mac, factory_id, 
      gateway_vendor, device_model_id, gateway_productclass, 
      province_code, city_code, enterprise_id, 
      customer_id, account, sample_time, 
      device_name, dhcp_name, mac, 
      ip, ipv6, lan_port, 
      online_class, type, wlan_radio_type, 
      wlan_radio_power, aver_txrate, aver_rxrate, 
      max_txrate, max_rxrate, up_staticstics, 
      down_staticstics, lan_bit_rate, lan_duplex_mode, 
      wlan_rxrate, wlan_txrate, online_time
      )
    values (#{gatewaySn,jdbcType=VARCHAR}, #{gatewayMac,jdbcType=VARCHAR}, #{factoryId,jdbcType=BIGINT}, 
      #{gatewayVendor,jdbcType=VARCHAR}, #{deviceModelId,jdbcType=BIGINT}, #{gatewayProductclass,jdbcType=VARCHAR}, 
      #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{enterpriseId,jdbcType=BIGINT}, 
      #{customerId,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{sampleTime,jdbcType=TIMESTAMP}, 
      #{deviceName,jdbcType=VARCHAR}, #{dhcpName,jdbcType=VARCHAR}, #{mac,jdbcType=VARCHAR}, 
      #{ip,jdbcType=VARCHAR}, #{ipv6,jdbcType=VARCHAR}, #{lanPort,jdbcType=VARCHAR}, 
      #{onlineClass,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{wlanRadioType,jdbcType=VARCHAR}, 
      #{wlanRadioPower,jdbcType=INTEGER}, #{averTxrate,jdbcType=DECIMAL}, #{averRxrate,jdbcType=DECIMAL}, 
      #{maxTxrate,jdbcType=DECIMAL}, #{maxRxrate,jdbcType=DECIMAL}, #{upStaticstics,jdbcType=DECIMAL}, 
      #{downStaticstics,jdbcType=DECIMAL}, #{lanBitRate,jdbcType=BIGINT}, #{lanDuplexMode,jdbcType=VARCHAR}, 
      #{wlanRxrate,jdbcType=BIGINT}, #{wlanTxrate,jdbcType=BIGINT}, #{onlineTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.DeviceAll">
    insert into t_device_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gatewaySn != null">
        gateway_sn,
      </if>
      <if test="gatewayMac != null">
        gateway_mac,
      </if>
      <if test="factoryId != null">
        factory_id,
      </if>
      <if test="gatewayVendor != null">
        gateway_vendor,
      </if>
      <if test="deviceModelId != null">
        device_model_id,
      </if>
      <if test="gatewayProductclass != null">
        gateway_productclass,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="sampleTime != null">
        sample_time,
      </if>
      <if test="deviceName != null">
        device_name,
      </if>
      <if test="dhcpName != null">
        dhcp_name,
      </if>
      <if test="mac != null">
        mac,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="ipv6 != null">
        ipv6,
      </if>
      <if test="lanPort != null">
        lan_port,
      </if>
      <if test="onlineClass != null">
        online_class,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="wlanRadioType != null">
        wlan_radio_type,
      </if>
      <if test="wlanRadioPower != null">
        wlan_radio_power,
      </if>
      <if test="averTxrate != null">
        aver_txrate,
      </if>
      <if test="averRxrate != null">
        aver_rxrate,
      </if>
      <if test="maxTxrate != null">
        max_txrate,
      </if>
      <if test="maxRxrate != null">
        max_rxrate,
      </if>
      <if test="upStaticstics != null">
        up_staticstics,
      </if>
      <if test="downStaticstics != null">
        down_staticstics,
      </if>
      <if test="lanBitRate != null">
        lan_bit_rate,
      </if>
      <if test="lanDuplexMode != null">
        lan_duplex_mode,
      </if>
      <if test="wlanRxrate != null">
        wlan_rxrate,
      </if>
      <if test="wlanTxrate != null">
        wlan_txrate,
      </if>
      <if test="onlineTime != null">
        online_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gatewaySn != null">
        #{gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="gatewayMac != null">
        #{gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="factoryId != null">
        #{factoryId,jdbcType=BIGINT},
      </if>
      <if test="gatewayVendor != null">
        #{gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="deviceModelId != null">
        #{deviceModelId,jdbcType=BIGINT},
      </if>
      <if test="gatewayProductclass != null">
        #{gatewayProductclass,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="sampleTime != null">
        #{sampleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceName != null">
        #{deviceName,jdbcType=VARCHAR},
      </if>
      <if test="dhcpName != null">
        #{dhcpName,jdbcType=VARCHAR},
      </if>
      <if test="mac != null">
        #{mac,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="ipv6 != null">
        #{ipv6,jdbcType=VARCHAR},
      </if>
      <if test="lanPort != null">
        #{lanPort,jdbcType=VARCHAR},
      </if>
      <if test="onlineClass != null">
        #{onlineClass,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="wlanRadioType != null">
        #{wlanRadioType,jdbcType=VARCHAR},
      </if>
      <if test="wlanRadioPower != null">
        #{wlanRadioPower,jdbcType=INTEGER},
      </if>
      <if test="averTxrate != null">
        #{averTxrate,jdbcType=DECIMAL},
      </if>
      <if test="averRxrate != null">
        #{averRxrate,jdbcType=DECIMAL},
      </if>
      <if test="maxTxrate != null">
        #{maxTxrate,jdbcType=DECIMAL},
      </if>
      <if test="maxRxrate != null">
        #{maxRxrate,jdbcType=DECIMAL},
      </if>
      <if test="upStaticstics != null">
        #{upStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="downStaticstics != null">
        #{downStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="lanBitRate != null">
        #{lanBitRate,jdbcType=BIGINT},
      </if>
      <if test="lanDuplexMode != null">
        #{lanDuplexMode,jdbcType=VARCHAR},
      </if>
      <if test="wlanRxrate != null">
        #{wlanRxrate,jdbcType=BIGINT},
      </if>
      <if test="wlanTxrate != null">
        #{wlanTxrate,jdbcType=BIGINT},
      </if>
      <if test="onlineTime != null">
        #{onlineTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.DeviceAllExample" resultType="java.lang.Long">
    select count(*) from t_device_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_device_all
    <set>
      <if test="record.gatewaySn != null">
        gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayMac != null">
        gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="record.factoryId != null">
        factory_id = #{record.factoryId,jdbcType=BIGINT},
      </if>
      <if test="record.gatewayVendor != null">
        gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceModelId != null">
        device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
      </if>
      <if test="record.gatewayProductclass != null">
        gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.enterpriseId != null">
        enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.account != null">
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleTime != null">
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deviceName != null">
        device_name = #{record.deviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.dhcpName != null">
        dhcp_name = #{record.dhcpName,jdbcType=VARCHAR},
      </if>
      <if test="record.mac != null">
        mac = #{record.mac,jdbcType=VARCHAR},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.ipv6 != null">
        ipv6 = #{record.ipv6,jdbcType=VARCHAR},
      </if>
      <if test="record.lanPort != null">
        lan_port = #{record.lanPort,jdbcType=VARCHAR},
      </if>
      <if test="record.onlineClass != null">
        online_class = #{record.onlineClass,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.wlanRadioType != null">
        wlan_radio_type = #{record.wlanRadioType,jdbcType=VARCHAR},
      </if>
      <if test="record.wlanRadioPower != null">
        wlan_radio_power = #{record.wlanRadioPower,jdbcType=INTEGER},
      </if>
      <if test="record.averTxrate != null">
        aver_txrate = #{record.averTxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.averRxrate != null">
        aver_rxrate = #{record.averRxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.maxTxrate != null">
        max_txrate = #{record.maxTxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.maxRxrate != null">
        max_rxrate = #{record.maxRxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.upStaticstics != null">
        up_staticstics = #{record.upStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="record.downStaticstics != null">
        down_staticstics = #{record.downStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="record.lanBitRate != null">
        lan_bit_rate = #{record.lanBitRate,jdbcType=BIGINT},
      </if>
      <if test="record.lanDuplexMode != null">
        lan_duplex_mode = #{record.lanDuplexMode,jdbcType=VARCHAR},
      </if>
      <if test="record.wlanRxrate != null">
        wlan_rxrate = #{record.wlanRxrate,jdbcType=BIGINT},
      </if>
      <if test="record.wlanTxrate != null">
        wlan_txrate = #{record.wlanTxrate,jdbcType=BIGINT},
      </if>
      <if test="record.onlineTime != null">
        online_time = #{record.onlineTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_device_all
    set gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      factory_id = #{record.factoryId,jdbcType=BIGINT},
      gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
      gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      account = #{record.account,jdbcType=VARCHAR},
      sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      device_name = #{record.deviceName,jdbcType=VARCHAR},
      dhcp_name = #{record.dhcpName,jdbcType=VARCHAR},
      mac = #{record.mac,jdbcType=VARCHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      ipv6 = #{record.ipv6,jdbcType=VARCHAR},
      lan_port = #{record.lanPort,jdbcType=VARCHAR},
      online_class = #{record.onlineClass,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      wlan_radio_type = #{record.wlanRadioType,jdbcType=VARCHAR},
      wlan_radio_power = #{record.wlanRadioPower,jdbcType=INTEGER},
      aver_txrate = #{record.averTxrate,jdbcType=DECIMAL},
      aver_rxrate = #{record.averRxrate,jdbcType=DECIMAL},
      max_txrate = #{record.maxTxrate,jdbcType=DECIMAL},
      max_rxrate = #{record.maxRxrate,jdbcType=DECIMAL},
      up_staticstics = #{record.upStaticstics,jdbcType=DECIMAL},
      down_staticstics = #{record.downStaticstics,jdbcType=DECIMAL},
      lan_bit_rate = #{record.lanBitRate,jdbcType=BIGINT},
      lan_duplex_mode = #{record.lanDuplexMode,jdbcType=VARCHAR},
      wlan_rxrate = #{record.wlanRxrate,jdbcType=BIGINT},
      wlan_txrate = #{record.wlanTxrate,jdbcType=BIGINT},
      online_time = #{record.onlineTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.DeviceAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_device_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>