<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.HguNetworkVisitDetailMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.HguNetworkVisitDetail">
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn" />
    <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="domain" jdbcType="VARCHAR" property="domain" />
    <result column="domain_type" jdbcType="VARCHAR" property="domainType" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="mac" jdbcType="VARCHAR" property="mac" />
    <result column="visit_duration" jdbcType="BIGINT" property="visitDuration" />
    <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    enterprise_id, customer_id, gateway_sn, gateway_mac, province_code, city_code, domain, 
    domain_type, domain_name, ip, mac, visit_duration, sample_time
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.HguNetworkVisitDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_hgu_network_visit_detail_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.HguNetworkVisitDetailExample">
    delete from t_hgu_network_visit_detail_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.HguNetworkVisitDetail">
    insert into t_hgu_network_visit_detail_all (enterprise_id, customer_id, gateway_sn, 
      gateway_mac, province_code, city_code, 
      domain, domain_type, domain_name, 
      ip, mac, visit_duration, 
      sample_time)
    values (#{enterpriseId,jdbcType=BIGINT}, #{customerId,jdbcType=VARCHAR}, #{gatewaySn,jdbcType=VARCHAR}, 
      #{gatewayMac,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{domain,jdbcType=VARCHAR}, #{domainType,jdbcType=VARCHAR}, #{domainName,jdbcType=VARCHAR}, 
      #{ip,jdbcType=VARCHAR}, #{mac,jdbcType=VARCHAR}, #{visitDuration,jdbcType=BIGINT}, 
      #{sampleTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.HguNetworkVisitDetail">
    insert into t_hgu_network_visit_detail_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="gatewaySn != null">
        gateway_sn,
      </if>
      <if test="gatewayMac != null">
        gateway_mac,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="domain != null">
        domain,
      </if>
      <if test="domainType != null">
        domain_type,
      </if>
      <if test="domainName != null">
        domain_name,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="mac != null">
        mac,
      </if>
      <if test="visitDuration != null">
        visit_duration,
      </if>
      <if test="sampleTime != null">
        sample_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="gatewaySn != null">
        #{gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="gatewayMac != null">
        #{gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="domainType != null">
        #{domainType,jdbcType=VARCHAR},
      </if>
      <if test="domainName != null">
        #{domainName,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="mac != null">
        #{mac,jdbcType=VARCHAR},
      </if>
      <if test="visitDuration != null">
        #{visitDuration,jdbcType=BIGINT},
      </if>
      <if test="sampleTime != null">
        #{sampleTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.HguNetworkVisitDetailExample" resultType="java.lang.Long">
    select count(*) from t_hgu_network_visit_detail_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_hgu_network_visit_detail_all
    <set>
      <if test="record.enterpriseId != null">
        enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewaySn != null">
        gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayMac != null">
        gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.domain != null">
        domain = #{record.domain,jdbcType=VARCHAR},
      </if>
      <if test="record.domainType != null">
        domain_type = #{record.domainType,jdbcType=VARCHAR},
      </if>
      <if test="record.domainName != null">
        domain_name = #{record.domainName,jdbcType=VARCHAR},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.mac != null">
        mac = #{record.mac,jdbcType=VARCHAR},
      </if>
      <if test="record.visitDuration != null">
        visit_duration = #{record.visitDuration,jdbcType=BIGINT},
      </if>
      <if test="record.sampleTime != null">
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_hgu_network_visit_detail_all
    set enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      domain = #{record.domain,jdbcType=VARCHAR},
      domain_type = #{record.domainType,jdbcType=VARCHAR},
      domain_name = #{record.domainName,jdbcType=VARCHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      mac = #{record.mac,jdbcType=VARCHAR},
      visit_duration = #{record.visitDuration,jdbcType=BIGINT},
      sample_time = #{record.sampleTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.HguNetworkVisitDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_hgu_network_visit_detail_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
    <select id="selectCount" resultType="com.cmiot.report.bean.NetworkPerferCount">
      select sum(t1.visit_duration) as visitDuration, t1.domain_name as name
      from (select customer_id, province_code, city_code, gateway_mac, mac, `domain`, `domain_name`,
                   arrayJoin(groupArray(1)(visit_duration)) as visit_duration
      from t_hgu_network_visit_detail_all
      where 1 = 1
      <if test="startTime != null">
        and toYYYYMMDD(sample_time) &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        and toYYYYMMDD(sample_time) &lt;= #{endTime}
      </if>
      <if test="provinceCodes != null and provinceCodes.size() > 0">
        and province_code in
        <foreach collection="provinceCodes" index="index" item="p" open="(" close=")" separator=",">
          #{p}
        </foreach>
      </if>
      <if test="cityCodes != null and cityCodes.size() > 0">
        and city_code in
        <foreach collection="cityCodes" index="index" item="c" open="(" close=")" separator=",">
          #{c}
        </foreach>
      </if>
      group by customer_id,province_code, city_code, gateway_mac, mac, `domain`, `domain_name`) t1
      group by t1.domain_name
      order by visitDuration desc
    </select>
</mapper>
