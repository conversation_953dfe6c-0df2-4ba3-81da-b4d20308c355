<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.CustomerMarketingDayCountAllMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.customer.CustomerMarketingDayCountAll">
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="total_num" jdbcType="BIGINT" property="totalNum" />
    <result column="last_day_incr" jdbcType="BIGINT" property="lastDayIncr" />
    <result column="last_day_decr" jdbcType="BIGINT" property="lastDayDecr" />
    <result column="last_week_incr" jdbcType="BIGINT" property="lastWeekIncr" />
    <result column="last_week_decr" jdbcType="BIGINT" property="lastWeekDecr" />
    <result column="last_month_incr" jdbcType="BIGINT" property="lastMonthIncr" />
    <result column="last_month_decr" jdbcType="BIGINT" property="lastMonthDecr" />
    <result column="gdate" jdbcType="INTEGER" property="gdate" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    province_code, city_code, total_num, last_day_incr, last_day_decr, last_week_incr, 
    last_week_decr, last_month_incr, last_month_decr, gdate
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.customer.CustomerMarketingDayCountAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer_marketing_day_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.customer.CustomerMarketingDayCountAllExample">
    delete from t_customer_marketing_day_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.customer.CustomerMarketingDayCountAll">
    insert into t_customer_marketing_day_count_all (province_code, city_code, total_num, 
      last_day_incr, last_day_decr, last_week_incr, 
      last_week_decr, last_month_incr, last_month_decr, 
      gdate)
    values (#{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{totalNum,jdbcType=BIGINT}, 
      #{lastDayIncr,jdbcType=BIGINT}, #{lastDayDecr,jdbcType=BIGINT}, #{lastWeekIncr,jdbcType=BIGINT}, 
      #{lastWeekDecr,jdbcType=BIGINT}, #{lastMonthIncr,jdbcType=BIGINT}, #{lastMonthDecr,jdbcType=BIGINT}, 
      #{gdate,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.customer.CustomerMarketingDayCountAll">
    insert into t_customer_marketing_day_count_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="lastDayIncr != null">
        last_day_incr,
      </if>
      <if test="lastDayDecr != null">
        last_day_decr,
      </if>
      <if test="lastWeekIncr != null">
        last_week_incr,
      </if>
      <if test="lastWeekDecr != null">
        last_week_decr,
      </if>
      <if test="lastMonthIncr != null">
        last_month_incr,
      </if>
      <if test="lastMonthDecr != null">
        last_month_decr,
      </if>
      <if test="gdate != null">
        gdate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null">
        #{totalNum,jdbcType=BIGINT},
      </if>
      <if test="lastDayIncr != null">
        #{lastDayIncr,jdbcType=BIGINT},
      </if>
      <if test="lastDayDecr != null">
        #{lastDayDecr,jdbcType=BIGINT},
      </if>
      <if test="lastWeekIncr != null">
        #{lastWeekIncr,jdbcType=BIGINT},
      </if>
      <if test="lastWeekDecr != null">
        #{lastWeekDecr,jdbcType=BIGINT},
      </if>
      <if test="lastMonthIncr != null">
        #{lastMonthIncr,jdbcType=BIGINT},
      </if>
      <if test="lastMonthDecr != null">
        #{lastMonthDecr,jdbcType=BIGINT},
      </if>
      <if test="gdate != null">
        #{gdate,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.customer.CustomerMarketingDayCountAllExample" resultType="java.lang.Long">
    select count(*) from t_customer_marketing_day_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_customer_marketing_day_count_all
    <set>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.totalNum != null">
        total_num = #{record.totalNum,jdbcType=BIGINT},
      </if>
      <if test="record.lastDayIncr != null">
        last_day_incr = #{record.lastDayIncr,jdbcType=BIGINT},
      </if>
      <if test="record.lastDayDecr != null">
        last_day_decr = #{record.lastDayDecr,jdbcType=BIGINT},
      </if>
      <if test="record.lastWeekIncr != null">
        last_week_incr = #{record.lastWeekIncr,jdbcType=BIGINT},
      </if>
      <if test="record.lastWeekDecr != null">
        last_week_decr = #{record.lastWeekDecr,jdbcType=BIGINT},
      </if>
      <if test="record.lastMonthIncr != null">
        last_month_incr = #{record.lastMonthIncr,jdbcType=BIGINT},
      </if>
      <if test="record.lastMonthDecr != null">
        last_month_decr = #{record.lastMonthDecr,jdbcType=BIGINT},
      </if>
      <if test="record.gdate != null">
        gdate = #{record.gdate,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_customer_marketing_day_count_all
    set province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      total_num = #{record.totalNum,jdbcType=BIGINT},
      last_day_incr = #{record.lastDayIncr,jdbcType=BIGINT},
      last_day_decr = #{record.lastDayDecr,jdbcType=BIGINT},
      last_week_incr = #{record.lastWeekIncr,jdbcType=BIGINT},
      last_week_decr = #{record.lastWeekDecr,jdbcType=BIGINT},
      last_month_incr = #{record.lastMonthIncr,jdbcType=BIGINT},
      last_month_decr = #{record.lastMonthDecr,jdbcType=BIGINT},
      gdate = #{record.gdate,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.customer.CustomerMarketingDayCountAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer_marketing_day_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="selectByExampleByProvince"
          parameterType="com.cmiot.report.bean.customer.CustomerMarketingDayCountAllExample"
          resultMap="BaseResultMap">
    select t1.province_code,
    dictGetOrDefault('ge_report.t_dic_area_info', 'gname', t1.province_code, '其他') as area_name,
    sum(t1.total_num) as total_num,
    sum(t1.last_day_incr) as last_day_incr,
    sum(t1.last_day_decr) as last_day_decr,
    sum(t1.last_week_incr) as last_week_incr,
    sum(t1.last_week_decr) as last_week_decr,
    sum(t1.last_month_incr) as last_month_incr,
    sum(t1.last_month_decr) as last_month_decr
    from
    (
    select
    city_code, gdate, province_code,
    arrayJoin(groupArray(1)(total_num)) AS total_num,
    arrayJoin(groupArray(1)(last_day_incr)) AS last_day_incr,
    arrayJoin(groupArray(1)(last_day_decr)) AS last_day_decr,
    arrayJoin(groupArray(1)(last_week_incr)) AS last_week_incr,
    arrayJoin(groupArray(1)(last_week_decr)) AS last_week_decr,
    arrayJoin(groupArray(1)(last_month_incr)) AS last_month_incr,
    arrayJoin(groupArray(1)(last_month_decr)) AS last_month_decr
    from t_customer_marketing_day_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by province_code, city_code, gdate) t1
    group by t1.province_code
  </select>

  <select id="selectByExampleByCity"
          parameterType="com.cmiot.report.bean.customer.CustomerMarketingDayCountAllExample"
          resultMap="BaseResultMap">
    select
    city_code, dictGetOrDefault('ge_report.t_dic_area_info', 'gname', city_code, '其他') as area_name,
    arrayJoin(groupArray(1)(total_num)) AS total_num,
    arrayJoin(groupArray(1)(last_day_incr)) AS last_day_incr,
    arrayJoin(groupArray(1)(last_day_decr)) AS last_day_decr,
    arrayJoin(groupArray(1)(last_week_incr)) AS last_week_incr,
    arrayJoin(groupArray(1)(last_week_decr)) AS last_week_decr,
    arrayJoin(groupArray(1)(last_month_incr)) AS last_month_incr,
    arrayJoin(groupArray(1)(last_month_decr)) AS last_month_decr
    from t_customer_marketing_day_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by city_code, gdate
  </select>
  <select id="selectScoreDistribution"
          resultType="com.cmiot.report.bean.customer.CustomerMarketingScoreDistribution">
    select t1.scoreFloor, count(DISTINCT t1.enterprise_id) cnum from
      (select enterprise_id, floor(arrayJoin(groupArray(1)(score)) / 20 )  * 20 as scoreFloor
       from t_customer_marketing_score_all
       where toYYYYMMDD(sample_time) = #{time}
        <if test="provList != null and provList.size > 0">
          and province_code in
            <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
              #{itemp}
            </foreach>
        </if>
    <if test="cityList != null and cityList.size > 0">
      and city_code in
      <foreach collection="cityList" index="index" item="itemc" open="(" separator="," close=")">
        #{itemc}
      </foreach>
    </if>
       group by enterprise_id ) t1
    group by t1.scoreFloor
    order by t1.scoreFloor asc
  </select>

  <select id="selectByExampleByDate" parameterType="com.cmiot.report.bean.customer.CustomerMarketingDayCountAllExample"
          resultMap="BaseResultMap">
    select t1.gdate,
    sum(total_num) as total_num,
    sum(last_day_incr) as last_day_incr,
    sum(last_day_decr) as last_day_decr
    from
    (
    select
    city_code, gdate,
    arrayJoin(groupArray(1)(total_num)) AS total_num,
    arrayJoin(groupArray(1)(last_day_incr)) AS last_day_incr,
    arrayJoin(groupArray(1)(last_day_decr)) AS last_day_decr
    from t_customer_marketing_day_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by city_code, gdate) t1
    group by t1.gdate
    order by t1.gdate asc
  </select>

</mapper>