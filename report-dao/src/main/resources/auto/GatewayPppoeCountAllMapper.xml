<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayPppoeCountAllMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.GatewayPppoeCountAll">
    <result column="pdate" jdbcType="BIGINT" property="pdate" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="gateway_vendor" jdbcType="VARCHAR" property="gatewayVendor" />
    <result column="pppoeerror_status" jdbcType="VARCHAR" property="pppoeerrorStatus" />
    <result column="pppoeerror_count" jdbcType="BIGINT" property="pppoeerrorCount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    pdate, province_code, city_code, gateway_vendor, pppoeerror_status, pppoeerror_count
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.GatewayPppoeCountAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_pppoe_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.GatewayPppoeCountAllExample">
    delete from t_gateway_pppoe_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.GatewayPppoeCountAll">
    insert into t_gateway_pppoe_count_all (pdate, province_code, city_code, 
      gateway_vendor, pppoeerror_status, pppoeerror_count
      )
    values (#{pdate,jdbcType=BIGINT}, #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{gatewayVendor,jdbcType=VARCHAR}, #{pppoeerrorStatus,jdbcType=VARCHAR}, #{pppoeerrorCount,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.GatewayPppoeCountAll">
    insert into t_gateway_pppoe_count_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pdate != null">
        pdate,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="gatewayVendor != null">
        gateway_vendor,
      </if>
      <if test="pppoeerrorStatus != null">
        pppoeerror_status,
      </if>
      <if test="pppoeerrorCount != null">
        pppoeerror_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pdate != null">
        #{pdate,jdbcType=BIGINT},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="gatewayVendor != null">
        #{gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="pppoeerrorStatus != null">
        #{pppoeerrorStatus,jdbcType=VARCHAR},
      </if>
      <if test="pppoeerrorCount != null">
        #{pppoeerrorCount,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.GatewayPppoeCountAllExample" resultType="java.lang.Long">
    select count(*) from t_gateway_pppoe_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_gateway_pppoe_count_all
    <set>
      <if test="record.pdate != null">
        pdate = #{record.pdate,jdbcType=BIGINT},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayVendor != null">
        gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="record.pppoeerrorStatus != null">
        pppoeerror_status = #{record.pppoeerrorStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.pppoeerrorCount != null">
        pppoeerror_count = #{record.pppoeerrorCount,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_gateway_pppoe_count_all
    set pdate = #{record.pdate,jdbcType=BIGINT},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      pppoeerror_status = #{record.pppoeerrorStatus,jdbcType=VARCHAR},
      pppoeerror_count = #{record.pppoeerrorCount,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.GatewayPppoeCountAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_pppoe_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>