<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.NetworkDeviceAllMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.NetworkDeviceAll">
        <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId"/>
        <result column="device_mac" jdbcType="VARCHAR" property="deviceMac"/>
        <result column="running_time" jdbcType="BIGINT" property="runningTime"/>
        <result column="up_freq" jdbcType="INTEGER" property="upFreq"/>
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        enterprise_id, device_mac, running_time, up_freq, sample_time
    </sql>
    <select id="selectByExample" parameterType="com.cmiot.report.bean.NetworkDeviceAllExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_network_device_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.NetworkDeviceAllExample">
        delete from t_network_device_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.NetworkDeviceAll">
        insert into t_network_device_all (enterprise_id, device_mac, running_time,
        up_freq, sample_time)
        values (#{enterpriseId,jdbcType=BIGINT}, #{deviceMac,jdbcType=VARCHAR}, #{runningTime,jdbcType=BIGINT},
        #{upFreq,jdbcType=INTEGER}, #{sampleTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.NetworkDeviceAll">
        insert into t_network_device_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="enterpriseId != null">
                enterprise_id,
            </if>
            <if test="deviceMac != null">
                device_mac,
            </if>
            <if test="runningTime != null">
                running_time,
            </if>
            <if test="upFreq != null">
                up_freq,
            </if>
            <if test="sampleTime != null">
                sample_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="enterpriseId != null">
                #{enterpriseId,jdbcType=BIGINT},
            </if>
            <if test="deviceMac != null">
                #{deviceMac,jdbcType=VARCHAR},
            </if>
            <if test="runningTime != null">
                #{runningTime,jdbcType=BIGINT},
            </if>
            <if test="upFreq != null">
                #{upFreq,jdbcType=INTEGER},
            </if>
            <if test="sampleTime != null">
                #{sampleTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.NetworkDeviceAllExample"
            resultType="java.lang.Long">
        select count(*) from t_network_device_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_network_device_all
        <set>
            <if test="record.enterpriseId != null">
                enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
            </if>
            <if test="record.deviceMac != null">
                device_mac = #{record.deviceMac,jdbcType=VARCHAR},
            </if>
            <if test="record.runningTime != null">
                running_time = #{record.runningTime,jdbcType=BIGINT},
            </if>
            <if test="record.upFreq != null">
                up_freq = #{record.upFreq,jdbcType=INTEGER},
            </if>
            <if test="record.sampleTime != null">
                sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_network_device_all
        set enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
        device_mac = #{record.deviceMac,jdbcType=VARCHAR},
        running_time = #{record.runningTime,jdbcType=BIGINT},
        up_freq = #{record.upFreq,jdbcType=INTEGER},
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.NetworkDeviceAllExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_network_device_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <!--这些组网设备的连续运行时长-->
    <select id="getNetRunTime" resultType="java.lang.Double">
        select ceiling(sum(t1.running_time)/60/60) as running_time from (
        select device_mac,argMax(running_time, sample_time) as running_time from t_network_device_all
        where toDate(sample_time) >= toDate(#{query.startTime})
        and toDate(sample_time) &lt;= toDate(#{query.endTime})
        <if test="query.eid !=null and query.eid !=''">
            and enterprise_id = #{query.eid}
        </if>
        group by device_mac) t1
    </select>

    <!--组网设备运行超时列表-->
    <select id="networkDeviceList" resultType="com.cmiot.report.dto.NetworkDeviceRunTimeDetail">
        select
        replace(device_mac, ':', '') as mac,
        argMax(running_time, sample_time) as duration from t_network_device_all
        where toDate(sample_time) >= toDate(#{query.startTime})
        and toDate(sample_time) &lt;= toDate(#{query.endTime})
        <if test="query.eid !=null">
            and enterprise_id = #{query.eid}
        </if>
        group by device_mac
        having duration >= (#{query.overTime}*60*60)
    </select>

    <!--组网设备运行超时数量-->
    <select id="networkDeviceCount" resultType="java.lang.Long">
        select uniqExact(tt.device_mac) as count from (select
        device_mac,
        argMax(running_time, sample_time) as duration from t_network_device_all
        where toDate(sample_time) >= toDate(#{query.startTime})
        and toDate(sample_time) &lt;= toDate(#{query.endTime})
        <if test="query.eid !=null">
            and enterprise_id = #{query.eid}
        </if>
        group by device_mac
        having duration >= (#{query.overTime}*60*60)) tt
    </select>

</mapper>