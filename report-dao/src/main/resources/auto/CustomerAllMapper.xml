<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.CustomerAllMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.CustomerAll">
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="id_type" jdbcType="INTEGER" property="idType" />
    <result column="id_number" jdbcType="VARCHAR" property="idNumber" />
    <result column="parent_customer_id" jdbcType="VARCHAR" property="parentCustomerId" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="county_code" jdbcType="VARCHAR" property="countyCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="industry" jdbcType="BIGINT" property="industry" />
    <result column="industry_category" jdbcType="VARCHAR" property="industryCategory" />
    <result column="alias" jdbcType="VARCHAR" property="alias" />
    <result column="enterprise_mark" jdbcType="VARCHAR" property="enterpriseMark" />
    <result column="customer_status" jdbcType="INTEGER" property="customerStatus" />
    <result column="customer_code_boss" jdbcType="VARCHAR" property="customerCodeBoss" />
    <result column="customer_size" jdbcType="VARCHAR" property="customerSize" />
    <result column="last_business_time" jdbcType="TIMESTAMP" property="lastBusinessTime" />
    <result column="business_count" jdbcType="INTEGER" property="businessCount" />
    <result column="value_category" jdbcType="VARCHAR" property="valueCategory" />
    <result column="manager_number" jdbcType="VARCHAR" property="managerNumber" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="region_feature" jdbcType="VARCHAR" property="regionFeature" />
    <result column="ownership" jdbcType="VARCHAR" property="ownership" />
    <result column="organization_code" jdbcType="VARCHAR" property="organizationCode" />
    <result column="post_code" jdbcType="VARCHAR" property="postCode" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    enterprise_id, customer_id, customer_name, address, id_type, id_number, parent_customer_id, 
    contact_phone, contact_email, province_code, city_code, county_code, create_time, 
    industry, industry_category, alias, enterprise_mark, customer_status, customer_code_boss, 
    customer_size, last_business_time, business_count, value_category, manager_number, 
    website, region_feature, ownership, organization_code, post_code, last_update_time, 
    sample_time
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.CustomerAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.CustomerAllExample">
    delete from t_customer_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.CustomerAll">
    insert into t_customer_all (enterprise_id, customer_id, customer_name, 
      address, id_type, id_number, 
      parent_customer_id, contact_phone, contact_email, 
      province_code, city_code, county_code, 
      create_time, industry, industry_category, 
      alias, enterprise_mark, customer_status, 
      customer_code_boss, customer_size, last_business_time, 
      business_count, value_category, manager_number, 
      website, region_feature, ownership, 
      organization_code, post_code, last_update_time, 
      sample_time)
    values (#{enterpriseId,jdbcType=BIGINT}, #{customerId,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{idType,jdbcType=INTEGER}, #{idNumber,jdbcType=VARCHAR}, 
      #{parentCustomerId,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, #{contactEmail,jdbcType=VARCHAR}, 
      #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{countyCode,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{industry,jdbcType=BIGINT}, #{industryCategory,jdbcType=VARCHAR}, 
      #{alias,jdbcType=VARCHAR}, #{enterpriseMark,jdbcType=VARCHAR}, #{customerStatus,jdbcType=INTEGER}, 
      #{customerCodeBoss,jdbcType=VARCHAR}, #{customerSize,jdbcType=VARCHAR}, #{lastBusinessTime,jdbcType=TIMESTAMP}, 
      #{businessCount,jdbcType=INTEGER}, #{valueCategory,jdbcType=VARCHAR}, #{managerNumber,jdbcType=VARCHAR}, 
      #{website,jdbcType=VARCHAR}, #{regionFeature,jdbcType=VARCHAR}, #{ownership,jdbcType=VARCHAR}, 
      #{organizationCode,jdbcType=VARCHAR}, #{postCode,jdbcType=VARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{sampleTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.CustomerAll">
    insert into t_customer_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="idType != null">
        id_type,
      </if>
      <if test="idNumber != null">
        id_number,
      </if>
      <if test="parentCustomerId != null">
        parent_customer_id,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="contactEmail != null">
        contact_email,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="countyCode != null">
        county_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="industry != null">
        industry,
      </if>
      <if test="industryCategory != null">
        industry_category,
      </if>
      <if test="alias != null">
        alias,
      </if>
      <if test="enterpriseMark != null">
        enterprise_mark,
      </if>
      <if test="customerStatus != null">
        customer_status,
      </if>
      <if test="customerCodeBoss != null">
        customer_code_boss,
      </if>
      <if test="customerSize != null">
        customer_size,
      </if>
      <if test="lastBusinessTime != null">
        last_business_time,
      </if>
      <if test="businessCount != null">
        business_count,
      </if>
      <if test="valueCategory != null">
        value_category,
      </if>
      <if test="managerNumber != null">
        manager_number,
      </if>
      <if test="website != null">
        website,
      </if>
      <if test="regionFeature != null">
        region_feature,
      </if>
      <if test="ownership != null">
        ownership,
      </if>
      <if test="organizationCode != null">
        organization_code,
      </if>
      <if test="postCode != null">
        post_code,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="sampleTime != null">
        sample_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        #{idType,jdbcType=INTEGER},
      </if>
      <if test="idNumber != null">
        #{idNumber,jdbcType=VARCHAR},
      </if>
      <if test="parentCustomerId != null">
        #{parentCustomerId,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactEmail != null">
        #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="countyCode != null">
        #{countyCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="industry != null">
        #{industry,jdbcType=BIGINT},
      </if>
      <if test="industryCategory != null">
        #{industryCategory,jdbcType=VARCHAR},
      </if>
      <if test="alias != null">
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseMark != null">
        #{enterpriseMark,jdbcType=VARCHAR},
      </if>
      <if test="customerStatus != null">
        #{customerStatus,jdbcType=INTEGER},
      </if>
      <if test="customerCodeBoss != null">
        #{customerCodeBoss,jdbcType=VARCHAR},
      </if>
      <if test="customerSize != null">
        #{customerSize,jdbcType=VARCHAR},
      </if>
      <if test="lastBusinessTime != null">
        #{lastBusinessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessCount != null">
        #{businessCount,jdbcType=INTEGER},
      </if>
      <if test="valueCategory != null">
        #{valueCategory,jdbcType=VARCHAR},
      </if>
      <if test="managerNumber != null">
        #{managerNumber,jdbcType=VARCHAR},
      </if>
      <if test="website != null">
        #{website,jdbcType=VARCHAR},
      </if>
      <if test="regionFeature != null">
        #{regionFeature,jdbcType=VARCHAR},
      </if>
      <if test="ownership != null">
        #{ownership,jdbcType=VARCHAR},
      </if>
      <if test="organizationCode != null">
        #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="postCode != null">
        #{postCode,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sampleTime != null">
        #{sampleTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>


  <select id="countByExample" parameterType="com.cmiot.report.bean.CustomerAllExample" resultType="java.lang.Long">
    select uniqExact(customer_id) as count from t_customer_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>


  <update id="updateByExampleSelective" parameterType="map">
    update t_customer_all
    <set>
      <if test="record.enterpriseId != null">
        enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerName != null">
        customer_name = #{record.customerName,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.idType != null">
        id_type = #{record.idType,jdbcType=INTEGER},
      </if>
      <if test="record.idNumber != null">
        id_number = #{record.idNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.parentCustomerId != null">
        parent_customer_id = #{record.parentCustomerId,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.contactEmail != null">
        contact_email = #{record.contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.countyCode != null">
        county_code = #{record.countyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.industry != null">
        industry = #{record.industry,jdbcType=BIGINT},
      </if>
      <if test="record.industryCategory != null">
        industry_category = #{record.industryCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.alias != null">
        alias = #{record.alias,jdbcType=VARCHAR},
      </if>
      <if test="record.enterpriseMark != null">
        enterprise_mark = #{record.enterpriseMark,jdbcType=VARCHAR},
      </if>
      <if test="record.customerStatus != null">
        customer_status = #{record.customerStatus,jdbcType=INTEGER},
      </if>
      <if test="record.customerCodeBoss != null">
        customer_code_boss = #{record.customerCodeBoss,jdbcType=VARCHAR},
      </if>
      <if test="record.customerSize != null">
        customer_size = #{record.customerSize,jdbcType=VARCHAR},
      </if>
      <if test="record.lastBusinessTime != null">
        last_business_time = #{record.lastBusinessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.businessCount != null">
        business_count = #{record.businessCount,jdbcType=INTEGER},
      </if>
      <if test="record.valueCategory != null">
        value_category = #{record.valueCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.managerNumber != null">
        manager_number = #{record.managerNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.website != null">
        website = #{record.website,jdbcType=VARCHAR},
      </if>
      <if test="record.regionFeature != null">
        region_feature = #{record.regionFeature,jdbcType=VARCHAR},
      </if>
      <if test="record.ownership != null">
        ownership = #{record.ownership,jdbcType=VARCHAR},
      </if>
      <if test="record.organizationCode != null">
        organization_code = #{record.organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.postCode != null">
        post_code = #{record.postCode,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sampleTime != null">
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_customer_all
    set enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      customer_name = #{record.customerName,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      id_type = #{record.idType,jdbcType=INTEGER},
      id_number = #{record.idNumber,jdbcType=VARCHAR},
      parent_customer_id = #{record.parentCustomerId,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      contact_email = #{record.contactEmail,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      county_code = #{record.countyCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      industry = #{record.industry,jdbcType=BIGINT},
      industry_category = #{record.industryCategory,jdbcType=VARCHAR},
      alias = #{record.alias,jdbcType=VARCHAR},
      enterprise_mark = #{record.enterpriseMark,jdbcType=VARCHAR},
      customer_status = #{record.customerStatus,jdbcType=INTEGER},
      customer_code_boss = #{record.customerCodeBoss,jdbcType=VARCHAR},
      customer_size = #{record.customerSize,jdbcType=VARCHAR},
      last_business_time = #{record.lastBusinessTime,jdbcType=TIMESTAMP},
      business_count = #{record.businessCount,jdbcType=INTEGER},
      value_category = #{record.valueCategory,jdbcType=VARCHAR},
      manager_number = #{record.managerNumber,jdbcType=VARCHAR},
      website = #{record.website,jdbcType=VARCHAR},
      region_feature = #{record.regionFeature,jdbcType=VARCHAR},
      ownership = #{record.ownership,jdbcType=VARCHAR},
      organization_code = #{record.organizationCode,jdbcType=VARCHAR},
      post_code = #{record.postCode,jdbcType=VARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      sample_time = #{record.sampleTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.CustomerAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <!--根据eid查询企业名称-->
  <select id="selectNameByExample" resultType="java.lang.String">
    select customer_name from t_customer_all where toDate(sample_time)=toDate(addDays(now(), -1))
    <if test="eid !=null and eid !=''">
      and enterprise_id = #{eid}
    </if>
    group by customer_name
  </select>
</mapper>