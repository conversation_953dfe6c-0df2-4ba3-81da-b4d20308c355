<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.AlarmThresholdMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.AlarmThreshold">
        <result column="timestamp" jdbcType="BIGINT" property="timestamp"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="value" jdbcType="DECIMAL" property="value"/>
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime"/>
        <result column="gdate" jdbcType="BIGINT" property="gdate"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        timestamp, type, value, sample_time, gdate
    </sql>


    <insert id="insertBatch" parameterType="java.util.List">
        insert into t_gateway_alarm_threshold_all (timestamp, type, value, sample_time, gdate)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.timestamp,jdbcType=BIGINT},#{item.type,jdbcType=TINYINT},#{item.value,jdbcType=INTEGER},#{item.sampleTime,jdbcType=TIMESTAMP},#{item.gdate,jdbcType=BIGINT})
        </foreach>
    </insert>


    <select id="selectNewestValueByExample" resultMap="BaseResultMap">
        select t2.timestamp as timestamp,t2.type as type,t2.value as value,t2.sample_time as sample_time,t2.gdate as gdate from (select t1.*,row_number() over (partition by
        t1.type
        order by t1.sample_time desc) rank from (select timestamp,type,value,sample_time,gdate from
        t_gateway_alarm_threshold_all) t1) t2 where t2.rank=1
    </select>


    <select id="selectByExample" parameterType="com.cmiot.report.bean.AlarmThresholdExample" resultMap="BaseResultMap">
        select t2.timestamp as timestamp,t2.type as type,t2.value as value,t2.sample_time as sample_time,t2.gdate as gdate from (select t1.*,row_number() over (partition by
        t1.type
        order by t1.sample_time desc) rank from (select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_alarm_threshold_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>) t1) t2 where t2.rank=1
    </select>

    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.AlarmThresholdExample">
        delete from t_gateway_alarm_threshold_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.AlarmThreshold">
        insert into t_gateway_alarm_threshold_all (timestamp, type, value,
        sample_time, gdate)
        values (#{timestamp,jdbcType=BIGINT}, #{type,jdbcType=TINYINT}, #{value,jdbcType=DECIMAL},
        #{sampleTime,jdbcType=TIMESTAMP}, #{gdate,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.AlarmThreshold">
        insert into t_gateway_alarm_threshold_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">
                timestamp,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="value != null">
                value,
            </if>
            <if test="sampleTime != null">
                sample_time,
            </if>
            <if test="gdate != null">
                gdate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">
                #{timestamp,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="value != null">
                #{value,jdbcType=DECIMAL},
            </if>
            <if test="sampleTime != null">
                #{sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="gdate != null">
                #{gdate,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.AlarmThresholdExample" resultType="java.lang.Long">
        select count(*) from t_gateway_alarm_threshold_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_gateway_alarm_threshold_all
        <set>
            <if test="record.timestamp != null">
                timestamp = #{record.timestamp,jdbcType=BIGINT},
            </if>
            <if test="record.type != null">
                type = #{record.type,jdbcType=TINYINT},
            </if>
            <if test="record.value != null">
                value = #{record.value,jdbcType=DECIMAL},
            </if>
            <if test="record.sampleTime != null">
                sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gdate != null">
                gdate = #{record.gdate,jdbcType=BIGINT},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_gateway_alarm_threshold_all
        set timestamp = #{record.timestamp,jdbcType=BIGINT},
        type = #{record.type,jdbcType=TINYINT},
        value = #{record.value,jdbcType=DECIMAL},
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
        gdate = #{record.gdate,jdbcType=BIGINT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.AlarmThresholdExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_alarm_threshold_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>