<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.CustomerOffGridoverallFactorThresholdMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.CustomerOffGridoverallFactorThreshold">
    <result column="timestamp" jdbcType="BIGINT" property="timestamp" />
    <result column="factor_threshold" jdbcType="INTEGER" property="factorThreshold" />
    <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    timestamp, factor_threshold, sample_time
  </sql>

<!-- 获取综合离网指数信息 -->
  <select id="selectNewestByExample" parameterType="com.cmiot.report.bean.CustomerOffGridoverallFactorThresholdExample" resultType="java.lang.Integer">
      select argMax(factor_threshold, sample_time) as factor_threshold from t_customer_off_grid_overall_all
  </select>

  <select id="selectByExample" parameterType="com.cmiot.report.bean.CustomerOffGridoverallFactorThresholdExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer_off_grid_overall_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.CustomerOffGridoverallFactorThresholdExample">
    delete from t_customer_off_grid_overall_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.CustomerOffGridoverallFactorThreshold">
    insert into t_customer_off_grid_overall_all (timestamp, factor_threshold, sample_time
      )
    values (#{timestamp,jdbcType=BIGINT}, #{factorThreshold,jdbcType=INTEGER}, #{sampleTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.CustomerOffGridoverallFactorThreshold">
    insert into t_customer_off_grid_overall_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="timestamp != null">
        timestamp,
      </if>
      <if test="factorThreshold != null">
        factor_threshold,
      </if>
      <if test="sampleTime != null">
        sample_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="timestamp != null">
        #{timestamp,jdbcType=BIGINT},
      </if>
      <if test="factorThreshold != null">
        #{factorThreshold,jdbcType=INTEGER},
      </if>
      <if test="sampleTime != null">
        #{sampleTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.CustomerOffGridoverallFactorThresholdExample" resultType="java.lang.Long">
    select count(*) from t_customer_off_grid_overall_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_customer_off_grid_overall_all
    <set>
      <if test="record.timestamp != null">
        timestamp = #{record.timestamp,jdbcType=BIGINT},
      </if>
      <if test="record.factorThreshold != null">
        factor_threshold = #{record.factorThreshold,jdbcType=INTEGER},
      </if>
      <if test="record.sampleTime != null">
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_customer_off_grid_overall_all
    set timestamp = #{record.timestamp,jdbcType=BIGINT},
      factor_threshold = #{record.factorThreshold,jdbcType=INTEGER},
      sample_time = #{record.sampleTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.CustomerOffGridoverallFactorThresholdExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer_off_grid_overall_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>