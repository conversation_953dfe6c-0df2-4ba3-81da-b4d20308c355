<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayNetworkFlowMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.GatewayNetworkFlow">
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="industry" jdbcType="VARCHAR" property="industry" />
    <result column="value_category" jdbcType="VARCHAR" property="valueCategory" />
    <result column="bussiness_type" jdbcType="VARCHAR" property="bussinessType" />
    <result column="bandwidth_all" jdbcType="DECIMAL" property="bandwidthAll" />
    <result column="total_txrate" jdbcType="DECIMAL" property="totalTxrate" />
    <result column="total_rxrate" jdbcType="DECIMAL" property="totalRxrate" />
    <result column="total_flow" jdbcType="DECIMAL" property="totalFlow" />
    <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
    <result column="gdate" jdbcType="BIGINT" property="gdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    province_code, city_code, industry, value_category, bussiness_type, bandwidth_all, 
    total_txrate, total_rxrate, total_flow, sample_time, gdate
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_network_flow_local
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample">
    delete from t_gateway_network_flow_local
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.GatewayNetworkFlow">
    insert into t_gateway_network_flow_local (province_code, city_code, industry, 
      value_category, bussiness_type, bandwidth_all, 
      total_txrate, total_rxrate, total_flow, 
      sample_time, gdate)
    values (#{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{industry,jdbcType=VARCHAR}, 
      #{valueCategory,jdbcType=VARCHAR}, #{bussinessType,jdbcType=VARCHAR}, #{bandwidthAll,jdbcType=DECIMAL},
      #{totalTxrate,jdbcType=DECIMAL}, #{totalRxrate,jdbcType=DECIMAL}, #{totalFlow,jdbcType=DECIMAL}, 
      #{sampleTime,jdbcType=TIMESTAMP}, #{gdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.GatewayNetworkFlow">
    insert into t_gateway_network_flow_local
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="industry != null">
        industry,
      </if>
      <if test="valueCategory != null">
        value_category,
      </if>
      <if test="bussinessType != null">
        bussiness_type,
      </if>
      <if test="bandwidthAll != null">
        bandwidth_all,
      </if>
      <if test="totalTxrate != null">
        total_txrate,
      </if>
      <if test="totalRxrate != null">
        total_rxrate,
      </if>
      <if test="totalFlow != null">
        total_flow,
      </if>
      <if test="sampleTime != null">
        sample_time,
      </if>
      <if test="gdate != null">
        gdate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="industry != null">
        #{industry,jdbcType=VARCHAR},
      </if>
      <if test="valueCategory != null">
        #{valueCategory,jdbcType=VARCHAR},
      </if>
      <if test="bussinessType != null">
        #{bussinessType,jdbcType=VARCHAR},
      </if>
      <if test="bandwidthAll != null">
        #{bandwidthAll,jdbcType=DECIMAL},
      </if>
      <if test="totalTxrate != null">
        #{totalTxrate,jdbcType=DECIMAL},
      </if>
      <if test="totalRxrate != null">
        #{totalRxrate,jdbcType=DECIMAL},
      </if>
      <if test="totalFlow != null">
        #{totalFlow,jdbcType=DECIMAL},
      </if>
      <if test="sampleTime != null">
        #{sampleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gdate != null">
        #{gdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample" resultType="java.lang.Long">
    select count(*) from t_gateway_network_flow_local
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_gateway_network_flow_local
    <set>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.industry != null">
        industry = #{record.industry,jdbcType=VARCHAR},
      </if>
      <if test="record.valueCategory != null">
        value_category = #{record.valueCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.bussinessType != null">
        bussiness_type = #{record.bussinessType,jdbcType=VARCHAR},
      </if>
      <if test="record.bandwidthAll != null">
        bandwidth_all = #{record.bandwidthAll,jdbcType=DECIMAL},
      </if>
      <if test="record.totalTxrate != null">
        total_txrate = #{record.totalTxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.totalRxrate != null">
        total_rxrate = #{record.totalRxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.totalFlow != null">
        total_flow = #{record.totalFlow,jdbcType=DECIMAL},
      </if>
      <if test="record.sampleTime != null">
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gdate != null">
        gdate = #{record.gdate,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_gateway_network_flow_local
    set province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      industry = #{record.industry,jdbcType=VARCHAR},
      value_category = #{record.valueCategory,jdbcType=VARCHAR},
      bussiness_type = #{record.bussinessType,jdbcType=VARCHAR},
      bandwidth_all = #{record.bandwidthAll,jdbcType=DECIMAL},
      total_txrate = #{record.totalTxrate,jdbcType=DECIMAL},
      total_rxrate = #{record.totalRxrate,jdbcType=DECIMAL},
      total_flow = #{record.totalFlow,jdbcType=DECIMAL},
      sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      gdate = #{record.gdate,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_network_flow_local
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="selectByCityExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample"
          resultType="com.cmiot.report.bean.NetworkUsageStaData">
    select city_code as name,
    sum(bandwidth_all) as totalBandwidth, sum(total_flow) as totalUsage
    from t_gateway_network_detail_flow_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by city_code
  </select>

  <select id="selectByProvExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample"
          resultType="com.cmiot.report.bean.NetworkUsageStaData">
    select province_code as name,
    sum(bandwidth_all) as totalBandwidth, sum(total_flow) as totalUsage
    from t_gateway_network_detail_flow_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by province_code
  </select>

  <select id="selectByIndustryExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample"
          resultType="com.cmiot.report.bean.NetworkUsageStaData">
    select t4.name,  sum(t4.totalBandwidth) as totalBandwidth, sum(t4.totalUsage) as totalUsage from (
    select if(isNull(t3.industry_name) or t3.industry_name == '', '其他', t3.industry_name) as name, t2.totalBandwidth,
     t2.totalUsage from
    (select industry ,
    sum(bandwidth_all) as totalBandwidth, sum(total_flow) as totalUsage
    from t_gateway_network_detail_flow_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by industry) t2
    left join t_dict_industry_info t3 on toString(toUInt32(t3.code)) = t2.industry
    ) t4 group by t4.name
  </select>
  <select id="selectByTimeExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample"
          resultType="com.cmiot.report.bean.NetworkUsageStaData">
    select gdate as name,
    sum(bandwidth_all) as totalBandwidth, sum(total_flow) as totalUsage
    from t_gateway_network_detail_flow_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by gdate
    order by gdate asc
  </select>

  <select id="selectDeviceCountByExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample"
          resultType="com.cmiot.report.bean.NetworkUsageDeviceCountData">
    select count(*) cnum, t1.deviceUsage from (
    select round((sum(total_flow) * 100 / sum(bandwidth_all)), 0)
    as deviceUsage , gateway_mac
    from t_gateway_network_detail_flow_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by gateway_mac
    ) t1
    group by t1.deviceUsage
  </select>

  <select id="selectDeviceHourCountByExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample"
          resultType="com.cmiot.report.bean.NetworkUsageDeviceCountData">
    select count(*) cnum, t1.deviceUsage from (
    select round((sum(total_flow) * 100 / sum(bandwidth_all)), 0)
    as deviceUsage , gateway_mac
    from t_gateway_network_detail_flow_hour_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by gateway_mac
    ) t1
    group by t1.deviceUsage
  </select>

  <select id="selectByTimeHourExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample"
          resultType="com.cmiot.report.bean.NetworkUsageStaData">
    select time_hour as name,
    sum(bandwidth_all) as totalBandwidth, sum(total_flow) as totalUsage
    from t_gateway_network_detail_flow_hour_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by time_hour
    order by time_hour asc
  </select>

  <select id="selectByTimeMonthExample" parameterType="com.cmiot.report.bean.GatewayNetworkFlowExample"
          resultType="com.cmiot.report.bean.NetworkUsageStaData">
    select FLOOR(gdate/100) as name,
    sum(bandwidth_all) as totalBandwidth, sum(total_flow) as totalUsage
    from t_gateway_network_detail_flow_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by FLOOR(gdate/100)
    order by name asc
  </select>
</mapper>