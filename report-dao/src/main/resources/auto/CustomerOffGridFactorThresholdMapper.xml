<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.CustomerOffGridFactorThresholdMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.CustomerOffGridFactorThreshold">
        <result column="timestamp" jdbcType="BIGINT" property="timestamp"/>
        <result column="factor_type" jdbcType="TINYINT" property="factorType"/>
        <result column="factor_name" jdbcType="VARCHAR" property="factorName"/>
        <result column="factor_weight" jdbcType="FLOAT" property="factorWeight"/>
        <result column="factor_threshold" jdbcType="INTEGER" property="factorThreshold"/>
        <result column="factor_indicate_type" jdbcType="TINYINT" property="factorIndicateType"/>
        <result column="factor_indicate_desc" jdbcType="VARCHAR" property="factorIndicateDesc"/>
        <result column="factor_indicate_weight" jdbcType="FLOAT" property="factorIndicateWeight"/>
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime"/>
        <result column="gdate" jdbcType="BIGINT" property="gdate"/>
    </resultMap>

    <resultMap id="FactorResultMap" type="com.cmiot.report.dto.OffGridFactorThresholdResult">
        <result column="factor_name" jdbcType="VARCHAR" property="factorName"/>
        <result column="factor_type" jdbcType="INTEGER" property="factorKey"/>
        <result column="factor_weight" jdbcType="FLOAT" property="weight"/>
        <result column="factor_threshold" jdbcType="INTEGER" property="threshold"/>
    </resultMap>


    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        timestamp, factor_type, factor_name, factor_weight, factor_threshold, factor_indicate_type,
        factor_indicate_desc, factor_indicate_weight, sample_time, gdate
    </sql>


    <!-- 1.获取离网因素权重和阈值 -->
    <select id="selectFactorByExample" parameterType="com.cmiot.report.bean.CustomerOffGridFactorThresholdExample"
            resultMap="FactorResultMap">
        select t2.factor_name, t2.factor_type, t2.factor_weight,t2.factor_threshold from (
        select t1.*,row_number() over (partition by t1.factor_name,t1.factor_type order by t1.sample_time desc) rank from (
        select factor_name, factor_type, factor_weight, factor_threshold, sample_time from t_customer_off_grid_factor_threshold_all) t1) t2
        where t2.rank=1 order by t2.factor_type
    </select>


    <!-- 2.获取离网因素详情 -->
    <select id="selectFactorDetailByExample" parameterType="com.cmiot.report.bean.CustomerOffGridFactorThresholdExample"
            resultMap="BaseResultMap">
        select t2.timestamp, t2.factor_type, t2.factor_name, t2.factor_weight, t2.factor_threshold, t2.factor_indicate_type,
        t2.factor_indicate_desc, t2.factor_indicate_weight, t2.sample_time, t2.gdate from (
            select t1.*,row_number() over (partition by t1.factor_type,t1.factor_indicate_type order by t1.sample_time desc) rank from (
            select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_customer_off_grid_factor_threshold_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>) t1) t2 where t2.rank=1
    </select>
    <!--<select id="selectFactorDetailByExample" parameterType="com.cmiot.report.bean.CustomerOffGridFactorThresholdExample"
            resultMap="BaseResultMap">
        select factor_name,factor_type,factor_weight,factor_threshold from (select
        <if test="distinct">
            distinct
        </if>
        argMax(factor_name, sample_time) as factor_name,argMax(factor_type, sample_time) as factor_type,argMax(factor_weight, sample_time) as factor_weight,argMax(factor_threshold, sample_time) as factor_threshold from t_customer_off_grid_factor_threshold_all) t1
        where notEquals(factor_name,'')
    </select>-->


    <!-- 4.设置离网因素权重阈值 -->
    <insert id="insertFactorByBatch" parameterType="java.util.List">
        insert into t_customer_off_grid_factor_threshold_all (timestamp, factor_type, factor_name, factor_weight, factor_threshold, factor_indicate_type, factor_indicate_desc, factor_indicate_weight, sample_time, gdate) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.timestamp},#{item.factorType},#{item.factorName},#{item.factorWeight},#{item.factorThreshold},#{item.factorIndicateType},#{item.factorIndicateDesc},#{item.factorIndicateWeight},#{item.sampleTime},#{item.gdate})
        </foreach>
    </insert>



    <select id="selectByExample" parameterType="com.cmiot.report.bean.CustomerOffGridFactorThresholdExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_customer_off_grid_factor_threshold_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.CustomerOffGridFactorThresholdExample">
        delete from t_customer_off_grid_factor_threshold_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.CustomerOffGridFactorThreshold">
        insert into t_customer_off_grid_factor_threshold_all (timestamp, factor_type, factor_name,
        factor_weight, factor_threshold, factor_indicate_type,
        factor_indicate_desc, factor_indicate_weight,
        sample_time, gdate)
        values (#{timestamp,jdbcType=BIGINT}, #{factorType,jdbcType=TINYINT}, #{factorName,jdbcType=VARCHAR},
        #{factorWeight,jdbcType=FLOAT}, #{factorThreshold,jdbcType=INTEGER}, #{factorIndicateType,jdbcType=TINYINT},
        #{factorIndicateDesc,jdbcType=VARCHAR}, #{factorIndicateWeight,jdbcType=FLOAT},
        #{sampleTime,jdbcType=TIMESTAMP}, #{gdate,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.CustomerOffGridFactorThreshold">
        insert into t_customer_off_grid_factor_threshold_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">
                timestamp,
            </if>
            <if test="factorType != null">
                factor_type,
            </if>
            <if test="factorName != null">
                factor_name,
            </if>
            <if test="factorWeight != null">
                factor_weight,
            </if>
            <if test="factorThreshold != null">
                factor_threshold,
            </if>
            <if test="factorIndicateType != null">
                factor_indicate_type,
            </if>
            <if test="factorIndicateDesc != null">
                factor_indicate_desc,
            </if>
            <if test="factorIndicateWeight != null">
                factor_indicate_weight,
            </if>
            <if test="sampleTime != null">
                sample_time,
            </if>
            <if test="gdate != null">
                gdate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">
                #{timestamp,jdbcType=BIGINT},
            </if>
            <if test="factorType != null">
                #{factorType,jdbcType=TINYINT},
            </if>
            <if test="factorName != null">
                #{factorName,jdbcType=VARCHAR},
            </if>
            <if test="factorWeight != null">
                #{factorWeight,jdbcType=FLOAT},
            </if>
            <if test="factorThreshold != null">
                #{factorThreshold,jdbcType=INTEGER},
            </if>
            <if test="factorIndicateType != null">
                #{factorIndicateType,jdbcType=TINYINT},
            </if>
            <if test="factorIndicateDesc != null">
                #{factorIndicateDesc,jdbcType=VARCHAR},
            </if>
            <if test="factorIndicateWeight != null">
                #{factorIndicateWeight,jdbcType=FLOAT},
            </if>
            <if test="sampleTime != null">
                #{sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="gdate != null">
                #{gdate,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.CustomerOffGridFactorThresholdExample"
            resultType="java.lang.Long">
        select count(*) from t_customer_off_grid_factor_threshold_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_customer_off_grid_factor_threshold_all
        <set>
            <if test="record.timestamp != null">
                timestamp = #{record.timestamp,jdbcType=BIGINT},
            </if>
            <if test="record.factorType != null">
                factor_type = #{record.factorType,jdbcType=TINYINT},
            </if>
            <if test="record.factorName != null">
                factor_name = #{record.factorName,jdbcType=VARCHAR},
            </if>
            <if test="record.factorWeight != null">
                factor_weight = #{record.factorWeight,jdbcType=FLOAT},
            </if>
            <if test="record.factorThreshold != null">
                factor_threshold = #{record.factorThreshold,jdbcType=INTEGER},
            </if>
            <if test="record.factorIndicateType != null">
                factor_indicate_type = #{record.factorIndicateType,jdbcType=TINYINT},
            </if>
            <if test="record.factorIndicateDesc != null">
                factor_indicate_desc = #{record.factorIndicateDesc,jdbcType=VARCHAR},
            </if>
            <if test="record.factorIndicateWeight != null">
                factor_indicate_weight = #{record.factorIndicateWeight,jdbcType=FLOAT},
            </if>
            <if test="record.sampleTime != null">
                sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gdate != null">
                gdate = #{record.gdate,jdbcType=BIGINT},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_customer_off_grid_factor_threshold_all
        set timestamp = #{record.timestamp,jdbcType=BIGINT},
        factor_type = #{record.factorType,jdbcType=TINYINT},
        factor_name = #{record.factorName,jdbcType=VARCHAR},
        factor_weight = #{record.factorWeight,jdbcType=FLOAT},
        factor_threshold = #{record.factorThreshold,jdbcType=INTEGER},
        factor_indicate_type = #{record.factorIndicateType,jdbcType=TINYINT},
        factor_indicate_desc = #{record.factorIndicateDesc,jdbcType=VARCHAR},
        factor_indicate_weight = #{record.factorIndicateWeight,jdbcType=FLOAT},
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
        gdate = #{record.gdate,jdbcType=BIGINT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds"
            parameterType="com.cmiot.report.bean.CustomerOffGridFactorThresholdExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_customer_off_grid_factor_threshold_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>