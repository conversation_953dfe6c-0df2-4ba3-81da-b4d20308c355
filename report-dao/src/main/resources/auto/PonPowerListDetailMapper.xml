<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.PonPowerListDetailMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.PonPowerListDetail">
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="adsl_account" jdbcType="VARCHAR" property="adslAccount"/>
        <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn"/>
        <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac"/>
        <result column="factory_id" jdbcType="BIGINT" property="factoryId"/>
        <result column="factory_name" jdbcType="VARCHAR" property="factoryName"/>
        <result column="device_model_id" jdbcType="BIGINT" property="deviceModelId"/>
        <result column="device_model" jdbcType="VARCHAR" property="deviceModel"/>
        <result column="runing_time_total" jdbcType="BIGINT" property="runingTimeTotal"/>
        <result column="pon_rx_power_avg" jdbcType="INTEGER" property="ponRxPowerAvg"/>
        <result column="pon_rx_power_min_value" jdbcType="INTEGER" property="ponRxPowerMinValue"/>
        <result column="pon_rx_power_min_value_time" jdbcType="TIMESTAMP" property="ponRxPowerMinValueTime"/>
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime"/>
        <result column="gdate" jdbcType="BIGINT" property="gdate"/>
    </resultMap>

    <resultMap id="ListResultMap" type="com.cmiot.report.bean.PonPowerListDetail">
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="adsl_account" jdbcType="VARCHAR" property="adslAccount"/>
        <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn"/>
        <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac"/>
        <result column="factory_name" jdbcType="VARCHAR" property="factoryName"/>
        <result column="device_model" jdbcType="VARCHAR" property="deviceModel"/>
        <result column="runing_time_total" jdbcType="BIGINT" property="runingTimeTotal"/>
        <result column="pon_rx_power_avg" jdbcType="INTEGER" property="ponRxPowerAvg"/>
        <result column="pon_rx_power_min_value" jdbcType="INTEGER" property="ponRxPowerMinValue"/>
        <result column="pon_rx_power_min_value_time" jdbcType="TIMESTAMP" property="ponRxPowerMinValueTime"/>
    </resultMap>

    <resultMap id="ListDTOResultMap" type="com.cmiot.report.dto.PonListDTO">
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="groupName" jdbcType="VARCHAR" property="groupName"/>
        <result column="sn" jdbcType="VARCHAR" property="sn"/>
        <result column="mac" jdbcType="VARCHAR" property="mac"/>
        <result column="vendor" jdbcType="VARCHAR" property="vendor"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="runningTime" jdbcType="BIGINT" property="runningTime"/>
        <result column="receiveLightPowerAverage" jdbcType="INTEGER" property="receiveLightPowerAverage"/>
        <result column="receiveLightPowerMin" jdbcType="INTEGER" property="receiveLightPowerMin"/>
        <result column="receiveTime" jdbcType="TIMESTAMP" property="receiveTime"/>
    </resultMap>


    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Base_Column_List">
        province_code, province_name, city_code, city_name, customer_name, adsl_account,
        gateway_sn, gateway_mac, factory_id, factory_name, device_model_id, device_model,
        runing_time_total, pon_rx_power_avg, pon_rx_power_min_value, pon_rx_power_min_value_time,
        sample_time, gdate
    </sql>

    <sql id="Query_Base_Column_List">
        province_name as province, city_name as city, customer_name as groupName, gateway_sn as sn, gateway_mac as mac,
        factory_name as vendor, device_model as model,
        runing_time_total as runningTime,
        if(equals(pon_rx_power_avg,********) or equals(if(pon_rx_power_avg>1000000,round(10*log(pon_rx_power_avg/1000000)/log(10)),round(10*log(pon_rx_power_avg/10000)/log(10))),-inf) or isNaN(if(pon_rx_power_avg>1000000,round(10*log(pon_rx_power_avg/1000000)/log(10)),round(10*log(pon_rx_power_avg/10000)/log(10)))),'',cast(if(pon_rx_power_avg>1000000,round(10*log(pon_rx_power_avg/1000000)/log(10)),round(10*log(pon_rx_power_avg/10000)/log(10))) as String)) as receiveLightPowerAverage,
        if(equals(pon_rx_power_min_value,********) or equals(if(pon_rx_power_min_value>1000000,round(10*log(pon_rx_power_min_value/1000000)/log(10)),round(10*log(pon_rx_power_min_value/10000)/log(10))),-inf) or isNaN(if(pon_rx_power_min_value>1000000,round(10*log(pon_rx_power_min_value/1000000)/log(10)),round(10*log(pon_rx_power_min_value/10000)/log(10)))),'',cast(if(pon_rx_power_min_value>1000000,round(10*log(pon_rx_power_min_value/1000000)/log(10)),round(10*log(pon_rx_power_min_value/10000)/log(10))) as String)) as receiveLightPowerMin,
        pon_rx_power_min_value_time as receiveTime
    </sql>

    <select id="selectByExample" parameterType="com.cmiot.report.bean.PonPowerListDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_pon_power_list_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <!--pon口光功率列表查询-->
    <select id="selectDetailByExample" parameterType="com.cmiot.report.bean.PonPowerListDetailExample"
            resultMap="ListDTOResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Query_Base_Column_List"/>
        from t_gateway_pon_power_list_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>


    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.PonPowerListDetailExample">
        delete from t_gateway_pon_power_list_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.PonPowerListDetail">
        insert into t_gateway_pon_power_list_all (province_code, province_name, city_code,
        city_name, customer_name, adsl_account,
        gateway_sn, gateway_mac, factory_id,
        factory_name, device_model_id, device_model,
        runing_time_total, pon_rx_power_avg, pon_rx_power_min_value,
        pon_rx_power_min_value_time, sample_time,
        gdate)
        values (#{provinceCode,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR},
        #{cityName,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{adslAccount,jdbcType=VARCHAR},
        #{gatewaySn,jdbcType=VARCHAR}, #{gatewayMac,jdbcType=VARCHAR}, #{factoryId,jdbcType=BIGINT},
        #{factoryName,jdbcType=VARCHAR}, #{deviceModelId,jdbcType=BIGINT}, #{deviceModel,jdbcType=VARCHAR},
        #{runingTimeTotal,jdbcType=BIGINT}, #{ponRxPowerAvg,jdbcType=INTEGER}, #{ponRxPowerMinValue,jdbcType=INTEGER},
        #{ponRxPowerMinValueTime,jdbcType=TIMESTAMP}, #{sampleTime,jdbcType=TIMESTAMP},
        #{gdate,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.PonPowerListDetail">
        insert into t_gateway_pon_power_list_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="provinceName != null">
                province_name,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="cityName != null">
                city_name,
            </if>
            <if test="customerName != null">
                customer_name,
            </if>
            <if test="adslAccount != null">
                adsl_account,
            </if>
            <if test="gatewaySn != null">
                gateway_sn,
            </if>
            <if test="gatewayMac != null">
                gateway_mac,
            </if>
            <if test="factoryId != null">
                factory_id,
            </if>
            <if test="factoryName != null">
                factory_name,
            </if>
            <if test="deviceModelId != null">
                device_model_id,
            </if>
            <if test="deviceModel != null">
                device_model,
            </if>
            <if test="runingTimeTotal != null">
                runing_time_total,
            </if>
            <if test="ponRxPowerAvg != null">
                pon_rx_power_avg,
            </if>
            <if test="ponRxPowerMinValue != null">
                pon_rx_power_min_value,
            </if>
            <if test="ponRxPowerMinValueTime != null">
                pon_rx_power_min_value_time,
            </if>
            <if test="sampleTime != null">
                sample_time,
            </if>
            <if test="gdate != null">
                gdate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="adslAccount != null">
                #{adslAccount,jdbcType=VARCHAR},
            </if>
            <if test="gatewaySn != null">
                #{gatewaySn,jdbcType=VARCHAR},
            </if>
            <if test="gatewayMac != null">
                #{gatewayMac,jdbcType=VARCHAR},
            </if>
            <if test="factoryId != null">
                #{factoryId,jdbcType=BIGINT},
            </if>
            <if test="factoryName != null">
                #{factoryName,jdbcType=VARCHAR},
            </if>
            <if test="deviceModelId != null">
                #{deviceModelId,jdbcType=BIGINT},
            </if>
            <if test="deviceModel != null">
                #{deviceModel,jdbcType=VARCHAR},
            </if>
            <if test="runingTimeTotal != null">
                #{runingTimeTotal,jdbcType=BIGINT},
            </if>
            <if test="ponRxPowerAvg != null">
                #{ponRxPowerAvg,jdbcType=INTEGER},
            </if>
            <if test="ponRxPowerMinValue != null">
                #{ponRxPowerMinValue,jdbcType=INTEGER},
            </if>
            <if test="ponRxPowerMinValueTime != null">
                #{ponRxPowerMinValueTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sampleTime != null">
                #{sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="gdate != null">
                #{gdate,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.PonPowerListDetailExample"
            resultType="java.lang.Long">
        select count(*) from t_gateway_pon_power_list_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_gateway_pon_power_list_all
        <set>
            <if test="record.provinceCode != null">
                province_code = #{record.provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="record.provinceName != null">
                province_name = #{record.provinceName,jdbcType=VARCHAR},
            </if>
            <if test="record.cityCode != null">
                city_code = #{record.cityCode,jdbcType=VARCHAR},
            </if>
            <if test="record.cityName != null">
                city_name = #{record.cityName,jdbcType=VARCHAR},
            </if>
            <if test="record.customerName != null">
                customer_name = #{record.customerName,jdbcType=VARCHAR},
            </if>
            <if test="record.adslAccount != null">
                adsl_account = #{record.adslAccount,jdbcType=VARCHAR},
            </if>
            <if test="record.gatewaySn != null">
                gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
            </if>
            <if test="record.gatewayMac != null">
                gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
            </if>
            <if test="record.factoryId != null">
                factory_id = #{record.factoryId,jdbcType=BIGINT},
            </if>
            <if test="record.factoryName != null">
                factory_name = #{record.factoryName,jdbcType=VARCHAR},
            </if>
            <if test="record.deviceModelId != null">
                device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
            </if>
            <if test="record.deviceModel != null">
                device_model = #{record.deviceModel,jdbcType=VARCHAR},
            </if>
            <if test="record.runingTimeTotal != null">
                runing_time_total = #{record.runingTimeTotal,jdbcType=BIGINT},
            </if>
            <if test="record.ponRxPowerAvg != null">
                pon_rx_power_avg = #{record.ponRxPowerAvg,jdbcType=INTEGER},
            </if>
            <if test="record.ponRxPowerMinValue != null">
                pon_rx_power_min_value = #{record.ponRxPowerMinValue,jdbcType=INTEGER},
            </if>
            <if test="record.ponRxPowerMinValueTime != null">
                pon_rx_power_min_value_time = #{record.ponRxPowerMinValueTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.sampleTime != null">
                sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gdate != null">
                gdate = #{record.gdate,jdbcType=BIGINT},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_gateway_pon_power_list_all
        set province_code = #{record.provinceCode,jdbcType=VARCHAR},
        province_name = #{record.provinceName,jdbcType=VARCHAR},
        city_code = #{record.cityCode,jdbcType=VARCHAR},
        city_name = #{record.cityName,jdbcType=VARCHAR},
        customer_name = #{record.customerName,jdbcType=VARCHAR},
        adsl_account = #{record.adslAccount,jdbcType=VARCHAR},
        gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
        gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
        factory_id = #{record.factoryId,jdbcType=BIGINT},
        factory_name = #{record.factoryName,jdbcType=VARCHAR},
        device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
        device_model = #{record.deviceModel,jdbcType=VARCHAR},
        runing_time_total = #{record.runingTimeTotal,jdbcType=BIGINT},
        pon_rx_power_avg = #{record.ponRxPowerAvg,jdbcType=INTEGER},
        pon_rx_power_min_value = #{record.ponRxPowerMinValue,jdbcType=INTEGER},
        pon_rx_power_min_value_time = #{record.ponRxPowerMinValueTime,jdbcType=TIMESTAMP},
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
        gdate = #{record.gdate,jdbcType=BIGINT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.PonPowerListDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_pon_power_list_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>