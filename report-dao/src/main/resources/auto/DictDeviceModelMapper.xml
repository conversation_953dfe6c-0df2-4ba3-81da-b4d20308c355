<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DictDeviceModelMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.DictDeviceModel">
    <result column="device_model_id" jdbcType="BIGINT" property="deviceModelId" />
    <result column="device_model" jdbcType="VARCHAR" property="deviceModel" />
    <result column="factory_id" jdbcType="BIGINT" property="factoryId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List">
    device_model_id, device_model, factory_id, create_time, update_time
  </sql>


  <sql id="FactModel_Base_Column_List">
    device_model_id, concat(dictGetOrDefault(ge_report.t_dic_factory, 'factory_name', factory_id, '其他'),'_',device_model) as device_model, factory_id, create_time, update_time
  </sql>


  <select id="selectByExample" parameterType="com.cmiot.report.bean.DictDeviceModelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_dic_device_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="selectFactModelByExample" parameterType="com.cmiot.report.bean.DictDeviceModelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="FactModel_Base_Column_List" />
    from t_dic_device_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>


  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.DictDeviceModelExample">
    delete from t_dic_device_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.DictDeviceModel">
    insert into t_dic_device_model (device_model_id, device_model, factory_id, 
      create_time, update_time)
    values (#{deviceModelId,jdbcType=BIGINT}, #{deviceModel,jdbcType=VARCHAR}, #{factoryId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.DictDeviceModel">
    insert into t_dic_device_model
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deviceModelId != null">
        device_model_id,
      </if>
      <if test="deviceModel != null">
        device_model,
      </if>
      <if test="factoryId != null">
        factory_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deviceModelId != null">
        #{deviceModelId,jdbcType=BIGINT},
      </if>
      <if test="deviceModel != null">
        #{deviceModel,jdbcType=VARCHAR},
      </if>
      <if test="factoryId != null">
        #{factoryId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.DictDeviceModelExample" resultType="java.lang.Long">
    select count(*) from t_dic_device_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_dic_device_model
    <set>
      <if test="record.deviceModelId != null">
        device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
      </if>
      <if test="record.deviceModel != null">
        device_model = #{record.deviceModel,jdbcType=VARCHAR},
      </if>
      <if test="record.factoryId != null">
        factory_id = #{record.factoryId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_dic_device_model
    set device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
      device_model = #{record.deviceModel,jdbcType=VARCHAR},
      factory_id = #{record.factoryId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.DictDeviceModelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_dic_device_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>