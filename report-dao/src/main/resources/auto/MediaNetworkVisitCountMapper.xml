<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.MediaNetworkVisitCountMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.MediaNetworkVisitCount">
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="media_type" jdbcType="VARCHAR" property="mediaType" />
    <result column="visit_type" jdbcType="SMALLINT" property="visitType" />
    <result column="visit_duration" jdbcType="BIGINT" property="visitDuration" />
    <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    province_code, city_code, media_type, visit_type, visit_duration, sample_time
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.MediaNetworkVisitCountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_media_network_visit_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.MediaNetworkVisitCountExample">
    delete from t_media_network_visit_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.MediaNetworkVisitCount">
    insert into t_media_network_visit_count_all (province_code, city_code, media_type, 
      visit_type, visit_duration, sample_time
      )
    values (#{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{mediaType,jdbcType=VARCHAR}, 
      #{visitType,jdbcType=SMALLINT}, #{visitDuration,jdbcType=BIGINT}, #{sampleTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.MediaNetworkVisitCount">
    insert into t_media_network_visit_count_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="mediaType != null">
        media_type,
      </if>
      <if test="visitType != null">
        visit_type,
      </if>
      <if test="visitDuration != null">
        visit_duration,
      </if>
      <if test="sampleTime != null">
        sample_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="mediaType != null">
        #{mediaType,jdbcType=VARCHAR},
      </if>
      <if test="visitType != null">
        #{visitType,jdbcType=SMALLINT},
      </if>
      <if test="visitDuration != null">
        #{visitDuration,jdbcType=BIGINT},
      </if>
      <if test="sampleTime != null">
        #{sampleTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.MediaNetworkVisitCountExample" resultType="java.lang.Long">
    select count(*) from t_media_network_visit_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_media_network_visit_count_all
    <set>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mediaType != null">
        media_type = #{record.mediaType,jdbcType=VARCHAR},
      </if>
      <if test="record.visitType != null">
        visit_type = #{record.visitType,jdbcType=SMALLINT},
      </if>
      <if test="record.visitDuration != null">
        visit_duration = #{record.visitDuration,jdbcType=BIGINT},
      </if>
      <if test="record.sampleTime != null">
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_media_network_visit_count_all
    set province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      media_type = #{record.mediaType,jdbcType=VARCHAR},
      visit_type = #{record.visitType,jdbcType=SMALLINT},
      visit_duration = #{record.visitDuration,jdbcType=BIGINT},
      sample_time = #{record.sampleTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.MediaNetworkVisitCountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_media_network_visit_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="selectCount" resultType="com.cmiot.report.bean.NetworkPerferCount">
    select sum(visit_duration) as visitDuration, media_type as name
    from t_media_network_visit_count_all
    where 1 = 1
    <if test="startTime != null">
      and toYYYYMMDD(sample_time) &gt;= #{startTime}
    </if>
    <if test="endTime != null">
      and toYYYYMMDD(sample_time) &lt;= #{endTime}
    </if>
    <if test="provinceCodes != null and provinceCodes.size() > 0">
      and province_code in
      <foreach collection="provinceCodes" index="index" item="p" open="(" close=")" separator=",">
        #{p}
      </foreach>
    </if>
    <if test="cityCodes != null and cityCodes.size() > 0">
      and city_code in
      <foreach collection="cityCodes" index="index" item="c" open="(" close=")" separator=",">
        #{c}
      </foreach>
    </if>
    <if test="type != null">
      and `visit_type` = #{type}
    </if>
    group by media_type
    order by visitDuration desc
  </select>
</mapper>