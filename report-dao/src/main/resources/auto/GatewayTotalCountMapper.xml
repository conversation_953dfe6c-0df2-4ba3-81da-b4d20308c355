<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayTotalCountMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.GatewayTotalCount">
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="factory_id" jdbcType="BIGINT" property="factoryId"/>
        <result column="factory_model_id" jdbcType="BIGINT" property="factoryModelId"/>
        <result column="count" jdbcType="BIGINT" property="count"/>
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime"/>
        <result column="gdate" jdbcType="BIGINT" property="gdate"/>
    </resultMap>

    <resultMap id="QueryResultMap" type="com.cmiot.report.bean.GatewayTotalCountDTO">
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="count" jdbcType="BIGINT" property="count"/>
    </resultMap>


    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        province_code, city_code, factory_id, factory_model_id, count, sample_time, gdate
    </sql>


    <select id="selectByExample" parameterType="com.cmiot.report.bean.GatewayTotalCountExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_total_count_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <!--根据省份统计-->
    <select id="selectByProvExample" parameterType="com.cmiot.report.bean.GatewayTotalCountExample"
            resultMap="QueryResultMap">
        select province_code as name,sum(count) as count from t_gateway_total_count_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by province_code
    </select>


    <!--根据地市统计-->
    <select id="selectByCityExample" parameterType="com.cmiot.report.bean.GatewayTotalCountExample"
            resultMap="QueryResultMap">
        <!--select city_code as name,sum(count) as count from t_gateway_total_count_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by name-->
        select city_code as name,sum(count) as count from t_gateway_total_count_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by name
    </select>


    <!--根据厂商统计-->
    <select id="selectByFactoryExample" parameterType="com.cmiot.report.bean.GatewayTotalCountExample"
            resultMap="QueryResultMap">
        select dictGetOrDefault('ge_report.t_dic_factory', 'factory_name', factory_id, '其他') as name ,sum(count) as
        count from t_gateway_total_count_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by factory_id
    </select>


    <!--根据厂商型号统计-->
    <select id="selectByFactoryModelExample" parameterType="com.cmiot.report.bean.GatewayTotalCountExample"
            resultMap="QueryResultMap">
        select concat(dictGetOrDefault(ge_report.t_dic_factory, 'factory_name', t1.factory_id, '其他'),'_',dictGetOrDefault(ge_report.t_dic_device_model, 'device_model', t1.factory_model_id, '其他')) as name,t1.count from
        (select factory_id,factory_model_id,sum(count) as count from t_gateway_total_count_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by factory_id,factory_model_id) t1
    </select>


    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.GatewayTotalCountExample">
        delete from t_gateway_total_count_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.GatewayTotalCount">
        insert into t_gateway_total_count_all (province_code, city_code, factory_id,
        factory_model_id, count, sample_time,
        gdate)
        values (#{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{factoryId,jdbcType=BIGINT},
        #{factoryModelId,jdbcType=BIGINT}, #{count,jdbcType=BIGINT}, #{sampleTime,jdbcType=TIMESTAMP},
        #{gdate,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.GatewayTotalCount">
        insert into t_gateway_total_count_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="factoryId != null">
                factory_id,
            </if>
            <if test="factoryModelId != null">
                factory_model_id,
            </if>
            <if test="count != null">
                count,
            </if>
            <if test="sampleTime != null">
                sample_time,
            </if>
            <if test="gdate != null">
                gdate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="factoryId != null">
                #{factoryId,jdbcType=BIGINT},
            </if>
            <if test="factoryModelId != null">
                #{factoryModelId,jdbcType=BIGINT},
            </if>
            <if test="count != null">
                #{count,jdbcType=BIGINT},
            </if>
            <if test="sampleTime != null">
                #{sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="gdate != null">
                #{gdate,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.GatewayTotalCountExample"
            resultType="java.lang.Long">
        select count(*) from t_gateway_total_count_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_gateway_total_count_all
        <set>
            <if test="record.provinceCode != null">
                province_code = #{record.provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="record.cityCode != null">
                city_code = #{record.cityCode,jdbcType=VARCHAR},
            </if>
            <if test="record.factoryId != null">
                factory_id = #{record.factoryId,jdbcType=BIGINT},
            </if>
            <if test="record.factoryModelId != null">
                factory_model_id = #{record.factoryModelId,jdbcType=BIGINT},
            </if>
            <if test="record.count != null">
                count = #{record.count,jdbcType=BIGINT},
            </if>
            <if test="record.sampleTime != null">
                sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gdate != null">
                gdate = #{record.gdate,jdbcType=BIGINT},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_gateway_total_count_all
        set province_code = #{record.provinceCode,jdbcType=VARCHAR},
        city_code = #{record.cityCode,jdbcType=VARCHAR},
        factory_id = #{record.factoryId,jdbcType=BIGINT},
        factory_model_id = #{record.factoryModelId,jdbcType=BIGINT},
        count = #{record.count,jdbcType=BIGINT},
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
        gdate = #{record.gdate,jdbcType=BIGINT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.GatewayTotalCountExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_total_count_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>