<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.CustomerOrderAggAllMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.CustomerOrderAggAll">
        <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="industry" jdbcType="BIGINT" property="industry"/>
        <result column="customer_status" jdbcType="INTEGER" property="customerStatus"/>
        <result column="business_product_id" jdbcType="VARCHAR" property="businessProductId"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="business_subscribe_time" jdbcType="TIMESTAMP" property="businessSubscribeTime"/>
        <result column="business_unsubscribe_time" jdbcType="TIMESTAMP" property="businessUnsubscribeTime"/>
        <result column="business_status" jdbcType="TINYINT" property="businessStatus"/>
        <result column="package_effect_time" jdbcType="TIMESTAMP" property="packageEffectTime"/>
        <result column="package_uneffect_time" jdbcType="TIMESTAMP" property="packageUneffectTime"/>
        <result column="package_status" jdbcType="TINYINT" property="packageStatus"/>
        <result column="order_create_time" jdbcType="TIMESTAMP" property="orderCreateTime"/>
        <result column="order_update_time" jdbcType="TIMESTAMP" property="orderUpdateTime"/>
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        enterprise_id, customer_id, customer_name, province_code, city_code, create_time,
        industry, customer_status, business_product_id, business_type, business_subscribe_time,
        business_unsubscribe_time, business_status, package_effect_time, package_uneffect_time,
        package_status, order_create_time, order_update_time, sample_time
    </sql>
    <select id="selectByExample" parameterType="com.cmiot.report.bean.CustomerOrderAggAllExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_customer_order_agg_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.CustomerOrderAggAllExample">
        delete from t_customer_order_agg_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.CustomerOrderAggAll">
        insert into t_customer_order_agg_all (enterprise_id, customer_id, customer_name,
        province_code, city_code, create_time,
        industry, customer_status, business_product_id,
        business_type, business_subscribe_time,
        business_unsubscribe_time, business_status,
        package_effect_time, package_uneffect_time,
        package_status, order_create_time, order_update_time,
        sample_time)
        values (#{enterpriseId,jdbcType=BIGINT}, #{customerId,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR},
        #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{industry,jdbcType=BIGINT}, #{customerStatus,jdbcType=INTEGER}, #{businessProductId,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR}, #{businessSubscribeTime,jdbcType=TIMESTAMP},
        #{businessUnsubscribeTime,jdbcType=TIMESTAMP}, #{businessStatus,jdbcType=TINYINT},
        #{packageEffectTime,jdbcType=TIMESTAMP}, #{packageUneffectTime,jdbcType=TIMESTAMP},
        #{packageStatus,jdbcType=TINYINT}, #{orderCreateTime,jdbcType=TIMESTAMP}, #{orderUpdateTime,jdbcType=TIMESTAMP},
        #{sampleTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.CustomerOrderAggAll">
        insert into t_customer_order_agg_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="enterpriseId != null">
                enterprise_id,
            </if>
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="customerName != null">
                customer_name,
            </if>
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="industry != null">
                industry,
            </if>
            <if test="customerStatus != null">
                customer_status,
            </if>
            <if test="businessProductId != null">
                business_product_id,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="businessSubscribeTime != null">
                business_subscribe_time,
            </if>
            <if test="businessUnsubscribeTime != null">
                business_unsubscribe_time,
            </if>
            <if test="businessStatus != null">
                business_status,
            </if>
            <if test="packageEffectTime != null">
                package_effect_time,
            </if>
            <if test="packageUneffectTime != null">
                package_uneffect_time,
            </if>
            <if test="packageStatus != null">
                package_status,
            </if>
            <if test="orderCreateTime != null">
                order_create_time,
            </if>
            <if test="orderUpdateTime != null">
                order_update_time,
            </if>
            <if test="sampleTime != null">
                sample_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="enterpriseId != null">
                #{enterpriseId,jdbcType=BIGINT},
            </if>
            <if test="customerId != null">
                #{customerId,jdbcType=VARCHAR},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="industry != null">
                #{industry,jdbcType=BIGINT},
            </if>
            <if test="customerStatus != null">
                #{customerStatus,jdbcType=INTEGER},
            </if>
            <if test="businessProductId != null">
                #{businessProductId,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="businessSubscribeTime != null">
                #{businessSubscribeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessUnsubscribeTime != null">
                #{businessUnsubscribeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessStatus != null">
                #{businessStatus,jdbcType=TINYINT},
            </if>
            <if test="packageEffectTime != null">
                #{packageEffectTime,jdbcType=TIMESTAMP},
            </if>
            <if test="packageUneffectTime != null">
                #{packageUneffectTime,jdbcType=TIMESTAMP},
            </if>
            <if test="packageStatus != null">
                #{packageStatus,jdbcType=TINYINT},
            </if>
            <if test="orderCreateTime != null">
                #{orderCreateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderUpdateTime != null">
                #{orderUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sampleTime != null">
                #{sampleTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.CustomerOrderAggAllExample"
            resultType="java.lang.Long">
        select count(*) from t_customer_order_agg_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_customer_order_agg_all
        <set>
            <if test="record.enterpriseId != null">
                enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
            </if>
            <if test="record.customerId != null">
                customer_id = #{record.customerId,jdbcType=VARCHAR},
            </if>
            <if test="record.customerName != null">
                customer_name = #{record.customerName,jdbcType=VARCHAR},
            </if>
            <if test="record.provinceCode != null">
                province_code = #{record.provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="record.cityCode != null">
                city_code = #{record.cityCode,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.industry != null">
                industry = #{record.industry,jdbcType=BIGINT},
            </if>
            <if test="record.customerStatus != null">
                customer_status = #{record.customerStatus,jdbcType=INTEGER},
            </if>
            <if test="record.businessProductId != null">
                business_product_id = #{record.businessProductId,jdbcType=VARCHAR},
            </if>
            <if test="record.businessType != null">
                business_type = #{record.businessType,jdbcType=VARCHAR},
            </if>
            <if test="record.businessSubscribeTime != null">
                business_subscribe_time = #{record.businessSubscribeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.businessUnsubscribeTime != null">
                business_unsubscribe_time = #{record.businessUnsubscribeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.businessStatus != null">
                business_status = #{record.businessStatus,jdbcType=TINYINT},
            </if>
            <if test="record.packageEffectTime != null">
                package_effect_time = #{record.packageEffectTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.packageUneffectTime != null">
                package_uneffect_time = #{record.packageUneffectTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.packageStatus != null">
                package_status = #{record.packageStatus,jdbcType=TINYINT},
            </if>
            <if test="record.orderCreateTime != null">
                order_create_time = #{record.orderCreateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.orderUpdateTime != null">
                order_update_time = #{record.orderUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.sampleTime != null">
                sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_customer_order_agg_all
        set enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
        customer_id = #{record.customerId,jdbcType=VARCHAR},
        customer_name = #{record.customerName,jdbcType=VARCHAR},
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
        city_code = #{record.cityCode,jdbcType=VARCHAR},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        industry = #{record.industry,jdbcType=BIGINT},
        customer_status = #{record.customerStatus,jdbcType=INTEGER},
        business_product_id = #{record.businessProductId,jdbcType=VARCHAR},
        business_type = #{record.businessType,jdbcType=VARCHAR},
        business_subscribe_time = #{record.businessSubscribeTime,jdbcType=TIMESTAMP},
        business_unsubscribe_time = #{record.businessUnsubscribeTime,jdbcType=TIMESTAMP},
        business_status = #{record.businessStatus,jdbcType=TINYINT},
        package_effect_time = #{record.packageEffectTime,jdbcType=TIMESTAMP},
        package_uneffect_time = #{record.packageUneffectTime,jdbcType=TIMESTAMP},
        package_status = #{record.packageStatus,jdbcType=TINYINT},
        order_create_time = #{record.orderCreateTime,jdbcType=TIMESTAMP},
        order_update_time = #{record.orderUpdateTime,jdbcType=TIMESTAMP},
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.CustomerOrderAggAllExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_customer_order_agg_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>


    <!--1.企业客户区域分布-->
    <!-- 分省地市存量统计 -->
    <select id="countCustByQuery" resultType="com.cmiot.report.dto.CustNameCount">
        select province_code as province,city_code as city,uniqExact(enterprise_id) as count from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        group by province_code,city_code having count>0
    </select>

    <!-- 办理过业务客户统计 -->
    <select id="countBussCustByExample" resultType="com.cmiot.report.dto.CustNameCount">
        select province_code as province,city_code as city,uniqExact(enterprise_id) as count from t_customer_order_agg_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1))) group by province_code,city_code
    </select>

    <!-- 业务生效中客户数 -->
    <select id="countActiveCustByQuery" resultType="com.cmiot.report.dto.CustNameCount">
        select province_code as province,city_code as city,uniqExact(enterprise_id) as count from t_customer_order_agg_all
        where (equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1))) and create_time&lt;=#{query.endDate}
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>)
        and ( (package_effect_time>#{query.initDate} and package_effect_time&lt;=#{query.startDate} and package_uneffect_time>=#{query.endDate})
        or (package_uneffect_time=#{query.initDate} and package_effect_time>#{query.initDate}) )
        group by province_code,city_code
    </select>


    <!--2.新增企业客户统计-->
    <!-- 新增企业客户统计 -->
    <select id="countNewCustByExample" resultType="java.lang.Long">
        select uniqExact(customer_id) as count from t_customer_order_agg_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
    </select>

    <!-- 新增企业客户列表 -->
    <select id="countNewCustList" resultType="com.cmiot.report.dto.NewCustNameCount">
        select toDate(create_time) as date,uniqExact(customer_id) as count from t_customer_order_agg_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1))) group by date
    </select>

    <!-- 新增且办理了业务的企业客户统计 -->
    <select id="countNewCustBussByExample" resultType="java.lang.Long">
        select uniqExact(customer_id) as count from t_customer_order_agg_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
    </select>

    <!-- 新增且办理了业务的企业客户列表 -->
    <select id="countNewCustBussList" resultType="com.cmiot.report.dto.NewCustNameCount">
        select toDate(business_subscribe_time) as date,uniqExact(customer_id) as count from t_customer_order_agg_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1))) group by date
    </select>


    <!--3.存量企业客户统计-->
    <select id="countBase" resultType="com.cmiot.report.dto.customerOrder.CustomerOrderCountData">
        select uniqExact(enterprise_id) as allCount,
               countif(distinct enterprise_id,business_status = 1) as allExistBussCount
        from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
    </select>

    <!-- 存量客户数(个) -->
    <select id="countAllCustByQuery" resultType="java.lang.Long">
        select uniqExact(enterprise_id) as count from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
    </select>

    <!-- 未办理业务的客户(包含从未办理，已退订，已到期) -->
    <select id="countCustNoBussByQuery" resultType="java.lang.Long">
        select uniqExact(enterprise_id) as count from (select enterprise_id from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and enterprise_id global not in (select enterprise_id from t_customer_order_agg_all where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        and (business_status=1 or package_status=1) group by enterprise_id))
    </select>

    <!-- 已办理客户总数(包含已办理未生效、已生效) -->
    <select id="countCustExistBussByQuery" resultType="java.lang.Long">
        select uniqExact(enterprise_id) as count from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and equals(business_status,1)
    </select>

    <!-- 周期内新办理业务的存量客户统计 -->
    <select id="countCustNewBussByQuery" resultType="java.lang.Long">
        select uniqExact(enterprise_id) as count from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and business_subscribe_time between #{query.startDate} and #{query.endDate}
    </select>

    <!-- 周期内新办理业务的存量客户列表 -->
    <select id="countCustNewBussListByQuery" resultType="com.cmiot.report.dto.NewCustNameCount">
        select t1.date,uniqExact(t1.enterprise_id) as count from (
        select toDate(business_subscribe_time) as date,enterprise_id from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and (business_subscribe_time between #{query.startDate} and #{query.endDate})) t1
        where t1.enterprise_id global not in (select enterprise_id from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and business_subscribe_time &lt; #{query.startDate} and notEquals(business_unsubscribe_time,#{query.initDate}) group by enterprise_id)
        group by date
    </select>

    <!-- 周期内首次办理业务的存量客户数：包含已办理未生效，已生效。（不包含已退订、已到期但重新办理了） -->
    <select id="queryCustFirstNewBuss" resultType="java.lang.Long">
        select uniqExact(t1.enterprise_id) as count from (
            select enterprise_id,uniqExact(business_product_id) as bus_count from t_customer_order_agg_all
            where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
            <if test="query.province !=null">
                and province_code in
                <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.city !=null">
                and city_code in
                <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.businessType!=null and query.businessType!=''">
                and business_type = #{query.businessType}
            </if>
            and business_subscribe_time between #{query.startDate} and #{query.endDate}
            and notEquals(business_product_id,'0') group by enterprise_id having bus_count=1) t1
        where t1.enterprise_id global in (select t2.enterprise_id from (select enterprise_id,uniqExact(business_product_id) as bus_count from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and notEquals(business_product_id,'0') group by enterprise_id having bus_count=1) t2 group by t2.enterprise_id)
    </select>

    <select id="queryCustFirstNewBussV2" resultType="java.lang.Long">
        select uniqExact(enterprise_id) as countNum
        from t_customer_business_day_count_all final
        where pdate = toString(toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and first_business_time between #{query.startDate} and #{query.endDate}
    </select>

    <!-- 周期内首次办理业务的存量客户列表：包含已办理未生效,已生效(不包含已退订、包含已到期但重新办理了)-->
    <select id="queryCustFirstNewBussList" resultType="com.cmiot.report.dto.NewCustNameCount">
        select t1.date as date,uniqExact(t1.enterprise_id) as count from (select toDate(business_subscribe_time) as date,enterprise_id from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and enterprise_id global in (select tt.enterprise_id from (select enterprise_id,uniqExact(business_product_id) as bus_count from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and business_subscribe_time between #{query.startDate} and #{query.endDate}
        and notEquals(business_product_id,'0') group by enterprise_id having bus_count=1) tt where tt.enterprise_id
        global in (select t2.enterprise_id from (select enterprise_id,uniqExact(business_product_id) as bus_count from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and notEquals(business_product_id,'0') group by enterprise_id having bus_count=1) t2 group by t2.enterprise_id) group by tt.enterprise_id) group by date,enterprise_id) t1
        where t1.enterprise_id global not in (select enterprise_id from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and business_subscribe_time &lt; #{query.startDate} and notEquals(business_unsubscribe_time,#{query.initDate}) group by enterprise_id)
        group by t1.date
    </select>


    <select id="queryCustFirstNewBussListV2" resultType="com.cmiot.report.dto.NewCustNameCount">
        select uniqExact(enterprise_id) as count, toDate(first_business_time) as date
        from t_customer_business_day_count_all final
        where pdate = toString(toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and first_business_time between #{query.startDate} and #{query.endDate}
        group by toDate(first_business_time)
    </select>

    <!--退订客户数统计-->
    <select id="countUnsubBussByQuery" resultType="java.lang.Long">
        select uniqExact(enterprise_id) as count from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and (business_unsubscribe_time between #{query.startDate} and #{query.endDate}
        or package_uneffect_time between #{query.startDate} and #{query.endDate})
    </select>

    <!--退订客户列表-->
    <select id="countCustUnsubBussListByQuery" resultType="com.cmiot.report.dto.NewCustNameCount">
        select t1.date as date,uniqExact(t1.enterprise_id) as count from (
        select toDate(business_unsubscribe_time) as date,enterprise_id from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and business_unsubscribe_time between #{query.startDate} and #{query.endDate}
        union all
        select toDate(package_uneffect_time) as date,enterprise_id from t_customer_order_agg_all
        where equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1)))
        <if test="query.province !=null">
            and province_code in
            <foreach collection="query.province" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null">
            and city_code in
            <foreach collection="query.city" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.businessType!=null and query.businessType!=''">
            and business_type = #{query.businessType}
        </if>
        and package_uneffect_time between #{query.startDate} and #{query.endDate}
        ) t1 group by t1.date
    </select>


    <!-- 4.企业行业新增客户统计 -->
    <!-- 企业行业新增客户统计 -->
    <select id="countNewIndusCustListByExample" resultType="com.cmiot.report.dto.NewIndusCustCount">
        select dictGetOrDefault(ge_report.t_dict_industry_info, 'industry_name', t1.industry, '其他') as
        industry,sum(t1.count) as count from
        (select if(isNull(industry),99,industry) as industry,uniqExact(customer_id) as count from
        t_customer_order_agg_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and equals(toYYYYMMDD(sample_time),toYYYYMMDD(addDays(now(), -1))) group by industry) t1 group by industry
    </select>
</mapper>