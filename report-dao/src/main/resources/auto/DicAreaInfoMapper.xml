<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DicAreaInfoMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.DicAreaInfo">
    <result column="gcode" jdbcType="VARCHAR" property="gcode" />
    <result column="gname" jdbcType="VARCHAR" property="gname" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="pcode" jdbcType="VARCHAR" property="pcode" />
  </resultMap>


  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    gcode, gname, level, id, pid, pcode
  </sql>

  <!--重庆市->重庆 省份(一级code)-->
  <select id="selectSubProvNameByExample" parameterType="com.cmiot.report.bean.DicAreaInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    gcode, substringUTF8(gname,1,3) as gname, level, id, pid,pcode
    from t_dic_area_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if> and gcode in ('230000','150000') union all
    select
    <if test="distinct">
      distinct
    </if>
    gcode, substringUTF8(gname,1,2) as gname, level, id, pid,pcode
    from t_dic_area_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if> and gcode not in ('230000','150000')
  </select>

  <select id="selectByExample" parameterType="com.cmiot.report.bean.DicAreaInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_dic_area_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.DicAreaInfoExample">
    delete from t_dic_area_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.DicAreaInfo">
    insert into t_dic_area_info (gcode, gname, level,
      id, pid, pcode)
    values (#{gcode,jdbcType=VARCHAR}, #{gname,jdbcType=VARCHAR}, #{level,jdbcType=INTEGER},
      #{id,jdbcType=INTEGER}, #{pid,jdbcType=INTEGER}, #{pcode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.DicAreaInfo">
    insert into t_dic_area_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gcode != null">
        gcode,
      </if>
      <if test="gname != null">
        gname,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="id != null">
        id,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="pcode != null">
        pcode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gcode != null">
        #{gcode,jdbcType=VARCHAR},
      </if>
      <if test="gname != null">
        #{gname,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=INTEGER},
      </if>
      <if test="pcode != null">
        #{pcode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.DicAreaInfoExample" resultType="java.lang.Long">
    select count(*) from t_dic_area_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_dic_area_info
    <set>
      <if test="record.gcode != null">
        gcode = #{record.gcode,jdbcType=VARCHAR},
      </if>
      <if test="record.gname != null">
        gname = #{record.gname,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        level = #{record.level,jdbcType=INTEGER},
      </if>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.pid != null">
        pid = #{record.pid,jdbcType=INTEGER},
      </if>
      <if test="record.pcode != null">
        pcode = #{record.pcode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_dic_area_info
    set gcode = #{record.gcode,jdbcType=VARCHAR},
    gname = #{record.gname,jdbcType=VARCHAR},
    level = #{record.level,jdbcType=INTEGER},
    id = #{record.id,jdbcType=INTEGER},
    pid = #{record.pid,jdbcType=INTEGER},
    pcode = #{record.pcode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.DicAreaInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_dic_area_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>