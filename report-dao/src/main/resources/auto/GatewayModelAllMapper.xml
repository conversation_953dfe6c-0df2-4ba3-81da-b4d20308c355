<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayModelAllMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.GatewayModelAll">
    <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn" />
    <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac" />
    <result column="factory_id" jdbcType="BIGINT" property="factoryId" />
    <result column="device_model_id" jdbcType="BIGINT" property="deviceModelId" />
    <result column="gateway_model_int" jdbcType="INTEGER" property="gatewayModelInt" />
    <result column="gateway_model" jdbcType="VARCHAR" property="gatewayModel" />
    <result column="gateway_vendor" jdbcType="VARCHAR" property="gatewayVendor" />
    <result column="gateway_productclass" jdbcType="VARCHAR" property="gatewayProductclass" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    gateway_sn, gateway_mac, factory_id, device_model_id, gateway_model_int, gateway_model, 
    gateway_vendor, gateway_productclass, province_code, city_code, enterprise_id, customer_id
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.GatewayModelAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_model_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.GatewayModelAllExample">
    delete from t_gateway_model_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.GatewayModelAll">
    insert into t_gateway_model_all (gateway_sn, gateway_mac, factory_id, 
      device_model_id, gateway_model_int, gateway_model, 
      gateway_vendor, gateway_productclass, province_code, 
      city_code, enterprise_id, customer_id
      )
    values (#{gatewaySn,jdbcType=VARCHAR}, #{gatewayMac,jdbcType=VARCHAR}, #{factoryId,jdbcType=BIGINT}, 
      #{deviceModelId,jdbcType=BIGINT}, #{gatewayModelInt,jdbcType=SMALLINT}, #{gatewayModel,jdbcType=VARCHAR}, 
      #{gatewayVendor,jdbcType=VARCHAR}, #{gatewayProductclass,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{enterpriseId,jdbcType=BIGINT}, #{customerId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.GatewayModelAll">
    insert into t_gateway_model_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gatewaySn != null">
        gateway_sn,
      </if>
      <if test="gatewayMac != null">
        gateway_mac,
      </if>
      <if test="factoryId != null">
        factory_id,
      </if>
      <if test="deviceModelId != null">
        device_model_id,
      </if>
      <if test="gatewayModelInt != null">
        gateway_model_int,
      </if>
      <if test="gatewayModel != null">
        gateway_model,
      </if>
      <if test="gatewayVendor != null">
        gateway_vendor,
      </if>
      <if test="gatewayProductclass != null">
        gateway_productclass,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gatewaySn != null">
        #{gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="gatewayMac != null">
        #{gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="factoryId != null">
        #{factoryId,jdbcType=BIGINT},
      </if>
      <if test="deviceModelId != null">
        #{deviceModelId,jdbcType=BIGINT},
      </if>
      <if test="gatewayModelInt != null">
        #{gatewayModelInt,jdbcType=SMALLINT},
      </if>
      <if test="gatewayModel != null">
        #{gatewayModel,jdbcType=VARCHAR},
      </if>
      <if test="gatewayVendor != null">
        #{gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="gatewayProductclass != null">
        #{gatewayProductclass,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.GatewayModelAllExample" resultType="java.lang.Long">
    select count(*) from t_gateway_model_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_gateway_model_all
    <set>
      <if test="record.gatewaySn != null">
        gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayMac != null">
        gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="record.factoryId != null">
        factory_id = #{record.factoryId,jdbcType=BIGINT},
      </if>
      <if test="record.deviceModelId != null">
        device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
      </if>
      <if test="record.gatewayModelInt != null">
        gateway_model_int = #{record.gatewayModelInt,jdbcType=SMALLINT},
      </if>
      <if test="record.gatewayModel != null">
        gateway_model = #{record.gatewayModel,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayVendor != null">
        gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayProductclass != null">
        gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.enterpriseId != null">
        enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_gateway_model_all
    set gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      factory_id = #{record.factoryId,jdbcType=BIGINT},
      device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
      gateway_model_int = #{record.gatewayModelInt,jdbcType=SMALLINT},
      gateway_model = #{record.gatewayModel,jdbcType=VARCHAR},
      gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.GatewayModelAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_model_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>