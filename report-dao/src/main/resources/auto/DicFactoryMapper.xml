<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DicFactoryMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.DicFactory">
        <result column="factory_id" jdbcType="BIGINT" property="factoryId" />
        <result column="factory_code" jdbcType="VARCHAR" property="factoryCode" />
        <result column="factory_name" jdbcType="VARCHAR" property="factoryName" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        factory_id, factory_code, factory_name, create_time, update_time
    </sql>

    <select id="selectByWindowExample" parameterType="com.cmiot.report.bean.DicFactoryExample"
            resultMap="BaseResultMap">
        select t2.factory_code,t2.factory_id,t2.factory_name,t2.create_time,t2.update_time from (
        select t1.*,row_number() over(partition by t1.factory_code,t1.factory_name order by t1.update_time desc) rank
        from (
        select factory_id,factory_code, factory_name, create_time, update_time from t_dic_factory order by update_time
        desc) t1) t2
        where t2.rank=1
    </select>

    <select id="selectByExample" parameterType="com.cmiot.report.bean.DicFactoryExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from t_dic_factory
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.DicFactoryExample">
        delete from t_dic_factory
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.DicFactory">
        insert into t_dic_factory (factory_id, factory_code, factory_name,
        create_time, update_time)
        values (#{factoryId,jdbcType=BIGINT}, #{factoryCode,jdbcType=VARCHAR}, #{factoryName,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.DicFactory">
        insert into t_dic_factory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factoryId != null">
                factory_id,
            </if>
            <if test="factoryCode != null">
                factory_code,
            </if>
            <if test="factoryName != null">
                factory_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factoryId != null">
                #{factoryId,jdbcType=BIGINT},
            </if>
            <if test="factoryCode != null">
                #{factoryCode,jdbcType=VARCHAR},
            </if>
            <if test="factoryName != null">
                #{factoryName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.DicFactoryExample" resultType="java.lang.Long">
        select count(*) from t_dic_factory
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_dic_factory
        <set>
            <if test="record.factoryId != null">
                factory_id = #{record.factoryId,jdbcType=BIGINT},
            </if>
            <if test="record.factoryCode != null">
                factory_code = #{record.factoryCode,jdbcType=VARCHAR},
            </if>
            <if test="record.factoryName != null">
                factory_name = #{record.factoryName,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_dic_factory
        set factory_id = #{record.factoryId,jdbcType=BIGINT},
        factory_code = #{record.factoryCode,jdbcType=VARCHAR},
        factory_name = #{record.factoryName,jdbcType=VARCHAR},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.DicFactoryExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from t_dic_factory
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>