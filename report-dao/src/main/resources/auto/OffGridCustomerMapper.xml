<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.OffGridCustomerMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.OffGridCustomer">
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="customer_status" jdbcType="INTEGER" property="customerStatus" />
    <result column="value_category" jdbcType="VARCHAR" property="valueCategory" />
    <result column="off_grid_score" jdbcType="INTEGER" property="offGridScore" />
    <result column="off_grid_tag" jdbcType="INTEGER" property="offGridTag" />
    <result column="terminal_score" jdbcType="INTEGER" property="terminalScore" />
    <result column="terminal_tag" jdbcType="INTEGER" property="terminalTag" />
    <result column="network_score" jdbcType="INTEGER" property="networkScore" />
    <result column="network_tag" jdbcType="INTEGER" property="networkTag" />
    <result column="service_score" jdbcType="INTEGER" property="serviceScore" />
    <result column="service_tag" jdbcType="INTEGER" property="serviceTag" />
    <result column="business_score" jdbcType="INTEGER" property="businessScore" />
    <result column="business_tag" jdbcType="INTEGER" property="businessTag" />
    <result column="maintenance_score" jdbcType="INTEGER" property="maintenanceScore" />
    <result column="maintenance_tag" jdbcType="INTEGER" property="maintenanceTag" />
    <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    enterprise_id, customer_id, customer_name, province_code, province_name, city_code, 
    city_name, customer_status, value_category, off_grid_score, off_grid_tag, terminal_score, 
    terminal_tag, network_score, network_tag, service_score, service_tag, business_score, 
    business_tag, maintenance_score, maintenance_tag, sample_time
  </sql>


  <!-- 离网预警信息列表查询 -->
  <select id="selectListByExample" parameterType="com.cmiot.report.bean.OffGridCustomerExample"
          resultType="com.cmiot.report.dto.OffGridCustList">
    select
    <if test="distinct">
      distinct
    </if>
    customer_name as business,province_name as areaName,customer_id as groupCustomer,if(or(isNull(value_category),equals(value_category,'')),'未知',dictGetOrDefault(ge_report.t_dict_bass_std, 'name', toUInt64(value_category), '未知')) as enterpriseValueCategory,
    dictGetOrDefault(ge_report.t_dict_bass_std, 'name', toUInt64(if(customer_status=20,13,customer_status)), '未知') as customerStatus,
    cast(off_grid_score as String) as syntheticalValue,cast(off_grid_tag as String) as syntheticalTag,
    cast(terminal_score as String) as terminalQualityValue,cast(terminal_tag as String) as terminalQualityTag,
    cast(network_score as String) as networkQualityValue,cast(network_tag as String) as networkQualityTag,
    cast(service_score as String) as customerServiceValue,cast(service_tag as String) as customerServiceTag,
    cast(business_score as String) as businessOrderValue,cast(business_tag as String) as businessOrderTag,
    cast(maintenance_score as String) as installationAndMaintenanceServiceValue,cast(maintenance_tag as String) as installationAndMaintenanceServiceTag,
    cast(enterprise_id as String) as id from t_customer_off_grid_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    group by customer_name,province_name,customer_id,value_category,customer_status,off_grid_score,off_grid_tag,terminal_score,terminal_tag,network_score,network_tag,service_score,service_tag,business_score,business_tag,maintenance_score,maintenance_tag,enterprise_id
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>


  <!-- 离网预警客户导出明细 -->
  <select id="selectExportByExample" resultType="com.cmiot.report.dto.OffGridCustExportVO">
    select
    <if test="distinct">
      distinct
    </if>
    customer_name as customerName, province_name as provinceName, customer_id as customerId,
    dictGetOrDefault(ge_report.t_dict_bass_std, 'name', toUInt64(value_category), '未知') as valueCategory, dictGetOrDefault(ge_report.t_dict_bass_std, 'name', toUInt64(customer_status), '未知') as customerStatus, off_grid_score as offGridScore,
    terminal_score as terminalScore, network_score as networkScore, service_score as serviceScore,
    business_score as businessScore, maintenance_score as maintenanceScore from t_customer_off_grid_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by customer_name,province_name,customer_id,value_category,customer_status,off_grid_score,terminal_score,network_score,service_score,business_score,maintenance_score
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>


  <select id="selectCustListByExample" resultType="com.cmiot.report.dto.OffGridCustList">
    select
    <if test="distinct">
      distinct
    </if>
    customer_name as business,province_name as areaName,customer_id as groupCustomer,value_category as enterpriseValueCategory,cast(customer_status as String) as customerStatus,
    cast(off_grid_score as String) as syntheticalValue,cast(terminal_score as String) as terminalQualityValue,cast(network_score as String) as networkQualityValue,
    cast(service_score as String) as customerServiceValue,cast(business_score as String) as businessOrderValue,cast(maintenance_score as String) as installationAndMaintenanceServiceValue,
    cast(enterprise_id as String) as id from t_customer_off_grid_all where
    value_category=#{query.valueCategory} and customer_status=#{query.customerStatus}
    <if test="query.provList !=null">
      and province_code in
      <foreach item="item" collection="query.province" separator="," open="(" close=")" index="">
        #{item}
      </foreach>
    </if>
    <if test="query.cityList !=null">
      and city_code in
      <foreach item="item" collection="query.city" separator="," open="(" close=")" index="">
        #{item}
      </foreach>
    </if>
    <if test="query.customerName !=null">
      and customer_name like #{query.business}
    </if>
    <if test="query.groupCustomer !=null">
      and customer_id like #{query.groupCustomer}
    </if>
    <if test="query.enterpriseValueCategory !=null">
      and value_category = #{query.enterpriseValueCategory}
    </if>
    <if test="query.customerStatus !=null">
      and customer_status = #{query.customerStatus}
    </if>
    <if test="query.syntheticalStart !=0 and query.syntheticalEnd !=0">
      or (off_grid_score > #{query.syntheticalStart} and off_grid_score &lt;= #{query.syntheticalEnd})
    </if>
    <if test="query.terminalQualityStart !=0 and query.terminalQualityEnd !=0">
      or (terminal_score > #{query.terminalQualityStart} and terminal_score &lt;= #{query.terminalQualityEnd})
    </if>

  </select>



  <select id="selectByExample" parameterType="com.cmiot.report.bean.OffGridCustomerExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer_off_grid_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.OffGridCustomerExample">
    delete from t_customer_off_grid_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.OffGridCustomer">
    insert into t_customer_off_grid_all (enterprise_id, customer_id, customer_name, 
      province_code, province_name, city_code, 
      city_name, customer_status, value_category, 
      off_grid_score, off_grid_tag, terminal_score, 
      terminal_tag, network_score, network_tag, 
      service_score, service_tag, business_score, 
      business_tag, maintenance_score, maintenance_tag, 
      sample_time)
    values (#{enterpriseId,jdbcType=BIGINT}, #{customerId,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, 
      #{provinceCode,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR}, #{customerStatus,jdbcType=INTEGER}, #{valueCategory,jdbcType=VARCHAR}, 
      #{offGridScore,jdbcType=INTEGER}, #{offGridTag,jdbcType=INTEGER}, #{terminalScore,jdbcType=INTEGER}, 
      #{terminalTag,jdbcType=INTEGER}, #{networkScore,jdbcType=INTEGER}, #{networkTag,jdbcType=INTEGER}, 
      #{serviceScore,jdbcType=INTEGER}, #{serviceTag,jdbcType=INTEGER}, #{businessScore,jdbcType=INTEGER}, 
      #{businessTag,jdbcType=INTEGER}, #{maintenanceScore,jdbcType=INTEGER}, #{maintenanceTag,jdbcType=INTEGER}, 
      #{sampleTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.OffGridCustomer">
    insert into t_customer_off_grid_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="customerStatus != null">
        customer_status,
      </if>
      <if test="valueCategory != null">
        value_category,
      </if>
      <if test="offGridScore != null">
        off_grid_score,
      </if>
      <if test="offGridTag != null">
        off_grid_tag,
      </if>
      <if test="terminalScore != null">
        terminal_score,
      </if>
      <if test="terminalTag != null">
        terminal_tag,
      </if>
      <if test="networkScore != null">
        network_score,
      </if>
      <if test="networkTag != null">
        network_tag,
      </if>
      <if test="serviceScore != null">
        service_score,
      </if>
      <if test="serviceTag != null">
        service_tag,
      </if>
      <if test="businessScore != null">
        business_score,
      </if>
      <if test="businessTag != null">
        business_tag,
      </if>
      <if test="maintenanceScore != null">
        maintenance_score,
      </if>
      <if test="maintenanceTag != null">
        maintenance_tag,
      </if>
      <if test="sampleTime != null">
        sample_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="customerStatus != null">
        #{customerStatus,jdbcType=INTEGER},
      </if>
      <if test="valueCategory != null">
        #{valueCategory,jdbcType=VARCHAR},
      </if>
      <if test="offGridScore != null">
        #{offGridScore,jdbcType=INTEGER},
      </if>
      <if test="offGridTag != null">
        #{offGridTag,jdbcType=INTEGER},
      </if>
      <if test="terminalScore != null">
        #{terminalScore,jdbcType=INTEGER},
      </if>
      <if test="terminalTag != null">
        #{terminalTag,jdbcType=INTEGER},
      </if>
      <if test="networkScore != null">
        #{networkScore,jdbcType=INTEGER},
      </if>
      <if test="networkTag != null">
        #{networkTag,jdbcType=INTEGER},
      </if>
      <if test="serviceScore != null">
        #{serviceScore,jdbcType=INTEGER},
      </if>
      <if test="serviceTag != null">
        #{serviceTag,jdbcType=INTEGER},
      </if>
      <if test="businessScore != null">
        #{businessScore,jdbcType=INTEGER},
      </if>
      <if test="businessTag != null">
        #{businessTag,jdbcType=INTEGER},
      </if>
      <if test="maintenanceScore != null">
        #{maintenanceScore,jdbcType=INTEGER},
      </if>
      <if test="maintenanceTag != null">
        #{maintenanceTag,jdbcType=INTEGER},
      </if>
      <if test="sampleTime != null">
        #{sampleTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <!-- 去重统计客户数 -->
  <select id="countByExample" parameterType="com.cmiot.report.bean.OffGridCustomerExample" resultType="java.lang.Long">
    select uniqExact(customer_id) as count from t_customer_off_grid_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>


  <update id="updateByExampleSelective" parameterType="map">
    update t_customer_off_grid_all
    <set>
      <if test="record.enterpriseId != null">
        enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerName != null">
        customer_name = #{record.customerName,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.customerStatus != null">
        customer_status = #{record.customerStatus,jdbcType=INTEGER},
      </if>
      <if test="record.valueCategory != null">
        value_category = #{record.valueCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.offGridScore != null">
        off_grid_score = #{record.offGridScore,jdbcType=INTEGER},
      </if>
      <if test="record.offGridTag != null">
        off_grid_tag = #{record.offGridTag,jdbcType=INTEGER},
      </if>
      <if test="record.terminalScore != null">
        terminal_score = #{record.terminalScore,jdbcType=INTEGER},
      </if>
      <if test="record.terminalTag != null">
        terminal_tag = #{record.terminalTag,jdbcType=INTEGER},
      </if>
      <if test="record.networkScore != null">
        network_score = #{record.networkScore,jdbcType=INTEGER},
      </if>
      <if test="record.networkTag != null">
        network_tag = #{record.networkTag,jdbcType=INTEGER},
      </if>
      <if test="record.serviceScore != null">
        service_score = #{record.serviceScore,jdbcType=INTEGER},
      </if>
      <if test="record.serviceTag != null">
        service_tag = #{record.serviceTag,jdbcType=INTEGER},
      </if>
      <if test="record.businessScore != null">
        business_score = #{record.businessScore,jdbcType=INTEGER},
      </if>
      <if test="record.businessTag != null">
        business_tag = #{record.businessTag,jdbcType=INTEGER},
      </if>
      <if test="record.maintenanceScore != null">
        maintenance_score = #{record.maintenanceScore,jdbcType=INTEGER},
      </if>
      <if test="record.maintenanceTag != null">
        maintenance_tag = #{record.maintenanceTag,jdbcType=INTEGER},
      </if>
      <if test="record.sampleTime != null">
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_customer_off_grid_all
    set enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      customer_name = #{record.customerName,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      customer_status = #{record.customerStatus,jdbcType=INTEGER},
      value_category = #{record.valueCategory,jdbcType=VARCHAR},
      off_grid_score = #{record.offGridScore,jdbcType=INTEGER},
      off_grid_tag = #{record.offGridTag,jdbcType=INTEGER},
      terminal_score = #{record.terminalScore,jdbcType=INTEGER},
      terminal_tag = #{record.terminalTag,jdbcType=INTEGER},
      network_score = #{record.networkScore,jdbcType=INTEGER},
      network_tag = #{record.networkTag,jdbcType=INTEGER},
      service_score = #{record.serviceScore,jdbcType=INTEGER},
      service_tag = #{record.serviceTag,jdbcType=INTEGER},
      business_score = #{record.businessScore,jdbcType=INTEGER},
      business_tag = #{record.businessTag,jdbcType=INTEGER},
      maintenance_score = #{record.maintenanceScore,jdbcType=INTEGER},
      maintenance_tag = #{record.maintenanceTag,jdbcType=INTEGER},
      sample_time = #{record.sampleTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.OffGridCustomerExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer_off_grid_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <resultMap id="AnomalyRecordBaseResultMap" type="com.cmiot.report.bean.GatewayAnomalyRecord">
    <result column="province" property="province" jdbcType="VARCHAR"/>
    <result column="city" property="city" jdbcType="VARCHAR"/>
    <result column="areaName" property="areaName" jdbcType="VARCHAR"/>
    <result column="gateway_sn" property="gatewaySn" jdbcType="VARCHAR"/>
    <result column="factory_id" property="factoryId" jdbcType="BIGINT"/>
    <result column="factory_name" property="factoryName" jdbcType="VARCHAR"/>
    <result column="device_model_id" property="deviceModelId" jdbcType="BIGINT"/>
    <result column="device_model" property="deviceModel" jdbcType="VARCHAR"/>
    <result column="customer_id" property="customerId" jdbcType="VARCHAR"/>
    <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
    <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR"/>
    <result column="pon_max" property="ponMax" jdbcType="INTEGER"/>
    <result column="pon_min" property="ponMin" jdbcType="INTEGER"/>
    <result column="pon_avg" property="ponAvg" jdbcType="DOUBLE"/>
    <result column="pppoe_error_num" property="pppoeErrorNum" jdbcType="INTEGER"/>
    <result column="pppoe_error_days_limit" property="pppoeErrorDaysLimit" jdbcType="INTEGER"/>
    <result column="pppoe_error_num_limit" property="pppoeErrorNumLimit" jdbcType="INTEGER"/>
    <result column="cpu_avg" property="cpuAvg" jdbcType="DOUBLE"/>
    <result column="cpu_limit" property="cpuLimit" jdbcType="INTEGER"/>
    <result column="ram_avg" property="ramAvg" jdbcType="DOUBLE"/>
    <result column="ram_limit" property="ramLimit" jdbcType="INTEGER"/>
    <result column="net_delay" property="netDelay" jdbcType="DOUBLE"/>
    <result column="net_delay_days_limit" property="netDelayDaysLimit" jdbcType="INTEGER"/>
    <result column="net_delay_limit" property="netDelayLimit" jdbcType="INTEGER"/>
    <result column="sub_device_over" property="subDeviceOver" jdbcType="INTEGER"/>
    <result column="sub_device_over_days_limit" property="subDeviceOverDaysLimit" jdbcType="INTEGER"/>
    <result column="sub_device_over_num_limit" property="subDeviceOverNumLimit" jdbcType="INTEGER"/>
    <result column="wlan_radio_power_avg" property="wlanRadioPowerAvg" jdbcType="DOUBLE"/>
    <result column="wlan_radio_power_avg_limit" property="wlanRadioPowerAvgLimit" jdbcType="DOUBLE"/>
    <result column="errs" property="errs" jdbcType="INTEGER"/>

  </resultMap>

  <sql id="Anomaly_Record_Base_Column_List">
    t.province,t.city,
    concat(dictGetOrDefault('ge_report.t_dic_area_info', 'gname', t.province, '其他'),'-',
           dictGetOrDefault('ge_report.t_dic_area_info', 'gname', t.city, '其他')) as areaName,
    t.gateway_sn,t.factory_id,t.factory_name,t.device_model_id,t.device_model,t.customer_id,t.customer_name,t.contact_phone,t.pon_max,t.pon_min,t.pon_avg,t.pppoe_error_num,t.pppoe_error_days_limit,t.pppoe_error_num_limit,
    t.cpu_avg,t.cpu_limit,t.ram_avg,t.ram_limit,t.net_delay,t.net_delay_days_limit,t.net_delay_limit,t.sub_device_over,t.sub_device_over_days_limit,t.sub_device_over_num_limit,
    t.wlan_radio_power_avg,t.wlan_radio_power_avg_limit,
    (case when t.pon_avg!=0 then 1 else 0 end
    +case when t.pppoe_error_num!=0 then 1 else 0 end
    +case when t.cpu_avg!=0 then 1 else 0 end
    +case when t.ram_avg!=0 then 1 else 0 end
    +case when t.net_delay!=0 then 1 else 0 end
    +case when t.sub_device_over!=0 then 1 else 0 end
    +case when t.wlan_radio_power_avg!=0 then 1 else 0 end) errs
  </sql>

  <select id="queryAnomalyRecordList" resultMap="AnomalyRecordBaseResultMap">
    select
    <include refid="Anomaly_Record_Base_Column_List"/>
    from t_gateway_anomaly_record_all t
<!--    global LEFT JOIN (select customer_id,contact_phone from t_customer_all where toYYYYMMDD(sample_time)=#{lastday}) tca on tca.customer_id = t.customer_id-->
    where pdate = #{lastday}
    <if test="provinceListStr != null and provinceListStr.size > 0">and t.province in
      <foreach collection="provinceListStr" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
    </if>
    <if test="cityListStr != null and cityListStr.size > 0">and t.city in
      <foreach collection="cityListStr" index="index" item="itemCity" open="(" separator="," close=")">#{itemCity}</foreach>
    </if>
    <if test="customerName != null and customerName != ''"> and t.customer_name like concat('%',#{customerName},'%')</if>
    <if test="gatewaySn != null and gatewaySn != ''"> and t.gateway_sn = #{gatewaySn}</if>
    <if test="vendorListNum != null and vendorListNum.size > 0">and t.factory_id in
      <foreach collection="vendorListNum" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
    </if>
    <if test="modelListNum != null and modelListNum.size > 0">and t.device_model_id in
      <foreach collection="modelListNum" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
    </if>
    <if test="orderName != null" > order by ${orderName} </if>
    <if test="orderType == 'ascend'"> asc </if>
    <if test="orderType == 'descend'"> desc </if>
  </select>

  <select id="queryNotOnline" resultType="com.cmiot.report.bean.GatewayNotOnline">
    SELECT province provinceCode,city cityCode,
    concat(dictGetOrDefault('ge_report.t_dic_area_info', 'gname', provinceCode, '其他'),'-',
           dictGetOrDefault('ge_report.t_dic_area_info', 'gname', cityCode, '其他')) as areaName,
    customer_name customerName,contact_phone contactPhone, gateway_sn gatewaySn,gateway_mac gatewayMac,
    factory_id factoryId,factory_name factoryName,device_model deviceModel,idle_time idleTime, customer_id as customerId
    from t_gateway_anomaly_not_online_record_all
    WHERE pdate = #{lastday}
    <if test="provinceListStr != null and provinceListStr.size > 0"> and province in <foreach collection="provinceListStr" index="index" item="item" open="(" separator="," close=")">#{item}</foreach></if>
    <if test="cityListStr != null and cityListStr.size > 0"> and city in <foreach collection="cityListStr" index="index" item="itemCity" open="(" separator="," close=")">#{itemCity}</foreach></if>
    <if test="customerName != null and customerName != ''"> and customer_name like concat('%',#{customerName},'%') </if>
    <if test="gatewaySn != null and gatewaySn != ''"> and gateway_sn = #{gatewaySn}</if>
    <if test="vendorListNum != null and vendorListNum.size > 0"> and factory_id in <foreach collection="vendorListNum" index="index" item="item" open="(" separator="," close=")">#{item}</foreach></if>
    <if test="modelListNum != null and modelListNum.size > 0"> and device_model_id in <foreach collection="modelListNum" index="index" item="item" open="(" separator="," close=")">#{item}</foreach></if>
    <if test="orderName != null" > order by ${orderName} </if>
    <if test="orderType == 'ascend'"> asc </if>
    <if test="orderType == 'descend'"> desc </if>
  </select>

  <select id="queryUneffect" resultType="com.cmiot.report.bean.CustomerUneffect">
    SELECT province provinceCode,city cityCode,
    concat(dictGetOrDefault('ge_report.t_dic_area_info', 'gname', provinceCode, '其他'),'-',
           dictGetOrDefault('ge_report.t_dic_area_info', 'gname', cityCode, '其他')) as areaName,
    customer_name customerName,contact_phone contactPhone,
    package_uneffect_time packageUneffectTime,uneffect_days uneffectDays,
    business_product_id as businessProductId, customer_id as customerId, manager_number as managerNumber
    from t_gateway_anomaly_package_uneffect_record_all
    WHERE pdate=#{lastday}
    <if test="provinceListStr != null and provinceListStr.size > 0"> and province in <foreach collection="provinceListStr" index="index" item="item" open="(" separator="," close=")">#{item}</foreach></if>
    <if test="cityListStr != null and cityListStr.size > 0"> and city in <foreach collection="cityListStr" index="index" item="itemCity" open="(" separator="," close=")">#{itemCity}</foreach></if>
    <if test="customerName != null and customerName != ''"> and customer_name like concat('%',#{customerName},'%') </if>
    <if test="orderName != null" > order by ${orderName} </if>
    <if test="orderType == 'ascend'"> asc </if>
    <if test="orderType == 'descend'"> desc </if>
  </select>

</mapper>