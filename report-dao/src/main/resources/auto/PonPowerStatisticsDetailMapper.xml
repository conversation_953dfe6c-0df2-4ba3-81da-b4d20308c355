<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.PonPowerStatisticsDetailMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.PonPowerStatisticsDetail">
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn"/>
        <result column="factory_id" jdbcType="BIGINT" property="factoryId"/>
        <result column="factory_name" jdbcType="VARCHAR" property="factoryName"/>
        <result column="factory_model_id" jdbcType="BIGINT" property="factoryModelId"/>
        <result column="factory_model" jdbcType="VARCHAR" property="factoryModel"/>
        <result column="runing_time_curr" jdbcType="BIGINT" property="runingTimeCurr"/>
        <result column="pon_rx_power_curr_sum" jdbcType="INTEGER" property="ponRxPowerCurrSum"/>
        <result column="pon_rx_power_curr_count" jdbcType="INTEGER" property="ponRxPowerCurrCount"/>
        <result column="pon_rx_power_curr_type" jdbcType="TINYINT" property="ponRxPowerCurrType"/>
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime"/>
        <result column="gdate" jdbcType="BIGINT" property="gdate"/>
    </resultMap>

    <resultMap id="PonResultMap" type="com.cmiot.report.bean.PonPowerStatisticsQ">
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="gtwCount" jdbcType="BIGINT" property="gtwCount"/>
        <result column="ponRxPowerCurrAvg" jdbcType="INTEGER" property="ponRxPowerCurrAvg"/>
        <result column="ponRxPowerCurrType" jdbcType="TINYINT" property="ponRxPowerCurrType"/>
    </resultMap>


    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Base_Column_List">
        province_code, city_code, gateway_sn, factory_id, factory_name, factory_model_id,
        factory_model, runing_time_curr, pon_rx_power_curr_sum, pon_rx_power_curr_count,
        pon_rx_power_curr_type, sample_time, gdate
    </sql>

    <select id="selectByExample" parameterType="com.cmiot.report.bean.PonPowerStatisticsDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_pon_power_statistics_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <!--按照省份统计-->
    <select id="selectByProvExample" parameterType="com.cmiot.report.bean.PonPowerStatisticsDetailExample"
            resultMap="PonResultMap">
        select name,gtwCount,if(equals(round(divide(sum,count),0),10000000) or equals(if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10))),-inf) or isNaN(if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10)))),0,if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10)))) as ponRxPowerCurrAvg
        ,ponRxPowerCurrType from (select province_code as name,pon_rx_power_curr_type as ponRxPowerCurrType,uniqExact(gateway_sn) as
        gtwCount,sum(pon_rx_power_curr_sum) as sum,sum(pon_rx_power_curr_count) as count from
        t_gateway_pon_power_statistics_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by province_code,pon_rx_power_curr_type) t1
    </select>

    <!--按照地市统计-->
    <select id="selectByCityExample" parameterType="com.cmiot.report.bean.PonPowerStatisticsDetailExample"
            resultMap="PonResultMap">
        select name,gtwCount,if(equals(round(divide(sum,count),0),10000000) or equals(if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10))),-inf) or isNaN(if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10)))),0,if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10)))) as ponRxPowerCurrAvg
        ,ponRxPowerCurrType from (select city_code as name,pon_rx_power_curr_type as ponRxPowerCurrType,uniqExact(gateway_sn) as
        gtwCount,sum(pon_rx_power_curr_sum) as sum,sum(pon_rx_power_curr_count) as count from
        t_gateway_pon_power_statistics_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by city_code,pon_rx_power_curr_type) t1
    </select>

    <!--按照厂商统计-->
    <select id="selectByFactoryExample" parameterType="com.cmiot.report.bean.PonPowerStatisticsDetailExample"
            resultMap="PonResultMap">
        select name,gtwCount,if(equals(round(divide(sum,count),0),10000000) or equals(if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10))),-inf) or isNaN(if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10)))),0,if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10)))) as ponRxPowerCurrAvg
        ,ponRxPowerCurrType from (select factory_name as name,pon_rx_power_curr_type as ponRxPowerCurrType,uniqExact(gateway_sn) as
        gtwCount,sum(pon_rx_power_curr_sum) as sum,sum(pon_rx_power_curr_count) as count from
        t_gateway_pon_power_statistics_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by factory_name,pon_rx_power_curr_type) t1
    </select>

    <!--按照厂商型号统计-->
    <select id="selectByFactoryModelExample" parameterType="com.cmiot.report.bean.PonPowerStatisticsDetailExample"
            resultMap="PonResultMap">
        select concat(factory_name,'_',dictGetOrDefault(ge_report.t_dic_device_model, 'device_model', factory_model_id, '其他')) as name,gtwCount,if(equals(round(divide(sum,count),0),10000000) or equals(if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10))),-inf) or isNaN(if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10)))),0,if(round(divide(sum,count),0)>1000000,round(10*log(round(divide(sum,count),0)/1000000)/log(10)),round(10*log(round(divide(sum,count),0)/10000)/log(10)))) as ponRxPowerCurrAvg
        ,ponRxPowerCurrType from (select factory_name,factory_model_id,pon_rx_power_curr_type as ponRxPowerCurrType,uniqExact(gateway_sn) as
        gtwCount,sum(pon_rx_power_curr_sum) as sum,sum(pon_rx_power_curr_count) as count from
        t_gateway_pon_power_statistics_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by factory_name,factory_model_id,pon_rx_power_curr_type) t1
    </select>


    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.PonPowerStatisticsDetailExample">
        delete from t_gateway_pon_power_statistics_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.PonPowerStatisticsDetail">
        insert into t_gateway_pon_power_statistics_detail_all (province_code, city_code, gateway_sn,
        factory_id, factory_name, factory_model_id,
        factory_model, runing_time_curr, pon_rx_power_curr_sum,
        pon_rx_power_curr_count, pon_rx_power_curr_type,
        sample_time, gdate)
        values (#{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{gatewaySn,jdbcType=VARCHAR},
        #{factoryId,jdbcType=BIGINT}, #{factoryName,jdbcType=VARCHAR}, #{factoryModelId,jdbcType=BIGINT},
        #{factoryModel,jdbcType=VARCHAR}, #{runingTimeCurr,jdbcType=BIGINT}, #{ponRxPowerCurrSum,jdbcType=INTEGER},
        #{ponRxPowerCurrCount,jdbcType=INTEGER}, #{ponRxPowerCurrType,jdbcType=TINYINT},
        #{sampleTime,jdbcType=TIMESTAMP}, #{gdate,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.PonPowerStatisticsDetail">
        insert into t_gateway_pon_power_statistics_detail_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="gatewaySn != null">
                gateway_sn,
            </if>
            <if test="factoryId != null">
                factory_id,
            </if>
            <if test="factoryName != null">
                factory_name,
            </if>
            <if test="factoryModelId != null">
                factory_model_id,
            </if>
            <if test="factoryModel != null">
                factory_model,
            </if>
            <if test="runingTimeCurr != null">
                runing_time_curr,
            </if>
            <if test="ponRxPowerCurrSum != null">
                pon_rx_power_curr_sum,
            </if>
            <if test="ponRxPowerCurrCount != null">
                pon_rx_power_curr_count,
            </if>
            <if test="ponRxPowerCurrType != null">
                pon_rx_power_curr_type,
            </if>
            <if test="sampleTime != null">
                sample_time,
            </if>
            <if test="gdate != null">
                gdate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="gatewaySn != null">
                #{gatewaySn,jdbcType=VARCHAR},
            </if>
            <if test="factoryId != null">
                #{factoryId,jdbcType=BIGINT},
            </if>
            <if test="factoryName != null">
                #{factoryName,jdbcType=VARCHAR},
            </if>
            <if test="factoryModelId != null">
                #{factoryModelId,jdbcType=BIGINT},
            </if>
            <if test="factoryModel != null">
                #{factoryModel,jdbcType=VARCHAR},
            </if>
            <if test="runingTimeCurr != null">
                #{runingTimeCurr,jdbcType=BIGINT},
            </if>
            <if test="ponRxPowerCurrSum != null">
                #{ponRxPowerCurrSum,jdbcType=INTEGER},
            </if>
            <if test="ponRxPowerCurrCount != null">
                #{ponRxPowerCurrCount,jdbcType=INTEGER},
            </if>
            <if test="ponRxPowerCurrType != null">
                #{ponRxPowerCurrType,jdbcType=TINYINT},
            </if>
            <if test="sampleTime != null">
                #{sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="gdate != null">
                #{gdate,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.PonPowerStatisticsDetailExample"
            resultType="java.lang.Long">
        select count(*) from t_gateway_pon_power_statistics_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_gateway_pon_power_statistics_detail_all
        <set>
            <if test="record.provinceCode != null">
                province_code = #{record.provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="record.cityCode != null">
                city_code = #{record.cityCode,jdbcType=VARCHAR},
            </if>
            <if test="record.gatewaySn != null">
                gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
            </if>
            <if test="record.factoryId != null">
                factory_id = #{record.factoryId,jdbcType=BIGINT},
            </if>
            <if test="record.factoryName != null">
                factory_name = #{record.factoryName,jdbcType=VARCHAR},
            </if>
            <if test="record.factoryModelId != null">
                factory_model_id = #{record.factoryModelId,jdbcType=BIGINT},
            </if>
            <if test="record.factoryModel != null">
                factory_model = #{record.factoryModel,jdbcType=VARCHAR},
            </if>
            <if test="record.runingTimeCurr != null">
                runing_time_curr = #{record.runingTimeCurr,jdbcType=BIGINT},
            </if>
            <if test="record.ponRxPowerCurrSum != null">
                pon_rx_power_curr_sum = #{record.ponRxPowerCurrSum,jdbcType=INTEGER},
            </if>
            <if test="record.ponRxPowerCurrCount != null">
                pon_rx_power_curr_count = #{record.ponRxPowerCurrCount,jdbcType=INTEGER},
            </if>
            <if test="record.ponRxPowerCurrType != null">
                pon_rx_power_curr_type = #{record.ponRxPowerCurrType,jdbcType=TINYINT},
            </if>
            <if test="record.sampleTime != null">
                sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gdate != null">
                gdate = #{record.gdate,jdbcType=BIGINT},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_gateway_pon_power_statistics_detail_all
        set province_code = #{record.provinceCode,jdbcType=VARCHAR},
        city_code = #{record.cityCode,jdbcType=VARCHAR},
        gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
        factory_id = #{record.factoryId,jdbcType=BIGINT},
        factory_name = #{record.factoryName,jdbcType=VARCHAR},
        factory_model_id = #{record.factoryModelId,jdbcType=BIGINT},
        factory_model = #{record.factoryModel,jdbcType=VARCHAR},
        runing_time_curr = #{record.runingTimeCurr,jdbcType=BIGINT},
        pon_rx_power_curr_sum = #{record.ponRxPowerCurrSum,jdbcType=INTEGER},
        pon_rx_power_curr_count = #{record.ponRxPowerCurrCount,jdbcType=INTEGER},
        pon_rx_power_curr_type = #{record.ponRxPowerCurrType,jdbcType=TINYINT},
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
        gdate = #{record.gdate,jdbcType=BIGINT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.PonPowerStatisticsDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_pon_power_statistics_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>