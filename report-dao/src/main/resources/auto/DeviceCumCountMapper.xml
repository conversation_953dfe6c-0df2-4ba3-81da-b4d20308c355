<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DeviceCumCountMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.DeviceCumCount">
    <result column="pdate" jdbcType="VARCHAR" property="pdate" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="gateway_vendor" jdbcType="VARCHAR" property="gatewayVendor" />
    <result column="device_all_count" jdbcType="BIGINT" property="deviceAllCount" />
    <result column="device_day_alive" jdbcType="BIGINT" property="deviceDayAlive" />
    <result column="device_week_alive" jdbcType="BIGINT" property="deviceWeekAlive" />
    <result column="device_month_alive" jdbcType="BIGINT" property="deviceMonthAlive" />
    <result column="device_all_wired" jdbcType="BIGINT" property="deviceAllWired" />
    <result column="device_all_wireless" jdbcType="BIGINT" property="deviceAllWireless" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    pdate, province_code, city_code, gateway_vendor, device_all_count, device_day_alive, 
    device_week_alive, device_month_alive, device_all_wired, device_all_wireless
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.DeviceCumCountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_device_cum_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.DeviceCumCountExample">
    delete from t_device_cum_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.DeviceCumCount">
    insert into t_device_cum_count_all (pdate, province_code, city_code,
      gateway_vendor, device_all_count, device_day_alive, 
      device_week_alive, device_month_alive, device_all_wired, 
      device_all_wireless)
    values (#{pdate,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{gatewayVendor,jdbcType=VARCHAR}, #{deviceAllCount,jdbcType=BIGINT}, #{deviceDayAlive,jdbcType=BIGINT}, 
      #{deviceWeekAlive,jdbcType=BIGINT}, #{deviceMonthAlive,jdbcType=BIGINT}, #{deviceAllWired,jdbcType=BIGINT}, 
      #{deviceAllWireless,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.DeviceCumCount">
    insert into t_device_cum_count_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pdate != null">
        pdate,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="gatewayVendor != null">
        gateway_vendor,
      </if>
      <if test="deviceAllCount != null">
        device_all_count,
      </if>
      <if test="deviceDayAlive != null">
        device_day_alive,
      </if>
      <if test="deviceWeekAlive != null">
        device_week_alive,
      </if>
      <if test="deviceMonthAlive != null">
        device_month_alive,
      </if>
      <if test="deviceAllWired != null">
        device_all_wired,
      </if>
      <if test="deviceAllWireless != null">
        device_all_wireless,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pdate != null">
        #{pdate,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="gatewayVendor != null">
        #{gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="deviceAllCount != null">
        #{deviceAllCount,jdbcType=BIGINT},
      </if>
      <if test="deviceDayAlive != null">
        #{deviceDayAlive,jdbcType=BIGINT},
      </if>
      <if test="deviceWeekAlive != null">
        #{deviceWeekAlive,jdbcType=BIGINT},
      </if>
      <if test="deviceMonthAlive != null">
        #{deviceMonthAlive,jdbcType=BIGINT},
      </if>
      <if test="deviceAllWired != null">
        #{deviceAllWired,jdbcType=BIGINT},
      </if>
      <if test="deviceAllWireless != null">
        #{deviceAllWireless,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.DeviceCumCountExample" resultType="java.lang.Long">
    select count(*) from t_device_cum_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_device_cum_count_all
    <set>
      <if test="record.pdate != null">
        pdate = #{record.pdate,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayVendor != null">
        gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceAllCount != null">
        device_all_count = #{record.deviceAllCount,jdbcType=BIGINT},
      </if>
      <if test="record.deviceDayAlive != null">
        device_day_alive = #{record.deviceDayAlive,jdbcType=BIGINT},
      </if>
      <if test="record.deviceWeekAlive != null">
        device_week_alive = #{record.deviceWeekAlive,jdbcType=BIGINT},
      </if>
      <if test="record.deviceMonthAlive != null">
        device_month_alive = #{record.deviceMonthAlive,jdbcType=BIGINT},
      </if>
      <if test="record.deviceAllWired != null">
        device_all_wired = #{record.deviceAllWired,jdbcType=BIGINT},
      </if>
      <if test="record.deviceAllWireless != null">
        device_all_wireless = #{record.deviceAllWireless,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_device_cum_count_all
    set pdate = #{record.pdate,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      device_all_count = #{record.deviceAllCount,jdbcType=BIGINT},
      device_day_alive = #{record.deviceDayAlive,jdbcType=BIGINT},
      device_week_alive = #{record.deviceWeekAlive,jdbcType=BIGINT},
      device_month_alive = #{record.deviceMonthAlive,jdbcType=BIGINT},
      device_all_wired = #{record.deviceAllWired,jdbcType=BIGINT},
      device_all_wireless = #{record.deviceAllWireless,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.DeviceCumCountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_device_cum_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>