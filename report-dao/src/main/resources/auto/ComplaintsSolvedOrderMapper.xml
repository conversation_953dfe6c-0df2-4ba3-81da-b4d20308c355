<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.ComplaintsSolvedOrderMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.ComplaintsSolvedOrder">
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="install_orders" jdbcType="INTEGER" property="installOrders"/>
        <result column="maintenance_solved_orders" jdbcType="INTEGER" property="maintenanceSolvedOrders"/>
        <result column="maintenance_solved_ext_orders" jdbcType="INTEGER" property="maintenanceSolvedExtOrders"/>
        <result column="complaints_solved_orders" jdbcType="INTEGER" property="complaintsSolvedOrders"/>
        <result column="complaints_solved_ext_orders" jdbcType="INTEGER" property="complaintsSolvedExtOrders"/>
        <result column="service_orders" jdbcType="INTEGER" property="serviceOrders"/>
        <result column="total_orders" jdbcType="INTEGER" property="totalOrders"/>
        <result column="gdate" jdbcType="INTEGER" property="gdate"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        customer_id, province_code, city_code, install_orders, maintenance_solved_orders,
        maintenance_solved_ext_orders, complaints_solved_orders, complaints_solved_ext_orders,
        service_orders, total_orders, gdate
    </sql>

    <!--统计各个工单处理效率-->
    <select id="countOrdersByExample" resultType="com.cmiot.report.dto.ComplaintsSolOrder">
        select if(isNaN(t2.timelyRateOfInstallation),0,t2.timelyRateOfInstallation)*100 as timelyRateOfInstallation,
        if(isNaN(t2.timelyMaintenanceRate),0,t2.timelyMaintenanceRate)*100 as timelyMaintenanceRate,
        if(isNaN(t2.failureResolutionRate),0,t2.failureResolutionRate)*100 as failureResolutionRate,
        if(isNaN(t2.complaintResolutionRate),0,t2.complaintResolutionRate)*100 as complaintResolutionRate,
        if(isNaN(t2.timelyComplaintRate),0,t2.timelyComplaintRate)*100 as timelyComplaintRate,
        if(isNaN(t2.customerServiceAccuracy),0,t2.customerServiceAccuracy)*100 as customerServiceAccuracy from
        (select round(divide(t1.install_count, t1.total),4) as timelyRateOfInstallation,
        round(divide(t1.maintenance_solved_ext_count, t1.total),4) as timelyMaintenanceRate,
        round(divide(t1.maintenance_solved_count, t1.total),4) as failureResolutionRate,
        round(divide(t1.complaints_solved_count, t1.total),4) as complaintResolutionRate,
        round(divide(t1.complaints_solved_ext_count, t1.total),4) as timelyComplaintRate,
        round(divide(t1.service_count, t1.total),4) as customerServiceAccuracy
        from (select sum(install_orders) as install_count,
        sum(maintenance_solved_orders) as maintenance_solved_count,
        sum(maintenance_solved_ext_orders) as maintenance_solved_ext_count,
        sum(complaints_solved_orders) as complaints_solved_count,
        sum(complaints_solved_ext_orders) as complaints_solved_ext_count,
        sum(service_orders) as service_count,
        sum(total_orders) as total from t_complaints_solved_order_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>) t1) t2
    </select>


    <select id="selectOrdersByExample" parameterType="com.cmiot.report.bean.ComplaintsSolvedOrderExample"
            resultType="com.cmiot.report.dto.ComplaintsSolOrderExt">
        select sum(install_orders) as installOrders,
        sum(maintenance_solved_orders) as maintenanceSolvedOrders,
        sum(maintenance_solved_ext_orders) as timelyMaintenanceSolvedOrders,
        sum(complaints_solved_orders) as
        complaintsSolvedOrders,
        sum(complaints_solved_ext_orders) as timelyComplaintsSolvedOrders,
        sum(service_orders) as serviceOrders,
        sum(total_orders) as totalOrders
        from t_complaints_solved_order_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectByExample" parameterType="com.cmiot.report.bean.ComplaintsSolvedOrderExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_complaints_solved_order_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.ComplaintsSolvedOrderExample">
        delete from t_complaints_solved_order_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.ComplaintsSolvedOrder">
        insert into t_complaints_solved_order_all (customer_id, province_code, city_code,
        install_orders, maintenance_solved_orders,
        maintenance_solved_ext_orders, complaints_solved_orders,
        complaints_solved_ext_orders, service_orders,
        total_orders, gdate)
        values (#{customerId,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR},
        #{installOrders,jdbcType=INTEGER}, #{maintenanceSolvedOrders,jdbcType=INTEGER},
        #{maintenanceSolvedExtOrders,jdbcType=INTEGER}, #{complaintsSolvedOrders,jdbcType=INTEGER},
        #{complaintsSolvedExtOrders,jdbcType=INTEGER}, #{serviceOrders,jdbcType=INTEGER},
        #{totalOrders,jdbcType=INTEGER}, #{gdate,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.ComplaintsSolvedOrder">
        insert into t_complaints_solved_order_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="installOrders != null">
                install_orders,
            </if>
            <if test="maintenanceSolvedOrders != null">
                maintenance_solved_orders,
            </if>
            <if test="maintenanceSolvedExtOrders != null">
                maintenance_solved_ext_orders,
            </if>
            <if test="complaintsSolvedOrders != null">
                complaints_solved_orders,
            </if>
            <if test="complaintsSolvedExtOrders != null">
                complaints_solved_ext_orders,
            </if>
            <if test="serviceOrders != null">
                service_orders,
            </if>
            <if test="totalOrders != null">
                total_orders,
            </if>
            <if test="gdate != null">
                gdate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">
                #{customerId,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="installOrders != null">
                #{installOrders,jdbcType=INTEGER},
            </if>
            <if test="maintenanceSolvedOrders != null">
                #{maintenanceSolvedOrders,jdbcType=INTEGER},
            </if>
            <if test="maintenanceSolvedExtOrders != null">
                #{maintenanceSolvedExtOrders,jdbcType=INTEGER},
            </if>
            <if test="complaintsSolvedOrders != null">
                #{complaintsSolvedOrders,jdbcType=INTEGER},
            </if>
            <if test="complaintsSolvedExtOrders != null">
                #{complaintsSolvedExtOrders,jdbcType=INTEGER},
            </if>
            <if test="serviceOrders != null">
                #{serviceOrders,jdbcType=INTEGER},
            </if>
            <if test="totalOrders != null">
                #{totalOrders,jdbcType=INTEGER},
            </if>
            <if test="gdate != null">
                #{gdate,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.ComplaintsSolvedOrderExample"
            resultType="java.lang.Long">
        select count(*) from t_complaints_solved_order_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_complaints_solved_order_all
        <set>
            <if test="record.customerId != null">
                customer_id = #{record.customerId,jdbcType=VARCHAR},
            </if>
            <if test="record.provinceCode != null">
                province_code = #{record.provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="record.cityCode != null">
                city_code = #{record.cityCode,jdbcType=VARCHAR},
            </if>
            <if test="record.installOrders != null">
                install_orders = #{record.installOrders,jdbcType=INTEGER},
            </if>
            <if test="record.maintenanceSolvedOrders != null">
                maintenance_solved_orders = #{record.maintenanceSolvedOrders,jdbcType=INTEGER},
            </if>
            <if test="record.maintenanceSolvedExtOrders != null">
                maintenance_solved_ext_orders = #{record.maintenanceSolvedExtOrders,jdbcType=INTEGER},
            </if>
            <if test="record.complaintsSolvedOrders != null">
                complaints_solved_orders = #{record.complaintsSolvedOrders,jdbcType=INTEGER},
            </if>
            <if test="record.complaintsSolvedExtOrders != null">
                complaints_solved_ext_orders = #{record.complaintsSolvedExtOrders,jdbcType=INTEGER},
            </if>
            <if test="record.serviceOrders != null">
                service_orders = #{record.serviceOrders,jdbcType=INTEGER},
            </if>
            <if test="record.totalOrders != null">
                total_orders = #{record.totalOrders,jdbcType=INTEGER},
            </if>
            <if test="record.gdate != null">
                gdate = #{record.gdate,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_complaints_solved_order_all
        set customer_id = #{record.customerId,jdbcType=VARCHAR},
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
        city_code = #{record.cityCode,jdbcType=VARCHAR},
        install_orders = #{record.installOrders,jdbcType=INTEGER},
        maintenance_solved_orders = #{record.maintenanceSolvedOrders,jdbcType=INTEGER},
        maintenance_solved_ext_orders = #{record.maintenanceSolvedExtOrders,jdbcType=INTEGER},
        complaints_solved_orders = #{record.complaintsSolvedOrders,jdbcType=INTEGER},
        complaints_solved_ext_orders = #{record.complaintsSolvedExtOrders,jdbcType=INTEGER},
        service_orders = #{record.serviceOrders,jdbcType=INTEGER},
        total_orders = #{record.totalOrders,jdbcType=INTEGER},
        gdate = #{record.gdate,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.ComplaintsSolvedOrderExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_complaints_solved_order_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>