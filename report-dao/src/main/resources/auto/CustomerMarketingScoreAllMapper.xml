<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.CustomerMarketingScoreAllMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.customer.CustomerMarketingScoreAll">
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_status" jdbcType="VARCHAR" property="customerStatus" />
    <result column="value_category" jdbcType="VARCHAR" property="valueCategory" />
    <result column="score" jdbcType="BIGINT" property="score" />
    <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    enterprise_id, province_code, city_code, customer_id, customer_name, customer_status, 
    value_category, score, sample_time
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.customer.CustomerMarketingScoreAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer_marketing_score_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.customer.CustomerMarketingScoreAllExample">
    delete from t_customer_marketing_score_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.customer.CustomerMarketingScoreAll">
    insert into t_customer_marketing_score_all (enterprise_id, province_code, city_code, 
      customer_id, customer_name, customer_status, 
      value_category, score, sample_time
      )
    values (#{enterpriseId,jdbcType=BIGINT}, #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{customerId,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{customerStatus,jdbcType=VARCHAR}, 
      #{valueCategory,jdbcType=VARCHAR}, #{score,jdbcType=BIGINT}, #{sampleTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.customer.CustomerMarketingScoreAll">
    insert into t_customer_marketing_score_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerStatus != null">
        customer_status,
      </if>
      <if test="valueCategory != null">
        value_category,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="sampleTime != null">
        sample_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerStatus != null">
        #{customerStatus,jdbcType=VARCHAR},
      </if>
      <if test="valueCategory != null">
        #{valueCategory,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        #{score,jdbcType=BIGINT},
      </if>
      <if test="sampleTime != null">
        #{sampleTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.customer.CustomerMarketingScoreAllExample" resultType="java.lang.Long">
    select count(*) from t_customer_marketing_score_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_customer_marketing_score_all
    <set>
      <if test="record.enterpriseId != null">
        enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerName != null">
        customer_name = #{record.customerName,jdbcType=VARCHAR},
      </if>
      <if test="record.customerStatus != null">
        customer_status = #{record.customerStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.valueCategory != null">
        value_category = #{record.valueCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.score != null">
        score = #{record.score,jdbcType=BIGINT},
      </if>
      <if test="record.sampleTime != null">
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_customer_marketing_score_all
    set enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      customer_name = #{record.customerName,jdbcType=VARCHAR},
      customer_status = #{record.customerStatus,jdbcType=VARCHAR},
      value_category = #{record.valueCategory,jdbcType=VARCHAR},
      score = #{record.score,jdbcType=BIGINT},
      sample_time = #{record.sampleTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.customer.CustomerMarketingScoreAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer_marketing_score_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByParam" parameterType="com.cmiot.report.bean.customer.CustomerMarketingScoreQueryParam"
          resultType="com.cmiot.report.bean.customer.CustomerMarketingScoreAll">
    select  enterprise_id as enterpriseId,
            concat(dictGetOrDefault('ge_report.t_dic_area_info', 'gname', province_code, '其他'), '-', dictGetOrDefault('ge_report.t_dic_area_info', 'gname', city_code, '其他')) as areaName,
            province_code as provinceCode,
            city_code as cityCode,
            customer_id as customerId, customer_name as customerName,
            customer_status as customerStatus,
            value_category as valueCategory,
            arrayJoin(groupArray(1)(t1.score)) AS score
    from t_customer_marketing_score_all t1
    where toYYYYMMDD(sample_time) = #{time}
    <if test="provList != null and provList.size > 0">
      and province_code in <foreach collection="provList" index="index" item="itemProv" open="(" separator="," close=")">
            #{itemProv}
    </foreach>
    </if>
    <if test="cityList != null and cityList.size > 0">
      and city_code in <foreach collection="cityList" index="index" item="itemCity" open="(" separator="," close=")">
            #{itemCity}
    </foreach>
    </if>
    <if test="customerName != null and customerName != ''">
      and customer_name like concat('%', #{customerName}, '%')
    </if>
    <if test="customerId != null and customerId != ''">
      and customer_id like concat('%', #{customerId}, '%')
    </if>
    <if test="valueCategoryList != null and valueCategoryList.size > 0">
      and value_category in <foreach collection="valueCategoryList" index="index" item="itemValue" open="(" separator="," close=")">
      #{itemValue}
    </foreach>
    </if>
    <if test="customerStatusList != null and customerStatusList.size > 0">
      and customer_status in <foreach collection="customerStatusList" index="index" item="itemSatus" open="(" separator="," close=")">
      #{itemSatus}
    </foreach>
    </if>
    <if test="scoreStart != null and scoreStart != ''">
      and t1.score &gt;= #{scoreStart}
    </if>
    <if test="scoreEnd != null and scoreEnd != ''">
      and t1.score &lt;= #{scoreEnd}
    </if>
    group by enterprise_id, province_code, city_code , customer_id , customer_name , customer_status , value_category
    <if test="offset != null ">
      limit #{offset}, #{pageSize}
    </if>
  </select>
  <select id="countByParam" parameterType="com.cmiot.report.bean.customer.CustomerMarketingScoreQueryParam"
          resultType="java.lang.Long">
    select count(*) from (
    select  enterprise_id as enterpriseId,
    concat(dictGetOrDefault('ge_report.t_dic_area_info', 'gname', province_code, '其他'), '-',
        dictGetOrDefault('ge_report.t_dic_area_info', 'gname', city_code, '其他')) as areaName,
    province_code as provinceCode,
    city_code as cityCode,
    customer_id as customerId, customer_name as customerName,
    customer_status as customerStatus,
    value_category as valueCategory
    from t_customer_marketing_score_all t1
    where toYYYYMMDD(sample_time) = #{time}
    <if test="provList != null and provList.size > 0">
      and province_code in <foreach collection="provList" index="index" item="itemProv" open="(" separator="," close=")">
      #{itemProv}
    </foreach>
    </if>
    <if test="cityList != null and cityList.size > 0">
      and city_code in <foreach collection="cityList" index="index" item="itemCity" open="(" separator="," close=")">
      #{itemCity}
    </foreach>
    </if>
    <if test="customerName != null and customerName != ''">
      and customer_name like concat('%', #{customerName}, '%')
    </if>
    <if test="customerId != null and customerId != ''">
      and customer_id like concat('%', #{customerId}, '%')
    </if>
    <if test="valueCategoryList != null and valueCategoryList.size > 0">
      and value_category in <foreach collection="valueCategoryList" index="index" item="itemValue" open="(" separator="," close=")">
      #{itemValue}
    </foreach>
    </if>
    <if test="customerStatusList != null and customerStatusList.size > 0">
      and customer_status in <foreach collection="customerStatusList" index="index" item="itemSatus" open="(" separator="," close=")">
      #{itemSatus}
    </foreach>
    </if>
    <if test="scoreStart != null and scoreStart != ''">
      and t1.score &gt;= #{scoreStart}
    </if>
    <if test="scoreEnd != null and scoreEnd != ''">
      and t1.score &lt;= #{scoreEnd}
    </if>
    group by enterprise_id, province_code, city_code , customer_id , customer_name , customer_status , value_category
    ) t1
  </select>
</mapper>