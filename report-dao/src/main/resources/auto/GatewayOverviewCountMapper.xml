<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayOverviewCountMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.GatewayOverviewCount">
    <result column="gateway_count" jdbcType="BIGINT" property="gatewayCount" />
    <result column="gateway_inc_count" jdbcType="BIGINT" property="gatewayIncCount" />
    <result column="gateway_active_count" jdbcType="BIGINT" property="gatewayActiveCount" />
    <result column="gateway_lost_count" jdbcType="BIGINT" property="gatewayLostCount" />
    <result column="gateway_yoy_ratio" jdbcType="FLOAT" property="gatewayYoyRatio" />
    <result column="gateway_mom_ratio" jdbcType="FLOAT" property="gatewayMomRatio" />
    <result column="family_count" jdbcType="BIGINT" property="connectFamilyGatewayNum" />
    <result column="family_rate" jdbcType="FLOAT" property="connectFamilyGatewayRate" />
    <result column="gdate" jdbcType="BIGINT" property="gdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List">
    gateway_count, gateway_inc_count, gateway_active_count, gateway_lost_count, gateway_yoy_ratio,
    gateway_mom_ratio, gdate
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.GatewayOverviewCountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_overview_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

<!--  List<GatewayOverviewCount> selectLastDay(@Param("lastday") Long lastday,
  @Param("provinceCodes")  List<String> provinceCodes);-->
  <select id="selectByDay" resultMap="BaseResultMap">
    select
    gateway_count, gateway_inc_count, gateway_active_count, gateway_lost_count, gateway_yoy_ratio,
    gateway_mom_ratio, gdate, province_code, family_count, family_rate
    from t_gateway_overview_area_count_all
    where gdate = #{day,jdbcType=BIGINT}
    <if test="provinceCodes != null">
        and province_code in
          <foreach collection="provinceCodes" index="index" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
    </if>
  </select>
</mapper>