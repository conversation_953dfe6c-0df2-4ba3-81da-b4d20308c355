<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayPeriodAllMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.GatewayPeriodAll">
    <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn" />
    <result column="gateway_name" jdbcType="VARCHAR" property="gatewayName" />
    <result column="factory_id" jdbcType="BIGINT" property="factoryId" />
    <result column="gateway_vendor" jdbcType="VARCHAR" property="gatewayVendor" />
    <result column="gateway_cpu" jdbcType="VARCHAR" property="gatewayCpu" />
    <result column="gateway_hardware_ver" jdbcType="VARCHAR" property="gatewayHardwareVer" />
    <result column="gateway_firmware_ver" jdbcType="VARCHAR" property="gatewayFirmwareVer" />
    <result column="device_model_id" jdbcType="BIGINT" property="deviceModelId" />
    <result column="gateway_productclass" jdbcType="VARCHAR" property="gatewayProductclass" />
    <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac" />
    <result column="gateway_flash_size" jdbcType="INTEGER" property="gatewayFlashSize" />
    <result column="gateway_ram_size" jdbcType="INTEGER" property="gatewayRamSize" />
    <result column="gateway_nfc" jdbcType="VARCHAR" property="gatewayNfc" />
    <result column="osgi_name" jdbcType="VARCHAR" property="osgiName" />
    <result column="osgi_vendor" jdbcType="VARCHAR" property="osgiVendor" />
    <result column="osgi_version" jdbcType="VARCHAR" property="osgiVersion" />
    <result column="jvm_name" jdbcType="VARCHAR" property="jvmName" />
    <result column="jvm_vendor" jdbcType="VARCHAR" property="jvmVendor" />
    <result column="jvm_version" jdbcType="VARCHAR" property="jvmVersion" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="bundle_version" jdbcType="VARCHAR" property="bundleVersion" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="boot_time" jdbcType="TIMESTAMP" property="bootTime" />
    <result column="runing_time" jdbcType="INTEGER" property="runingTime" />
    <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
    <result column="cpu" jdbcType="TINYINT" property="cpu" />
    <result column="ram" jdbcType="TINYINT" property="ram" />
    <result column="main_chip_temperature" jdbcType="VARCHAR" property="mainChipTemperature" />
    <result column="lan_ip" jdbcType="VARCHAR" property="lanIp" />
    <result column="wan_ip" jdbcType="VARCHAR" property="wanIp" />
    <result column="lan_ipv6" jdbcType="VARCHAR" property="lanIpv6" />
    <result column="wan_ipv6" jdbcType="VARCHAR" property="wanIpv6" />
    <result column="lan1ConnectStatus" jdbcType="VARCHAR" property="lan1connectstatus" />
    <result column="lan2ConnectStatus" jdbcType="VARCHAR" property="lan2connectstatus" />
    <result column="lan3ConnectStatus" jdbcType="VARCHAR" property="lan3connectstatus" />
    <result column="lan4ConnectStatus" jdbcType="VARCHAR" property="lan4connectstatus" />
    <result column="lan5ConnectStatus" jdbcType="VARCHAR" property="lan5connectstatus" />
    <result column="lan6ConnectStatus" jdbcType="VARCHAR" property="lan6connectstatus" />
    <result column="lan7ConnectStatus" jdbcType="VARCHAR" property="lan7connectstatus" />
    <result column="lan8ConnectStatus" jdbcType="VARCHAR" property="lan8connectstatus" />
    <result column="wan" jdbcType="VARCHAR" property="wan" />
    <result column="wifi" jdbcType="VARCHAR" property="wifi" />
    <result column="pppoe_up_time" jdbcType="VARCHAR" property="pppoeUpTime" />
    <result column="pppoe_error" jdbcType="VARCHAR" property="pppoeError" />
    <result column="pppoe_status" jdbcType="VARCHAR" property="pppoeStatus" />
    <result column="pon_tx_power" jdbcType="INTEGER" property="ponTxPower" />
    <result column="pon_rx_power" jdbcType="INTEGER" property="ponRxPower" />
    <result column="transceiver_temperature" jdbcType="INTEGER" property="transceiverTemperature" />
    <result column="wan_index" jdbcType="VARCHAR" property="wanIndex" />
    <result column="wan_name" jdbcType="VARCHAR" property="wanName" />
    <result column="aver_txrate" jdbcType="DECIMAL" property="averTxrate" />
    <result column="aver_rxrate" jdbcType="DECIMAL" property="averRxrate" />
    <result column="max_txrate" jdbcType="DECIMAL" property="maxTxrate" />
    <result column="max_rxrate" jdbcType="DECIMAL" property="maxRxrate" />
    <result column="up_staticstics" jdbcType="DECIMAL" property="upStaticstics" />
    <result column="down_staticstics" jdbcType="DECIMAL" property="downStaticstics" />
    <result column="support" jdbcType="VARCHAR" property="support" />
    <result column="pots_index" jdbcType="VARCHAR" property="potsIndex" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="status" jdbcType="VARCHAR" property="status" />

    <result column="device_num" jdbcType="INTEGER" property="deviceNum" />
    <result column="online_device_num" jdbcType="INTEGER" property="onlineDeviceNum" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    gateway_sn, gateway_name, factory_id, gateway_vendor, gateway_cpu, gateway_hardware_ver, 
    gateway_firmware_ver, device_model_id, gateway_productclass, gateway_mac, gateway_flash_size, 
    gateway_ram_size, gateway_nfc, osgi_name, osgi_vendor, osgi_version, jvm_name, jvm_vendor, 
    jvm_version, account, province_code, city_code, bundle_version, enterprise_id, customer_id, 
    boot_time, runing_time, sample_time, cpu, ram, main_chip_temperature, lan_ip, wan_ip, 
    lan_ipv6, wan_ipv6, lan1ConnectStatus, lan2ConnectStatus, lan3ConnectStatus, lan4ConnectStatus, 
    lan5ConnectStatus, lan6ConnectStatus, lan7ConnectStatus, lan8ConnectStatus, wan, 
    wifi, pppoe_up_time, pppoe_error, pppoe_status, pon_tx_power, pon_rx_power, transceiver_temperature, 
    wan_index, wan_name, aver_txrate, aver_rxrate, max_txrate, max_rxrate, up_staticstics, 
    down_staticstics, support, pots_index, phone_number, status
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.GatewayPeriodAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_period_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.GatewayPeriodAllExample">
    delete from t_gateway_period_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.GatewayPeriodAll">
    insert into t_gateway_period_all (gateway_sn, gateway_name, factory_id, 
      gateway_vendor, gateway_cpu, gateway_hardware_ver, 
      gateway_firmware_ver, device_model_id, gateway_productclass, 
      gateway_mac, gateway_flash_size, gateway_ram_size, 
      gateway_nfc, osgi_name, osgi_vendor, 
      osgi_version, jvm_name, jvm_vendor, 
      jvm_version, account, province_code, 
      city_code, bundle_version, enterprise_id, 
      customer_id, boot_time, runing_time, 
      sample_time, cpu, ram, 
      main_chip_temperature, lan_ip, wan_ip, 
      lan_ipv6, wan_ipv6, lan1ConnectStatus, 
      lan2ConnectStatus, lan3ConnectStatus, lan4ConnectStatus, 
      lan5ConnectStatus, lan6ConnectStatus, lan7ConnectStatus, 
      lan8ConnectStatus, wan, wifi, 
      pppoe_up_time, pppoe_error, pppoe_status, 
      pon_tx_power, pon_rx_power, transceiver_temperature, 
      wan_index, wan_name, aver_txrate, 
      aver_rxrate, max_txrate, max_rxrate, 
      up_staticstics, down_staticstics, support, 
      pots_index, phone_number, status
      )
    values (#{gatewaySn,jdbcType=VARCHAR}, #{gatewayName,jdbcType=VARCHAR}, #{factoryId,jdbcType=BIGINT}, 
      #{gatewayVendor,jdbcType=VARCHAR}, #{gatewayCpu,jdbcType=VARCHAR}, #{gatewayHardwareVer,jdbcType=VARCHAR}, 
      #{gatewayFirmwareVer,jdbcType=VARCHAR}, #{deviceModelId,jdbcType=BIGINT}, #{gatewayProductclass,jdbcType=VARCHAR}, 
      #{gatewayMac,jdbcType=VARCHAR}, #{gatewayFlashSize,jdbcType=INTEGER}, #{gatewayRamSize,jdbcType=INTEGER}, 
      #{gatewayNfc,jdbcType=VARCHAR}, #{osgiName,jdbcType=VARCHAR}, #{osgiVendor,jdbcType=VARCHAR}, 
      #{osgiVersion,jdbcType=VARCHAR}, #{jvmName,jdbcType=VARCHAR}, #{jvmVendor,jdbcType=VARCHAR}, 
      #{jvmVersion,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{bundleVersion,jdbcType=VARCHAR}, #{enterpriseId,jdbcType=BIGINT}, 
      #{customerId,jdbcType=VARCHAR}, #{bootTime,jdbcType=TIMESTAMP}, #{runingTime,jdbcType=INTEGER}, 
      #{sampleTime,jdbcType=TIMESTAMP}, #{cpu,jdbcType=TINYINT}, #{ram,jdbcType=TINYINT}, 
      #{mainChipTemperature,jdbcType=VARCHAR}, #{lanIp,jdbcType=VARCHAR}, #{wanIp,jdbcType=VARCHAR}, 
      #{lanIpv6,jdbcType=VARCHAR}, #{wanIpv6,jdbcType=VARCHAR}, #{lan1connectstatus,jdbcType=VARCHAR}, 
      #{lan2connectstatus,jdbcType=VARCHAR}, #{lan3connectstatus,jdbcType=VARCHAR}, #{lan4connectstatus,jdbcType=VARCHAR}, 
      #{lan5connectstatus,jdbcType=VARCHAR}, #{lan6connectstatus,jdbcType=VARCHAR}, #{lan7connectstatus,jdbcType=VARCHAR}, 
      #{lan8connectstatus,jdbcType=VARCHAR}, #{wan,jdbcType=VARCHAR}, #{wifi,jdbcType=VARCHAR}, 
      #{pppoeUpTime,jdbcType=VARCHAR}, #{pppoeError,jdbcType=VARCHAR}, #{pppoeStatus,jdbcType=VARCHAR}, 
      #{ponTxPower,jdbcType=INTEGER}, #{ponRxPower,jdbcType=INTEGER}, #{transceiverTemperature,jdbcType=INTEGER}, 
      #{wanIndex,jdbcType=VARCHAR}, #{wanName,jdbcType=VARCHAR}, #{averTxrate,jdbcType=DECIMAL}, 
      #{averRxrate,jdbcType=DECIMAL}, #{maxTxrate,jdbcType=DECIMAL}, #{maxRxrate,jdbcType=DECIMAL}, 
      #{upStaticstics,jdbcType=DECIMAL}, #{downStaticstics,jdbcType=DECIMAL}, #{support,jdbcType=VARCHAR}, 
      #{potsIndex,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.GatewayPeriodAll">
    insert into t_gateway_period_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gatewaySn != null">
        gateway_sn,
      </if>
      <if test="gatewayName != null">
        gateway_name,
      </if>
      <if test="factoryId != null">
        factory_id,
      </if>
      <if test="gatewayVendor != null">
        gateway_vendor,
      </if>
      <if test="gatewayCpu != null">
        gateway_cpu,
      </if>
      <if test="gatewayHardwareVer != null">
        gateway_hardware_ver,
      </if>
      <if test="gatewayFirmwareVer != null">
        gateway_firmware_ver,
      </if>
      <if test="deviceModelId != null">
        device_model_id,
      </if>
      <if test="gatewayProductclass != null">
        gateway_productclass,
      </if>
      <if test="gatewayMac != null">
        gateway_mac,
      </if>
      <if test="gatewayFlashSize != null">
        gateway_flash_size,
      </if>
      <if test="gatewayRamSize != null">
        gateway_ram_size,
      </if>
      <if test="gatewayNfc != null">
        gateway_nfc,
      </if>
      <if test="osgiName != null">
        osgi_name,
      </if>
      <if test="osgiVendor != null">
        osgi_vendor,
      </if>
      <if test="osgiVersion != null">
        osgi_version,
      </if>
      <if test="jvmName != null">
        jvm_name,
      </if>
      <if test="jvmVendor != null">
        jvm_vendor,
      </if>
      <if test="jvmVersion != null">
        jvm_version,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="bundleVersion != null">
        bundle_version,
      </if>
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="bootTime != null">
        boot_time,
      </if>
      <if test="runingTime != null">
        runing_time,
      </if>
      <if test="sampleTime != null">
        sample_time,
      </if>
      <if test="cpu != null">
        cpu,
      </if>
      <if test="ram != null">
        ram,
      </if>
      <if test="mainChipTemperature != null">
        main_chip_temperature,
      </if>
      <if test="lanIp != null">
        lan_ip,
      </if>
      <if test="wanIp != null">
        wan_ip,
      </if>
      <if test="lanIpv6 != null">
        lan_ipv6,
      </if>
      <if test="wanIpv6 != null">
        wan_ipv6,
      </if>
      <if test="lan1connectstatus != null">
        lan1ConnectStatus,
      </if>
      <if test="lan2connectstatus != null">
        lan2ConnectStatus,
      </if>
      <if test="lan3connectstatus != null">
        lan3ConnectStatus,
      </if>
      <if test="lan4connectstatus != null">
        lan4ConnectStatus,
      </if>
      <if test="lan5connectstatus != null">
        lan5ConnectStatus,
      </if>
      <if test="lan6connectstatus != null">
        lan6ConnectStatus,
      </if>
      <if test="lan7connectstatus != null">
        lan7ConnectStatus,
      </if>
      <if test="lan8connectstatus != null">
        lan8ConnectStatus,
      </if>
      <if test="wan != null">
        wan,
      </if>
      <if test="wifi != null">
        wifi,
      </if>
      <if test="pppoeUpTime != null">
        pppoe_up_time,
      </if>
      <if test="pppoeError != null">
        pppoe_error,
      </if>
      <if test="pppoeStatus != null">
        pppoe_status,
      </if>
      <if test="ponTxPower != null">
        pon_tx_power,
      </if>
      <if test="ponRxPower != null">
        pon_rx_power,
      </if>
      <if test="transceiverTemperature != null">
        transceiver_temperature,
      </if>
      <if test="wanIndex != null">
        wan_index,
      </if>
      <if test="wanName != null">
        wan_name,
      </if>
      <if test="averTxrate != null">
        aver_txrate,
      </if>
      <if test="averRxrate != null">
        aver_rxrate,
      </if>
      <if test="maxTxrate != null">
        max_txrate,
      </if>
      <if test="maxRxrate != null">
        max_rxrate,
      </if>
      <if test="upStaticstics != null">
        up_staticstics,
      </if>
      <if test="downStaticstics != null">
        down_staticstics,
      </if>
      <if test="support != null">
        support,
      </if>
      <if test="potsIndex != null">
        pots_index,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gatewaySn != null">
        #{gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="gatewayName != null">
        #{gatewayName,jdbcType=VARCHAR},
      </if>
      <if test="factoryId != null">
        #{factoryId,jdbcType=BIGINT},
      </if>
      <if test="gatewayVendor != null">
        #{gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="gatewayCpu != null">
        #{gatewayCpu,jdbcType=VARCHAR},
      </if>
      <if test="gatewayHardwareVer != null">
        #{gatewayHardwareVer,jdbcType=VARCHAR},
      </if>
      <if test="gatewayFirmwareVer != null">
        #{gatewayFirmwareVer,jdbcType=VARCHAR},
      </if>
      <if test="deviceModelId != null">
        #{deviceModelId,jdbcType=BIGINT},
      </if>
      <if test="gatewayProductclass != null">
        #{gatewayProductclass,jdbcType=VARCHAR},
      </if>
      <if test="gatewayMac != null">
        #{gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="gatewayFlashSize != null">
        #{gatewayFlashSize,jdbcType=INTEGER},
      </if>
      <if test="gatewayRamSize != null">
        #{gatewayRamSize,jdbcType=INTEGER},
      </if>
      <if test="gatewayNfc != null">
        #{gatewayNfc,jdbcType=VARCHAR},
      </if>
      <if test="osgiName != null">
        #{osgiName,jdbcType=VARCHAR},
      </if>
      <if test="osgiVendor != null">
        #{osgiVendor,jdbcType=VARCHAR},
      </if>
      <if test="osgiVersion != null">
        #{osgiVersion,jdbcType=VARCHAR},
      </if>
      <if test="jvmName != null">
        #{jvmName,jdbcType=VARCHAR},
      </if>
      <if test="jvmVendor != null">
        #{jvmVendor,jdbcType=VARCHAR},
      </if>
      <if test="jvmVersion != null">
        #{jvmVersion,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="bundleVersion != null">
        #{bundleVersion,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="bootTime != null">
        #{bootTime,jdbcType=TIMESTAMP},
      </if>
      <if test="runingTime != null">
        #{runingTime,jdbcType=INTEGER},
      </if>
      <if test="sampleTime != null">
        #{sampleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cpu != null">
        #{cpu,jdbcType=TINYINT},
      </if>
      <if test="ram != null">
        #{ram,jdbcType=TINYINT},
      </if>
      <if test="mainChipTemperature != null">
        #{mainChipTemperature,jdbcType=VARCHAR},
      </if>
      <if test="lanIp != null">
        #{lanIp,jdbcType=VARCHAR},
      </if>
      <if test="wanIp != null">
        #{wanIp,jdbcType=VARCHAR},
      </if>
      <if test="lanIpv6 != null">
        #{lanIpv6,jdbcType=VARCHAR},
      </if>
      <if test="wanIpv6 != null">
        #{wanIpv6,jdbcType=VARCHAR},
      </if>
      <if test="lan1connectstatus != null">
        #{lan1connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="lan2connectstatus != null">
        #{lan2connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="lan3connectstatus != null">
        #{lan3connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="lan4connectstatus != null">
        #{lan4connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="lan5connectstatus != null">
        #{lan5connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="lan6connectstatus != null">
        #{lan6connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="lan7connectstatus != null">
        #{lan7connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="lan8connectstatus != null">
        #{lan8connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="wan != null">
        #{wan,jdbcType=VARCHAR},
      </if>
      <if test="wifi != null">
        #{wifi,jdbcType=VARCHAR},
      </if>
      <if test="pppoeUpTime != null">
        #{pppoeUpTime,jdbcType=VARCHAR},
      </if>
      <if test="pppoeError != null">
        #{pppoeError,jdbcType=VARCHAR},
      </if>
      <if test="pppoeStatus != null">
        #{pppoeStatus,jdbcType=VARCHAR},
      </if>
      <if test="ponTxPower != null">
        #{ponTxPower,jdbcType=INTEGER},
      </if>
      <if test="ponRxPower != null">
        #{ponRxPower,jdbcType=INTEGER},
      </if>
      <if test="transceiverTemperature != null">
        #{transceiverTemperature,jdbcType=INTEGER},
      </if>
      <if test="wanIndex != null">
        #{wanIndex,jdbcType=VARCHAR},
      </if>
      <if test="wanName != null">
        #{wanName,jdbcType=VARCHAR},
      </if>
      <if test="averTxrate != null">
        #{averTxrate,jdbcType=DECIMAL},
      </if>
      <if test="averRxrate != null">
        #{averRxrate,jdbcType=DECIMAL},
      </if>
      <if test="maxTxrate != null">
        #{maxTxrate,jdbcType=DECIMAL},
      </if>
      <if test="maxRxrate != null">
        #{maxRxrate,jdbcType=DECIMAL},
      </if>
      <if test="upStaticstics != null">
        #{upStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="downStaticstics != null">
        #{downStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="support != null">
        #{support,jdbcType=VARCHAR},
      </if>
      <if test="potsIndex != null">
        #{potsIndex,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.GatewayPeriodAllExample" resultType="java.lang.Long">
    select count(*) from t_gateway_period_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_gateway_period_all
    <set>
      <if test="record.gatewaySn != null">
        gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayName != null">
        gateway_name = #{record.gatewayName,jdbcType=VARCHAR},
      </if>
      <if test="record.factoryId != null">
        factory_id = #{record.factoryId,jdbcType=BIGINT},
      </if>
      <if test="record.gatewayVendor != null">
        gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayCpu != null">
        gateway_cpu = #{record.gatewayCpu,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayHardwareVer != null">
        gateway_hardware_ver = #{record.gatewayHardwareVer,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayFirmwareVer != null">
        gateway_firmware_ver = #{record.gatewayFirmwareVer,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceModelId != null">
        device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
      </if>
      <if test="record.gatewayProductclass != null">
        gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayMac != null">
        gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayFlashSize != null">
        gateway_flash_size = #{record.gatewayFlashSize,jdbcType=INTEGER},
      </if>
      <if test="record.gatewayRamSize != null">
        gateway_ram_size = #{record.gatewayRamSize,jdbcType=INTEGER},
      </if>
      <if test="record.gatewayNfc != null">
        gateway_nfc = #{record.gatewayNfc,jdbcType=VARCHAR},
      </if>
      <if test="record.osgiName != null">
        osgi_name = #{record.osgiName,jdbcType=VARCHAR},
      </if>
      <if test="record.osgiVendor != null">
        osgi_vendor = #{record.osgiVendor,jdbcType=VARCHAR},
      </if>
      <if test="record.osgiVersion != null">
        osgi_version = #{record.osgiVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.jvmName != null">
        jvm_name = #{record.jvmName,jdbcType=VARCHAR},
      </if>
      <if test="record.jvmVendor != null">
        jvm_vendor = #{record.jvmVendor,jdbcType=VARCHAR},
      </if>
      <if test="record.jvmVersion != null">
        jvm_version = #{record.jvmVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.account != null">
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bundleVersion != null">
        bundle_version = #{record.bundleVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.enterpriseId != null">
        enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.bootTime != null">
        boot_time = #{record.bootTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.runingTime != null">
        runing_time = #{record.runingTime,jdbcType=INTEGER},
      </if>
      <if test="record.sampleTime != null">
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cpu != null">
        cpu = #{record.cpu,jdbcType=TINYINT},
      </if>
      <if test="record.ram != null">
        ram = #{record.ram,jdbcType=TINYINT},
      </if>
      <if test="record.mainChipTemperature != null">
        main_chip_temperature = #{record.mainChipTemperature,jdbcType=VARCHAR},
      </if>
      <if test="record.lanIp != null">
        lan_ip = #{record.lanIp,jdbcType=VARCHAR},
      </if>
      <if test="record.wanIp != null">
        wan_ip = #{record.wanIp,jdbcType=VARCHAR},
      </if>
      <if test="record.lanIpv6 != null">
        lan_ipv6 = #{record.lanIpv6,jdbcType=VARCHAR},
      </if>
      <if test="record.wanIpv6 != null">
        wan_ipv6 = #{record.wanIpv6,jdbcType=VARCHAR},
      </if>
      <if test="record.lan1connectstatus != null">
        lan1ConnectStatus = #{record.lan1connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.lan2connectstatus != null">
        lan2ConnectStatus = #{record.lan2connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.lan3connectstatus != null">
        lan3ConnectStatus = #{record.lan3connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.lan4connectstatus != null">
        lan4ConnectStatus = #{record.lan4connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.lan5connectstatus != null">
        lan5ConnectStatus = #{record.lan5connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.lan6connectstatus != null">
        lan6ConnectStatus = #{record.lan6connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.lan7connectstatus != null">
        lan7ConnectStatus = #{record.lan7connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.lan8connectstatus != null">
        lan8ConnectStatus = #{record.lan8connectstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.wan != null">
        wan = #{record.wan,jdbcType=VARCHAR},
      </if>
      <if test="record.wifi != null">
        wifi = #{record.wifi,jdbcType=VARCHAR},
      </if>
      <if test="record.pppoeUpTime != null">
        pppoe_up_time = #{record.pppoeUpTime,jdbcType=VARCHAR},
      </if>
      <if test="record.pppoeError != null">
        pppoe_error = #{record.pppoeError,jdbcType=VARCHAR},
      </if>
      <if test="record.pppoeStatus != null">
        pppoe_status = #{record.pppoeStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.ponTxPower != null">
        pon_tx_power = #{record.ponTxPower,jdbcType=INTEGER},
      </if>
      <if test="record.ponRxPower != null">
        pon_rx_power = #{record.ponRxPower,jdbcType=INTEGER},
      </if>
      <if test="record.transceiverTemperature != null">
        transceiver_temperature = #{record.transceiverTemperature,jdbcType=INTEGER},
      </if>
      <if test="record.wanIndex != null">
        wan_index = #{record.wanIndex,jdbcType=VARCHAR},
      </if>
      <if test="record.wanName != null">
        wan_name = #{record.wanName,jdbcType=VARCHAR},
      </if>
      <if test="record.averTxrate != null">
        aver_txrate = #{record.averTxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.averRxrate != null">
        aver_rxrate = #{record.averRxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.maxTxrate != null">
        max_txrate = #{record.maxTxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.maxRxrate != null">
        max_rxrate = #{record.maxRxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.upStaticstics != null">
        up_staticstics = #{record.upStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="record.downStaticstics != null">
        down_staticstics = #{record.downStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="record.support != null">
        support = #{record.support,jdbcType=VARCHAR},
      </if>
      <if test="record.potsIndex != null">
        pots_index = #{record.potsIndex,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNumber != null">
        phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_gateway_period_all
    set gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      gateway_name = #{record.gatewayName,jdbcType=VARCHAR},
      factory_id = #{record.factoryId,jdbcType=BIGINT},
      gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      gateway_cpu = #{record.gatewayCpu,jdbcType=VARCHAR},
      gateway_hardware_ver = #{record.gatewayHardwareVer,jdbcType=VARCHAR},
      gateway_firmware_ver = #{record.gatewayFirmwareVer,jdbcType=VARCHAR},
      device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
      gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
      gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      gateway_flash_size = #{record.gatewayFlashSize,jdbcType=INTEGER},
      gateway_ram_size = #{record.gatewayRamSize,jdbcType=INTEGER},
      gateway_nfc = #{record.gatewayNfc,jdbcType=VARCHAR},
      osgi_name = #{record.osgiName,jdbcType=VARCHAR},
      osgi_vendor = #{record.osgiVendor,jdbcType=VARCHAR},
      osgi_version = #{record.osgiVersion,jdbcType=VARCHAR},
      jvm_name = #{record.jvmName,jdbcType=VARCHAR},
      jvm_vendor = #{record.jvmVendor,jdbcType=VARCHAR},
      jvm_version = #{record.jvmVersion,jdbcType=VARCHAR},
      account = #{record.account,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      bundle_version = #{record.bundleVersion,jdbcType=VARCHAR},
      enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      boot_time = #{record.bootTime,jdbcType=TIMESTAMP},
      runing_time = #{record.runingTime,jdbcType=INTEGER},
      sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      cpu = #{record.cpu,jdbcType=TINYINT},
      ram = #{record.ram,jdbcType=TINYINT},
      main_chip_temperature = #{record.mainChipTemperature,jdbcType=VARCHAR},
      lan_ip = #{record.lanIp,jdbcType=VARCHAR},
      wan_ip = #{record.wanIp,jdbcType=VARCHAR},
      lan_ipv6 = #{record.lanIpv6,jdbcType=VARCHAR},
      wan_ipv6 = #{record.wanIpv6,jdbcType=VARCHAR},
      lan1ConnectStatus = #{record.lan1connectstatus,jdbcType=VARCHAR},
      lan2ConnectStatus = #{record.lan2connectstatus,jdbcType=VARCHAR},
      lan3ConnectStatus = #{record.lan3connectstatus,jdbcType=VARCHAR},
      lan4ConnectStatus = #{record.lan4connectstatus,jdbcType=VARCHAR},
      lan5ConnectStatus = #{record.lan5connectstatus,jdbcType=VARCHAR},
      lan6ConnectStatus = #{record.lan6connectstatus,jdbcType=VARCHAR},
      lan7ConnectStatus = #{record.lan7connectstatus,jdbcType=VARCHAR},
      lan8ConnectStatus = #{record.lan8connectstatus,jdbcType=VARCHAR},
      wan = #{record.wan,jdbcType=VARCHAR},
      wifi = #{record.wifi,jdbcType=VARCHAR},
      pppoe_up_time = #{record.pppoeUpTime,jdbcType=VARCHAR},
      pppoe_error = #{record.pppoeError,jdbcType=VARCHAR},
      pppoe_status = #{record.pppoeStatus,jdbcType=VARCHAR},
      pon_tx_power = #{record.ponTxPower,jdbcType=INTEGER},
      pon_rx_power = #{record.ponRxPower,jdbcType=INTEGER},
      transceiver_temperature = #{record.transceiverTemperature,jdbcType=INTEGER},
      wan_index = #{record.wanIndex,jdbcType=VARCHAR},
      wan_name = #{record.wanName,jdbcType=VARCHAR},
      aver_txrate = #{record.averTxrate,jdbcType=DECIMAL},
      aver_rxrate = #{record.averRxrate,jdbcType=DECIMAL},
      max_txrate = #{record.maxTxrate,jdbcType=DECIMAL},
      max_rxrate = #{record.maxRxrate,jdbcType=DECIMAL},
      up_staticstics = #{record.upStaticstics,jdbcType=DECIMAL},
      down_staticstics = #{record.downStaticstics,jdbcType=DECIMAL},
      support = #{record.support,jdbcType=VARCHAR},
      pots_index = #{record.potsIndex,jdbcType=VARCHAR},
      phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.GatewayPeriodAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_period_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectContinuousRunningOverTimeDevice" resultType="com.cmiot.report.bean.GatewayPeriodAll">
    select t.gateway_sn as gatewaySn, t.gateway_mac as gatewayMac,
           t.nowRunningTime as runingTime
                            from
                                 (select gateway_sn, gateway_mac, argMax(runing_time, sample_time) as nowRunningTime
                                 from t_gateway_period_detail
                                 where toYYYYMMDDhhmmss(sample_time) &gt;= #{timePreStr}
                                   and enterprise_id  = #{eid}
                                 group by gateway_mac, gateway_sn) t
    where t.nowRunningTime > toUInt32(#{continuousRunningTimeLimit}) * 3600
    order by t.nowRunningTime desc

  </select>

  <select id="getLastPeriodInfo" resultType="com.cmiot.report.bean.GatewayPeriodAll">
    select
    gateway_sn as gatewaySn, gateway_mac as gatewayMac, argMax(device_num, sample_time) AS deviceNum,
    argMax(online_device_num, sample_time) AS onlineDeviceNum
    from t_gateway_period_detail
--     where subtractHours(sample_time, -${finalperiodDetailQueryLimitHour}) > toDate(now())
    WHERE sample_time > now() - INTERVAL ${finalperiodDetailQueryLimitHour} HOUR
    and  gateway_sn in
    <foreach collection="gatewaySns" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    group by gateway_sn, gateway_mac

  </select>

</mapper>