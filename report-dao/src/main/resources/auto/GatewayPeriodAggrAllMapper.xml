<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayPeriodAggrAllMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.GatewayPeriodAggrAll">
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn" />
    <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac" />
    <result column="gateway_productclass_id" jdbcType="BIGINT" property="gatewayProductclassId" />
    <result column="gateway_productclass" jdbcType="VARCHAR" property="gatewayProductclass" />
    <result column="gateway_vendor_id" jdbcType="BIGINT" property="gatewayVendorId" />
    <result column="gateway_vendor" jdbcType="VARCHAR" property="gatewayVendor" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="max_txrate" jdbcType="DECIMAL" property="maxTxrate" />
    <result column="aver_txrate" jdbcType="DECIMAL" property="averTxrate" />
    <result column="max_rxrate" jdbcType="DECIMAL" property="maxRxrate" />
    <result column="aver_rxrate" jdbcType="DECIMAL" property="averRxrate" />
    <result column="down_staticstics" jdbcType="DECIMAL" property="downStaticstics" />
    <result column="up_staticstics" jdbcType="DECIMAL" property="upStaticstics" />
    <result column="runing_time" jdbcType="DECIMAL" property="runingTime" />
    <result column="cpu_rate_max" jdbcType="DECIMAL" property="cpuRateMax" />
    <result column="cpu_rate_max_time" jdbcType="TIMESTAMP" property="cpuRateMaxTime" />
    <result column="cpu_rate_avg" jdbcType="DECIMAL" property="cpuRateAvg" />
    <result column="ram_rate_max" jdbcType="DECIMAL" property="ramRateMax" />
    <result column="ram_rate_max_time" jdbcType="TIMESTAMP" property="ramRateMaxTime" />
    <result column="ram_rate_avg" jdbcType="DECIMAL" property="ramRateAvg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    record_time, enterprise_id, customer_id, customer_name, gateway_sn, gateway_mac, 
    gateway_productclass_id, gateway_productclass, gateway_vendor_id, gateway_vendor, 
    province_code, city_code, max_txrate, aver_txrate, max_rxrate, aver_rxrate, down_staticstics, 
    up_staticstics, runing_time, cpu_rate_max, cpu_rate_max_time, cpu_rate_avg, ram_rate_max, 
    ram_rate_max_time, ram_rate_avg
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.GatewayPeriodAggrAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_period_aggr_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.GatewayPeriodAggrAllExample">
    delete from t_gateway_period_aggr_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.GatewayPeriodAggrAll">
    insert into t_gateway_period_aggr_all (record_time, enterprise_id, customer_id, 
      customer_name, gateway_sn, gateway_mac, 
      gateway_productclass_id, gateway_productclass, 
      gateway_vendor_id, gateway_vendor, province_code, 
      city_code, max_txrate, aver_txrate, 
      max_rxrate, aver_rxrate, down_staticstics, 
      up_staticstics, runing_time, cpu_rate_max, 
      cpu_rate_max_time, cpu_rate_avg, ram_rate_max, 
      ram_rate_max_time, ram_rate_avg)
    values (#{recordTime,jdbcType=TIMESTAMP}, #{enterpriseId,jdbcType=BIGINT}, #{customerId,jdbcType=VARCHAR}, 
      #{customerName,jdbcType=VARCHAR}, #{gatewaySn,jdbcType=VARCHAR}, #{gatewayMac,jdbcType=VARCHAR}, 
      #{gatewayProductclassId,jdbcType=BIGINT}, #{gatewayProductclass,jdbcType=VARCHAR}, 
      #{gatewayVendorId,jdbcType=BIGINT}, #{gatewayVendor,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{maxTxrate,jdbcType=DECIMAL}, #{averTxrate,jdbcType=DECIMAL}, 
      #{maxRxrate,jdbcType=DECIMAL}, #{averRxrate,jdbcType=DECIMAL}, #{downStaticstics,jdbcType=DECIMAL}, 
      #{upStaticstics,jdbcType=DECIMAL}, #{runingTime,jdbcType=DECIMAL}, #{cpuRateMax,jdbcType=DECIMAL}, 
      #{cpuRateMaxTime,jdbcType=TIMESTAMP}, #{cpuRateAvg,jdbcType=DECIMAL}, #{ramRateMax,jdbcType=DECIMAL}, 
      #{ramRateMaxTime,jdbcType=TIMESTAMP}, #{ramRateAvg,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.GatewayPeriodAggrAll">
    insert into t_gateway_period_aggr_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordTime != null">
        record_time,
      </if>
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="gatewaySn != null">
        gateway_sn,
      </if>
      <if test="gatewayMac != null">
        gateway_mac,
      </if>
      <if test="gatewayProductclassId != null">
        gateway_productclass_id,
      </if>
      <if test="gatewayProductclass != null">
        gateway_productclass,
      </if>
      <if test="gatewayVendorId != null">
        gateway_vendor_id,
      </if>
      <if test="gatewayVendor != null">
        gateway_vendor,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="maxTxrate != null">
        max_txrate,
      </if>
      <if test="averTxrate != null">
        aver_txrate,
      </if>
      <if test="maxRxrate != null">
        max_rxrate,
      </if>
      <if test="averRxrate != null">
        aver_rxrate,
      </if>
      <if test="downStaticstics != null">
        down_staticstics,
      </if>
      <if test="upStaticstics != null">
        up_staticstics,
      </if>
      <if test="runingTime != null">
        runing_time,
      </if>
      <if test="cpuRateMax != null">
        cpu_rate_max,
      </if>
      <if test="cpuRateMaxTime != null">
        cpu_rate_max_time,
      </if>
      <if test="cpuRateAvg != null">
        cpu_rate_avg,
      </if>
      <if test="ramRateMax != null">
        ram_rate_max,
      </if>
      <if test="ramRateMaxTime != null">
        ram_rate_max_time,
      </if>
      <if test="ramRateAvg != null">
        ram_rate_avg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="gatewaySn != null">
        #{gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="gatewayMac != null">
        #{gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="gatewayProductclassId != null">
        #{gatewayProductclassId,jdbcType=BIGINT},
      </if>
      <if test="gatewayProductclass != null">
        #{gatewayProductclass,jdbcType=VARCHAR},
      </if>
      <if test="gatewayVendorId != null">
        #{gatewayVendorId,jdbcType=BIGINT},
      </if>
      <if test="gatewayVendor != null">
        #{gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="maxTxrate != null">
        #{maxTxrate,jdbcType=DECIMAL},
      </if>
      <if test="averTxrate != null">
        #{averTxrate,jdbcType=DECIMAL},
      </if>
      <if test="maxRxrate != null">
        #{maxRxrate,jdbcType=DECIMAL},
      </if>
      <if test="averRxrate != null">
        #{averRxrate,jdbcType=DECIMAL},
      </if>
      <if test="downStaticstics != null">
        #{downStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="upStaticstics != null">
        #{upStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="runingTime != null">
        #{runingTime,jdbcType=DECIMAL},
      </if>
      <if test="cpuRateMax != null">
        #{cpuRateMax,jdbcType=DECIMAL},
      </if>
      <if test="cpuRateMaxTime != null">
        #{cpuRateMaxTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cpuRateAvg != null">
        #{cpuRateAvg,jdbcType=DECIMAL},
      </if>
      <if test="ramRateMax != null">
        #{ramRateMax,jdbcType=DECIMAL},
      </if>
      <if test="ramRateMaxTime != null">
        #{ramRateMaxTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ramRateAvg != null">
        #{ramRateAvg,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.GatewayPeriodAggrAllExample" resultType="java.lang.Long">
    select count(*) from t_gateway_period_aggr_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_gateway_period_aggr_all
    <set>
      <if test="record.recordTime != null">
        record_time = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enterpriseId != null">
        enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerName != null">
        customer_name = #{record.customerName,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewaySn != null">
        gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayMac != null">
        gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayProductclassId != null">
        gateway_productclass_id = #{record.gatewayProductclassId,jdbcType=BIGINT},
      </if>
      <if test="record.gatewayProductclass != null">
        gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayVendorId != null">
        gateway_vendor_id = #{record.gatewayVendorId,jdbcType=BIGINT},
      </if>
      <if test="record.gatewayVendor != null">
        gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.maxTxrate != null">
        max_txrate = #{record.maxTxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.averTxrate != null">
        aver_txrate = #{record.averTxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.maxRxrate != null">
        max_rxrate = #{record.maxRxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.averRxrate != null">
        aver_rxrate = #{record.averRxrate,jdbcType=DECIMAL},
      </if>
      <if test="record.downStaticstics != null">
        down_staticstics = #{record.downStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="record.upStaticstics != null">
        up_staticstics = #{record.upStaticstics,jdbcType=DECIMAL},
      </if>
      <if test="record.runingTime != null">
        runing_time = #{record.runingTime,jdbcType=DECIMAL},
      </if>
      <if test="record.cpuRateMax != null">
        cpu_rate_max = #{record.cpuRateMax,jdbcType=DECIMAL},
      </if>
      <if test="record.cpuRateMaxTime != null">
        cpu_rate_max_time = #{record.cpuRateMaxTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cpuRateAvg != null">
        cpu_rate_avg = #{record.cpuRateAvg,jdbcType=DECIMAL},
      </if>
      <if test="record.ramRateMax != null">
        ram_rate_max = #{record.ramRateMax,jdbcType=DECIMAL},
      </if>
      <if test="record.ramRateMaxTime != null">
        ram_rate_max_time = #{record.ramRateMaxTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ramRateAvg != null">
        ram_rate_avg = #{record.ramRateAvg,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_gateway_period_aggr_all
    set record_time = #{record.recordTime,jdbcType=TIMESTAMP},
      enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      customer_name = #{record.customerName,jdbcType=VARCHAR},
      gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      gateway_productclass_id = #{record.gatewayProductclassId,jdbcType=BIGINT},
      gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
      gateway_vendor_id = #{record.gatewayVendorId,jdbcType=BIGINT},
      gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      max_txrate = #{record.maxTxrate,jdbcType=DECIMAL},
      aver_txrate = #{record.averTxrate,jdbcType=DECIMAL},
      max_rxrate = #{record.maxRxrate,jdbcType=DECIMAL},
      aver_rxrate = #{record.averRxrate,jdbcType=DECIMAL},
      down_staticstics = #{record.downStaticstics,jdbcType=DECIMAL},
      up_staticstics = #{record.upStaticstics,jdbcType=DECIMAL},
      runing_time = #{record.runingTime,jdbcType=DECIMAL},
      cpu_rate_max = #{record.cpuRateMax,jdbcType=DECIMAL},
      cpu_rate_max_time = #{record.cpuRateMaxTime,jdbcType=TIMESTAMP},
      cpu_rate_avg = #{record.cpuRateAvg,jdbcType=DECIMAL},
      ram_rate_max = #{record.ramRateMax,jdbcType=DECIMAL},
      ram_rate_max_time = #{record.ramRateMaxTime,jdbcType=TIMESTAMP},
      ram_rate_avg = #{record.ramRateAvg,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.GatewayPeriodAggrAllExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_period_aggr_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>