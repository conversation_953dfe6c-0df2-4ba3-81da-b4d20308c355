<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DeviceCumulativeAllMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.DeviceCumulativeAll">
        <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn"/>
        <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac"/>
        <result column="factory_id" jdbcType="BIGINT" property="factoryId"/>
        <result column="gateway_vendor" jdbcType="VARCHAR" property="gatewayVendor"/>
        <result column="device_model_id" jdbcType="BIGINT" property="deviceModelId"/>
        <result column="gateway_productclass" jdbcType="VARCHAR" property="gatewayProductclass"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="account" jdbcType="VARCHAR" property="account"/>
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="dhcp_name" jdbcType="VARCHAR" property="dhcpName"/>
        <result column="mac" jdbcType="VARCHAR" property="mac"/>
        <result column="wifi_name" jdbcType="VARCHAR" property="wifiName"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="ipv6" jdbcType="VARCHAR" property="ipv6"/>
        <result column="wlan_radio_type" jdbcType="VARCHAR" property="wlanRadioType"/>
        <result column="wlan_radio_power" jdbcType="INTEGER" property="wlanRadioPower"/>
        <result column="lan_port" jdbcType="VARCHAR" property="lanPort"/>
        <result column="lan_bit_rate" jdbcType="BIGINT" property="lanBitRate"/>
        <result column="freq" jdbcType="BIGINT" property="freq"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="last_time" jdbcType="TIMESTAMP" property="lastTime"/>
        <result column="pdate" jdbcType="TIMESTAMP" property="pdate"/>
    </resultMap>

    <resultMap id="ListResultMap" type="com.cmiot.report.dto.SubDeviceList">
        <result column="province_name" jdbcType="VARCHAR" property="province"/>
        <result column="city_name" jdbcType="VARCHAR" property="city"/>
        <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn"/>
        <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac"/>
        <result column="gateway_vendor" jdbcType="VARCHAR" property="gatewayVendor"/>
        <result column="gateway_productclass" jdbcType="VARCHAR" property="gatewayModel"/>
        <result column="device_name" jdbcType="VARCHAR" property="hangingDeviceName"/>
        <result column="mac" jdbcType="VARCHAR" property="hangingDeviceMac"/>
        <result column="start_time" jdbcType="VARCHAR" property="firstConnectTime"/>
        <result column="last_time" jdbcType="VARCHAR" property="lastConnectTime"/>
        <result column="wifi_name" jdbcType="VARCHAR" property="connectWifiName"/>
        <result column="ip" jdbcType="VARCHAR" property="hangingDeviceIp"/>
        <result column="wlan_radio_power" jdbcType="VARCHAR" property="signalIntensity"/>
        <result column="lan_port" jdbcType="VARCHAR" property="lanPort"/>
        <result column="freq" jdbcType="VARCHAR" property="hangingDeviceTimes"/>
        <result column="lan_bit_rate" jdbcType="VARCHAR" property="lanPortPlanRate"/>
        <result column="sub_model" jdbcType="VARCHAR" property="hangingDeviceModel"/>
        <result column="sub_factory" jdbcType="VARCHAR" property="hangingDeviceVendor"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        gateway_sn, gateway_mac, factory_id, gateway_vendor, device_model_id, gateway_productclass,
        province_code, city_code, enterprise_id, customer_id, account, sample_time, device_name,
        dhcp_name, mac, wifi_name, ip, ipv6, wlan_radio_type, wlan_radio_power, lan_port,
        lan_bit_rate, freq, start_time, last_time, pdate
    </sql>

    <sql id="Query_List_Column">
        province_code,city_code,gateway_sn,gateway_mac,factory_id,gateway_productclass,device_name,mac,cast(start_time
        as String) as start_time,cast(last_time as String) as last_time,wifi_name,ip,cast(wlan_radio_power as String) as
        wlan_radio_power,lan_port,cast(freq as String) as freq,cast(lan_bit_rate as String) as lan_bit_rate,''
        as sub_model,'' as sub_factory,pdate
    </sql>


    <select id="selectListByExample" parameterType="com.cmiot.report.bean.DeviceAllExample" resultMap="ListResultMap">
        select
        if(isNotNull(pinfo.gname),pinfo.gname,'其他') as province_name,if(isNotNull(cinfo.gname),cinfo.gname,'其他') as
        city_name,gateway_sn,gateway_mac,dictGetOrDefault('ge_report.t_dic_factory', 'factory_name', factory_id,'其他') as
        gateway_vendor,gateway_productclass,device_name,mac,start_time,last_time,wifi_name,ip,wlan_radio_power,lan_port,freq,lan_bit_rate,sub_model,sub_factory
        from (select tt.* from (select
        <if test="distinct">
            distinct
        </if>
        <include refid="Query_List_Column"/>
         from t_device_cumulative_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>) tt where toYYYYMMDD(tt.pdate)=toYYYYMMDD(addDays(now(), -1))) t1 left join (select gname,gcode
        from t_dic_area_info where level=1) pinfo on
        t1.province_code=pinfo.gcode left join (select gname,gcode from t_dic_area_info where level=2) cinfo on
        t1.city_code=cinfo.gcode

        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>

    </select>


    <select id="selectByExample" parameterType="com.cmiot.report.bean.DeviceCumulativeAllExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_device_cumulative_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.DeviceCumulativeAllExample">
        delete from t_device_cumulative_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.DeviceCumulativeAll">
        insert into t_device_cumulative_all (gateway_sn, gateway_mac, factory_id,
        gateway_vendor, device_model_id, gateway_productclass,
        province_code, city_code, enterprise_id,
        customer_id, account, sample_time,
        device_name, dhcp_name, mac,
        wifi_name, ip, ipv6,
        wlan_radio_type, wlan_radio_power, lan_port,
        lan_bit_rate, freq, start_time,
        last_time, pdate)
        values (#{gatewaySn,jdbcType=VARCHAR}, #{gatewayMac,jdbcType=VARCHAR}, #{factoryId,jdbcType=BIGINT},
        #{gatewayVendor,jdbcType=VARCHAR}, #{deviceModelId,jdbcType=BIGINT}, #{gatewayProductclass,jdbcType=VARCHAR},
        #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{enterpriseId,jdbcType=BIGINT},
        #{customerId,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{sampleTime,jdbcType=TIMESTAMP},
        #{deviceName,jdbcType=VARCHAR}, #{dhcpName,jdbcType=VARCHAR}, #{mac,jdbcType=VARCHAR},
        #{wifiName,jdbcType=VARCHAR}, #{ip,jdbcType=VARCHAR}, #{ipv6,jdbcType=VARCHAR},
        #{wlanRadioType,jdbcType=VARCHAR}, #{wlanRadioPower,jdbcType=INTEGER}, #{lanPort,jdbcType=VARCHAR},
        #{lanBitRate,jdbcType=BIGINT}, #{freq,jdbcType=BIGINT}, #{startTime,jdbcType=TIMESTAMP},
        #{lastTime,jdbcType=TIMESTAMP}, #{pdate,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.DeviceCumulativeAll">
        insert into t_device_cumulative_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="gatewaySn != null">
                gateway_sn,
            </if>
            <if test="gatewayMac != null">
                gateway_mac,
            </if>
            <if test="factoryId != null">
                factory_id,
            </if>
            <if test="gatewayVendor != null">
                gateway_vendor,
            </if>
            <if test="deviceModelId != null">
                device_model_id,
            </if>
            <if test="gatewayProductclass != null">
                gateway_productclass,
            </if>
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="enterpriseId != null">
                enterprise_id,
            </if>
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="account != null">
                account,
            </if>
            <if test="sampleTime != null">
                sample_time,
            </if>
            <if test="deviceName != null">
                device_name,
            </if>
            <if test="dhcpName != null">
                dhcp_name,
            </if>
            <if test="mac != null">
                mac,
            </if>
            <if test="wifiName != null">
                wifi_name,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="ipv6 != null">
                ipv6,
            </if>
            <if test="wlanRadioType != null">
                wlan_radio_type,
            </if>
            <if test="wlanRadioPower != null">
                wlan_radio_power,
            </if>
            <if test="lanPort != null">
                lan_port,
            </if>
            <if test="lanBitRate != null">
                lan_bit_rate,
            </if>
            <if test="freq != null">
                freq,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="lastTime != null">
                last_time,
            </if>
            <if test="pdate != null">
                pdate,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="gatewaySn != null">
                #{gatewaySn,jdbcType=VARCHAR},
            </if>
            <if test="gatewayMac != null">
                #{gatewayMac,jdbcType=VARCHAR},
            </if>
            <if test="factoryId != null">
                #{factoryId,jdbcType=BIGINT},
            </if>
            <if test="gatewayVendor != null">
                #{gatewayVendor,jdbcType=VARCHAR},
            </if>
            <if test="deviceModelId != null">
                #{deviceModelId,jdbcType=BIGINT},
            </if>
            <if test="gatewayProductclass != null">
                #{gatewayProductclass,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="enterpriseId != null">
                #{enterpriseId,jdbcType=BIGINT},
            </if>
            <if test="customerId != null">
                #{customerId,jdbcType=VARCHAR},
            </if>
            <if test="account != null">
                #{account,jdbcType=VARCHAR},
            </if>
            <if test="sampleTime != null">
                #{sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deviceName != null">
                #{deviceName,jdbcType=VARCHAR},
            </if>
            <if test="dhcpName != null">
                #{dhcpName,jdbcType=VARCHAR},
            </if>
            <if test="mac != null">
                #{mac,jdbcType=VARCHAR},
            </if>
            <if test="wifiName != null">
                #{wifiName,jdbcType=VARCHAR},
            </if>
            <if test="ip != null">
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="ipv6 != null">
                #{ipv6,jdbcType=VARCHAR},
            </if>
            <if test="wlanRadioType != null">
                #{wlanRadioType,jdbcType=VARCHAR},
            </if>
            <if test="wlanRadioPower != null">
                #{wlanRadioPower,jdbcType=INTEGER},
            </if>
            <if test="lanPort != null">
                #{lanPort,jdbcType=VARCHAR},
            </if>
            <if test="lanBitRate != null">
                #{lanBitRate,jdbcType=BIGINT},
            </if>
            <if test="freq != null">
                #{freq,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastTime != null">
                #{lastTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pdate != null">
                #{pdate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.DeviceCumulativeAllExample"
            resultType="java.lang.Long">
        select count(*) from t_device_cumulative_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_device_cumulative_all
        <set>
            <if test="record.gatewaySn != null">
                gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
            </if>
            <if test="record.gatewayMac != null">
                gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
            </if>
            <if test="record.factoryId != null">
                factory_id = #{record.factoryId,jdbcType=BIGINT},
            </if>
            <if test="record.gatewayVendor != null">
                gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
            </if>
            <if test="record.deviceModelId != null">
                device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
            </if>
            <if test="record.gatewayProductclass != null">
                gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
            </if>
            <if test="record.provinceCode != null">
                province_code = #{record.provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="record.cityCode != null">
                city_code = #{record.cityCode,jdbcType=VARCHAR},
            </if>
            <if test="record.enterpriseId != null">
                enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
            </if>
            <if test="record.customerId != null">
                customer_id = #{record.customerId,jdbcType=VARCHAR},
            </if>
            <if test="record.account != null">
                account = #{record.account,jdbcType=VARCHAR},
            </if>
            <if test="record.sampleTime != null">
                sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.deviceName != null">
                device_name = #{record.deviceName,jdbcType=VARCHAR},
            </if>
            <if test="record.dhcpName != null">
                dhcp_name = #{record.dhcpName,jdbcType=VARCHAR},
            </if>
            <if test="record.mac != null">
                mac = #{record.mac,jdbcType=VARCHAR},
            </if>
            <if test="record.wifiName != null">
                wifi_name = #{record.wifiName,jdbcType=VARCHAR},
            </if>
            <if test="record.ip != null">
                ip = #{record.ip,jdbcType=VARCHAR},
            </if>
            <if test="record.ipv6 != null">
                ipv6 = #{record.ipv6,jdbcType=VARCHAR},
            </if>
            <if test="record.wlanRadioType != null">
                wlan_radio_type = #{record.wlanRadioType,jdbcType=VARCHAR},
            </if>
            <if test="record.wlanRadioPower != null">
                wlan_radio_power = #{record.wlanRadioPower,jdbcType=INTEGER},
            </if>
            <if test="record.lanPort != null">
                lan_port = #{record.lanPort,jdbcType=VARCHAR},
            </if>
            <if test="record.lanBitRate != null">
                lan_bit_rate = #{record.lanBitRate,jdbcType=BIGINT},
            </if>
            <if test="record.freq != null">
                freq = #{record.freq,jdbcType=BIGINT},
            </if>
            <if test="record.startTime != null">
                start_time = #{record.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.lastTime != null">
                last_time = #{record.lastTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.pdate != null">
                pdate = #{record.pdate,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_device_cumulative_all
        set gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
        gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
        factory_id = #{record.factoryId,jdbcType=BIGINT},
        gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
        device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
        gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
        city_code = #{record.cityCode,jdbcType=VARCHAR},
        enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
        customer_id = #{record.customerId,jdbcType=VARCHAR},
        account = #{record.account,jdbcType=VARCHAR},
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
        device_name = #{record.deviceName,jdbcType=VARCHAR},
        dhcp_name = #{record.dhcpName,jdbcType=VARCHAR},
        mac = #{record.mac,jdbcType=VARCHAR},
        wifi_name = #{record.wifiName,jdbcType=VARCHAR},
        ip = #{record.ip,jdbcType=VARCHAR},
        ipv6 = #{record.ipv6,jdbcType=VARCHAR},
        wlan_radio_type = #{record.wlanRadioType,jdbcType=VARCHAR},
        wlan_radio_power = #{record.wlanRadioPower,jdbcType=INTEGER},
        lan_port = #{record.lanPort,jdbcType=VARCHAR},
        lan_bit_rate = #{record.lanBitRate,jdbcType=BIGINT},
        freq = #{record.freq,jdbcType=BIGINT},
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
        last_time = #{record.lastTime,jdbcType=TIMESTAMP},
        pdate = #{record.pdate,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.DeviceCumulativeAllExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_device_cumulative_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>