<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayIncrementDetailMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.GatewayIncrementDetail">
    <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn" />
    <result column="factory_code" jdbcType="VARCHAR" property="factoryCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="industry" jdbcType="VARCHAR" property="industry" />
    <result column="gdate" jdbcType="BIGINT" property="gdate" />
  </resultMap>

  <resultMap id="CountBaseResultMap" type="com.cmiot.report.bean.GatewayIncrementDetailCount">
    <result column="factory_code" jdbcType="VARCHAR" property="factoryCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="industry" jdbcType="VARCHAR" property="industry" />
    <result column="count" jdbcType="BIGINT" property="count" />
  </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    gateway_sn, factory_code, province_code, industry, gdate
  </sql>

  <select id="selectCountByExample" parameterType="com.cmiot.report.bean.GatewayIncrementDetailExample" resultMap="CountBaseResultMap">
    select factory_code,province_code,industry,uniqExact(gateway_sn) as count from t_gateway_increment_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if> group by factory_code,province_code,industry
  </select>

  <select id="selectByExample" parameterType="com.cmiot.report.bean.GatewayIncrementDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_increment_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>


  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.GatewayIncrementDetailExample">
    delete from t_gateway_increment_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.GatewayIncrementDetail">
    insert into t_gateway_increment_count_all (gateway_sn, factory_code, province_code,
      industry, gdate)
    values (#{gatewaySn,jdbcType=VARCHAR}, #{factoryCode,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{industry,jdbcType=VARCHAR}, #{gdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.GatewayIncrementDetail">
    insert into t_gateway_increment_count_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gatewaySn != null">
        gateway_sn,
      </if>
      <if test="factoryCode != null">
        factory_code,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="industry != null">
        industry,
      </if>
      <if test="gdate != null">
        gdate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gatewaySn != null">
        #{gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="factoryCode != null">
        #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="industry != null">
        #{industry,jdbcType=VARCHAR},
      </if>
      <if test="gdate != null">
        #{gdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.GatewayIncrementDetailExample" resultType="java.lang.Long">
    select count(*) from t_gateway_increment_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_gateway_increment_count_all
    <set>
      <if test="record.gatewaySn != null">
        gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="record.factoryCode != null">
        factory_code = #{record.factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.industry != null">
        industry = #{record.industry,jdbcType=VARCHAR},
      </if>
      <if test="record.gdate != null">
        gdate = #{record.gdate,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_gateway_increment_count_all
    set gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      factory_code = #{record.factoryCode,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      industry = #{record.industry,jdbcType=VARCHAR},
      gdate = #{record.gdate,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.GatewayIncrementDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_gateway_increment_count_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>