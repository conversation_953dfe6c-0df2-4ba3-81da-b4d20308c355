<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.PluginInstallDetailMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.PluginInstallDetail">
    <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn" />
    <result column="gateway_mac" jdbcType="VARCHAR" property="gatewayMac" />
    <result column="factory_id" jdbcType="BIGINT" property="factoryId" />
    <result column="device_model_id" jdbcType="BIGINT" property="deviceModelId" />
    <result column="gateway_model_int" jdbcType="SMALLINT" property="gatewayModelInt" />
    <result column="gateway_model" jdbcType="VARCHAR" property="gatewayModel" />
    <result column="gateway_vendor" jdbcType="VARCHAR" property="gatewayVendor" />
    <result column="gateway_productclass" jdbcType="VARCHAR" property="gatewayProductclass" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
    <result column="plugin_name" jdbcType="VARCHAR" property="pluginName" />
    <result column="plugin_version" jdbcType="VARCHAR" property="pluginVersion" />
    <result column="plugin_status" jdbcType="VARCHAR" property="pluginStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    gateway_sn, gateway_mac, factory_id, device_model_id, gateway_model_int, gateway_model, 
    gateway_vendor, gateway_productclass, province_code, city_code, enterprise_id, customer_id, 
    sample_time, plugin_name, plugin_version, plugin_status
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.PluginInstallDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_plugin_install_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.PluginInstallDetailExample">
    delete from t_plugin_install_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.PluginInstallDetail">
    insert into t_plugin_install_detail (gateway_sn, gateway_mac, factory_id, 
      device_model_id, gateway_model_int, gateway_model, 
      gateway_vendor, gateway_productclass, province_code, 
      city_code, enterprise_id, customer_id, 
      sample_time, plugin_name, plugin_version, 
      plugin_status)
    values (#{gatewaySn,jdbcType=VARCHAR}, #{gatewayMac,jdbcType=VARCHAR}, #{factoryId,jdbcType=BIGINT}, 
      #{deviceModelId,jdbcType=BIGINT}, #{gatewayModelInt,jdbcType=SMALLINT}, #{gatewayModel,jdbcType=VARCHAR}, 
      #{gatewayVendor,jdbcType=VARCHAR}, #{gatewayProductclass,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{enterpriseId,jdbcType=BIGINT}, #{customerId,jdbcType=VARCHAR}, 
      #{sampleTime,jdbcType=TIMESTAMP}, #{pluginName,jdbcType=VARCHAR}, #{pluginVersion,jdbcType=VARCHAR}, 
      #{pluginStatus,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.PluginInstallDetail">
    insert into t_plugin_install_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gatewaySn != null">
        gateway_sn,
      </if>
      <if test="gatewayMac != null">
        gateway_mac,
      </if>
      <if test="factoryId != null">
        factory_id,
      </if>
      <if test="deviceModelId != null">
        device_model_id,
      </if>
      <if test="gatewayModelInt != null">
        gateway_model_int,
      </if>
      <if test="gatewayModel != null">
        gateway_model,
      </if>
      <if test="gatewayVendor != null">
        gateway_vendor,
      </if>
      <if test="gatewayProductclass != null">
        gateway_productclass,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="sampleTime != null">
        sample_time,
      </if>
      <if test="pluginName != null">
        plugin_name,
      </if>
      <if test="pluginVersion != null">
        plugin_version,
      </if>
      <if test="pluginStatus != null">
        plugin_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gatewaySn != null">
        #{gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="gatewayMac != null">
        #{gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="factoryId != null">
        #{factoryId,jdbcType=BIGINT},
      </if>
      <if test="deviceModelId != null">
        #{deviceModelId,jdbcType=BIGINT},
      </if>
      <if test="gatewayModelInt != null">
        #{gatewayModelInt,jdbcType=SMALLINT},
      </if>
      <if test="gatewayModel != null">
        #{gatewayModel,jdbcType=VARCHAR},
      </if>
      <if test="gatewayVendor != null">
        #{gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="gatewayProductclass != null">
        #{gatewayProductclass,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="sampleTime != null">
        #{sampleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pluginName != null">
        #{pluginName,jdbcType=VARCHAR},
      </if>
      <if test="pluginVersion != null">
        #{pluginVersion,jdbcType=VARCHAR},
      </if>
      <if test="pluginStatus != null">
        #{pluginStatus,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.PluginInstallDetailExample" resultType="java.lang.Long">
    select count(*) from t_plugin_install_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_plugin_install_detail
    <set>
      <if test="record.gatewaySn != null">
        gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayMac != null">
        gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      </if>
      <if test="record.factoryId != null">
        factory_id = #{record.factoryId,jdbcType=BIGINT},
      </if>
      <if test="record.deviceModelId != null">
        device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
      </if>
      <if test="record.gatewayModelInt != null">
        gateway_model_int = #{record.gatewayModelInt,jdbcType=SMALLINT},
      </if>
      <if test="record.gatewayModel != null">
        gateway_model = #{record.gatewayModel,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayVendor != null">
        gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      </if>
      <if test="record.gatewayProductclass != null">
        gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.enterpriseId != null">
        enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleTime != null">
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pluginName != null">
        plugin_name = #{record.pluginName,jdbcType=VARCHAR},
      </if>
      <if test="record.pluginVersion != null">
        plugin_version = #{record.pluginVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.pluginStatus != null">
        plugin_status = #{record.pluginStatus,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_plugin_install_detail
    set gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
      gateway_mac = #{record.gatewayMac,jdbcType=VARCHAR},
      factory_id = #{record.factoryId,jdbcType=BIGINT},
      device_model_id = #{record.deviceModelId,jdbcType=BIGINT},
      gateway_model_int = #{record.gatewayModelInt,jdbcType=SMALLINT},
      gateway_model = #{record.gatewayModel,jdbcType=VARCHAR},
      gateway_vendor = #{record.gatewayVendor,jdbcType=VARCHAR},
      gateway_productclass = #{record.gatewayProductclass,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      enterprise_id = #{record.enterpriseId,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
      plugin_name = #{record.pluginName,jdbcType=VARCHAR},
      plugin_version = #{record.pluginVersion,jdbcType=VARCHAR},
      plugin_status = #{record.pluginStatus,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.PluginInstallDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_plugin_install_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>