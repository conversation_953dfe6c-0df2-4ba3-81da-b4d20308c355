<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.ComplaintsOrderMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.ComplaintsOrder">
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="order_name" jdbcType="VARCHAR" property="orderName" />
    <result column="gdate" jdbcType="INTEGER" property="gdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    customer_id, province_code, city_code, order_no, order_type, order_name, gdate
  </sql>


  <!--投诉类型统计-->
  <select id="selectCountByExample" resultType="com.cmiot.report.dto.ServicePieData">
   select t1.order_name as name,t1.value as value,round(divide(t1.value,t2.total),2) as rate from
    (select order_name,uniqExact(order_no) as value from t_complaints_order_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by order_name) t1
    left join (select uniqExact(order_no) as total from t_complaints_order_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>) t2 on 1=1 order by t1.value desc
  </select>


  <select id="selectByExample" parameterType="com.cmiot.report.bean.ComplaintsOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_complaints_order_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.ComplaintsOrderExample">
    delete from t_complaints_order_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.ComplaintsOrder">
    insert into t_complaints_order_all (customer_id, province_code, city_code, 
      order_no, order_type, order_name, 
      gdate)
    values (#{customerId,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=TINYINT}, #{orderName,jdbcType=VARCHAR}, 
      #{gdate,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.ComplaintsOrder">
    insert into t_complaints_order_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderName != null">
        order_name,
      </if>
      <if test="gdate != null">
        gdate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderName != null">
        #{orderName,jdbcType=VARCHAR},
      </if>
      <if test="gdate != null">
        #{gdate,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.ComplaintsOrderExample" resultType="java.lang.Long">
    select count(*) from t_complaints_order_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_complaints_order_all
    <set>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=TINYINT},
      </if>
      <if test="record.orderName != null">
        order_name = #{record.orderName,jdbcType=VARCHAR},
      </if>
      <if test="record.gdate != null">
        gdate = #{record.gdate,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_complaints_order_all
    set customer_id = #{record.customerId,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=TINYINT},
      order_name = #{record.orderName,jdbcType=VARCHAR},
      gdate = #{record.gdate,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.ComplaintsOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_complaints_order_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>