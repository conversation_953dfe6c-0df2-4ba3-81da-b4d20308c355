<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayOnOfflineDetailMapper">
    <resultMap id="BaseResultMap" type="com.cmiot.report.bean.GatewayOnOfflineDetail">
        <result column="gateway_sn" jdbcType="VARCHAR" property="gatewaySn"/>
        <result column="factory_code" jdbcType="VARCHAR" property="factoryCode"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="gtype" jdbcType="TINYINT" property="gtype"/>
        <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId"/>
        <result column="gdate" jdbcType="BIGINT" property="gdate"/>
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime"/>
    </resultMap>

    <resultMap id="CountResultMap" type="com.cmiot.report.bean.GatewayOnOfflineCount">
        <result column="factory_code" jdbcType="VARCHAR" property="factoryCode"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="gtype" jdbcType="TINYINT" property="gtype"/>
        <result column="count" jdbcType="BIGINT" property="count"/>
    </resultMap>

    <resultMap id="MiniCountResultMap" type="com.cmiot.report.bean.GatewayOnOfflineMiniCount">
        <result column="gdate" jdbcType="BIGINT" property="gdate"/>
        <result column="gtype" jdbcType="VARCHAR" property="gtype"/>
        <result column="count" jdbcType="BIGINT" property="count"/>
    </resultMap>

    <resultMap id="OverviewCountResultMap" type="com.cmiot.report.bean.GatewayOnOffLineNumOverviewCount">
        <result column="gtype" jdbcType="TINYINT" property="gtype"/>
        <result column="count" jdbcType="BIGINT" property="count"/>
    </resultMap>

    <resultMap id="QueryResultMap" type="com.cmiot.report.dto.GatewayOnOfflineAllCount">
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="count" jdbcType="BIGINT" property="count"/>
    </resultMap>


    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        gateway_sn, factory_code, province_code, city_code, gtype, enterprise_id, gdate,
        sample_time
    </sql>


    <select id="selectCountByExample" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="CountResultMap">
        select factory_code,province_code,city_code,gtype,uniqExact(gateway_sn) as count from
        t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by factory_code,province_code,city_code,gtype
    </select>


    <select id="selectWeekOrMonthOnCountByExample" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="CountResultMap">
        select factory_code,province_code,city_code,gtype,uniqExact(gateway_sn) as count from
        t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and gtype=1
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by factory_code,province_code,city_code,gtype
    </select>

    <select id="selectWeekOrMonthOffCountByExample" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="CountResultMap">
        select factory_code,province_code,city_code,gtype,uniqExact(gateway_sn) as count from
        t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and gtype=0 and gateway_sn global not in (select gateway_sn from t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and gtype=1 group by gateway_sn)
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by factory_code,province_code,city_code,gtype
    </select>

    <select id="selectMiniCountByExample" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="MiniCountResultMap">
        select gdate,gtype,uniqExact(gateway_sn) as count from t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by gdate,gtype order by gdate,gtype
    </select>


    <select id="selectOverviewCountByExample" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="OverviewCountResultMap">
        select gtype,uniqExact(gateway_sn) as count from t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by gtype
    </select>


    <select id="selectAllCountByExample" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="CountResultMap">
        select factory_code,province_code,city_code,0 as gtype,uniqExact(gateway_sn) as count from
        t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by factory_code,province_code,city_code
    </select>


    <!--查询分省网关总数-->
    <select id="selectAllCountByProv" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="QueryResultMap">
        select province_code as name,uniqExact(gateway_sn) as count from
        t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by province_code
    </select>

    <!--查询分地市网关总数-->
    <select id="selectAllCountByCity" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="QueryResultMap">
        select city_code as name,uniqExact(gateway_sn) as count from
        t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by city_code
    </select>

    <!--查询分厂商网关总数-->
    <select id="selectAllCountByVendor" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="QueryResultMap">
        select factory_code as name,uniqExact(gateway_sn) as count from
        t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        group by factory_code
    </select>


    <select id="selectByExample" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample">
        delete from t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetail">
        insert into t_gateway_on_offline_detail_all (gateway_sn, factory_code, province_code,
        city_code, gtype, enterprise_id,
        gdate, sample_time)
        values (#{gatewaySn,jdbcType=VARCHAR}, #{factoryCode,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR},
        #{cityCode,jdbcType=VARCHAR}, #{gtype,jdbcType=TINYINT}, #{enterpriseId,jdbcType=VARCHAR},
        #{gdate,jdbcType=BIGINT}, #{sampleTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetail">
        insert into t_gateway_on_offline_detail_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="gatewaySn != null">
                gateway_sn,
            </if>
            <if test="factoryCode != null">
                factory_code,
            </if>
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="gtype != null">
                gtype,
            </if>
            <if test="enterpriseId != null">
                enterprise_id,
            </if>
            <if test="gdate != null">
                gdate,
            </if>
            <if test="sampleTime != null">
                sample_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="gatewaySn != null">
                #{gatewaySn,jdbcType=VARCHAR},
            </if>
            <if test="factoryCode != null">
                #{factoryCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="gtype != null">
                #{gtype,jdbcType=TINYINT},
            </if>
            <if test="enterpriseId != null">
                #{enterpriseId,jdbcType=VARCHAR},
            </if>
            <if test="gdate != null">
                #{gdate,jdbcType=BIGINT},
            </if>
            <if test="sampleTime != null">
                #{sampleTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultType="java.lang.Long">
        select count(*) from t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update t_gateway_on_offline_detail_all
        <set>
            <if test="record.gatewaySn != null">
                gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
            </if>
            <if test="record.factoryCode != null">
                factory_code = #{record.factoryCode,jdbcType=VARCHAR},
            </if>
            <if test="record.provinceCode != null">
                province_code = #{record.provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="record.cityCode != null">
                city_code = #{record.cityCode,jdbcType=VARCHAR},
            </if>
            <if test="record.gtype != null">
                gtype = #{record.gtype,jdbcType=TINYINT},
            </if>
            <if test="record.enterpriseId != null">
                enterprise_id = #{record.enterpriseId,jdbcType=VARCHAR},
            </if>
            <if test="record.gdate != null">
                gdate = #{record.gdate,jdbcType=BIGINT},
            </if>
            <if test="record.sampleTime != null">
                sample_time = #{record.sampleTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update t_gateway_on_offline_detail_all
        set gateway_sn = #{record.gatewaySn,jdbcType=VARCHAR},
        factory_code = #{record.factoryCode,jdbcType=VARCHAR},
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
        city_code = #{record.cityCode,jdbcType=VARCHAR},
        gtype = #{record.gtype,jdbcType=TINYINT},
        enterprise_id = #{record.enterpriseId,jdbcType=VARCHAR},
        gdate = #{record.gdate,jdbcType=BIGINT},
        sample_time = #{record.sampleTime,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.GatewayOnOfflineDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from t_gateway_on_offline_detail_all
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>