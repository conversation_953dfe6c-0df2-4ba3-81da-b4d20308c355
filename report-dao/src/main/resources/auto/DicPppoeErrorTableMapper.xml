<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DicPppoeErrorTableMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.DicPppoeErrorTable">
    <result column="pppoe_error" jdbcType="VARCHAR" property="pppoeError" />
    <result column="pppoe_error_desc" jdbcType="VARCHAR" property="pppoeErrorDesc" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    pppoe_error, pppoe_error_desc
  </sql>
  <select id="selectByExample" parameterType="com.cmiot.report.bean.DicPppoeErrorTableExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_dic_pppoe_error_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.cmiot.report.bean.DicPppoeErrorTableExample">
    delete from t_dic_pppoe_error_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cmiot.report.bean.DicPppoeErrorTable">
    insert into t_dic_pppoe_error_table (pppoe_error, pppoe_error_desc)
    values (#{pppoeError,jdbcType=VARCHAR}, #{pppoeErrorDesc,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cmiot.report.bean.DicPppoeErrorTable">
    insert into t_dic_pppoe_error_table
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pppoeError != null">
        pppoe_error,
      </if>
      <if test="pppoeErrorDesc != null">
        pppoe_error_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pppoeError != null">
        #{pppoeError,jdbcType=VARCHAR},
      </if>
      <if test="pppoeErrorDesc != null">
        #{pppoeErrorDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cmiot.report.bean.DicPppoeErrorTableExample" resultType="java.lang.Long">
    select count(*) from t_dic_pppoe_error_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_dic_pppoe_error_table
    <set>
      <if test="record.pppoeError != null">
        pppoe_error = #{record.pppoeError,jdbcType=VARCHAR},
      </if>
      <if test="record.pppoeErrorDesc != null">
        pppoe_error_desc = #{record.pppoeErrorDesc,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_dic_pppoe_error_table
    set pppoe_error = #{record.pppoeError,jdbcType=VARCHAR},
      pppoe_error_desc = #{record.pppoeErrorDesc,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.cmiot.report.bean.DicPppoeErrorTableExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_dic_pppoe_error_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>