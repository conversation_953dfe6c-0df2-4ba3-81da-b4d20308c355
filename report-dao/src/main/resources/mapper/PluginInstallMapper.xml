<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.PluginInstallMapper">


  <resultMap id="PluginModelInstallCountResultMap" type="com.cmiot.report.dto.plugin.PluginModelCount">
    <result column="gateway_model" jdbcType="VARCHAR" property="gatewayModel" />
    <result column="modelCount" jdbcType="BIGINT" property="modelCount" />
    <result column="modelId" jdbcType="BIGINT" property="modelId" />
  </resultMap>

  <select id="getInstallationByModelQuery"  resultMap="PluginModelInstallCountResultMap">
    select count(distinct gateway_sn) as modelCount,device_model_id as modelId  from t_plugin_install final
    <where>
      <if test="vendor != null and vendor.size > 0" >
        and factory_id in
        <foreach collection="vendor" index="index" item="itemp" open="(" separator="," close=")">
          #{itemp}
        </foreach>
      </if>
      <if test="model != null and model.size > 0">
        and device_model_id in
        <foreach collection="model" index="index" item="itemp" open="(" separator="," close=")">
          #{itemp}
        </foreach>
      </if>
      <if test="plugin != null and plugin !='' ">
         and plugin_name=#{plugin,jdbcType=VARCHAR}
      </if>
      <if test="version != null and version.size > 0">
        and plugin_version in
        <foreach collection="version" index="index" item="itemp" open="(" separator="," close=")">
          #{itemp}
        </foreach>
      </if>

    </where>
    group by device_model_id
  </select>
</mapper>