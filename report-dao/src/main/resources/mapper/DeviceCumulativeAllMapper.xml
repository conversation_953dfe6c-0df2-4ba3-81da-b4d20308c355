<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DeviceCumulativeAllMapper">
    <select id="countDistinctMacByExample" parameterType="com.cmiot.report.bean.DeviceCumulativeAllExample" resultMap="DistinctMacByMap">
    select 
    toYYYYMMDD(start_time) as pdate1,
    province_code,city_code,gateway_vendor,
    count(distinct mac) as new_mac_count 
     from t_device_cumulative_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by pdate1,province_code,city_code,gateway_vendor 
  </select>
  
  <resultMap id="DistinctMacByMap" type="com.cmiot.report.bean.DeviceCumulativeAllCount">
  	<result column="pdate1" jdbcType="BIGINT" property="pdate" />
    <result column="gateway_vendor" jdbcType="VARCHAR" property="gatewayVendor" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="new_mac_count" jdbcType="BIGINT" property="newMacCount" />
  </resultMap>
  
</mapper>