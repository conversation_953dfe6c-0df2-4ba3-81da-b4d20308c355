<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmiot.report.mapper.EnterpriseGatewayCountMapper">


    <select id="getDeviceOverviewDay" resultType="com.cmiot.report.dto.enterprise.EnterpriseGatewayCountInfo">
        select count_num as allNum, active_num as dayNum
        from t_gateway_enterprise_day_count_all
        where
            pdate = #{day}
            and enterprise_id = #{eid}
        limit 1

    </select>

    <select id="getDeviceOverviewWeek" resultType="com.cmiot.report.dto.enterprise.EnterpriseGatewayCountInfo">
        select active_num as weekNum, count_num as allNum
        from t_gateway_enterprise_week_count_all
        where
            pdate = #{weekDay}
            and enterprise_id = #{eid}
        limit 1
    </select>

    <select id="getDeviceOverviewMonth" resultType="com.cmiot.report.dto.enterprise.EnterpriseGatewayCountInfo">
        select active_num as monthNum
        from t_gateway_enterprise_month_count_all
        where
            pdate = #{month}
            and enterprise_id = #{eid}
        limit 1
    </select>

    <select id="getDeviceMonthActiveRecord" resultType="com.cmiot.report.dto.enterprise.DeviceMonthActiveInfo">
        select pdate as `time`, count_num as allDeviceNum, active_num as onlineDeviceNum
        from t_gateway_enterprise_month_count_all
        where pdate &gt;= #{monthStart} and pdate &lt;= #{monthEnd}
            and enterprise_id = #{eid}
        order by pdate asc
    </select>

    <select id="getSubDeviceDayCount" resultType="com.cmiot.report.dto.enterprise.DeviceEnterpriseCountInfo">
        select pdate as `pdate`, count_num as countNum, wire_num as wireNum, wireless_num as wirelessNum
        from t_device_enterprise_count_day_all
        where pdate &gt;= #{startDate} and pdate &lt;= #{endDate}
            and enterprise_id = #{eid}
        order by pdate asc
    </select>

    <select id="getSubDeviceWeekCount" resultType="com.cmiot.report.dto.enterprise.DeviceEnterpriseCountInfo">
        select pdate as `pdate`, count_num as countNum, wire_num as wireNum, wireless_num as wirelessNum
        from t_device_enterprise_count_week_all
        where pdate &gt;= #{startDate} and pdate &lt;= #{endDate}
            and enterprise_id = #{eid}
        order by pdate asc
    </select>

    <select id="getSubDeviceMonthCount" resultType="com.cmiot.report.dto.enterprise.DeviceEnterpriseCountInfo">
        select pdate as `pdate`, count_num as countNum, wire_num as wireNum, wireless_num as wirelessNum
        from t_device_enterprise_count_month_all
        where pdate &gt;= #{startDate} and pdate &lt;= #{endDate}
            and enterprise_id = #{eid}
        order by pdate asc
    </select>
    <select id="getEnterpriseDeviceUseCount"
            resultType="com.cmiot.report.dto.enterprise.GatewayEnterpriseUseCountInfo">
        select pdate as `pdate`, max_txrate as maxTxrate, max_rxrate as maxRxrate,
               aver_txrate as averTxrate, aver_rxrate as averRxrate, cpu_aver_rate as cpuAverRate,
               ram_aver_rate as ramAverRate
        from t_gateway_enterprise_use_day_all
        where pdate &gt;= #{startDate} and pdate &lt;= #{endDate}
            and enterprise_id = #{eid}
        order by pdate asc
    </select>

    <select id="getEnterpriseNetworkTotalCount" resultType="com.cmiot.report.dto.enterprise.EnterpriseNetworkTotalInfo">
        select gdate, total_flow as totalFlow from t_customer_networktotal_day_count_all
        where enterprise_id = #{enterpriseId} and gdate &gt;= #{startDate} and gdate &lt;= #{endDate}
    </select>


    <select id="getWeekReportVisitsCountRank"
            resultType="com.cmiot.report.dto.enterprise.EnterpriseVisitsCountInfo">
        select t1.enterpriseId, t1.domainName, t1.visitTimes
        from (SELECT enterprise_id as enterpriseId, domain_name as domainName, sum(visit_times) as visitTimes
                       FROM t_customer_network_visit_count_day_all
                       where enterprise_id = #{enterpriseId} and gdate &gt;= #{startDate} and gdate &lt;= #{endDate}
                         and domain_name is not null and domain_name != ''
                       group by enterprise_id, domain_name) t1
            order by t1.visitTimes desc, t1.domainName asc
            limit ${rankLimit}
    </select>

</mapper>
