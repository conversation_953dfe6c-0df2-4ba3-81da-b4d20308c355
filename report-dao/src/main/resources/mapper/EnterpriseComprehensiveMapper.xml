<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmiot.report.mapper.EnterpriseComprehensiveMapper">


    <resultMap id="ResultMapCustomerOrderAll" type="com.cmiot.report.bean.CustomerOrderAll">
        <result column="id" jdbcType="BIGINT" property="id" />
        <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
        <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
        <result column="business_type" jdbcType="VARCHAR" property="businessType" />
        <result column="business_product_id" jdbcType="VARCHAR" property="businessProductId" />
        <result column="discount_rate" jdbcType="DECIMAL" property="discountRate" />
        <result column="port_num" jdbcType="INTEGER" property="portNum" />
        <result column="bandwidth" jdbcType="INTEGER" property="bandwidth" />
        <result column="line_total" jdbcType="INTEGER" property="lineTotal" />
        <result column="business_subscribe_time" jdbcType="TIMESTAMP" property="businessSubscribeTime" />
        <result column="business_unsubscribe_time" jdbcType="TIMESTAMP" property="businessUnsubscribeTime" />
        <result column="business_status" jdbcType="INTEGER" property="businessStatus" />
        <result column="package_effect_time" jdbcType="TIMESTAMP" property="packageEffectTime" />
        <result column="package_uneffect_time" jdbcType="TIMESTAMP" property="packageUneffectTime" />
        <result column="package_status" jdbcType="INTEGER" property="packageStatus" />
        <result column="package_code" jdbcType="VARCHAR" property="packageCode" />
        <result column="province_package_code" jdbcType="VARCHAR" property="provincePackageCode" />
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
        <result column="user_identify" jdbcType="VARCHAR" property="userIdentify" />
        <result column="user_real" jdbcType="INTEGER" property="userReal" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
        <result column="sample_time" jdbcType="TIMESTAMP" property="sampleTime" />
    </resultMap>

    <select id="getComprehensive"
            resultType="com.cmiot.report.dto.enterprise.EnterpriseComprehensiveInfo">
        SELECT pdate,
               enterprise_id as enterpriseId, customer_id as customerId, customer_name as customerName,
               province_code as provinceCode, tdai1.gname as provinceName,
               city_code as cityCode, tdai2.gname as cityName,
               county_code as countyCode,
               industry as industryCode, tdii.industry_name as industryName,
               customer_status as customerStatus, tdbs.name as customerStatusName,
               business_count as businessCount, package_count as packageCount, gateway_count as gatewayCount,
               high_loading_gateway_count as highLoadingGatewayCount, low_loading_gateway_count as lowLoadingGatewayCount,
               long_time_idle_count as longTimeIdleCount, over_life_count as overLifeCount, bandwidth_nonsupport as bandwidthNonsupport,
               network_ping_exp as  networkPingExp , network_speed_exp as networkSpeedExp,
               cpu_ram_exp as cpuRamExp, sample_time
        FROM t_enterprise_day_comprehensive_all tedc
        global left join t_dict_industry_info tdii on toString(tdii.id) = toString(tedc.industry)
        global left join t_dic_area_info tdai1 on tdai1.gcode = tedc.province_code
        global left join t_dic_area_info tdai2 on tdai2.gcode = tedc.city_code
        global left join t_dict_bass_std tdbs on tdbs.bass_type  = 'BASS_STD1_0028' and tdbs.code = tedc.customer_status
        where enterprise_id = #{eid} and pdate = #{day}
        limit 1
    </select>

    <select id="countHighLoadingGateway" resultType="java.lang.Long">
        select uniqExact(gateway_sn) from t_gateway_high_loading_month_all
        where enterprise_id = #{eid} and pmonth = #{month}
    </select>

    <select id="getHighLoadingGateway" resultType="com.cmiot.report.dto.enterprise.ComprehensiveGatewayInfo">
        select pmonth, enterprise_id, gateway_mac as mac, gateway_sn as sn,
               factory_id as factoryId, ifnull(dictGetOrDefault('ge_report.t_dic_factory', 'factory_name', toUInt64OrNull(factory_id), '-'), '-') as factoryName,
               device_id as deviceModelId, ifnull(dictGetOrDefault('ge_report.t_dic_device_model', 'device_model', toUInt64OrNull(device_id), '-'), '-')  as deviceModelName,
               high_loading_time as highLoadingTime, running_time as runningTime, high_loading_percent as highLoadingPercent,
               sample_time
        from t_gateway_high_loading_month_all tghlm
        where enterprise_id = #{eid} and pmonth = #{month}
        order by gateway_sn asc
        limit #{offset}, #{limitSize}
    </select>
    <select id="countLowLoadingGateway" resultType="java.lang.Long">
        select uniqExact(gateway_sn) from t_gateway_low_loading_month_all
        where enterprise_id = #{eid} and pmonth = #{month}
    </select>
    <select id="getLowLoadingGateway" resultType="com.cmiot.report.dto.enterprise.ComprehensiveGatewayInfo">
        select pmonth, enterprise_id, gateway_mac as mac, gateway_sn as sn,
               factory_id as factoryId, ifnull(dictGetOrDefault('ge_report.t_dic_factory', 'factory_name', toUInt64OrNull(factory_id), '-'), '-') as factoryName,
               device_id as deviceModelId, ifnull(dictGetOrDefault('ge_report.t_dic_device_model', 'device_model', toUInt64OrNull(device_id), '-'), '-')  as deviceModelName,
               low_loading_time as lowLoadingTime, running_time as runningTime, low_loading_percent as lowLoadingPercent,
               sample_time
        from t_gateway_low_loading_month_all tgllm
        where enterprise_id = #{eid} and pmonth = #{month}
        order by gateway_sn asc
            limit #{offset}, #{limitSize}
    </select>

    <select id="countIdleGateway" resultType="java.lang.Long">
        select uniqExact(gateway_sn) from t_gateway_idle_record_all
        where enterprise_id = #{eid} and pdate = #{day} and idle_time >= 30
    </select>

    <select id="getIdleGateway" resultType="com.cmiot.report.dto.enterprise.ComprehensiveGatewayInfo">
        select pdate, enterprise_id, gateway_mac as mac, gateway_sn as sn,
               factory_id as factoryId, ifnull(dictGetOrDefault('ge_report.t_dic_factory', 'factory_name', toUInt64OrNull(factory_id), '-'), '-') as factoryName,
               device_id as deviceModelId, ifnull(dictGetOrDefault('ge_report.t_dic_device_model', 'device_model', toUInt64OrNull(device_id), '-'), '-')  as deviceModelName,
               idle_time as idleTime,
               sample_time
        from t_gateway_idle_record_all tgir
        where enterprise_id = #{eid} and pdate = #{day} and idle_time >= 30
        order by gateway_sn asc
            limit #{offset}, #{limitSize}
    </select>
    <select id="countOverLifeGateway" resultType="java.lang.Long">
        select uniqExact(gateway_sn) from t_gateway_over_life_record_all
        where enterprise_id = #{eid} and pdate = #{day}
    </select>
    <select id="getOverLifeGateway" resultType="com.cmiot.report.dto.enterprise.ComprehensiveGatewayInfo">
        select pdate, enterprise_id, gateway_mac as mac, gateway_sn as sn,
               factory_id as factoryId, ifnull(dictGetOrDefault('ge_report.t_dic_factory', 'factory_name', toUInt64OrNull(factory_id), '-'), '-') as factoryName,
               device_id as deviceModelId, ifnull(dictGetOrDefault('ge_report.t_dic_device_model', 'device_model', toUInt64OrNull(device_id), '-'), '-')  as deviceModelName,
               use_time as useTime, plan_use_time as planUseTime,
               sample_time
        from t_gateway_over_life_record_all tgolr
        where enterprise_id = #{eid} and pdate = #{day}
        order by gateway_sn asc
            limit #{offset}, #{limitSize}
    </select>

    <select id="countBandwidthNonsupportGateway" resultType="java.lang.Long">
        select uniqExact(gateway_sn) from t_gateway_bandwidth_nonsupport_all
        where enterprise_id = #{eid} and pdate = #{day}
    </select>

    <select id="getBandwidthNonsupportGateway"
            resultType="com.cmiot.report.dto.enterprise.ComprehensiveGatewayInfo">
        select pdate, enterprise_id, gateway_mac as mac, gateway_sn as sn,
               factory_id as factoryId, ifnull(dictGetOrDefault('ge_report.t_dic_factory', 'factory_name', toUInt64OrNull(factory_id), '-'), '-') as factoryName,
               device_id as deviceModelId, ifnull(dictGetOrDefault('ge_report.t_dic_device_model', 'device_model', toUInt64OrNull(device_id), '-'), '-')  as deviceModelName,
               bandwidth as bandwidth, support_bandwidth as supportBandwidth,
               sample_time
        from t_gateway_bandwidth_nonsupport_all tgbn
        where enterprise_id = #{eid} and pdate = #{day}
        order by gateway_sn asc
            limit #{offset}, #{limitSize}
    </select>

    <select id="countNetBadPingGateway" resultType="java.lang.Long">
        select uniqExact(gateway_sn) from t_gateway_net_bad_ping_all
        where enterprise_id = #{eid} and pdate = #{day}
    </select>

    <select id="getNetBadPingGateway" resultType="com.cmiot.report.dto.enterprise.ComprehensiveGatewayInfo">
        select pdate, enterprise_id, gateway_mac as mac, gateway_sn as sn,
               factory_id as factoryId, ifnull(dictGetOrDefault('ge_report.t_dic_factory', 'factory_name', toUInt64OrNull(factory_id), '-'), '-') as factoryName,
               device_id as deviceModelId, ifnull(dictGetOrDefault('ge_report.t_dic_device_model', 'device_model', toUInt64OrNull(device_id), '-'), '-')  as deviceModelName,
               ping_time as pingTime,
               sample_time
        from t_gateway_net_bad_ping_all tgnbp
        where enterprise_id = #{eid} and pdate = #{day}
        order by gateway_sn asc
            limit #{offset}, #{limitSize}
    </select>
    <select id="countCpuRamGateway" resultType="java.lang.Long">
        select uniqExact(gateway_sn) from t_gateway_cpu_ram_warning_all
        where enterprise_id = #{eid} and pdate = #{day}
    </select>
    <select id="getCpuRamGateway" resultType="com.cmiot.report.dto.enterprise.ComprehensiveGatewayInfo">
        select pdate, enterprise_id, gateway_mac as mac, gateway_sn as sn,
               factory_id as factoryId, ifnull(dictGetOrDefault('ge_report.t_dic_factory', 'factory_name', toUInt64OrNull(factory_id), '-'), '-') as factoryName,
               device_id as deviceModelId, ifnull(dictGetOrDefault('ge_report.t_dic_device_model', 'device_model', toUInt64OrNull(device_id), '-'), '-')  as deviceModelName,
               cpu, ram,
               sample_time
        from t_gateway_cpu_ram_warning_all tgcrw
        where enterprise_id = #{eid} and pdate = #{day}
        order by gateway_sn asc
            limit #{offset}, #{limitSize}
    </select>

    <select id="countPackage" resultType="java.lang.Long">
        select count(*) from t_customer_order_all
        where enterprise_id = #{eid} and toYYYYMMDD(sample_time) = #{day}
    </select>
    <select id="getPackage" resultMap="ResultMapCustomerOrderAll">
        SELECT id, enterprise_id, customer_id, business_type,
               business_product_id, discount_rate, port_num,
               bandwidth, line_total, business_subscribe_time,
               business_unsubscribe_time, business_status, package_effect_time,
               package_uneffect_time, package_status, package_code,
               province_package_code, province_code, user_identify,
               user_real, create_time, update_time, is_delete, sample_time
        FROM t_customer_order_all
        where enterprise_id = #{eid} and toYYYYMMDD(sample_time) = #{day}
        order by id desc
        limit #{offset}, #{limitSize}
    </select>
    <select id="getOrdersByCustomerIds" resultMap="ResultMapCustomerOrderAll">
        SELECT id, enterprise_id, customer_id, business_type,
               business_product_id, discount_rate, port_num,
               bandwidth, line_total, business_subscribe_time,
               business_unsubscribe_time, business_status, package_effect_time,
               package_uneffect_time, package_status, package_code,
               province_package_code, province_code, user_identify,
               user_real, create_time, update_time, is_delete, sample_time
        FROM t_customer_order_all
        where  toYYYYMMDD(sample_time) = #{day}
        and customer_id in
        <foreach collection="customerIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="countBaseGateway" resultType="java.lang.Long">
        select uniqExact(gateway_sn) from t_gateway_all
        where enterprise_id = #{eid} and toYYYYMMDD(sample_time) = #{day}
    </select>

    <select id="getBaseGateway" resultType="com.cmiot.report.dto.enterprise.ComprehensiveGatewayInfo">
        select enterprise_id, gateway_mac as mac, gateway_sn as sn,
               factory_id as factoryId, dictGetOrDefault('ge_report.t_dic_factory', 'factory_name', factory_id, '-') as factoryName,
               device_model_id as deviceModelId, dictGetOrDefault('ge_report.t_dic_device_model', 'device_model', device_model_id, '-') as deviceModelName,
               sample_time
        FROM t_gateway_all
        where enterprise_id = #{eid} and toYYYYMMDD(sample_time) = #{day}
        order by gateway_sn asc
            limit #{offset}, #{limitSize}
    </select>

    <!--        @Param("offset") Long offset,
                                 @Param("limitSize") Integer limitSize-->

</mapper>
