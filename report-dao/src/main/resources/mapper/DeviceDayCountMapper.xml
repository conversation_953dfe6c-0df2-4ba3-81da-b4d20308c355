<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DeviceDayCountMapper">
  <!-- 统计对象返回 -->
  <resultMap id="DeviceAllByCountMap" type="com.cmiot.report.bean.DeviceAllByCount">
  	<result column="pdate" jdbcType="BIGINT" property="pdate" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="device_all_count" jdbcType="BIGINT" property="deviceAllCount" />
    <result column="device_all_wireless" jdbcType="BIGINT" property="deviceAllWireless" />
    <result column="device_all_wired" jdbcType="BIGINT" property="deviceAllWired" />
  </resultMap>

  <select id="selectDeviceDayCount" resultMap="DeviceAllByCountMap">
    SELECT pdate, province_code, city_code,
           sum(device_all_count) as device_all_count,
           sum(device_all_wireless) as device_all_wireless,
           sum(device_all_wired) as device_all_wired
    FROM t_device_day_count_all
    where 1 = 1
    <if test="startTime != null and startTime != ''">
      and pdate >= #{startTime}
    </if>
    <if test="endTime != null and endTime != ''">
      and pdate &lt;= #{endTime}
    </if>
    <if test="provinces != null and provinces.size() > 0">
      and province_code in
        <foreach collection="provinces" item="itemp" index="index" open="(" separator="," close=")">
          #{itemp}
        </foreach>
    </if>
    <if test="vendorsIdList != null and vendorsIdList.size() > 0">
      and factory_id in
      <foreach collection="vendorsIdList" item="itemv" index="index" open="(" separator="," close=")">
        #{itemv}
      </foreach>
    </if>

    group by pdate, province_code, city_code
  </select>
  <select id="selectDeviceAllCount"  resultMap="DeviceAllByCountMap">
    select province_code, city_code, uniqExact(mac) as device_all_count
    from t_device_replacing_all
    where 1 = 1
    <if test="startTime != null and startTime != ''">
      and sample_date >= #{startTime}
    </if>
    <if test="endTime != null and endTime != ''">
      and sample_date &lt;= #{endTime}
    </if>
    <if test="provinces != null and provinces.size() > 0">
      and province_code in
      <foreach collection="provinces" item="itemp" index="index" open="(" separator="," close=")">
        #{itemp}
      </foreach>
    </if>
    <if test="vendorsIdList != null and vendorsIdList.size() > 0">
      and gateway_vendor in
      <foreach collection="vendorsIdList" item="itemv" index="index" open="(" separator="," close=")">
        #{itemv}
      </foreach>
    </if>
    group by province_code, city_code
  </select>

</mapper>