<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayModelAllMapper">

  <resultMap id="PluginModelInstallCountResultMap" type="com.cmiot.report.dto.plugin.PluginModelCount">
    <result column="gateway_model_int" jdbcType="INTEGER" property="gatewayModelInt" />
    <result column="modelCount" jdbcType="BIGINT" property="modelCount" />
  </resultMap>

  <select id="getCountByModelQuery"  resultMap="PluginModelInstallCountResultMap">
    select count(distinct gateway_sn) as modelCount,gateway_model_int from t_gateway_model_all final
    <where>
      <if test="vendor != null and vendor.size > 0" >
        and factory_id in
        <foreach collection="vendor" index="index" item="itemp" open="(" separator="," close=")">
          #{itemp}
        </foreach>
      </if>
      <if test="model != null and model.size > 0">
        and device_model_id in
        <foreach collection="model" index="index" item="itemp" open="(" separator="," close=")">
          #{itemp}
        </foreach>
      </if>
    </where>
    group by gateway_model_int
  </select>
</mapper>