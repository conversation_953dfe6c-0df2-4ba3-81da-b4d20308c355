<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmiot.report.mapper.DeviceMapper">
    <select id="queryDeviceOverviewCountAll" resultType="com.cmiot.report.dto.device.DeviceOverviewCount">
        select
            count(*) as totalNum,
            countIf(sample_date,toDate(sample_date) = yesterday() and online_class='ONLINE') as dayActiveNum,
            countIf(wlan_radio_type,wlan_radio_type='') as wiredNum,
            countIf(wlan_radio_type,wlan_radio_type!='') as wlanNum
        from
            ge_report.t_device_replacing_all final
        <if test="provinceCodes != null">
            where province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <!--<select id="queryDeviceOverviewCountAll" resultType="com.cmiot.report.dto.device.DeviceOverviewCount">
        select t0.totalNum as totalNum,t1.dayActiveNum as dayActiveNum,t2.wiredNum as wiredNum,t3.wlanNum as wlanNum from
        (select uniqExact(concat(gateway_sn,mac)) as totalNum from ge_report.t_device_replacing_all) t0
        global join (select uniqExact(concat(gateway_sn,mac)) as dayActiveNum from ge_report.t_device_replacing_all where toDate(sample_date) = yesterday() and equals(online_class,'ONLINE')) t1 on 1=1
        global join (select uniqExact(concat(gateway_sn,mac)) as wiredNum from ge_report.t_device_replacing_all where equals(wlan_radio_type,'')) t2 on 1=1
        global join (select uniqExact(concat(gateway_sn,mac)) as wlanNum from ge_report.t_device_replacing_all where notEquals(wlan_radio_type,'')) t3 on 1=1
    </select>-->
    

    <select id="queryOverviewSelectCount" resultType="integer">
        select
            sum(count) as totalUnderDeviceNum
        from
            ge_report.t_device_stat_web_trend_total
        where
            1=1
        <if test="provinceCode !=null and provinceCode != ''">
            and province_code in
            <foreach collection="provinceCode" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="vendorCode != null and vendorCode != ''">
            and gateway_vendor in
            <foreach collection="vendorCode" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="date != null and date != ''">
            and sample_date = toDate(#{date})
        </if>
        <if test="industry!=null and industry!=''">
            and industry_code = #{industry}
        </if>
        <if test="connectType != null and connectType != -1">
            and product_type = #{connectType}
        </if>
    </select>
    <select id="queryDeviceOverviewResult" resultType="com.cmiot.report.dto.device.DeviceOverviewObject">
        select
            <if test="type == 'province_code'">
                dictGetString ('ge_report.t_dic_area_info', 'gname', province_code) AS name,
            </if>
            <if test="type == 'gateway_vendor'">
                dictGetString ('ge_report.t_dic_factory', 'factory_name', gateway_vendor) AS name,
            </if>
            <if test="type == 'industry_code'">
                dictGetString ('ge_report.t_dict_industry_info', 'industry_name', industry_code) AS name,
            </if>
            sum(count) as value
        from
            ge_report.t_device_stat_web_trend_total
        where
            1=1
            <if test="provinceCode !=null and provinceCode != ''">
                and province_code in
            <foreach collection="provinceCode" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            </if>
            <if test="vendorCode != null and vendorCode != ''">
                and gateway_vendor in
                <foreach collection="vendorCode" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="date != null and date != ''">
                and sample_date = toDate(#{date})
            </if>
            <if test="industry != null and industry != ''">
                 and industry_code = #{industry}
            </if>
            <if test="connectType != null and connectType != -1">
                and product_type = #{connectType}
            </if>
        group by
            ${type}
   </select>
    <select id="queryDeviceWLANResult" resultType="com.cmiot.report.dto.device.DeviceWLANObject">
        select
            <if test="type == 'province_code'">
                dictGetString ('ge_report.t_dic_area_info', 'gname', province_code) AS name,
            </if>
            <if test="type == 'city_code'">
                dictGetString ('ge_report.t_dic_area_info', 'gname', city_code) AS name,
            </if>
            <if test="type == 'gateway_vendor'">
                dictGetString ('ge_report.t_dic_factory', 'factory_name', gateway_vendor) AS name,
            </if>
            <if test="type == 'gateway_model'">
                dictGetString ('ge_report.t_dic_device_model', 'device_model', gateway_model) AS name,
            </if>
            sum(sample_count_wlan * wlan_radio_power_avg) / sum(sample_count_wlan) as wlanPower,
            <if test="period == 'day'">
                sum(device_count_wlan) as deviceNum
            </if>
            <if test="period == 'week'">
                sum(device_count_wlan_last_week) as deviceNum
            </if>
            <if test="period == 'month'">
                sum(device_count_wlan_last_month) as deviceNum
            </if>
        from
            ge_report.t_device_stat_web_trend_wlan
        where
            1=1
            <if test="provinceCode !=null and provinceCode != ''">
                and province_code in
                <foreach collection="provinceCode" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="cityCode !=null and cityCode != ''">
                and city_code in
                <foreach collection="cityCode" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vendorCode !=null and vendorCode != ''">
                and gateway_vendor in
                <foreach collection="vendorCode" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="modelCode !=null and modelCode != ''">
                and gateway_model in
                <foreach collection="modelCode" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null and startTime != ''">
                and sample_date >= toDate(#{startTime})
            </if>
            <if test="endTime !=null and endTime != ''">
                and sample_date &lt;= toDate(#{endTime})
            </if>
        group by
            ${type}
    </select>

    <select id="queryDeviceAppStatCountTotal" resultType="com.cmiot.report.dto.device.DeviceAppStatCount">
    select
        count(*) as total,
        countIf(wlan_radio_type,wlan_radio_type='') as wiredDevice,
        countIf(wlan_radio_type,wlan_radio_type!='') as wirelessDevice,
        countIf(sample_date  = (today()-1)) as dayActive,
        countIf(sample_date >= (today()-7)) as weekActive,
        countIf(sample_date >= (today()-30)) as monthActive
    from
        ge_report.t_device_replacing_all
    where
        enterprise_id = ${enterprise_id}
    group by
        enterprise_id
    </select>
    <select id="queryDeviceAppStatCountTotalWeek" resultType="com.cmiot.report.dto.device.DeviceAppStatCount">
        select
            uniqExact(mac) as weekActive
        from
            ge_report.t_device_day_all
        where
            enterprise_id = ${enterpriseId}
            and toYYYYMMDD(sample_date) &gt;= #{dayStart} and toYYYYMMDD(sample_date) &lt;= #{dayEnd}
    </select>
    <select id="queryDeviceAppStatCountTotalMonth" resultType="com.cmiot.report.dto.device.DeviceAppStatCount">
        select
            uniqExact(mac)  as monthActive
        from
            ge_report.t_device_day_all
        where
            enterprise_id = ${enterpriseId}
          and toYYYYMM(sample_date)= #{lastMonth}
    </select>
    <select id="queryDeviceAppStatTrendCount" resultType="com.cmiot.report.dto.device.DeviceAppStatTrendObject">
        select
            sample_date as date,
            total as value
        from
            ge_report.t_device_stat_app_trend_active
        where
            enterprise_id = ${enterpriseId}
            and sample_date >= toDate(#{startDate})
            and sample_date &lt;= toDate(#{endDate})
    </select>
    <select id="queryDeviceAppStatTrendWlan" resultType="com.cmiot.report.dto.device.DeviceAppStatTrendObject">
        select
            sample_date as date,
            avg_wlan as value
        from
            ge_report.t_device_stat_app_trend_wlan
        where
                enterprise_id = ${enterpriseId}
            and sample_date >= toDate(#{startDate})
            and sample_date &lt;= toDate(#{endDate})
    </select>
    <select id="queryDeviceAppStatRunCount" resultType="int">
        select
            count(*) as subDeviceNumber
        from
            ge_report.t_device_replacing_all
        where
                enterprise_id = ${eid}
            and sample_date >= toDate(#{startTime})
            and sample_date &lt;= toDate(#{endTime})
    </select>


    <!-- 获取用户下挂设备统计信息 -->
    <select id="queryDeviceAppStatRunSpeed" resultType="com.cmiot.report.dto.DeviceAppStatRunVo">
        select t1.subDeviceCount as subDeviceCount,t1.subDeviceRateAverage as subDeviceRateAverage,if(or(isNaN(snCount/allSnCount),equals(snCount/allSnCount,inf)),0,round(snCount*100/allSnCount,2)) as subDeviceConnectRateAvg,t1.subDeviceTraffic as subDeviceTraffic,
        t1.subDeviceWifiConnectTime as subDeviceWifiConnectTime,t4.wifiUserCount as wifiUserCount,t5.favoriteWebsite as favoriteWebsite,t5.subDeviceWifiVisitWebTime as subDeviceWifiVisitWebTime,t5.subDeviceVisitWebCount as subDeviceVisitWebCount
        from (
        select max(subDeviceCount) as subDeviceCount,max(subDeviceRateAverage) as subDeviceRateAverage,max(snCount) as snCount,max(subDeviceTraffic) as subDeviceTraffic,max(subDeviceWifiConnectTime) as subDeviceWifiConnectTime from (select subDeviceCount,snCount,if(uniqExact(tt2.mac)==0,0,round(sum(tt2.speeds)/uniqExact(tt2.mac)/1024/1024/15/60,2)) as subDeviceRateAverage,subDeviceTraffic,subDeviceWifiConnectTime from (select if(or(isNaN(floor(uniqExact(mac)/uniqExact(toYYYYMMDD(sample_time)))),equals(floor(uniqExact(mac)/uniqExact(toYYYYMMDD(sample_time))),inf),equals(floor(uniqExact(mac)/uniqExact(toYYYYMMDD(sample_time))),-inf)),0,floor(uniqExact(mac)/uniqExact(toYYYYMMDD(sample_time)))) as subDeviceCount,
        uniqExact(gateway_sn) as snCount,
        round(sum(plus(up_staticstics,down_staticstics))/1024/1024/1024,2) as subDeviceTraffic,
        if(or(isNaN(round((count()*15/60)/uniqExact(mac)/uniqExact(toDate(sample_time)),2)),equals(round((count()*15/60)/uniqExact(mac)/uniqExact(toDate(sample_time)),2),inf),equals(round((count()*15/60)/uniqExact(mac)/uniqExact(toDate(sample_time)),2),-inf)),0,round((count()*15/60)/uniqExact(mac)/uniqExact(toDate(sample_time)),2))
        as subDeviceWifiConnectTime
        from
        t_subdevice_app_report_all
        where enterprise_id = ${eid}
        and toDate(sample_time) >= toDate(#{startTime})
        and toDate(sample_time) &lt;= toDate(#{endTime})
        and startsWith(lower(lan_port),'ssid')) tt
        left join (select mac,(sum(down_staticstics)/count()) as speeds from
        t_subdevice_app_report_all
        where enterprise_id = ${eid}
        and toDate(sample_time) >= toDate(#{startTime})
        and toDate(sample_time) &lt;= toDate(#{endTime})
        and startsWith(lower(lan_port),'ssid') group by mac) tt2 on 1=1
        group by subDeviceCount,snCount,subDeviceTraffic,subDeviceWifiConnectTime
        union all
        select 0.0 as subDeviceCount,0 as subDeviceRateAverage,toDecimal32(0.0,4) as subDeviceConnectRateAvg,toDecimal32(0.0,4) as subDeviceTraffic,
        0.0 as subDeviceWifiConnectTime) ts
        ) t1
        left join (
        select max(allSubCount) as allSubCount from (select uniqExact(mac) as allSubCount from t_subdevice_app_report_all
        where enterprise_id = ${eid}
        and toDate(sample_time) >= toDate(#{startTime})
        and toDate(sample_time) &lt;= toDate(#{endTime})
        union all
        select 0 as allSubCount) ts
        ) t2 on 1=1
        left join (
        select max(wifiUserCount) as wifiUserCount from (select uniqExact(mac) as wifiUserCount from t_subdevice_app_report_all
        where enterprise_id = ${eid}
        and toDate(sample_time) = toDate(now())
        and dateDiff('second', sample_time, now()) &lt;= 15*60
        union all
        select 0 as allSubCount) ts
        ) t4 on 1=1
        left join (
        select max(favoriteWebsite) as favoriteWebsite, max(subDeviceWifiVisitWebTime) as subDeviceWifiVisitWebTime, max(subDeviceVisitWebCount) as subDeviceVisitWebCount from (select max(favoriteWebsite) as favoriteWebsite, max(subDeviceWifiVisitWebTime) as subDeviceWifiVisitWebTime, max(subDeviceVisitWebCount) as subDeviceVisitWebCount from (select favoriteWebsite, subDeviceWifiVisitWebTime, subDeviceVisitWebCount from
        (select if(equals(tt2.domain_name,''),'-',tt2.domain_name) as favoriteWebsite,if(equals(tt2.domain_name,''),0,round((tt2.vtime/60)/subCount,2)) as subDeviceWifiVisitWebTime,if(equals(tt2.domain_name,''),0,round(tt2.count/subCount,2)) as subDeviceVisitWebCount from
        (select tt1.*,row_number() over (partition by tt1.tag order by (tt1.count,tt1.vtime) desc) rank from
        (select 1 as tag,if(equals(domain_name,'其他'),'',domain_name) as domain_name,if(equals(domain_name,''),0,sum(visit_duration)) as vtime,if(equals(domain_name,''),1,uniqExact(mac)) as subCount,if(equals(domain_name,''),1,count()) as count from t_hgu_network_visit_detail_all
        where enterprise_id = ${eid} and toDate(sample_time) >= addDays(now(),-365) group by domain_name) tt1)tt2 where tt2.rank=1) tt union all select '-' as favoriteWebsite,0.0 as subDeviceWifiVisitWebTime,0.0 as subDeviceVisitWebCount) tt2) ta
        ) t5 on 1=1
        left join (select max(allSnCount) as allSnCount from (select uniqExact(gateway_sn) as allSnCount from t_gateway_all where enterprise_id = ${eid} and toDate(sample_time) = toDate(addDays(now(),-1))
        union all
        select 0 as allSnCount) ts
        ) t6 on 1=1
    </select>

    <!--
    <select id="queryDeviceAppStatRunSpeed" resultType="com.cmiot.report.dto.DeviceAppStatRunVo">
        select
        round(sum(down_staticstics)/1024/1024/count(*)/15/60,2) as subDeviceRateAverage
        from
        ge_report.t_device_all
        where
        enterprise_id = ${eid}
        and toDate(sample_time) >= toDate(#{startTime})
        and toDate(sample_time) &lt;= toDate(#{endTime})
        and startsWith(lan_port,'ssid')
        group by
        enterprise_id
    </select> -->

    <select id="queryDeviceFlowRank" resultType="com.cmiot.report.dto.device.DeviceFlowInfo">
        select t1.mac, t1.device_name as deviceName, t1.dhcp_name as dhcpName, t1.`type` as deviceType, t1.flowtotal as flow
        from (select
             mac, any(device_name) as device_name, any(dhcp_name) as dhcp_name,
             any(`type`) as `type`, sum(up_staticstics + down_staticstics) / 1024 / 1024 /** MB **/ as flowtotal
         from
             ge_report.t_device_day_all
         where
             enterprise_id = #{enterpriseId}
           and toYYYYMMDD(sample_date) &gt;= #{startDate} and toYYYYMMDD(sample_date) &lt;= #{endDate}
         group by mac
        ) t1
        where t1.flowtotal > 0
        order by t1.flowtotal desc
        limit ${limit}

    </select>

    <select id="queryDeviceTimesRank" resultType="com.cmiot.report.dto.device.DeviceTimesInfo">
        select t1.mac, t1.device_name as deviceName, t1.dhcp_name as dhcpName,
               t1.`type` as deviceType, t1.sample_count as times
        from (select
                  mac, any(device_name) as device_name, any(dhcp_name) as dhcp_name,
                  any(`type`) as `type`, sum(sample_count) as sample_count
              from
                  ge_report.t_device_day_all
              where
                  enterprise_id = #{enterpriseId}
                and toYYYYMMDD(sample_date) &gt;= #{startDate} and toYYYYMMDD(sample_date) &lt;= #{endDate}
              group by mac
             ) t1
        where t1.sample_count > 0
        order by t1.sample_count desc
            limit ${limit}

    </select>
</mapper>