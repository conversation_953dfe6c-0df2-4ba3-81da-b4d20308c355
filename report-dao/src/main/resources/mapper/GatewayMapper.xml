<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmiot.report.mapper.GatewayMapper">

    <select id="getGatewayBaseDetail" resultType="com.cmiot.report.dto.GatewayDetailResult">
        select gateway_sn       as gatewaySn,
               adsl_account     as adslAccount,
               gateway_mac      as gatewayMac,
               wan_ip_addr      as wanIpAddr,
               wan_ipv6_addr    as wanIpv6Addr,
               factory_name     as factoryName,
               product_type     as productType,
               product_band     as productBand,
               hardware_version as hardwareVersion,
               firmware_version as firmwareVersion,
               os_version       as osVersion,
               register_time    as registerTime,
               lan_count        as lanCount,
               usb_count        as usbCount,
               cpu_class        as cpuClass,
               ram_size         as ramSize
        from t_gateway_all
        where sample_time = (select max(sample_time) from t_gateway_all where gateway_sn = #{query.gatewaySn})
          and gateway_sn = #{query.sn}
    </select>

    <select id="getGatewayRunDataDetail" resultType="com.cmiot.report.dto.GatewayDetailResult">
        select ROUND(avg(aver_txrate) / 8 / 1024, 2) as avgTxrate,
               ROUND(avg(aver_rxrate) / 8 / 1024, 2) as avgRxrate,
               ROUND(max(max_txrate) / 8 / 1024, 2)  as maxTxrate,
               ROUND(max(max_rxrate) / 8 / 1024, 2)  as maxRxrate
        from t_gateway_period_detail
        where toDate(sample_time) = toDate(now())
          and gateway_sn = #{query.sn}
        group by gateway_sn
    </select>

    <select id="getGatewayVersionStatistics" resultType="com.cmiot.report.dto.GatewayVersionResult">
        select count() as total,
        firmware_version as version,
        dictGetDateTime('ge_report.t_dic_firmware_info', 'create_time', toUInt64(firmware_version_id)) as createTime,
        factory_name as vendor,
        device_model as model,
        dictGet('ge_report.t_dic_area_info', 'gname', province_code) as province
        from t_gateway_all
        where toDate(sample_time) = toDate(addDays(now(), -1))
        <if test="query.province !=null and query.province !=''">
            and province_code in
            <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendor !=null and query.vendor !=''">
            and factory_id in
            <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.model !=null and query.model !=''">
            and device_model_id in
            <foreach item="item" collection="query.modelList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by firmware_version,firmware_version_id, factory_name, device_model, province
        order by createTime
    </select>

    <select id="getGatewayFlowList" resultType="com.cmiot.report.dto.GatewayFlowListResult">
        select
        dictGet('ge_report.t_dic_area_info', 'gname', province_code) as province,
        dictGet('ge_report.t_dic_area_info', 'gname', city_code) as city,
        customer_name as customerName,
        gateway_sn as gatewaySn,
        gateway_mac as mac,
        dictGet('ge_report.t_dic_factory', 'factory_name', gateway_vendor_id) as vendor,
        dictGet('ge_report.t_dic_device_model', 'device_model', gateway_productclass_id) as model,
        argMax(runing_time, record_time) as runingTime
        from t_gateway_period_aggr_all
        where toDate(record_time) &gt;= toDate(#{query.startTime})
        and toDate(record_time) &lt;= toDate(#{query.endTime})
        <if test="query.province !=null and query.province !=''">
            and province_code in
            <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null and query.city !=''">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendor !=null and query.vendor !=''">
            and gateway_vendor_id in
            <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.sn !=null and query.sn !=''">
            and gateway_sn in
            <foreach item="item" collection="query.gatewaySnList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by province_code,city_code,customer_name,gateway_sn,gateway_mac,gateway_vendor_id,gateway_productclass_id
        order by gateway_sn
    </select>

    <select id="getGatewayFlowOverview" resultType="com.cmiot.report.dto.GatewayFlowTrendResult">
        select
        ROUND(avg(aver_txrate)/8/1024, 2) as averTxrate,
        ROUND(avg(aver_rxrate)/8/1024, 2) as averRxrate,
        ROUND(max(max_txrate)/8/1024, 2) as maxTxrate,
        ROUND(max(max_rxrate)/8/1024, 2) as maxRxrate
        from t_gateway_period_aggr_all
        where toDate(record_time) &gt;= toDate(#{query.startTime})
        and toDate(record_time) &lt;= toDate(#{query.endTime})
        <if test="query.province !=null and query.province !=''">
            and province_code in
            <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null and query.city !=''">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendor !=null and query.vendor !=''">
            and gateway_vendor_id in
            <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getGatewayFlowTrend" resultType="com.cmiot.report.dto.GatewayFlowTrendResult">
        select toDate(record_time) as recordTime,
        ROUND(avg(aver_txrate)/8/1024, 2) as averTxrate,
        ROUND(avg(aver_rxrate)/8/1024, 2) as averRxrate,
        ROUND(max(max_txrate)/8/1024, 2) as maxTxrate,
        ROUND(max(max_rxrate)/8/1024, 2) as maxRxrate
        from t_gateway_period_aggr_all
        where toDate(record_time) &gt;= toDate(#{query.startTime})
        and toDate(record_time) &lt;= toDate(#{query.endTime})
        <if test="query.eid !=null and query.eid !=''">
            and enterprise_id = #{query.eid}
        </if>
        <if test="query.province !=null and query.province !=''">
            and province_code in
            <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null and query.city !=''">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendor !=null and query.vendor !=''">
            and gateway_vendor_id in
            <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.sn !=null and query.sn !=''">
            and gateway_sn in
            <foreach item="item" collection="query.gatewaySnList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by recordTime
        order by recordTime
    </select>

    <!--    <select id="getGatewayMaxCpuRamTime" resultType="com.cmiot.report.dto.GatewayCpuAndRamMaxTimeResult">-->
    <!--        select ROUND(max(cpu_rate_max), 2)             as cpuMax,-->
    <!--               argMax(cpu_rate_max_time, cpu_rate_max) as cpuMaxTime,-->
    <!--               ROUND(max(ram_rate_max), 2)             as ramMax,-->
    <!--               argMax(ram_rate_max_time, ram_rate_max) as ramMaxTime-->
    <!--        from t_gateway_period_aggr_all-->
    <!--        where toDate(record_time) &gt;= toDate(#{query.startTime})-->
    <!--          and toDate(record_time) &lt;= toDate(#{query.endTime})-->
    <!--          and province_code = #{query.province}-->
    <!--          and gateway_vendor = #{query.vendor}-->
    <!--          and gateway_sn = #{query.gatewaySn}-->
    <!--    </select>-->

<!--    <select id="getGatewayCpuAndRamOverview" resultType="com.cmiot.report.dto.GatewayCpuAndRamResult">
        select
        ROUND(avg(cpu_rate_avg), 2) as cpuAvg,
        ROUND(avg(ram_rate_avg), 2) as ramAvg
        from t_gateway_period_aggr_all
        where toDate(record_time) &gt;= toDate(#{query.startTime})
        and toDate(record_time) &lt;= toDate(#{query.endTime})
        <if test="query.province !=null and query.province !=''">
            and province_code in
            <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null and query.city !=''">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendor !=null and query.vendor !=''">
            and gateway_vendor_id in
            <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.model !=null and query.model !=''">
            and gateway_productclass_id in
            <foreach item="item" collection="query.modelList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>-->


    <select id="getGatewayCpuAndRamOverview" resultType="com.cmiot.report.dto.GatewayCpuAndRamResult">
        SELECT ROUND(avg(95_top_cpu), 2) as cpuAvg, ROUND(avg(95_top_ram), 2) as ramAvg
        FROM t_gateway_period_aggr_all
        <include refid="queryCondition"/>
    </select>

    <sql id="queryCondition">
        where toDate(record_time) &gt;= toDate(#{query.startTime})
        and toDate(record_time) &lt;= toDate(#{query.endTime})
        <if test="query.province !=null and query.province !=''">
            and province_code in
            <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null and query.city !=''">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendor !=null and query.vendor !=''">
            and gateway_vendor_id in
            <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.model !=null and query.model !=''">
            and gateway_productclass_id in
            <foreach item="item" collection="query.modelList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>


<!--    <select id="getGatewayCpuAndRamStatistics" resultType="com.cmiot.report.dto.GatewayCpuAndRamResult">
        select
        <choose>
            <when test="query.groupType == 1">
                dictGet('ge_report.t_dic_area_info', 'gname', province_code) as province,
            </when>
            <when test="query.groupType == 5">
                dictGet('ge_report.t_dic_area_info', 'gname', city_code) as city,
            </when>
            <when test="query.groupType == 2">
                dictGet('ge_report.t_dic_factory', 'factory_name', gateway_vendor_id) as gatewayVendor,
            </when>
            <when test="query.groupType == 3">
                gateway_productclass_id,
                gateway_productclass as deviceModel,
            </when>
            <otherwise>
                toDate(record_time) as recordTime,
            </otherwise>
        </choose>
        ROUND(avg(cpu_rate_avg), 2) as cpuAvg,
        ROUND(avg(ram_rate_avg), 2) as ramAvg
        from t_gateway_period_aggr_all
        where toDate(record_time) &gt;= toDate(#{query.startTime})
        and toDate(record_time) &lt;= toDate(#{query.endTime})
        <if test="query.eid !=null and query.eid !=''">
            and enterprise_id = #{query.eid}
        </if>
        <if test="query.sn !=null and query.sn !=''">
            and gateway_sn in
            <foreach item="item" collection="query.gatewaySnList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.province !=null and query.province !=''">
            and province_code in
            <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null and query.city !=''">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendor !=null and query.vendor !=''">
            and gateway_vendor_id in
            <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.model !=null and query.model !=''">
            and gateway_productclass_id in
            <foreach item="item" collection="query.modelList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="query.groupType == 1">
                group by province
                order by province
            </when>
            <when test="query.groupType == 5">
                group by city
                order by city
            </when>
            <when test="query.groupType == 2">
                group by gateway_vendor_id,gateway_vendor
                order by gatewayVendor
            </when>
            <when test="query.groupType == 3">
                group by gateway_productclass_id,gateway_productclass
                order by deviceModel
            </when>
            <otherwise>
                group by toDate(record_time)
                order by toDate(record_time)
            </otherwise>
        </choose>
    </select>-->


    <select id="getGatewayCpuAndRamStatistics" resultType="com.cmiot.report.dto.GatewayCpuAndRamResult">
        select
        <choose>
            <when test="query.groupType == 1">
                dictGet('ge_report.t_dic_area_info', 'gname', province_code) as province,
            </when>
            <when test="query.groupType == 5">
                dictGet('ge_report.t_dic_area_info', 'gname', city_code) as city,
            </when>
            <when test="query.groupType == 2">
                dictGet('ge_report.t_dic_factory', 'factory_name', gateway_vendor_id) as gatewayVendor,
            </when>
            <when test="query.groupType == 3">
                gateway_productclass_id,
                gateway_productclass as deviceModel,
            </when>
            <otherwise>
                toDate(record_time) as recordTime,
            </otherwise>
        </choose>
        ROUND(avg(95_top_cpu), 2) as cpuAvg,
        ROUND(avg(95_top_ram), 2) as ramAvg
        FROM t_gateway_period_aggr_all
        <include refid="queryCondition2"/>
        <choose>
            <when test="query.groupType == 1">
                group by province
                order by province
            </when>
            <when test="query.groupType == 5">
                group by city
                order by city
            </when>
            <when test="query.groupType == 2">
                group by gateway_vendor_id,gateway_vendor
                order by gatewayVendor
            </when>
            <when test="query.groupType == 3">
                group by gateway_productclass_id,gateway_productclass
                order by deviceModel
            </when>
            <otherwise>
                group by toDate(record_time)
                order by toDate(record_time)
            </otherwise>
        </choose>
    </select>

    <sql id="queryCondition2">
        where toDate(record_time) &gt;= toDate(#{query.startTime})
        and toDate(record_time) &lt;= toDate(#{query.endTime})
        <if test="query.eid !=null and query.eid !=''">
            and enterprise_id = #{query.eid}
        </if>
        <if test="query.sn !=null and query.sn !=''">
            and gateway_sn in
            <foreach item="item" collection="query.gatewaySnList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.province !=null and query.province !=''">
            and province_code in
            <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null and query.city !=''">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendor !=null and query.vendor !=''">
            and gateway_vendor_id in
            <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.model !=null and query.model !=''">
            and gateway_productclass_id in
            <foreach item="item" collection="query.modelList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>


    <select id="rangeCpuAndRamOverLevelRatio" resultType="com.cmiot.report.dto.GatewayCpuAndRamResult">
        SELECT
            <choose>
                <when test="query.groupType == 1">
                    dictGet('ge_report.t_dic_area_info', 'gname', province_code) as province,
                </when>
                <when test="query.groupType == 5">
                    dictGet('ge_report.t_dic_area_info', 'gname', city_code) as city,
                </when>
                <when test="query.groupType == 2">
                    dictGet('ge_report.t_dic_factory', 'factory_name', gateway_vendor_id) as gatewayVendor,
                </when>
                <when test="query.groupType == 3">
                    gateway_productclass_id,
                    gateway_productclass as deviceModel,
                </when>
                <otherwise>
                    toDate(record_time) as recordTime,
                </otherwise>
            </choose>
            ROUND(SUM(CASE WHEN avgCpu > 75 THEN 1 ELSE 0 END) * 100 / count(*), 2) AS cpuAvg,
            ROUND(SUM(CASE WHEN avgRam > 75 THEN 1 ELSE 0 END) * 100 / count(*), 2) AS ramAvg
        FROM (
            SELECT
                <choose>
                    <when test="query.groupType == 1">
                        province_code
                    </when>
                    <when test="query.groupType == 5">
                        city_code
                    </when>
                    <when test="query.groupType == 2">
                        gateway_vendor_id, gateway_vendor
                    </when>
                    <when test="query.groupType == 3">
                        gateway_productclass_id, gateway_productclass
                    </when>
                    <otherwise>
                        toDate(record_time) as record_time
                    </otherwise>
                </choose>
                , gateway_sn
                , AVG(95_top_cpu) as avgCpu
                , AVG(95_top_ram) as avgRam
            FROM t_gateway_period_aggr_all
            <include refid="queryCondition2"/>
            <choose>
                <when test="query.groupType == 1">
                    group by province_code
                </when>
                <when test="query.groupType == 5">
                    group by city_code
                </when>
                <when test="query.groupType == 2">
                    group by gateway_vendor_id, gateway_vendor
                </when>
                <when test="query.groupType == 3">
                    group by gateway_productclass_id, gateway_productclass
                </when>
                <otherwise>
                    group by toDate(record_time)
                </otherwise>
            </choose>
           , gateway_sn
        )
        <choose>
            <when test="query.groupType == 1">
                group by province
                order by province
            </when>
            <when test="query.groupType == 5">
                group by city
                order by city
            </when>
            <when test="query.groupType == 2">
                group by gateway_vendor_id,gateway_vendor
                order by gatewayVendor
            </when>
            <when test="query.groupType == 3">
                group by gateway_productclass_id,gateway_productclass
                order by deviceModel
            </when>
            <otherwise>
                group by toDate(record_time)
                order by toDate(record_time)
            </otherwise>
        </choose>
    </select>



<!--    <select id="getGatewayCpuGradStatistics" resultType="com.cmiot.report.dto.GatewayCpuOrRamRateResult">
        select countIf(cpuAvgAggr &gt;= 0 and cpuAvgAggr &lt;= 10) as lv1,
        countIf(cpuAvgAggr &gt; 10 and cpuAvgAggr &lt;= 20) as lv2,
        countIf(cpuAvgAggr &gt; 20 and cpuAvgAggr &lt;= 30) as lv3,
        countIf(cpuAvgAggr &gt; 30 and cpuAvgAggr &lt;= 40) as lv4,
        countIf(cpuAvgAggr &gt; 40 and cpuAvgAggr &lt;= 50) as lv5,
        countIf(cpuAvgAggr &gt; 50 and cpuAvgAggr &lt;= 60) as lv6,
        countIf(cpuAvgAggr &gt; 60 and cpuAvgAggr &lt;= 70) as lv7,
        countIf(cpuAvgAggr &gt; 70 and cpuAvgAggr &lt;= 80) as lv8,
        countIf(cpuAvgAggr &gt; 80 and cpuAvgAggr &lt;= 90) as lv9,
        countIf(cpuAvgAggr &gt; 90 and cpuAvgAggr &lt;= 100) as lv10,
        countIf(cpuAvgAggr &gt; 100 or cpuAvgAggr &lt; 0) as lv0
        from
        (
        select avg(cpu_rate_avg) as cpuAvgAggr
        from t_gateway_period_aggr_all
        where toDate(record_time) &gt;= toDate(#{query.startTime})
        and toDate(record_time) &lt;= toDate(#{query.endTime})
        <if test="query.province !=null and query.province !=''">
            and province_code in
            <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null and query.city !=''">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendor !=null and query.vendor !=''">
            and gateway_vendor_id in
            <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.model !=null and query.model !=''">
            and gateway_productclass_id in
            <foreach item="item" collection="query.modelList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by gateway_sn
        )
    </select>-->


    <select id="getGatewayCpuGradStatistics" resultType="com.cmiot.report.dto.GatewayCpuOrRamRateResult">
        select countIf(cpuAvgAggr &gt;= 0 and cpuAvgAggr &lt;= 10) as lv1,
        countIf(cpuAvgAggr &gt; 10 and cpuAvgAggr &lt;= 20) as lv2,
        countIf(cpuAvgAggr &gt; 20 and cpuAvgAggr &lt;= 30) as lv3,
        countIf(cpuAvgAggr &gt; 30 and cpuAvgAggr &lt;= 40) as lv4,
        countIf(cpuAvgAggr &gt; 40 and cpuAvgAggr &lt;= 50) as lv5,
        countIf(cpuAvgAggr &gt; 50 and cpuAvgAggr &lt;= 60) as lv6,
        countIf(cpuAvgAggr &gt; 60 and cpuAvgAggr &lt;= 70) as lv7,
        countIf(cpuAvgAggr &gt; 70 and cpuAvgAggr &lt;= 80) as lv8,
        countIf(cpuAvgAggr &gt; 80 and cpuAvgAggr &lt;= 90) as lv9,
        countIf(cpuAvgAggr &gt; 90 and cpuAvgAggr &lt;= 100) as lv10,
        countIf(cpuAvgAggr &gt; 100 or cpuAvgAggr &lt; 0) as lv0
        FROM (
            SELECT gateway_sn, ROUND(AVG(95_top_cpu), 2) as cpuAvgAggr
            FROM t_gateway_period_aggr_all
            <include refid="queryCondition"/>
            GROUP BY gateway_sn
        )
    </select>

<!--    <select id="getGatewayRamGradStatistics" resultType="com.cmiot.report.dto.GatewayCpuOrRamRateResult">
        select countIf(ramAvgAggr &gt;= 0 and ramAvgAggr &lt;= 10) as lv1,
        countIf(ramAvgAggr &gt; 10 and ramAvgAggr &lt;= 20) as lv2,
        countIf(ramAvgAggr &gt; 20 and ramAvgAggr &lt;= 30) as lv3,
        countIf(ramAvgAggr &gt; 30 and ramAvgAggr &lt;= 40) as lv4,
        countIf(ramAvgAggr &gt; 40 and ramAvgAggr &lt;= 50) as lv5,
        countIf(ramAvgAggr &gt; 50 and ramAvgAggr &lt;= 60) as lv6,
        countIf(ramAvgAggr &gt; 60 and ramAvgAggr &lt;= 70) as lv7,
        countIf(ramAvgAggr &gt; 70 and ramAvgAggr &lt;= 80) as lv8,
        countIf(ramAvgAggr &gt; 80 and ramAvgAggr &lt;= 90) as lv9,
        countIf(ramAvgAggr &gt; 90 and ramAvgAggr &lt;= 100) as lv10,
        countIf(ramAvgAggr &gt; 100 or ramAvgAggr &lt; 0) as lv0
        from
        (
        select avg(ram_rate_avg) as ramAvgAggr
        from t_gateway_period_aggr_all
        where toDate(record_time) &gt;= toDate(#{query.startTime})
        and toDate(record_time) &lt;= toDate(#{query.endTime})
        <if test="query.province !=null and query.province !=''">
            and province_code in
            <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.city !=null and query.city !=''">
            and city_code in
            <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.vendor !=null and query.vendor !=''">
            and gateway_vendor_id in
            <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.model !=null and query.model !=''">
            and gateway_productclass_id in
            <foreach item="item" collection="query.modelList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by gateway_sn
        )
    </select>-->


    <select id="getGatewayRamGradStatistics" resultType="com.cmiot.report.dto.GatewayCpuOrRamRateResult">
        select countIf(ramAvgAggr &gt;= 0 and ramAvgAggr &lt;= 10) as lv1,
        countIf(ramAvgAggr &gt; 10 and ramAvgAggr &lt;= 20) as lv2,
        countIf(ramAvgAggr &gt; 20 and ramAvgAggr &lt;= 30) as lv3,
        countIf(ramAvgAggr &gt; 30 and ramAvgAggr &lt;= 40) as lv4,
        countIf(ramAvgAggr &gt; 40 and ramAvgAggr &lt;= 50) as lv5,
        countIf(ramAvgAggr &gt; 50 and ramAvgAggr &lt;= 60) as lv6,
        countIf(ramAvgAggr &gt; 60 and ramAvgAggr &lt;= 70) as lv7,
        countIf(ramAvgAggr &gt; 70 and ramAvgAggr &lt;= 80) as lv8,
        countIf(ramAvgAggr &gt; 80 and ramAvgAggr &lt;= 90) as lv9,
        countIf(ramAvgAggr &gt; 90 and ramAvgAggr &lt;= 100) as lv10,
        countIf(ramAvgAggr &gt; 100 or ramAvgAggr &lt; 0) as lv0
        FROM (
            SELECT gateway_sn, ROUND(AVG(95_top_ram), 2) as ramAvgAggr
            FROM t_gateway_period_aggr_all
            <include refid="queryCondition"/>
            GROUP BY gateway_sn
        )
    </select>



    <select id="getGatewayLatestFlow" resultType="com.cmiot.report.dto.GatewayCurrentResult">
        select argMax(sample_time, aver_txrate) as averTxrate,
               argMax(sample_time, aver_rxrate) as averRxrate,
               argMax(sample_time, cpu)         as cpu,
               argMax(sample_time, ram)         as ram
        from t_gateway_period_all
        where gateway_sn = #{query.gatewaySn}
    </select>

    <select id="getGatewayCpuRamList" resultType="com.cmiot.report.dto.GatewayCpuRamListResult">
        select
        dictGet('ge_report.t_dic_area_info', 'gname', province_code) as province,
        dictGet('ge_report.t_dic_area_info', 'gname', city_code) as city,
        customer_name as customerName,
        gateway_sn as gatewaySn,
        gateway_mac as mac,
        dictGet('ge_report.t_dic_factory', 'factory_name', gateway_vendor_id) as vendor,
        gateway_productclass as model,
        argMax(runing_time, record_time) as runingTime,
        ROUND(max(cpu_rate_max) , 2)as cpuMax,
        argMax(cpu_rate_max_time, cpu_rate_max) as cpuMaxTime,
        ROUND(max(ram_rate_max) , 2) as ramMax,
        argMax(ram_rate_max_time, ram_rate_max) as ramMaxTime
        from (
            select *
            from t_gateway_period_aggr_all
            where toDate(record_time) &gt;= toDate(#{query.startTime})
            and toDate(record_time) &lt;= toDate(#{query.endTime})
            <if test="query.province !=null and query.province !=''">
                and province_code in
                <foreach item="item" collection="query.provinceList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.city !=null and query.city !=''">
                and city_code in
                <foreach item="item" collection="query.cityList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.vendor !=null and query.vendor !=''">
                and gateway_vendor_id in
                <foreach item="item" collection="query.vendorList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.model !=null and query.model !=''">
                and gateway_productclass_id in
                <foreach item="item" collection="query.modelList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.sn !=null and query.sn !=''">
                and gateway_sn in
                <foreach item="item" collection="query.gatewaySnList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            order by record_time desc
        )
        group by province,city,customerName,gatewaySn,mac,vendor,model
        order by gateway_sn,province,city,customerName
    </select>

    <select id="getGatewayRunTime" resultType="java.lang.Integer">
        select ceiling(argMax(runing_time, sample_time)/60/60)
        from t_gateway_period_detail
        where toDate(sample_time) &gt;= toDate(#{query.startTime})
        and toDate(sample_time) &lt;= toDate(#{query.endTime})
        <if test="query.eid !=null and query.eid !=''">
            and enterprise_id = #{query.eid}
        </if>
        group by gateway_sn
    </select>

    <select id="getGatewayRunTimeOver" resultType="com.cmiot.report.dto.GatewayRunTimeDetail">
        select
        gateway_name as name,
        argMax(runing_time, sample_time) as duration,
        gateway_mac as mac
        from t_gateway_period_all
        where toDate(sample_time) &gt;= toDate(#{query.startTime})
        and toDate(sample_time) &lt;= toDate(#{query.endTime})
        <if test="query.eid !=null and query.eid !=''">
            and enterprise_id = #{query.eid}
        </if>
        group by gateway_name,gateway_mac
        having duration &gt;= (#{query.overTime}*60*60)
    </select>

    <select id="getGatewayRunTimeOverCount" resultType="java.lang.Long">
        select uniqExact(tt.gateway_name) as count from (select
        gateway_name,
        argMax(runing_time, sample_time) as duration,
        gateway_mac as mac
        from t_gateway_period_all
        where toDate(sample_time) &gt;= toDate(#{query.startTime})
        and toDate(sample_time) &lt;= toDate(#{query.endTime})
        <if test="query.eid !=null and query.eid !=''">
            and enterprise_id = #{query.eid}
        </if>
        group by gateway_name,gateway_mac
        having duration &gt;= (#{query.overTime}*60*60)) tt
    </select>

    <select id="getGatewayWanBandwidth" resultType="com.cmiot.report.dto.GatewayWanBandwidthDetail">
        <if test="query.timeType != null and query.timeType == 1">
            SELECT `time` AS recordTime,
                   ROUND(avg(us_bandwidth)/1024, 2) AS upload,
                   ROUND(avg(ds_bandwidth)/1024, 2) AS download
            FROM t_wan_bandwidth_detail_all
            <where>
                gateway_mac = #{query.gatewayMac}
                <if test="query.startTime !=null and query.startTime !=''">
                    AND `time` >= toDateTime(#{query.startTime})
                </if>
            </where>
            GROUP BY `time`
            <if test="query.startTime != null and query.startTime != ''">
                ORDER BY `time` ASC
            </if>
            <if test="query.startTime == null or query.startTime == ''">
                ORDER BY `time` DESC
            </if>
            LIMIT 900
        </if>

        <if test="query.timeType != null and query.timeType == 2">
            SELECT
                TUMBLE_START(time3) AS recordTime,
                MAX(T.upload) AS upload,
                MAX(T.download) AS download
            FROM (
                SELECT `time` AS time2,
                    ROUND(avg(us_bandwidth)/1024, 2) AS upload,
                    ROUND(avg(ds_bandwidth)/1024, 2) AS download
                FROM ge_report.t_wan_bandwidth_detail_all
                WHERE gateway_mac = #{query.gatewayMac} AND `time` >= date_sub(day, 31, today())
                GROUP BY `time`
                ORDER BY `time` DESC
            ) T
            GROUP BY TUMBLE(T.time2, INTERVAL 30 minute) as time3
            ORDER BY time3 ASC
        </if>
    </select>

<!--    <select id="getGatewayConnectNumberStatistics"
            resultType="com.cmiot.report.dto.GateWayConnectNumberStatisticsResult">
        <if test="query.timeType != null and query.timeType == 1">
            SELECT max_connect_number AS connectNumber, COUNT(*) AS total
        </if>
        <if test="query.timeType != null and query.timeType != 1">
            SELECT ROUND(AVG(max_connect_number), 0) AS connectNumber, 1 AS total
        </if>
        FROM t_gateway_connect_number_day_count_all
        <where>
            <if test="query.provinceList != null and query.provinceList.size() > 0">
                province_code IN
                <foreach collection="query.provinceList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.cityList != null and query.cityList.size() > 0">
                AND city_code IN
                <foreach collection="query.cityList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.gatewaySnList != null and query.gatewaySnList.size() > 0">
                AND gateway_sn IN
                <foreach collection="query.gatewaySnList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="factoryIdList != null and factoryIdList.size() > 0">
                AND vendor IN
                <foreach collection="factoryIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deviceModelIdList != null and deviceModelIdList.size() > 0">
                AND model IN
                <foreach collection="deviceModelIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.startTime != null and query.endTime != null">
                AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
            </if>
        </where>
        <if test="query.timeType != null and query.timeType == 1">
            GROUP BY max_connect_number
        </if>
        <if test="query.timeType != null and query.timeType != 1">
            GROUP BY gateway_sn
        </if>
    </select>-->

    <select id="getGatewayConnectNumberStatistics"
            resultType="com.cmiot.report.dto.GateWayConnectNumberStatisticsResult">
        SELECT
            COUNT(*) AS total,
            SUM(CASE WHEN 1 &lt;= connectNumber AND connectNumber &lt;= 512
                THEN 1 ELSE 0 END) AS count1to512,
            SUM(CASE WHEN 512 &lt; connectNumber AND connectNumber &lt;= 1024
                THEN 1 ELSE 0 END) AS count513to1024,
            SUM(CASE WHEN 1024 &lt; connectNumber AND connectNumber &lt;= 1537
                THEN 1 ELSE 0 END) AS count1025to1537,
            SUM(CASE WHEN 1537 &lt; connectNumber AND connectNumber &lt;= 2048
                THEN 1 ELSE 0 END) AS count1538to2048,
            SUM(CASE WHEN 2048 &lt; connectNumber
                THEN 1 ELSE 0 END) AS count2049toup
        FROM (
            SELECT gateway_sn, MAX(max_connect_number) AS connectNumber
            FROM t_gateway_connect_number_day_count_all
            <include refid="whereCondition"/>
            GROUP BY gateway_sn
        )
    </select>


    <select id="getGatewayConnectNumberStatistics95"
            resultType="com.cmiot.report.dto.GateWayConnectNumberStatisticsResult">
        SELECT
            COUNT(*) AS total,
            SUM(CASE WHEN 1 &lt;= connectNumber AND connectNumber &lt;= 512
                THEN 1 ELSE 0 END) AS count1to512,
            SUM(CASE WHEN 512 &lt; connectNumber AND connectNumber &lt;= 1024
                THEN 1 ELSE 0 END) AS count513to1024,
            SUM(CASE WHEN 1024 &lt; connectNumber AND connectNumber &lt;= 1537
                THEN 1 ELSE 0 END) AS count1025to1537,
            SUM(CASE WHEN 1537 &lt; connectNumber AND connectNumber &lt;= 2048
                THEN 1 ELSE 0 END) AS count1538to2048,
            SUM(CASE WHEN 2048 &lt; connectNumber
                THEN 1 ELSE 0 END) AS count2049toup
        FROM (
            SELECT gateway_sn, ROUND(AVG(95_top_connect_numbers), 0) AS connectNumber
            FROM t_gateway_connect_number_day_count_all
            <include refid="whereCondition"/>
            GROUP BY gateway_sn
        )
    </select>

    <sql id="whereCondition">
        <where>
            <if test="query.provinceList != null and query.provinceList.size() > 0">
                province_code IN
                <foreach collection="query.provinceList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.cityList != null and query.cityList.size() > 0">
                AND city_code IN
                <foreach collection="query.cityList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.gatewaySnList != null and query.gatewaySnList.size() > 0">
                AND gateway_sn IN
                <foreach collection="query.gatewaySnList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="factoryIdList != null and factoryIdList.size() > 0">
                AND vendor IN
                <foreach collection="factoryIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deviceModelIdList != null and deviceModelIdList.size() > 0">
                AND model IN
                <foreach collection="deviceModelIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.startTime != null and query.endTime != null">
                AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
            </if>
        </where>
    </sql>


    <select id="getGatewayConnectNumberList" resultType="com.cmiot.report.dto.GateWayConnectNumberResult">
        <if test="query.timeType != null and query.timeType == 1">
            SELECT A.pdate AS pdate, A.gateway_sn AS gateway_sn, A.gateway_mac AS gateway_mac, A.province_code AS province_code, A.city_code AS city_code
            , A.customer_name AS customer_name, D.factory_name AS vendor, E.device_model AS model, A.avg_connect_number AS avg_connect_number
            , A.max_connect_number AS max_connect_number, A.runingTime AS runingTime, A.id AS id
            , B.gname AS province, C.gname AS city
        </if>
        <if test="query.timeType != null and query.timeType != 1">
            SELECT A.gateway_sn AS gateway_sn, A.gateway_mac AS gateway_mac
            , A.province_code AS province_code, A.city_code AS city_code
            , A.customer_name AS customer_name, D.factory_name AS vendor
            , E.device_model AS model, ROUND(AVG(A.avg_connect_number), 0) AS avg_connect_number
            , MAX(A.max_connect_number) AS max_connect_number, SUM(A.runingTime) AS runingTime
            , B.gname AS province, C.gname AS city
        </if>
        FROM t_gateway_connect_number_day_count_all A
        GLOBAL LEFT JOIN t_dic_area_info B ON A.province_code = B.gcode
        GLOBAL LEFT JOIN t_dic_area_info C ON A.city_code = C.gcode
        GLOBAL LEFT JOIN t_dic_factory D ON A.vendor = D.factory_id
        GLOBAL LEFT JOIN t_dic_device_model E ON A.model = E.device_model_id
        <where>
            <if test="query.provinceList != null and query.provinceList.size() > 0">
                A.province_code IN
                <foreach collection="query.provinceList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.cityList != null and query.cityList.size() > 0">
                AND A.city_code IN
                <foreach collection="query.cityList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.gatewaySnList != null and query.gatewaySnList.size() > 0">
                AND A.gateway_sn IN
                <foreach collection="query.gatewaySnList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="factoryIdList != null and factoryIdList.size() > 0">
                AND A.vendor IN
                <foreach collection="factoryIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deviceModelIdList != null and deviceModelIdList.size() > 0">
                AND A.model IN
                <foreach collection="deviceModelIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.startTime != null and query.endTime != null">
                AND toDate(#{query.startTime}) &lt;= toDate(A.pdate) AND toDate(A.pdate) &lt;= toDate(#{query.endTime})
            </if>
        </where>
        <if test="query.timeType != null and query.timeType != 1">
            GROUP BY A.gateway_sn, A.gateway_mac, A.province_code, A.city_code
            , A.customer_name, D.factory_name, E.device_model
            , B.gname, C.gname
        </if>
        <if test="query.orderType != null and query.orderType == -1">
            ORDER BY avg_connect_number DESC
        </if>
        <if test="query.orderType != null and query.orderType == 1">
            ORDER BY avg_connect_number ASC
        </if>
    </select>

    <select id="getGatewayConnectNumberBySn" resultType="com.cmiot.report.dto.GateWayConnectNumberStaticResult">
<!--
        <if test="query.ipVersion == null and query.wanConnType == null ">

        <if test="query.timeType != null and query.timeType == 1">
            SELECT pdate AS timeType,
                ROUND(AVG(avg_connect_number), 0) AS avgConnectNumber,
                MAX(max_connect_number) AS maxConnectNumber
            FROM t_gateway_connect_number_day_count_all
            <where>
                <if test="query.sn !=null and query.sn !=''">
                    gateway_sn = #{query.sn}
                </if>
                <if test="query.startTime != null and query.endTime != null">
                    AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
                </if>
            </where>
            GROUP BY pdate
            ORDER BY pdate ASC
        </if>

        <if test="query.timeType != null and query.timeType == 2">
            SELECT toYearWeek(toDate(pdate), 1) AS timeType,
            ROUND(AVG(avg_connect_number), 0) AS avg_connect_number,
            MAX(max_connect_number) AS max_connect_number
            from t_gateway_connect_number_day_count_all
            <where>
                <if test="query.sn !=null and query.sn !=''">
                    gateway_sn = #{query.sn}
                </if>
                <if test="query.startTime != null and query.endTime != null">
                    AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
                </if>
            </where>
            GROUP BY timeType
            ORDER BY timeType ASC
        </if>

        <if test="query.timeType != null and query.timeType == 3">
            SELECT toYYYYMM(pdate) AS timeType,
            ROUND(AVG(avg_connect_number), 0) AS avgConnectNumber,
            MAX(max_connect_number) AS maxConnectNumber
            from t_gateway_connect_number_day_count_all
            <where>
                <if test="query.sn !=null and query.sn !=''">
                    gateway_sn = #{query.sn}
                </if>
                <if test="query.startTime != null and query.endTime != null">
                    AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
                </if>
            </where>
            GROUP BY timeType
            ORDER BY timeType ASC
        </if>

        <if test="query.timeType != null and query.timeType == 4">
            SELECT toYear(pdate) AS timeType,
            ROUND(AVG(avg_connect_number), 0) AS avgConnectNumber,
            MAX(max_connect_number) AS maxConnectNumber
            from t_gateway_connect_number_day_count_all
            <where>
                <if test="query.sn !=null and query.sn !=''">
                    gateway_sn = #{query.sn}
                </if>
                <if test="query.startTime != null and query.endTime != null">
                    AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
                </if>
            </where>
            GROUP BY timeType
            ORDER BY timeType ASC
        </if>

        <if test="query.timeType != null and query.timeType == 5">
            SELECT sample_time AS timeType, connect_numbers AS maxConnectNumber
            FROM t_gateway_period_detail
            <where>
                <if test="query.sn !=null and query.sn !=''">
                    gateway_sn = #{query.sn}
                </if>
                <if test="query.startTime != null and query.endTime != null">
                    AND toDate(#{query.startTime}) &lt;= toDate(sample_time) AND toDate(sample_time) &lt;= toDate(#{query.endTime})
                </if>
            </where>
            ORDER BY sample_time ASC
        </if>

        </if>

        <if test="query.ipVersion != null or query.wanConnType != null ">
-->

            <if test="query.timeType != null and query.timeType == 1">
                SELECT pdate AS timeType
                <if test="query.ipVersion == null">
                    ,MAX(max_connect_number) AS maxConnectNumber
                </if>
                <if test="query.ipVersion == 'ipv4'">
                    ,MAX(max_ipv4_connection_count) AS maxConnectNumber
                </if>
                <if test="query.ipVersion == 'ipv6'">
                    ,MAX(max_ipv6_connection_count) AS maxConnectNumber
                </if>
                FROM t_gateway_connect_number_day_count_all
                <where>
                    <if test="query.sn !=null and query.sn !=''">
                        gateway_sn = #{query.sn}
                    </if>
                    <if test="query.startTime != null and query.endTime != null">
                        AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
                    </if>
                    <if test="query.wanConnType != null">
                        AND service_list = #{query.wanConnType}
                    </if>
                </where>
                GROUP BY pdate
                ORDER BY pdate ASC
            </if>

            <if test="query.timeType != null and query.timeType == 2">
                SELECT toYearWeek(toDate(pdate), 1) AS timeType
                <if test="query.ipVersion == null">
                    ,MAX(max_connect_number) AS maxConnectNumber
                </if>
                <if test="query.ipVersion == 'ipv4'">
                    ,MAX(max_ipv4_connection_count) AS maxConnectNumber
                </if>
                <if test="query.ipVersion == 'ipv6'">
                    ,MAX(max_ipv6_connection_count) AS maxConnectNumber
                </if>
                from t_gateway_connect_number_day_count_all
                <where>
                    <if test="query.sn !=null and query.sn !=''">
                        gateway_sn = #{query.sn}
                    </if>
                    <if test="query.startTime != null and query.endTime != null">
                        AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
                    </if>
                    <if test="query.wanConnType != null">
                        AND service_list = #{query.wanConnType}
                    </if>
                </where>
                GROUP BY timeType
                ORDER BY timeType ASC
            </if>

            <if test="query.timeType != null and query.timeType == 3">
                SELECT toYYYYMM(pdate) AS timeType
                <if test="query.ipVersion == null">
                    ,MAX(max_connect_number) AS maxConnectNumber
                </if>
                <if test="query.ipVersion == 'ipv4'">
                    ,MAX(max_ipv4_connection_count) AS maxConnectNumber
                </if>
                <if test="query.ipVersion == 'ipv6'">
                    ,MAX(max_ipv6_connection_count) AS maxConnectNumber
                </if>
                from t_gateway_connect_number_day_count_all
                <where>
                    <if test="query.sn !=null and query.sn !=''">
                        gateway_sn = #{query.sn}
                    </if>
                    <if test="query.startTime != null and query.endTime != null">
                        AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
                    </if>
                    <if test="query.wanConnType != null">
                        AND service_list = #{query.wanConnType}
                    </if>
                </where>
                GROUP BY timeType
                ORDER BY timeType ASC
            </if>

            <if test="query.timeType != null and query.timeType == 4">
                SELECT toYear(pdate) AS timeType
                <if test="query.ipVersion == null">
                    ,MAX(max_connect_number) AS maxConnectNumber
                </if>
                <if test="query.ipVersion == 'ipv4'">
                    ,MAX(max_ipv4_connection_count) AS maxConnectNumber
                </if>
                <if test="query.ipVersion == 'ipv6'">
                    ,MAX(max_ipv6_connection_count) AS maxConnectNumber
                </if>
                from t_gateway_connect_number_day_count_all
                <where>
                    <if test="query.sn !=null and query.sn !=''">
                        gateway_sn = #{query.sn}
                    </if>
                    <if test="query.startTime != null and query.endTime != null">
                        AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
                    </if>
                    <if test="query.wanConnType != null">
                        AND service_list = #{query.wanConnType}
                    </if>
                </where>
                GROUP BY timeType
                ORDER BY timeType ASC
            </if>

            <if test="query.timeType != null and query.timeType == 5">
<!--                <if test="query.ipVersion == null and query.wanConnType == null">-->
<!--                    SELECT sample_time AS timeType, connect_numbers AS maxConnectNumber-->
<!--                    FROM t_gateway_period_detail-->
<!--                    <where>-->
<!--                        <if test="query.sn !=null and query.sn !=''">-->
<!--                            gateway_sn = #{query.sn}-->
<!--                        </if>-->
<!--                        <if test="query.startTime != null and query.endTime != null">-->
<!--                            AND toDate(#{query.startTime}) &lt;= toDate(sample_time) AND toDate(sample_time) &lt;= toDate(#{query.endTime})-->
<!--                        </if>-->
<!--                    </where>-->
<!--                    ORDER BY sample_time ASC-->
<!--                </if>-->
<!--                <if test="query.ipVersion != null or query.wanConnType != null">-->
                    SELECT sample_time AS timeType
                    <if test="query.ipVersion == null">
                        ,SUM(ipv4_connection_count + ipv6_connection_count) AS maxConnectNumber
                    </if>
                    <if test="query.ipVersion == 'ipv4'">
                        ,SUM(ipv4_connection_count) AS maxConnectNumber
                    </if>
                    <if test="query.ipVersion == 'ipv6'">
                        ,SUM(ipv6_connection_count) AS maxConnectNumber
                    </if>
                    FROM t_wan_detail
                    <where>
                        <if test="query.sn !=null and query.sn !=''">
                            gateway_sn = #{query.sn}
                        </if>
                        <if test="query.startTime != null and query.endTime != null">
                            AND toDate(#{query.startTime}) &lt;= toDate(sample_time) AND toDate(sample_time) &lt;= toDate(#{query.endTime})
                        </if>
                        <if test="query.wanConnType != null">
                            AND service_list = #{query.wanConnType}
                        </if>
                    </where>
                    GROUP BY sample_time
                    ORDER BY sample_time ASC
                </if>
<!--            </if>-->
<!--        </if>-->

    </select>

    <select id="accessNetworkAnalysisList" resultType="com.cmiot.report.dto.AccessNetworkAnalysisResult">
        SELECT hostname AS targetUrl
        , first_stage_delay AS toGatewayTimeDelay
        , second_stage_delay AS toServerTimeDelay
        , access_time AS accessTime,
        deviceMac as deviceMac
        FROM ge_report.t_gateway_network_delay_sample_all
        <where>
            <if test="query.gatewayMac != null and query.gatewayMac != ''">
                gateway_mac = #{query.gatewayMac}
            </if>
            <if test="query.startTime != null and query.endTime != null">
                AND toDate(#{query.startTime}) &lt;= toDate(access_time) AND toDate(access_time) &lt;= toDate(#{query.endTime})
            </if>
        </where>
        ORDER BY access_time DESC
    </select>

<!--    <select id="getGatewayFlowVelocity" resultType="com.cmiot.report.dto.GatewayWanBandwidthDetail">
        SELECT
            TUMBLE_START(time2) AS recordTime,
            ROUND(avg(us_bandwidth)/1024, 2) AS upload,
            ROUND(avg(ds_bandwidth)/1024, 2) AS download
        FROM ge_report.t_wan_bandwidth_detail_all
        WHERE `time` >= date_sub(HOUR, 24, now()) AND gateway_mac = #{query.gatewayMac}
        GROUP BY TUMBLE(`time`, INTERVAL 30 MINUTE) AS time2
        ORDER BY time2 ASC
    </select>-->

    <select id="getGatewayFlowVelocity" resultType="com.cmiot.report.dto.GatewayWanBandwidthDetail">
        SELECT
            gateway_mac AS gatewayMac,
            TUMBLE_START(time2) AS recordTime,
            ROUND(avg(us_bandwidth)/1024, 2) AS upload,
            ROUND(avg(ds_bandwidth)/1024, 2) AS download
        FROM ge_report.t_wan_bandwidth_detail_all
        WHERE `time` >= now() - INTERVAL 24 HOUR
        <if test="query.gatewayMacList != null and query.gatewayMacList.size() > 0">
            AND gateway_mac IN
            <foreach collection="query.gatewayMacList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY gateway_mac, TUMBLE(`time`, INTERVAL 30 MINUTE) AS time2
        ORDER BY gateway_mac, time2 ASC
    </select>

<!--    <select id="getGatewayFlowVelocityByParam" resultType="com.cmiot.report.dto.GatewayWanBandwidthDetail">
        SELECT
            TUMBLE_START(time2) AS recordTime,
            ROUND(MAX(us_bandwidth)/1024, 2) AS upload,
            ROUND(MAX(ds_bandwidth)/1024, 2) AS download
        FROM ge_report.t_wan_bandwidth_detail_all
        WHERE gateway_mac = #{query.gatewayMac}
          <if test="query.startTime != null and query.endTime != null">
            AND toDate(#{query.startTime}) &lt;= toDate(time) AND toDate(time) &lt;= toDate(#{query.endTime})
        </if>
        GROUP BY TUMBLE(`time`, INTERVAL 30 MINUTE) AS time2
        ORDER BY time2 ASC
    </select>-->

    <select id="getGatewayFlowVelocityByParam" resultType="com.cmiot.report.dto.GatewayWanBandwidthDetail">
        SELECT
            gateway_mac AS gatewayMac,
            TUMBLE_START(time2) AS recordTime,
            ROUND(MAX(us_bandwidth)/1024, 2) AS upload,
            ROUND(MAX(ds_bandwidth)/1024, 2) AS download
        FROM ge_report.t_wan_bandwidth_detail_all
        <where>
            <if test="query.gatewayMacList != null and query.gatewayMacList.size() > 0">
                gateway_mac IN
                <foreach collection="query.gatewayMacList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
              <if test="query.startTime != null and query.endTime != null">
                AND #{query.startTime} &lt;= `time` AND `time` &lt;= #{query.endTime}
            </if>
        </where>
        GROUP BY gateway_mac, TUMBLE(`time`, INTERVAL 30 MINUTE) AS time2
        ORDER BY gateway_mac, time2 ASC
    </select>


    <select id="getGatewayWanConnByParam" resultType="com.cmiot.report.dto.GatewayWanConnDetail">
        SELECT gateway_mac AS gatewayMac
             , pdate AS recordTime
             , avg_connect_number AS avgConnectNumber
             , max_connect_number AS maxConnectNumber
        FROM ge_report.t_gateway_connect_number_day_count_all
        <where>
            <if test="query.gatewayMacList != null and query.gatewayMacList.size() > 0">
                gateway_mac IN
                <foreach collection="query.gatewayMacList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.startTime != null and query.endTime != null">
                AND #{query.startTime} &lt;= pdate AND pdate &lt;= #{query.endTime}
            </if>
        </where>
        ORDER BY gateway_mac, pdate ASC
    </select>

    <select id="getGatewayWanConnServiceList" resultType="java.lang.String">
        SELECT service_list
        FROM ge_report.t_wan_detail
        WHERE gateway_mac = #{query.gatewayMac}
<!--        <if test="query.startTime != null and query.endTime != null">
            AND toDate(#{query.startTime}) &lt;= toDate(pdate) AND toDate(pdate) &lt;= toDate(#{query.endTime})
        </if>-->
        AND service_list != '' AND service_list IS NOT NULL
        GROUP BY service_list
        ORDER BY service_list ASC
    </select>


</mapper>
