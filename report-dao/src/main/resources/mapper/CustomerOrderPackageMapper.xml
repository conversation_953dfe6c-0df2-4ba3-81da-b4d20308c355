<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.CustomerOrderPackageMapper">

    <select id="queryCustomerOrderPackageCount"
            resultType="com.cmiot.report.bean.customerOrder.OrderPackageCountAll">
        select
        province_code as province,
        sum(package_count) as subscriptionNum,
        round(100 * (divide(subscriptionNum, (
        select
        sum(package_count)
        from
        ge_report.t_customer_order_package_all
        <where>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
            </if>
            and greaterOrEquals(pdate, #{startTime})
            and lessOrEquals(pdate, #{endTime})
            and package_status = #{packageStatus}
        </where>
        )) as packageCount),2) as subscriptionRatio
        from
        ge_report.t_customer_order_package_all
        <where>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
            </if>
            and greaterOrEquals(pdate, #{startTime})
            and lessOrEquals(pdate, #{endTime})
            and package_status = #{packageStatus}
        </where>
        group by province_code
        order by subscriptionNum desc, province asc
    </select>

    <select id="queryCustomerOrderPackageAddSum" resultType="java.lang.Integer">
        select
        sum(package_add) as subscriptionNum
        from
        ge_report.t_customer_order_package_all
        <where>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
            </if>
            and greaterOrEquals(pdate, #{startTime})
            and lessOrEquals(pdate, #{endTime})
            and package_status = #{packageStatus}
        </where>
    </select>

    <select id="queryCustomerOrderPackageAddNum"
            resultType="com.cmiot.report.bean.customerOrder.OrderPackageAddAll">
        select
        province_code as province,
        sum(package_add) as subscriptionNum
        from
        ge_report.t_customer_order_package_all
        <where>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
            </if>
            and greaterOrEquals(pdate, #{startTime})
            and lessOrEquals(pdate, #{endTime})
            and package_status = #{packageStatus}
        </where>
        group by province_code
        order by subscriptionNum desc, province asc
    </select>

    <select id="queryCustomerOrderPackageAddTrend"
            resultType="com.cmiot.report.bean.customerOrder.OrderPackageAddAll">
        select
        sum(package_add) as subscriptionNum
        from
        ge_report.t_customer_order_package_all
        <where>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
            </if>
            and greaterOrEquals(pdate, #{startTime})
            and lessOrEquals(pdate, #{endTime})
            and package_status = #{packageStatus}
        </where>
        limit 1
    </select>

    <select id="queryCustomerOrderPackageAddTrendByArea"
            resultType="com.cmiot.report.bean.customerOrder.OrderPackageAddAll">
        select
        province_code as province,
        sum(package_add) as subscriptionNum
        from
        ge_report.t_customer_order_package_all
        <where>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
            </if>
            and greaterOrEquals(pdate, #{startTime})
            and lessOrEquals(pdate, #{endTime})
            and package_status = #{packageStatus}
        </where>
        group by province_code
        order by province
    </select>

    <select id="queryOrderPackageTop3"
            resultType="com.cmiot.report.bean.customerOrder.OrderPackageTop3All">
        select
        province_code as province,
        package_code as packageCode,
        sum(package_add) as addNum
        from
        ge_report.t_customer_order_package_all
        <where>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
                and greaterOrEquals(pdate, #{startTime})
                and lessOrEquals(pdate, #{endTime})
                and package_status = #{packageStatus}
            </if>
        </where>
        group by province_code, package_code
        order by addNum desc
        limit 3
    </select>

    <select id="queryOrderAddPackageTopN"
            resultType="com.cmiot.report.bean.customerOrder.OrderPackageTop3All">
        select
        package_code as packageCode,
        sum(package_add) as addNum
        from
        ge_report.t_customer_order_package_all
        <where>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
            </if>
            <if test="startTime != null">
                and greaterOrEquals(pdate, #{startTime})
            </if>
            <if test="endTime != null">
                and lessOrEquals(pdate, #{endTime})
            </if>
            <if test="packageStatus != null">
                and package_status = #{packageStatus}
            </if>
        </where>
        group by package_code
        order by addNum desc
    </select>

    <select id="queryOrderCountPackageTopN"
            resultType="com.cmiot.report.bean.customerOrder.OrderPackageTop3All">
        select
        package_code as packageCode,
        sum(package_count) as addNum
        from
        ge_report.t_customer_order_package_all
        <where>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
            </if>
            <if test="startTime != null">
                and greaterOrEquals(pdate, #{startTime})
            </if>
            <if test="endTime != null">
                and lessOrEquals(pdate, #{endTime})
            </if>
            <if test="packageStatus != null">
                and package_status = #{packageStatus}
            </if>
        </where>
        group by package_code
        order by addNum desc
    </select>

</mapper>