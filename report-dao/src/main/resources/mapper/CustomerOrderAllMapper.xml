<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.CustomerOrderAllMapper">

    <select id="queryCustomerOrderOverviewPieChart"
            resultType="com.cmiot.report.bean.customerOrder.OverviewPieChartAll">
        select
        if(
        or(
        equals(business_type, '1171'),
        equals(business_type, '1172'),
        equals(business_type, '1173')
        ),
        business_type,
        '9999'
        ) as businessType,
        count(distinct id) as orderCount
        from
        ge_report.t_customer_order_all
        where
        equals(toYYYYMMDD(sample_time), #{time})
        and business_status = 1
        and greaterOrEquals(toYYYYMMDD(package_uneffect_time), #{time})
        <if test="provList != null and provList.size > 0">
            and province_code in
            <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                #{itemp}
            </foreach>
        </if>
        group by
        business_type
    </select>

    <select id="queryCustomerOrderOverviewHistogram"
            resultType="com.cmiot.report.bean.customerOrder.OverviewHistogramAll">
        select
        province_code as provinceCode,
        dictGetOrDefault('ge_report.t_dic_area_info', 'gname', province_code, '其他') as provinceName,
        if(
        or(
        equals(business_type, '1171'),
        equals(business_type, '1172'),
        equals(business_type, '1173')
        ),
        business_type,
        '9999'
        ) as businessType,
        count(distinct id) as orderCount
        from
        ge_report.t_customer_order_all
        where
        equals (toYYYYMMDD(sample_time), #{time})
        and business_status = 1
        and greaterOrEquals(toYYYYMMDD(package_uneffect_time), #{time})
        <if test="provList != null and provList.size > 0">
            and province_code in
            <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                #{itemp}
            </foreach>
        </if>
        group by
        province_code,
        business_type
    </select>

    <select id="queryCustomerOrderTrendLineChart"
            resultType="com.cmiot.report.bean.customerOrder.OrderTrendLineChartAll">
        select
        if(
        or(
        equals(business_type, '1171'),
        equals(business_type, '1172'),
        equals(business_type, '1173')
        ),
        business_type,
        '9999'
        ) as businessType,
        count(distinct id) as orderCount
        from
        ge_report.t_customer_order_all
        where
        equals (toYYYYMMDD(sample_time), #{time})
        and business_status = #{businessStatus}
        <if test="businessStatus != null and 1 == businessStatus">
            and greaterOrEquals(business_subscribe_time, toDateTime(#{startTime}))
            and lessOrEquals(business_subscribe_time, toDateTime(#{endTime}))
        </if>
        <if test="businessStatus != null and 2 == businessStatus">
            and greaterOrEquals(business_unsubscribe_time, toDateTime(#{startTime}))
            and lessOrEquals(business_unsubscribe_time, toDateTime(#{endTime}))
        </if>
        <if test="provList != null and provList.size > 0">
            and province_code in
            <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                #{itemp}
            </foreach>
        </if>
        group by
        business_type
    </select>

    <select id="queryBandwidthStatistical" resultType="com.cmiot.report.bean.customerOrder.OrderBandwidthAll">
        select sum(line_total) as bandwidthNum,
        bandwidth
        from t_customer_order_all
        <where>
            <if test="null != businessType and businessType != ''">
                and equals(
                if(
                or(
                equals(business_type, '1171'),
                equals(business_type, '1172'),
                equals(business_type, '1173')
                ), business_type,
                '9999'
                ),
                #{businessType}
                )
            </if>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
            </if>
            and business_status = 1
            and greaterOrEquals(toYYYYMMDD(package_uneffect_time), #{time})
            and equals(toYYYYMMDD(sample_time), #{time})
        </where>
        group by bandwidth
        order by bandwidth
    </select>

</mapper>