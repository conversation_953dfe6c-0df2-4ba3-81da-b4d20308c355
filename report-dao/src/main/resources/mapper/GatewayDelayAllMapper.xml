<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayDelayAllMapper">
    <select id="queryGatewayDelayAll" resultType="com.cmiot.report.bean.gateway.GatewayDelayAll">
        select
            record_time as recordTime,
            province_code as provinceCode,
            city_code as cityCode,
            factory_id as factoryId,
            gateway_vendor as gatewayVendor,
            device_model_id as deviceModelId,
            gateway_productclass as gatewayProductclass,
            gateway_mac as gatewayMac,
            gateway_sn as gatewaySn,
            enterprise_id as enterpriseId,
            min_total_delay as minTotalDelay
        from ge_report.t_gateway_delay_all
        <where>
            <!-- 省单选 -->
            <if test="provinceList != null and provinceList.size == 1">
                and province_code = #{provinceList[0]}
            </if>
            <!-- 省多选 -->
            <if test="provinceList != null and provinceList.size > 1">
                and province_code in
                <foreach collection="provinceList" item="prov" open="(" separator="," close=")">
                    #{prov}
                </foreach>
            </if>
            <!-- 市多选 -->
            <if test="cityList != null and cityList.size > 0">
                and city_code in
                <foreach collection="cityList" item="city" open="(" separator="," close=")">
                    #{city}
                </foreach>
            </if>
            <!-- 厂商多选 -->
            <if test="vendorList != null and vendorList.size > 0">
                and factory_id in
                <foreach collection="vendorList" item="vendor" open="(" separator="," close=")">
                    #{vendor}
                </foreach>
            </if>
            <!-- 型号多选 -->
            <if test="modelList != null and modelList.size > 0">
                and device_model_id in
                <foreach collection="modelList" item="model" open="(" separator="," close=")">
                    #{model}
                </foreach>
            </if>
            <!-- 时间区间 -->
            <if test="startTime != null and startTime != ''">
                and record_time &gt;=  toDate(#{startTime})
            </if>
            <if test="endTime != null and endTime != ''">
                and record_time &lt;= toDate(#{endTime})
            </if>
        </where>
    </select>

    <select id="countGatewayDelayAll" resultType="int">
        select
            count(distinct (gateway_mac))
        from ge_report.t_gateway_delay_all
        <where>
            <!-- 省单选 -->
            <if test="provinceList != null and provinceList.size == 1">
                and province_code = #{provinceList[0]}
            </if>
            <!-- 省多选 -->
            <if test="provinceList != null and provinceList.size > 1">
                and province_code in
                <foreach collection="provinceList" item="prov" open="(" separator="," close=")">
                    #{prov}
                </foreach>
            </if>
            <!-- 市多选 -->
            <if test="cityList != null and cityList.size > 0">
                and city_code in
                <foreach collection="cityList" item="city" open="(" separator="," close=")">
                    #{city}
                </foreach>
            </if>
            <!-- 厂商多选 -->
            <if test="vendorList != null and vendorList.size > 0">
                and factory_id in
                <foreach collection="vendorList" item="vendor" open="(" separator="," close=")">
                    #{vendor}
                </foreach>
            </if>
            <!-- 型号多选 -->
            <if test="modelList != null and modelList.size > 0">
                and device_model_id in
                <foreach collection="modelList" item="model" open="(" separator="," close=")">
                    #{model}
                </foreach>
            </if>
            <!-- 时间区间 -->
            <if test="startTime != null and startTime != ''">
                and toDate(record_time) &gt;=  toDate(#{startTime})
            </if>
            <if test="endTime != null and endTime != ''">
                and toDate(record_time) &lt;= toDate(#{endTime})
            </if>
        </where>
    </select>

    <!-- 查询t_gateway_all表的总量，使用相同的厂商型号、省市查询条件，并且将endtime作为一天的时间段来匹配sample_time字段 -->
    <select id="countGatewayAll" resultType="int">
        select count(distinct gateway_sn) as total
        from ge_report.t_gateway_all
        <where>
            <!-- 省单选 -->
            <if test="provinceList != null and provinceList.size == 1">
                and province_code = #{provinceList[0]}
            </if>
            <!-- 省多选 -->
            <if test="provinceList != null and provinceList.size > 1">
                and province_code in
                <foreach collection="provinceList" item="prov" open="(" separator="," close=")">
                    #{prov}
                </foreach>
            </if>
            <!-- 市多选 -->
            <if test="cityList != null and cityList.size > 0">
                and city_code in
                <foreach collection="cityList" item="city" open="(" separator="," close=")">
                    #{city}
                </foreach>
            </if>
            <!-- 厂商多选 -->
            <if test="vendorList != null and vendorList.size > 0">
                and factory_id in
                <foreach collection="vendorList" item="vendor" open="(" separator="," close=")">
                    #{vendor}
                </foreach>
            </if>
            <!-- 型号多选 -->
            <if test="modelList != null and modelList.size > 0">
                and device_model_id in
                <foreach collection="modelList" item="model" open="(" separator="," close=")">
                    #{model}
                </foreach>
            </if>
            <!-- 时间匹配：将endTime作为一天的时间段来匹配sample_time -->
            <if test="endTime != null and endTime != ''">
                and toDate(sample_time) = toDate(#{endTime})
            </if>
        </where>
    </select>

    <!-- 按维度分组查询老旧网关统计数据 -->
    <select id="queryOldGatewayStatistics" resultType="com.cmiot.report.GatewayOldResult">
        select
        <choose>
            <when test="groupType == 1">
                dictGet('ge_report.t_dic_area_info', 'gname', t1.province_code) as province,
            </when>
            <when test="groupType == 5">
                dictGet('ge_report.t_dic_area_info', 'gname', t1.city_code) as city,
            </when>
            <when test="groupType == 2">
                dictGet('ge_report.t_dic_factory', 'factory_name', t1.factory_id) as gatewayVendor,
            </when>
            <when test="groupType == 3">
                dictGet('ge_report.t_dic_device_model', 'device_model', t1.device_model_id) as deviceModel,
            </when>
        </choose>
        t1.oldNum as oldNum,
        round(
        if(t2.totalNum > 0, (t1.oldNum / t2.totalNum) * 100, 0),
        2
        ) as oldPercentage
        from (
            <!-- 分子：老旧网关数量 -->
            select
            <choose>
                <when test="groupType == 1">
                    province_code,
                </when>
                <when test="groupType == 5">
                    city_code,
                </when>
                <when test="groupType == 2">
                    factory_id,
                </when>
                <when test="groupType == 3">
                    device_model_id,
                </when>
            </choose>
            count(distinct gateway_sn) as oldNum
            from ge_report.t_gateway_delay_all
            <where>
                <!-- 省份条件统一处理 -->
                <if test="provinceList != null and provinceList.size > 0">
                    and province_code in
                    <foreach collection="provinceList" item="prov" open="(" separator="," close=")">
                        #{prov}
                    </foreach>
                </if>
                <!-- 市多选 -->
                <if test="cityList != null and cityList.size > 0">
                    and city_code in
                    <foreach collection="cityList" item="city" open="(" separator="," close=")">
                        #{city}
                    </foreach>
                </if>
                <!-- 厂商多选 -->
                <if test="vendorList != null and vendorList.size > 0">
                    and factory_id in
                    <foreach collection="vendorList" item="vendor" open="(" separator="," close=")">
                        #{vendor}
                    </foreach>
                </if>
                <!-- 型号多选 -->
                <if test="modelList != null and modelList.size > 0">
                    and device_model_id in
                    <foreach collection="modelList" item="model" open="(" separator="," close=")">
                        #{model}
                    </foreach>
                </if>
                <!-- 时间区间 -->
                <if test="startTime != null and startTime != ''">
                    and toDate(record_time) &gt;=  toDate(#{startTime})
                </if>
                <if test="endTime != null and endTime != ''">
                    and toDate(record_time) &lt;= toDate(#{endTime})
                </if>
            </where>
            group by
            <choose>
                <when test="groupType == 1">
                    province_code
                </when>
                <when test="groupType == 5">
                    city_code
                </when>
                <when test="groupType == 2">
                    factory_id
                </when>
                <when test="groupType == 3">
                    device_model_id
                </when>
            </choose>
        ) t1
        left join (
            <!-- 分母：总网关数量 -->
            select
            <choose>
                <when test="groupType == 1">
                    province_code,
                </when>
                <when test="groupType == 5">
                    city_code,
                </when>
                <when test="groupType == 2">
                    factory_id,
                </when>
                <when test="groupType == 3">
                    device_model_id,
                </when>
            </choose>
            count(distinct gateway_sn) as totalNum
            from ge_report.t_gateway_all
            <where>
                <!-- 省份条件统一处理 -->
                <if test="provinceList != null and provinceList.size > 0">
                    and province_code in
                    <foreach collection="provinceList" item="prov" open="(" separator="," close=")">
                        #{prov}
                    </foreach>
                </if>
                <!-- 市多选 -->
                <if test="cityList != null and cityList.size > 0">
                    and city_code in
                    <foreach collection="cityList" item="city" open="(" separator="," close=")">
                        #{city}
                    </foreach>
                </if>
                <!-- 分母查询：按分组类型决定是否使用业务条件 -->
                <!-- 当按厂商分组时，分母也需要厂商条件 -->
                <if test="groupType == 2 and vendorList != null and vendorList.size > 0">
                    and factory_id in
                    <foreach collection="vendorList" item="vendor" open="(" separator="," close=")">
                        #{vendor}
                    </foreach>
                </if>
                <!-- 当按型号分组时，分母也需要型号条件 -->
                <if test="groupType == 3 and modelList != null and modelList.size > 0">
                    and device_model_id in
                    <foreach collection="modelList" item="model" open="(" separator="," close=")">
                        #{model}
                    </foreach>
                </if>
                <!-- 时间匹配：将endTime作为一天的时间段来匹配sample_time -->
                <if test="endTime != null and endTime != ''">
                    and toDate(sample_time) = toDate(#{endTime})
                </if>
            </where>
            group by
            <choose>
                <when test="groupType == 1">
                    province_code
                </when>
                <when test="groupType == 5">
                    city_code
                </when>
                <when test="groupType == 2">
                    factory_id
                </when>
                <when test="groupType == 3">
                    device_model_id
                </when>
            </choose>
        ) t2 on
        <choose>
            <when test="groupType == 1">
                t1.province_code = t2.province_code
            </when>
            <when test="groupType == 5">
                t1.city_code = t2.city_code
            </when>
            <when test="groupType == 2">
                t1.factory_id = t2.factory_id
            </when>
            <when test="groupType == 3">
                t1.device_model_id = t2.device_model_id
            </when>
        </choose>
        order by oldPercentage desc
    </select>

    <!-- 查询老旧网关列表数据 -->
<!--    <select id="queryOldGatewayList" resultType="com.cmiot.report.dto.GatewayOldListResult">-->
<!--        select-->
<!--            t1.province_code as provinceCode,-->
<!--            dictGet('ge_report.t_dic_area_info', 'gname', t1.province_code) as province,-->
<!--            dictGet('ge_report.t_dic_area_info', 'gname', t1.city_code) as city,-->
<!--            '-' as customerName,-->
<!--            t1.gateway_sn as gatewaySn,-->
<!--            t1.gateway_mac as mac,-->
<!--            dictGet('ge_report.t_dic_factory', 'factory_name', t1.factory_id) as vendor,-->
<!--            dictGet('ge_report.t_dic_device_model', 'device_model', t1.device_model_id) as model,-->
<!--            t1.min_total_delay as minTotalDelay-->
<!--        from ge_report.t_gateway_delay_all t1-->
<!--        <where>-->
<!--            &lt;!&ndash; 省单选 &ndash;&gt;-->
<!--            <if test="provinceList != null and provinceList.size == 1">-->
<!--                and t1.province_code = #{provinceList[0]}-->
<!--            </if>-->
<!--            &lt;!&ndash; 省多选 &ndash;&gt;-->
<!--            <if test="provinceList != null and provinceList.size > 1">-->
<!--                and t1.province_code in-->
<!--                <foreach collection="provinceList" item="prov" open="(" separator="," close=")">-->
<!--                    #{prov}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            &lt;!&ndash; 市多选 &ndash;&gt;-->
<!--            <if test="cityList != null and cityList.size > 0">-->
<!--                and t1.city_code in-->
<!--                <foreach collection="cityList" item="city" open="(" separator="," close=")">-->
<!--                    #{city}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            &lt;!&ndash; 厂商多选 &ndash;&gt;-->
<!--            <if test="vendorList != null and vendorList.size > 0">-->
<!--                and t1.factory_id in-->
<!--                <foreach collection="vendorList" item="vendor" open="(" separator="," close=")">-->
<!--                    #{vendor}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            &lt;!&ndash; 型号多选 &ndash;&gt;-->
<!--            <if test="modelList != null and modelList.size > 0">-->
<!--                and t1.device_model_id in-->
<!--                <foreach collection="modelList" item="model" open="(" separator="," close=")">-->
<!--                    #{model}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            &lt;!&ndash; 时间区间 &ndash;&gt;-->
<!--            <if test="startTime != null and startTime != ''">-->
<!--                and toDate(t1.record_time) &gt;= toDate(#{startTime})-->
<!--            </if>-->
<!--            <if test="endTime != null and endTime != ''">-->
<!--                and toDate(t1.record_time) &lt;= toDate(#{endTime})-->
<!--            </if>-->
<!--            &lt;!&ndash; 网关SN查询 &ndash;&gt;-->
<!--            <if test="gatewaySnList != null and gatewaySnList.size > 0">-->
<!--                and t1.gateway_sn in-->
<!--                <foreach collection="gatewaySnList" item="sn" open="(" separator="," close=")">-->
<!--                    #{sn}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            &lt;!&ndash; 网关MAC查询 &ndash;&gt;-->
<!--            <if test="gatewayMac != null and gatewayMac != ''">-->
<!--                and t1.gateway_mac like concat('%', #{gatewayMac}, '%')-->
<!--            </if>-->
<!--        </where>-->
<!--        <choose>-->
<!--            <when test="orderType != null and orderType == 1">-->
<!--                order by t1.min_total_delay asc-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                order by t1.min_total_delay desc-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--    </select>-->

    <!-- 查询老旧网关列表数据 -->
    <select id="queryOldGatewayList" resultType="com.cmiot.report.dto.GatewayOldListResult">
        select
        province_code as provinceCode,
        dictGet('ge_report.t_dic_area_info', 'gname', province_code) as province,
        dictGet('ge_report.t_dic_area_info', 'gname', city_code) as city,
        '-' as customerName,
        gateway_sn as gatewaySn,
        gateway_mac as mac,
        dictGet('ge_report.t_dic_factory', 'factory_name', factory_id) as vendor,
        dictGet('ge_report.t_dic_device_model', 'device_model', device_model_id) as model,
        min_total_delay as minTotalDelay
        from (
        select
        province_code,
        city_code,
        factory_id,
        device_model_id,
        gateway_sn,
        gateway_mac,
        min_total_delay,
        row_number() over (partition by gateway_mac order by min_total_delay asc) as rn
        from ge_report.t_gateway_delay_all
        <where>
            <!-- 省单选 -->
            <if test="provinceList != null and provinceList.size == 1">
                and province_code = #{provinceList[0]}
            </if>
            <!-- 省多选 -->
            <if test="provinceList != null and provinceList.size > 1">
                and province_code in
                <foreach collection="provinceList" item="prov" open="(" separator="," close=")">
                    #{prov}
                </foreach>
            </if>
            <!-- 市多选 -->
            <if test="cityList != null and cityList.size > 0">
                and city_code in
                <foreach collection="cityList" item="city" open="(" separator="," close=")">
                    #{city}
                </foreach>
            </if>
            <!-- 厂商多选 -->
            <if test="vendorList != null and vendorList.size > 0">
                and factory_id in
                <foreach collection="vendorList" item="vendor" open="(" separator="," close=")">
                    #{vendor}
                </foreach>
            </if>
            <!-- 型号多选 -->
            <if test="modelList != null and modelList.size > 0">
                and device_model_id in
                <foreach collection="modelList" item="model" open="(" separator="," close=")">
                    #{model}
                </foreach>
            </if>
            <!-- 时间区间 -->
            <if test="startTime != null and startTime != ''">
                and toDate(record_time) &gt;= toDate(#{startTime})
            </if>
            <if test="endTime != null and endTime != ''">
                and toDate(record_time) &lt;= toDate(#{endTime})
            </if>
            <!-- 网关SN查询 -->
            <if test="gatewaySnList != null and gatewaySnList.size > 0">
                and gateway_sn in
                <foreach collection="gatewaySnList" item="sn" open="(" separator="," close=")">
                    #{sn}
                </foreach>
            </if>
            <!-- 网关MAC查询 -->
            <if test="gatewayMac != null and gatewayMac != ''">
                and gateway_mac like concat('%', #{gatewayMac}, '%')
            </if>
        </where>
        ) t
        where rn = 1
        <choose>
            <when test="orderType != null and orderType == 1">
                order by min_total_delay asc
            </when>
            <otherwise>
                order by min_total_delay desc
            </otherwise>
        </choose>
    </select>


    <!-- 根据网关SN和时间段查询设备时延信息 -->
    <select id="queryDelayDevicesBySn" resultType="com.cmiot.report.dto.GatewayDelayDeviceResult">
        select
        deviceMac as deviceMac,
        min(min_total_delay) as minTotalDelay,
        any(data_date) as dataDate,
        any(gateway_sn) as gatewaySn,
        any(gateway_mac) as gatewayMac
        from ge_report.t_gateway_delay_day_all
        <where>
            <!-- 网关SN查询 -->
            <if test="sn != null and sn != ''">
                and gateway_sn = #{sn}
            </if>
            <!-- 开始时间 -->
            <if test="startTime != null and startTime != ''">
                and toDate(data_date) &gt;= toDate(#{startTime})
            </if>
            <!-- 结束时间 -->
            <if test="endTime != null and endTime != ''">
                and toDate(data_date) &lt;= toDate(#{endTime})
            </if>
        </where>
        group by deviceMac
        order by minTotalDelay asc
    </select>
</mapper>
