<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DicOperationTreeMapper">
  <resultMap id="BaseResultMap" type="com.cmiot.report.bean.DicOperationTreeInfo">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="operation_path" jdbcType="VARCHAR" property="operationPath" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="pid" jdbcType="BIGINT" property="pid" />
    <result column="need_log" jdbcType="TINYINT" property="needLog" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `method`, operation_path, operation_name, pid, need_log, update_time
  </sql>
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_dic_operation_tree_e
  </select>
</mapper>