<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.CustomerOrderTrendsMapper">

    <select id="queryCustomerOrderTrendLineChart"
            resultType="com.cmiot.report.bean.customerOrder.OrderTrendLineChartAll">
        select
            business_type as businessType,
            sum(business_count) as orderCount
        from t_customer_order_trends_all
        <where>
            <if test="provList != null and provList.size > 0">
                and province_code in
                <foreach collection="provList" index="index" item="itemp" open="(" separator="," close=")">
                    #{itemp}
                </foreach>
            </if>
            and greaterOrEquals(pdate, #{startTime})
            and lessOrEquals(pdate, #{endTime})
            and business_status = #{businessStatus}
            and greaterOrEquals(pdate, #{startTime})
            and lessOrEquals(pdate, #{endTime})
        </where>
        group by
        business_type
    </select>
</mapper>

