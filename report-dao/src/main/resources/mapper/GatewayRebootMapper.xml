<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayRebootMapper">

  <resultMap id="DtoResultMap" type="com.cmiot.report.bean.plugin.EkitRestartAlertAnalysisDto">
    <result column="alarm_level" jdbcType="INTEGER" property="alarmLevel" />
    <result column="gateway_model_int" jdbcType="INTEGER" property="gatewayModelInt" />
    <result column="reboot_count" jdbcType="BIGINT" property="restartsNumber" />
    <result column="sample_date" jdbcType="DATE" property="alarmTime" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="customer_name" jdbcType="VARCHAR" property="businessName" />
    <result column="factory_name" jdbcType="VARCHAR" property="vendor" />
    <result column="device_model" jdbcType="VARCHAR" property="model" />
    <result column="gateway_sn" jdbcType="VARCHAR" property="sn" />
    <result column="gateway_mac" jdbcType="VARCHAR" property="mac" />
  </resultMap>

  <select id="ekitRestartAlertAnalysis" parameterType="com.cmiot.report.bean.plugin.EkitRestartAlertAnalysisParam" resultMap="DtoResultMap">
    select gr.gateway_sn as gateway_sn
         ,gr.gateway_mac as gateway_mac
         ,gr.reboot_count as reboot_count
         ,gr.sample_date as sample_date
         ,gr.gateway_model_int as gateway_model_int
         ,area.gname as province

         ,gr.customer_name as customer_name

         ,tf.factory_name as factory_name
         ,dm.device_model as device_model
            ,if(gr.reboot_count <![CDATA[ >= ]]> 10,3
                ,if(gr.reboot_count <![CDATA[ >= ]]> 6,2
                    ,if(gr.reboot_count <![CDATA[ >= ]]> 3,1,null)
                    )
                ) as alarm_level

    from (
        select * from t_gateway_reboot_count_all final WHERE reboot_count <![CDATA[ >= 3 ]]>
          <if test="param.alarmLevel==1">
              and reboot_count <![CDATA[ < 6 ]]>
          </if>
          <if test="param.alarmLevel==2">
              and reboot_count <![CDATA[ >= 6 ]]> and reboot_count <![CDATA[ < 10 ]]>
          </if>
          <if test="param.alarmLevel==3">
              and reboot_count <![CDATA[ >= 10 ]]>
          </if>
          <if test="param.deviceType !=null">
              and gateway_model_int = #{param.deviceType}
          </if>
      <if test="param.sn != null and param.sn !=''">
          and gateway_sn= #{param.sn}
      </if>
      <if test="param.vendor != null and param.vendor.size > 0" >
          and factory_id in
          <foreach collection="param.vendor" index="index" item="itemp" open="(" separator="," close=")">
              #{itemp}
          </foreach>
      </if>
      <if test="param.model != null and param.model.size > 0">
          and device_model_id in
          <foreach collection="param.model" index="index" item="itemp" open="(" separator="," close=")">
              #{itemp}
          </foreach>
      </if>

      <if test="param.province != null and param.province.size > 0">
          and province_code in
          <foreach collection="param.province" index="index" item="itemp" open="(" separator="," close=")">
              #{itemp}
          </foreach>
      </if>

      <if test="param.businessName !=null and param.businessName!=''">
          and customer_name = #{param.businessName}
      </if>


                                             order by reboot_count desc,sample_date desc,gateway_sn asc
   ) gr

    left join t_dic_area_info area on gr.province_code=area.gcode
    left join t_dic_factory tf on gr.factory_id=tf.factory_id
    left join t_dic_device_model dm on gr.device_model_id=dm.device_model_id


  </select>

</mapper>