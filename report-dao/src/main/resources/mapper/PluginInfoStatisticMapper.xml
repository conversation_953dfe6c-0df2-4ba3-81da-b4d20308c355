<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.PluginInfoStatisticMapper">
    <insert id="insertPluginInfoDayCount" parameterType="com.cmiot.report.dto.plugin.PluginInfoDayCount">

        INSERT INTO t_plugin_info_day_count_all
        (pdate, bundle_id, area, factory_id, device_model_id,
         total_num, incr_num, no_use_num, thirty_day_active_num)
         values
            (#{countDate}, #{bundleId}, #{area}, #{factoryId}, #{deviceModelId},
             #{totalNum}, #{incrNum}, #{noUseNum}, #{thirtyDayActiveNum})

    </insert>
    <insert id="insertPluginInfoMonthCount" parameterType="com.cmiot.report.dto.plugin.PluginInfoMonthCount">

        INSERT INTO t_plugin_info_month_count_all
        (pdate, bundle_id, area, factory_id, device_model_id,
         total_num, incr_num)
        values
        (#{countDate}, #{bundleId}, #{area}, #{factoryId}, #{deviceModelId},
         #{totalNum}, #{incrNum})
    </insert>

    <select id="getPluginInfoOverview" resultType="com.cmiot.report.dto.plugin.PluginInfoMonthCount">
        select any(pdate) as countDate, bundle_id as bundleId,
        sum(total_num) as totalNum, sum(incr_num) as incrNum
        from t_plugin_info_month_count_all
        where pdate = #{lastMonth}
        <if test="bundleIds != null and bundleIds.size() > 0">
            and bundle_id in
            <foreach collection="bundleIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="provinceIds != null and provinceIds.size() > 0">
            and area in
            <foreach collection="provinceIds" index="index" item="provinceId" open="(" separator="," close=")">
                #{provinceId}
            </foreach>
        </if>
        <if test="deviceModelIds != null and deviceModelIds.size() > 0">
            and device_model_id in
            <foreach collection="deviceModelIds" index="index" item="deviceModelId" open="(" separator="," close=")">
                #{deviceModelId}
            </foreach>
        </if>
        group by bundle_id
    </select>

    <select id="getPluginInfoOverviewGroupProvince" resultType="com.cmiot.report.dto.plugin.PluginInfoMonthCount">
        select area, bundle_id as bundleId, any(pdate) as countDate,
        sum(total_num) as totalNum, sum(incr_num) as incrNum
        from t_plugin_info_month_count_all
        where pdate = #{lastMonth}
        <if test="bundleIds != null and bundleIds.size() > 0">
            and bundle_id in
            <foreach collection="bundleIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="provinceIds != null and provinceIds.size() > 0">
            and area in
            <foreach collection="provinceIds" index="index" item="provinceId" open="(" separator="," close=")">
                #{provinceId}
            </foreach>
        </if>
        <if test="deviceModelIds != null and deviceModelIds.size() > 0">
            and device_model_id in
            <foreach collection="deviceModelIds" index="index" item="deviceModelId" open="(" separator="," close=")">
                #{deviceModelId}
            </foreach>
        </if>
        group by bundle_id, area
    </select>


    <select id="getPluginInfoDayOverview" resultType="com.cmiot.report.dto.plugin.PluginInfoDayCount">
        select any(pdate) as countDate, bundle_id as bundleId,
        sum(total_num) as totalNum, sum(incr_num) as incrNum, sum(no_use_num) as noUseNum,
          sum(thirty_day_active_num) as thirtyDayActiveNum
        from t_plugin_info_day_count_all
        where pdate = #{lastDay}
        <if test="bundleIds != null and bundleIds.size() > 0">
            and bundle_id in
            <foreach collection="bundleIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="provinceIds != null and provinceIds.size() > 0">
            and area in
            <foreach collection="provinceIds" index="index" item="provinceId" open="(" separator="," close=")">
                #{provinceId}
            </foreach>
        </if>
        <if test="deviceModelIds != null and deviceModelIds.size() > 0">
            and device_model_id in
            <foreach collection="deviceModelIds" index="index" item="deviceModelId" open="(" separator="," close=")">
                #{deviceModelId}
            </foreach>
        </if>
        group by bundle_id
    </select>

    <!--查询15分钟以内活跃信息-->
    <select id="getPluginOverviewActiveCount"
            resultType="com.cmiot.report.dto.plugin.PluginInfoTotalOverviewCount">
        select count(distinct device_mac) as usingNum, bundle_id as bundleId
        from t_plugin_active_record_detail_all
        where sample_time >= now() - INTERVAL #{activeTime} MINUTE
        <if test="bundleIds != null">
            and bundle_id in
            <foreach collection="bundleIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="provinceIds != null and provinceIds.size() > 0">
            and province in
            <foreach collection="provinceIds" index="index" item="provinceId" open="(" separator="," close=")">
                #{provinceId}
            </foreach>
        </if>
        <if test="deviceModelIds != null and deviceModelIds.size() > 0">
            and device_model_id in
            <foreach collection="deviceModelIds" index="index" item="deviceModelId" open="(" separator="," close=")">
                #{deviceModelId}
            </foreach>
        </if>
        group by bundle_id
    </select>

    <select id="getPluginOverviewTotalCount"
            resultType="com.cmiot.report.dto.plugin.PluginInfoTotalOverviewCount">
        select count(distinct device_mac) as totalNum, bundle_id as bundleId
        from  t_plugin_info_first_time_record_all final
            <if test="bundleIds != null">
                where  bundle_id in
                <foreach collection="bundleIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        <if test="provinceIds != null and provinceIds.size() > 0">
            and province in
            <foreach collection="provinceIds" index="index" item="provinceId" open="(" separator="," close=")">
                #{provinceId}
            </foreach>
        </if>
        <if test="deviceModelIds != null and deviceModelIds.size() > 0">
            and device_model_id in
            <foreach collection="deviceModelIds" index="index" item="deviceModelId" open="(" separator="," close=")">
                #{deviceModelId}
            </foreach>
        </if>
        group by  bundle_id
    </select>

    <select id="getPluginOverviewTotalCountGroupProvince"
            resultType="com.cmiot.report.dto.plugin.PluginInfoTotalOverviewCount">
        select count(distinct device_mac) as totalNum, bundle_id as bundleId, province
        from  t_plugin_info_first_time_record_all final
        <if test="bundleIds != null">
            where  bundle_id in
            <foreach collection="bundleIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="provinceIds != null and provinceIds.size() > 0">
            and province in
            <foreach collection="provinceIds" index="index" item="provinceId" open="(" separator="," close=")">
                #{provinceId}
            </foreach>
        </if>
        <if test="deviceModelIds != null and deviceModelIds.size() > 0">
            and device_model_id in
            <foreach collection="deviceModelIds" index="index" item="deviceModelId" open="(" separator="," close=")">
                #{deviceModelId}
            </foreach>
        </if>
        group by  bundle_id, province
    </select>

    <select id="getPluginInfoIncrOverview"
            resultType="com.cmiot.report.dto.plugin.PluginInfoDayCount">
        select sum(incr_num) as incrNum, bundle_id as bundleId, area
        from  t_plugin_info_day_count_all
        <if test="bundleIds != null">
            where  bundle_id in
            <foreach collection="bundleIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="provinceIds != null and provinceIds.size() > 0">
            and area in
            <foreach collection="provinceIds" index="index" item="provinceId" open="(" separator="," close=")">
                #{provinceId}
            </foreach>
        </if>
        <if test="deviceModelIds != null and deviceModelIds.size() > 0">
            and device_model_id in
            <foreach collection="deviceModelIds" index="index" item="deviceModelId" open="(" separator="," close=")">
                #{deviceModelId}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and pdate &gt;= toYYYYMMDD(toDate(#{startTime}, 'yyyy-MM-dd'))
        </if>
        <if test="endTime != null and endTime !='' ">
            and pdate &lt;= toYYYYMMDD(toDate(#{endTime}, 'yyyy-MM-dd'))
        </if>
        group by  bundle_id, area
    </select>


    <select id="getPluginBaseInfo" resultType="com.cmiot.report.dto.plugin.PluginBaseInfo">
        SELECT plugin_id as id, plugin_name as pluginName,
               device_type as deviceType, bundle_id as bundleId,
               manufacture,
               plugin_des as pluginDes, last_version as version
        FROM t_dic_plugin_base_info

    </select>

    <select id="getPluginBaseInfoById" resultType="com.cmiot.report.dto.plugin.PluginBaseInfo">
        SELECT plugin_id as id, plugin_name as pluginName,
               device_type as deviceType, bundle_id as bundleId,
               manufacture,
               plugin_des as pluginDes, last_version as version
        FROM t_dic_plugin_base_info
        where id = #{pluginId}
        limit 1

    </select>
    <select id="getPluginInfoDayCount" resultType="com.cmiot.report.dto.plugin.PluginInfoDayCount"
            parameterType="java.lang.String">
        select pdate as countDate, bundle_id as bundleId, area,
               factory_id as factoryId, device_model_id as deviceModelId,
               total_num as totalNum, incr_num as incrNum,
               no_use_num as noUseNum, thirty_day_active_num as thirtyDayActiveNum
        from t_plugin_info_day_count_all where pdate = #{pdate} and bundle_id = #{bundleId}
    </select>

    <select id="getPluginInfoMonthCount" resultType="com.cmiot.report.dto.plugin.PluginInfoMonthCount"
            parameterType="java.lang.String">
        select pdate as countDate, bundle_id as bundleId, area,
               factory_id as factoryId, device_model_id as deviceModelId,
               total_num as totalNum, incr_num as incrNum
        from t_plugin_info_month_count_all where pdate = #{pdate} and bundle_id = #{bundleId}
    </select>

    <select id="getPluginOsgiBaseInfo" resultType="com.cmiot.report.dto.plugin.PluginBaseInfo">
        SELECT plugin_id as id,
                plugin_name as pluginName,
                device_type as deviceType,
                manufacture,
                plugin_desc as pluginDes,
                last_version as version,
                plugin_num as pluginNum,
                is_preset as IsPreset,
                create_id as createId
        FROM t_dic_plugin_osgi_base_info
        <where>
            <if test="pluginId != null">
                plugin_id = #{pluginId}
            </if>
        </where>
    </select>


    <select id="getPluginOsgiInstallCount" resultType="com.cmiot.report.dto.plugin.PluginStatisticInfo">
        SELECT plugin_name AS pluginName, COUNT(*) AS total
        FROM t_plugin_info_osgi_first_time_record_all FINAL
        <where>
            <include refid="Where_Clause"/>
            <if test="month != null">
                AND toYYYYMM(first_boot_time) == toYYYYMM(date_sub(month, #{month}, today()))
            </if>
        </where>
        GROUP BY plugin_name
    </select>

    <select id="getPluginOsgiInstallCountGroupVersion" resultType="com.cmiot.report.dto.plugin.PluginStatisticInfo">
        SELECT plugin_name AS pluginName, plugin_version AS pluginVersion, COUNT(*) AS total
        FROM t_plugin_info_osgi_first_time_record_all FINAL
        GROUP BY plugin_name, plugin_version
        ORDER BY COUNT(*) DESC
    </select>


    <select id="getPluginOsgiDayLiveNum" resultType="com.cmiot.report.dto.plugin.PluginStatisticInfo">
        SELECT plugin_name AS pluginName, COUNT(*) AS total
        FROM t_plugin_info_osgi_last_time_record_all
        WHERE toDate(last_active_time) == date_sub(day, 1, today())
        <if test="pluginName != null">
            AND plugin_name = #{pluginName}
        </if>
        GROUP BY plugin_name
    </select>


    <select id="getPluginOsgiMonthLiveNum" resultType="com.cmiot.report.dto.plugin.PluginStatisticInfo">
        SELECT t.plugin_name, COUNT(t.gateway_mac) AS total
        FROM (
                 SELECT gateway_mac, plugin_name, COUNT(*) AS total
                 FROM t_plugin_info_osgi_last_time_record_all
                 WHERE toYYYYMM(last_active_time) == toYYYYMM(date_sub(month, 1, today()))
                 <if test="pluginName != null">
                    AND plugin_name = #{pluginName}
                 </if>
                 GROUP BY gateway_mac, plugin_name
             ) t
        GROUP BY t.plugin_name
    </select>


    <select id="getPluginOsgiInstallCountByParams" resultType="com.cmiot.report.dto.plugin.PluginStatisticInfo">
        SELECT province_code AS province, COUNT(*) AS total
        FROM t_plugin_info_osgi_first_time_record_all FINAL
        <where>
            <include refid="Where_Clause"/>
            <if test="startDate != null and startDate != ''">
                AND toDate(#{startDate}) &lt;= toDate(first_boot_time)
            </if>
            <if test="endDate != null and endDate != ''">
                AND toDate(first_boot_time) &lt;= toDate(#{endDate})
            </if>
        </where>
        GROUP BY province_code
        ORDER BY COUNT(*) DESC
    </select>


    <select id="getPluginOsgiLiveNumByParams" resultType="java.lang.Long">
        SELECT COUNT(t.gateway_mac) AS total
        FROM (
            SELECT gateway_mac, COUNT(*) AS total
            FROM t_plugin_info_osgi_last_time_record_all
        <where>
            toDate(last_active_time) == date_sub(day, 1, today())
            <include refid="Where_Clause"/>
        </where>
            GROUP BY gateway_mac
        ) t
    </select>

    <select id="getPluginOsgiMonthLiveNumByParams" resultType="java.lang.Long">
        SELECT COUNT(t.gateway_mac) AS total
        FROM (
        SELECT gateway_mac, COUNT(*) AS total
        FROM t_plugin_info_osgi_last_time_record_all
        <where>
            toYYYYMM(last_active_time) == toYYYYMM(date_sub(month, 1, today()))
            <include refid="Where_Clause"/>
        </where>
        GROUP BY gateway_mac
        ) t
    </select>


    <select id="getGatewayCountByGroupProvince" resultType="com.cmiot.report.dto.plugin.PluginStatisticInfo">
        SELECT t.province_code AS province, count(*) AS total
        FROM (
             SELECT province_code, gateway_mac
             FROM t_gateway_all
             WHERE toDate(sample_time) = date_sub(day, 1, today())
             <if test="provinceCodes != null and provinceCodes.size() > 0">
                 AND province_code IN
                 <foreach collection="provinceCodes" index="index" item="provinceCode" open="(" separator="," close=")">
                     #{provinceCode}
                 </foreach>
             </if>
             GROUP BY province_code, gateway_mac
        ) t GROUP BY t.province_code
    </select>


    <sql id="Where_Clause">
        <if test="pluginName != null and pluginName != ''">
            AND plugin_name = #{pluginName}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            AND province_code IN
            <foreach collection="provinceCodes" index="index" item="provinceCode" open="(" separator="," close=")">
                #{provinceCode}
            </foreach>
        </if>
        <if test="factoryIds != null and factoryIds.size() > 0">
            AND factory_id IN
            <foreach collection="factoryIds" index="index" item="factoryId" open="(" separator="," close=")">
                #{factoryId}
            </foreach>
        </if>
        <if test="deviceModelIds != null and deviceModelIds.size() > 0">
            AND device_model_id IN
            <foreach collection="deviceModelIds" index="index" item="deviceModelId" open="(" separator="," close=")">
                #{deviceModelId}
            </foreach>
        </if>
        <if test="pluginVersion != null and pluginVersion != ''">
            AND plugin_version = #{pluginVersion}
        </if>
    </sql>

</mapper>