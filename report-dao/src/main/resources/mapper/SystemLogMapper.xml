<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmiot.report.mapper.FgeoOperationMapper">

    <resultMap id="BaseResultMapOperation" type="com.cmiot.report.bean.FgeoOperation">
        <result column="log_time" jdbcType="TIMESTAMP" property="logTime"/>
        <result column="topic" jdbcType="VARCHAR" property="topic"/>
        <result column="sys_type" jdbcType="VARCHAR" property="sysType"/>
        <result column="trace_id" jdbcType="VARCHAR" property="traceId"/>
        <result column="uid" jdbcType="BIGINT" property="uid"/>
        <result column="user_role" jdbcType="ARRAY" property="userRole" />
        <result column="user_account" jdbcType="VARCHAR" property="userAccount"/>
        <result column="operation_id" jdbcType="BIGINT" property="operationId"/>
        <result column="operation_name" jdbcType="VARCHAR" property="operationName"/>
        <result column="msg" jdbcType="VARCHAR" property="msg"/>
    </resultMap>

    <resultMap id="BaseResultMapOperationLog" type="com.cmiot.report.bean.FgeoOperationLog">
        <result column="log_time" jdbcType="TIMESTAMP" property="logTime"/>
        <result column="trace_id" jdbcType="VARCHAR" property="traceId"/>
        <result column="span_id" jdbcType="VARCHAR" property="spanId"/>
        <result column="msg" jdbcType="VARCHAR" property="msg"/>
    </resultMap>

    <sql id="Base_Column_List">
        log_time, topic, sys_type, trace_id, uid, user_role, user_account, operation_id, operation_name, msg
    </sql>
    <sql id="Base_Column_List_LOG">
        log_time, trace_id, span_id, msg
    </sql>

    <select id="countOperation" resultType="java.lang.Long"
            parameterType="com.cmiot.report.dto.syslog.SysLogQueryRequest">
        select
        count(*)
        from t_fgeo_operation_all
        <where>
            <if test=" userAccount != null ">
                and user_account like concat(#{userAccount,jdbcType=VARCHAR}, '%')
            </if>
            <if test=" roleId != null ">
                and has(user_role, #{roleId})
            </if>
            <if test=" operationId != null ">
                and operation_id = #{operationId,jdbcType=INTEGER}
            </if>
            <if test=" startTime != null and startTime != '' ">
                and log_time &gt;= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test=" endTime != null and endTime != '' ">
                and log_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
            and sys_type = 'm'
        </where>
    </select>


    <select id="selectOperation" resultMap="BaseResultMapOperation"
            parameterType="com.cmiot.report.dto.syslog.SysLogQueryRequest">
        select
        <include refid="Base_Column_List" />
        from t_fgeo_operation_all
        <where>
            <if test=" userAccount != null ">
                and user_account like concat(#{userAccount,jdbcType=VARCHAR}, '%')
            </if>
            <if test=" roleId != null ">
                and has(user_role, #{roleId})
            </if>
            <if test=" operationId != null ">
                and operation_id = #{operationId,jdbcType=BIGINT}
            </if>
            <if test=" startTime != null and startTime != ''">
                and log_time &gt;= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test=" endTime != null and endTime != ''">
                and log_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
            and sys_type = 'm'
        </where>
        order by log_time_mil desc
        limit ${offset}, ${pageSize}
    </select>
    <select id="countOperationLog" resultType="java.lang.Long"
            parameterType="com.cmiot.report.dto.syslog.SysLogDetailQueryRequest">
        select
        count(*)
        from t_fgeo_operation_log_all
        <where>
            <if test=" traceId != null ">
                and trace_id = #{traceId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectOperationLog" resultMap="BaseResultMapOperationLog"
            parameterType="com.cmiot.report.dto.syslog.SysLogDetailQueryRequest">
        select
        <include refid="Base_Column_List_LOG" />
        from t_fgeo_operation_log_all
        <where>
            <if test=" traceId != null ">
                trace_id = #{traceId,jdbcType=VARCHAR} AND
            </if>
            (msg LIKE '原始请求: request%' OR msg LIKE '响应: 200 OK%')
        </where>
        order by log_time_mil asc
        limit ${offset}, ${pageSize}
    </select>

    <select id="countOperationByGroupAccount" resultType="java.lang.Long">
        SELECT COUNT(B.log_time) AS total
        FROM (
            SELECT user_account, MAX(log_time_mil) AS log_time_mil
            FROM t_fgeo_operation_all
            <include refid="Where_Clause"/>
            GROUP BY user_account
            ORDER BY log_time_mil DESC
        ) A
        GLOBAL LEFT JOIN t_fgeo_operation_all B ON A.user_account = B.user_account
        WHERE A.log_time_mil = B.log_time_mil
    </select>

    <select id="selectOperationByGroupAccount" resultType="com.cmiot.report.bean.FgeoOperation">
        SELECT B.log_time AS log_time, B.topic AS topic, B.sys_type AS sys_type, B.trace_id AS trace_id
        , B.uid AS uid, B.user_role AS user_role, B.user_account AS user_account, B.operation_id AS operation_id
        , B.operation_name AS operation_name, B.msg AS msg
        FROM (
            SELECT user_account, MAX(log_time_mil) AS log_time_mil
            FROM t_fgeo_operation_all
            <include refid="Where_Clause"/>
            GROUP BY user_account
            ORDER BY log_time_mil DESC
            limit ${offset}, ${pageSize}
        ) A
        GLOBAL LEFT JOIN t_fgeo_operation_all B ON A.user_account = B.user_account
        WHERE A.log_time_mil = B.log_time_mil
    </select>

    <sql id="Where_Clause">
        <where>
            <if test=" userAccount != null ">
                and user_account like concat(#{userAccount,jdbcType=VARCHAR}, '%')
            </if>
            <if test=" roleId != null ">
                and has(user_role, #{roleId})
            </if>
            <if test=" operationId != null ">
                and operation_id = #{operationId,jdbcType=BIGINT}
            </if>
            <if test=" startTime != null and startTime != ''">
                and log_time &gt;= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test=" endTime != null and endTime != ''">
                and log_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
            and sys_type = 'm'
        </where>
    </sql>



    <select id="selectCountByGroupAccount4e" resultType="java.lang.Long">
        SELECT COUNT(B.log_time) AS total
        FROM (
            SELECT uid, user_account, MAX(log_time_mil) AS log_time_mil
            FROM t_fgeo_operation_all
            <include refid="Where_Clause2"/>
            GROUP BY uid, user_account
            ORDER BY log_time_mil DESC
        ) A
        GLOBAL LEFT JOIN t_fgeo_operation_all B ON A.uid = B.uid
        WHERE A.log_time_mil = B.log_time_mil
    </select>

    <select id="selectPageByGroupAccount4e" resultType="com.cmiot.report.bean.FgeoOperation">
        SELECT B.log_time AS log_time, B.topic AS topic, B.sys_type AS sys_type, B.trace_id AS trace_id
        , B.uid AS uid, B.user_role AS user_role, B.user_account AS user_account, B.operation_id AS operation_id
        , B.operation_name AS operation_name, B.msg AS msg
        FROM (
            SELECT uid, user_account, MAX(log_time_mil) AS log_time_mil
            FROM t_fgeo_operation_all
            <include refid="Where_Clause2"/>
            GROUP BY uid, user_account
            ORDER BY log_time_mil DESC
            limit ${pam.offset}, ${pam.pageSize}
        ) A
        GLOBAL LEFT JOIN t_fgeo_operation_all B ON A.uid = B.uid
        WHERE A.log_time_mil = B.log_time_mil
    </select>


    <select id="selectPageUserLogs" resultMap="BaseResultMapOperation">
        SELECT <include refid="Base_Column_List" />
        FROM t_fgeo_operation_all
        <include refid="Where_Clause2"/>
        ORDER BY log_time_mil DESC
    </select>

    <sql id="Where_Clause2">
        <where>
            sys_type = 'e'
            <if test="pam.eid != null">
                AND eid = #{pam.eid,jdbcType=BIGINT}
            </if>
            <if test="pam.userIds != null">
                AND uid IN
                <foreach collection="pam.userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="pam.userAccount != null">
                AND user_account LIKE concat(#{pam.userAccount,jdbcType=VARCHAR}, '%')
            </if>
            <if test="pam.roleId != null">
                AND has(user_role, #{pam.roleId})
            </if>
            <if test="pam.operationId != null">
                AND operation_id = #{pam.operationId,jdbcType=BIGINT}
            </if>
            <if test="pam.startTime != null and pam.startTime != ''">
                AND log_time &gt;= #{pam.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="pam.endTime != null and pam.endTime != ''">
                AND log_time &lt;= #{pam.endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>


    <select id="getUserLogByTraceId" resultMap="BaseResultMapOperation">
        SELECT <include refid="Base_Column_List" />
        FROM t_fgeo_operation_all
        WHERE trace_id = #{trace_id}
    </select>


    <select id="getLogDetail" resultMap="BaseResultMapOperationLog">
        SELECT <include refid="Base_Column_List_LOG" />
        FROM t_fgeo_operation_log_all
        WHERE trace_id = #{traceId} AND (msg LIKE '原始请求: request%' OR msg LIKE '响应: %')
        ORDER BY log_time_mil ASC
    </select>


</mapper>
