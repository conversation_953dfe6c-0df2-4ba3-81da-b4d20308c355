<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmiot.report.mapper.EnterpriseGatewayOnlineTimeMapper">

    <select id="getOnlineTimeList"
            resultType="com.cmiot.report.dto.enterprise.EnterpriseGatewayOnlineTimeInfo">
        SELECT record_date AS recordDate, ROUND(AVG(online_time)/60, 2) AS avgOnlineTime
        FROM ge_report.t_plugin_online_record_statistics_all FINAL
        <where>
            <if test="startDate != null and startDate != ''">
                AND #{startDate} &lt;= record_date
            </if>
            <if test="endDate != null and endDate != ''">
                AND record_date &lt;= #{endDate}
            </if>
            <if test="macList != null and macList.size() > 0">
                AND gateway_mac IN
                <foreach collection="macList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY record_date
        ORDER by record_date ASC
    </select>

</mapper>