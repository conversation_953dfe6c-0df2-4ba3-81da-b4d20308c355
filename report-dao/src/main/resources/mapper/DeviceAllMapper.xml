<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.DeviceAllMapper">
  <select id="selectDeviceAllCountByExample" parameterType="com.cmiot.report.bean.DeviceAllExample" resultMap="DeviceAllByCountMap">
    select
    toYYYYMMDD(sample_time) as pdate,
    province_code,city_code,
    count(distinct mac) as device_all_count,
    count(distinct case when wlan_radio_type='2.4G' or wlan_radio_type='5G' then mac else NULL end) as device_all_wireless,
    count(distinct case when wlan_radio_type!='2.4G' and wlan_radio_type!='5G' then mac else NULL end) as device_all_wired 
    from t_device_all
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by pdate,province_code,city_code
  </select>
  <!-- 统计对象返回 -->
  <resultMap id="DeviceAllByCountMap" type="com.cmiot.report.bean.DeviceAllByCount">
  	<result column="pdate" jdbcType="BIGINT" property="pdate" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="device_all_count" jdbcType="BIGINT" property="deviceAllCount" />
    <result column="device_all_wireless" jdbcType="BIGINT" property="deviceAllWireless" />
    <result column="device_all_wired" jdbcType="BIGINT" property="deviceAllWired" />
  </resultMap>
    <select id="selectDeviceAllCountNotByPdate" parameterType="com.cmiot.report.bean.DeviceAllExample" resultMap="DeviceAllByCountMap">
    select 
    0 as pdate, 
    province_code,city_code, 
    count(distinct mac) as device_all_count, 
    count(distinct case when wlan_radio_type='2.4G' or wlan_radio_type='5G' then mac else NULL end) as device_all_wireless,
    count(distinct case when wlan_radio_type!='2.4G' and wlan_radio_type!='5G' then mac else NULL end) as device_all_wired 
    from t_device_all 
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by province_code,city_code
  </select>
</mapper>