<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmiot.report.mapper.GatewayModelYesterdayMapper">
  <resultMap id="PluginModelInstallCountResultMap" type="com.cmiot.report.dto.plugin.PluginModelCount">
    <result column="gateway_model" jdbcType="VARCHAR" property="gatewayModel" />
    <result column="modelCount" jdbcType="BIGINT" property="modelCount" />
    <result column="modelId" jdbcType="BIGINT" property="modelId" />
  </resultMap>

  <select id="getCountByModelQuery"  resultMap="PluginModelInstallCountResultMap">
    select tt1.modelCount as modelCount,tt2.device_model as gateway_model,tt1.device_model_id as modelId from (
      select count(distinct gateway_sn) as modelCount,device_model_id from t_gateway_all
      <where>
        <if test="vendor != null and vendor.size > 0" >
          and factory_id in
          <foreach collection="vendor" index="index" item="itemp" open="(" separator="," close=")">
            #{itemp}
          </foreach>
        </if>
        <if test="model != null and model.size > 0">
          and device_model_id in
          <foreach collection="model" index="index" item="itemp" open="(" separator="," close=")">
            #{itemp}
          </foreach>
        </if>
      </where>
      group by device_model_id
    ) tt1 left join  t_dic_device_model tt2 on tt1.device_model_id = tt2.device_model_id order by tt1.modelCount asc

  </select>
</mapper>