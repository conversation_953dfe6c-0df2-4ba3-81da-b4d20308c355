<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <!-- 引用的jdbc的类路径，这里将jdbc jar和generator的jar包放在一起了 -->
    <!-- 数据库驱动-->
    <context id="MysqlTables" targetRuntime="MyBatis3">
        <!--分页插件RowBoundsPlugin-->
        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>
        <!--数据库链接URL，用户名、密码 -->
        <jdbcConnection driverClass="ru.yandex.clickhouse.ClickHouseDriver"
                        connectionURL="********************************************"
                        userId="ck" password="ck2022">
            <property name="useInformationSchema" value="true"/>
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!--生成Model类存放位置-->
        <javaModelGenerator targetPackage="com.cmiot.report.bean"
                            targetProject="D:/code/fgeo/ge-report/report-dao/src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!--生成映射文件存放位置-->
        <sqlMapGenerator targetPackage="auto" targetProject="D:/code/fgeo/ge-report/report-dao/src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!--生成Dao类存放位置-->
        <javaClientGenerator targetPackage="com.cmiot.report.mapper"
                             targetProject="D:/code/fgeo/ge-report/report-dao/src/main/java" type="XMLMAPPER">
            <property name="enableSubPackages" value="true"/>

        <!--生成对应表及类名-->
        </javaClientGenerator>
        <table tableName="t_gateway_model_yesterday_local" domainObjectName="GatewayModelYesterdayLocal"/>
        <table tableName="t_gateway_model_yesterday" domainObjectName="GatewayModelYesterday"/>
        <!--<table tableName="t_gateway_model_all" domainObjectName="GatewayModelAll"/>-->
        <!--<table tableName="t_plugin_install" domainObjectName="PluginInstall"/>
        <table tableName="t_plugin_install_detail" domainObjectName="PluginInstallDetail"/>
        <table tableName="t_gateway_reboot" domainObjectName="GatewayReboot"/>
        <table tableName="t_gateway_reboot_detail" domainObjectName="GatewayRebootDetail"/>-->
        <!--<table tableName="demo" domainObjectName="Demo"-->
        <!--enableCountByExample="false" enableUpdateByExample="false"-->
        <!--enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--selectByExampleQueryId="false">-->
        <!--</table>-->
        <!--<table tableName="demo" domainObjectName="Demo"/>-->

        <!--        <table tableName="t_user_op_info" domainObjectName="UserOpInfo"/>-->
        <!--        <table tableName="t_easenet_gateway_lan_info" domainObjectName="EaseNetGatewayLanInfo"/>-->
        <!--        <table tableName="t_network_statistics" domainObjectName="NetworkStatistics"/>-->


        <!--生成对应表及类名-->
        <!--<table tableName="t_gateway_overview_count_local" domainObjectName="GatewayOverviewCount"
               enableCountByExample="true" enableUpdateByExample="true"
               enableDeleteByExample="true" enableSelectByExample="true"
               selectByExampleQueryId="true" enableUpdateByPrimaryKey="true"
               enableDeleteByPrimaryKey="true">
            <property name="useActualColumnNames" value="false"/>
        </table>-->

        <!--        <table tableName="t_gateway_on_offline_detail_local" domainObjectName="GatewayOnOfflineDetail"/>-->
        <!--                <table tableName="t_gateway_increment_count_local" domainObjectName="GatewayIncrementDetail"/>-->
        <!--        <table tableName="t_dic_area_info" domainObjectName="DicAreaInfo"/>-->
        <!--        <table tableName="t_gateway_alarm_detail_local" domainObjectName="GatewayAlarmDetail"/>-->
        <!--        <table tableName="t_gateway_alarm_threshold_local" domainObjectName="AlarmThreshold"/>-->
        <!--        <table tableName="t_gateway_pon_power_statistics_detail_local" domainObjectName="PonPowerStatisticsDetail"/>-->
        <!--        <table tableName="t_gateway_total_count_local" domainObjectName="GatewayTotalCount"/>-->
        <!--        <table tableName="t_gateway_pppoe_count_all" domainObjectName="GatewayPppoeCountAll"/>-->
<!--        <table tableName="t_customer_order_agg_all" domainObjectName="CustomerOrderAggAll"/>-->
       <!-- <table tableName="t_customer_order_local" domainObjectName="CustomerOrderAll"/>-->
        <!--        <table tableName="t_gateway_pon_power_list_local" domainObjectName="PonPowerListDetail"/>-->
        <!--        <table tableName="t_dict_industry_info" domainObjectName="DictIndustry"/>-->

    </context>
</generatorConfiguration>