package com.cmiot.report.service;

import com.cmiot.report.GatewayOldResult;
import com.cmiot.report.bean.gateway.GatewayDelayAll;
import com.cmiot.report.dto.GatewayDelayDeviceResult;
import com.cmiot.report.dto.GatewayOldListResult;
import com.cmiot.report.dto.GatewayQuery;
import java.util.List;
import java.util.Map;

public interface GatewayDelayAllService {
    List<GatewayDelayAll> queryGatewayDelayAll(GatewayQuery query);


    int countGatewayDelayAll(GatewayQuery query);

    /**
     * 查询t_gateway_all表的总量，使用相同的厂商型号、省市查询条件，
     * 并且将endtime作为一天的时间段来匹配sample_time字段
     */
    int countGatewayAll(GatewayQuery query);

    /**
     * 按维度分组查询老旧网关统计数据
     * groupType: 1-省份, 2-厂商, 3-型号， 5-城市
     */
    List<GatewayOldResult> queryOldGatewayStatistics(GatewayQuery query);

    /**
     * 查询老旧网关列表数据
     */
    List<GatewayOldListResult> queryOldGatewayList(GatewayQuery query);

    /**
     * 分页查询老旧网关列表数据
     */
    Map<String, Object> queryOldGatewayListByPage(GatewayQuery query);

    /**
     * 根据网关SN和时间段查询设备时延信息
     * @param query 查询条件（包含sn、startTime、endTime）
     * @return 设备时延信息列表
     */
    List<GatewayDelayDeviceResult> queryDelayDevicesBySn(GatewayQuery query);
}
