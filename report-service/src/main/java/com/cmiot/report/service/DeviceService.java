package com.cmiot.report.service;

import com.cmiot.report.dto.DeviceAppStatRunVo;
import com.cmiot.report.dto.GatewayQuery;
import com.cmiot.report.dto.device.*;

import java.util.Map;

public interface DeviceService {

    //网关全景视图，下挂设备统计
    DeviceOverviewCount queryDeviceOverviewCount(String province);

    //网关全景视图，网关下挂设备概览
    DeviceOverviewResult queryDeviceOverviewResult(DeviceQuery deviceQuery);

    //下挂设备无线信号强度统计（区域、厂商）
    DeviceWLANResult queryDeviceWLANResult(DeviceQuery deviceQuery);

    //小程序：下挂设备数量统计
    DeviceAppStatCount queryDeviceAppStatCount(Long enterpriseId);

    //小程序：下挂设备趋势统计
    Map<String, Object> queryDeviceAppStatTrend(Long enterpriseId, String startDate, String endDate);

    //小程序：用户运行报告，统计周期内累计下挂设备数量
    Integer queryDeviceAppStatRunCount(GatewayQuery gatewayQuery);

    //小程序：用户运行报告，统计周期内下挂设备平均网速
    DeviceAppStatRunVo queryDeviceAppStatRunSpeed(GatewayQuery gatewayQuery);
}
