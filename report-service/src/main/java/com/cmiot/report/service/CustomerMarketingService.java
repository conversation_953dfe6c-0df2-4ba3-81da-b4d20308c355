package com.cmiot.report.service;

import com.cmiot.report.dto.ExportDataResult;
import com.cmiot.report.dto.customer.*;

/**
 * @Classname CustomerMarketingService
 * @Description
 * @Date 2022/8/25 14:12
 * @Created by lei
 */
public interface CustomerMarketingService {
    CustomerMarketingDistributionData targetCustomerDistribution(TargetCustomerDistributionRequest request);

    ChangesInTargetUsersData changesInTargetUsers(ChangesInTargetUsersRequest request);

    MarketingCustomerListData precisionMarketingCustomerList(MarketingCustomerListRequest request);

    ExportDataResult precisionMarketingCustomerListExport(MarketingCustomerListRequest request);
}
