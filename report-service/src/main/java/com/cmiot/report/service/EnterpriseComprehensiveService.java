package com.cmiot.report.service;

import com.cmiot.fgeo.common.dto.PageInfoDto;
import com.cmiot.report.facade.dto.enterprise.*;

/**
 * @Classname EnterpriseComprehensiveService
 * @Description
 * @Date 2023/10/11 15:26
 * @Created by lei
 */
public interface EnterpriseComprehensiveService {
    EnterpriseComprehensiveResult getComprehensive(Long eid);

    PageInfoDto<ComprehensiveGatewayResult> getComprehensiveGateway(
            GetComprehensiveGatewayParam getComprehensiveGatewayParam);

    PageInfoDto<ComprehensivePackageResult> getComprehensivePackage(GetComprehensivePackageParam getComprehensivePackageParam);
}
