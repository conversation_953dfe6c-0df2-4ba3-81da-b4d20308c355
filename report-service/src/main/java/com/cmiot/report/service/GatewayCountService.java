package com.cmiot.report.service;


import com.cmiot.report.dto.*;

import java.util.List;


public interface GatewayCountService {


    GatewayOverviewResult getOverviewCount(List<String> provinceCode);


    GatewayCountResult getGatewayCount(GatewayCountRequest gatewayCountRequest);


    GatewayIncrementCountResult getGatewayIncrementCount(GatewayIncrementCountRequst request);


    List<GatewayOnOffLineNumResult> gatewayOnOfflineNumStatistic(GatewayOnOfflineCountRequest request, String province);


    GatewayOnOffLineNumOverviewResult gatewayOnOfflineNumOverviewStatistic(GatewayOnOfflineCountRequest request);


    MiniProgramOnOfflineCountResult gatewayOnOfflineTrendStatistic(MiniProgramOnOfflineCountRequest request);


    void setAlarmThreshold(AlarmThresholdRequest request);


    AlarmThresholdResult getAlarmThreshold();


    GatewayAlarmCountResult getGatewayAlarmCount(GatewayAlarmRequst request, String province);


    GatewayAlarmListResult getGatewayAlarmList(GatewayAlarmListRequst request, String province);


    PonStatisticResult getGatewayPonStatistic(PonStatisticRequst request);


    PonListResult getGatewayPonList(PonListRequst request);


    String getGatewayPonListExport(PonListExportRequest request);

    // 小程序运行报告告警网关数统计
   int getAlarmGatewayCount(GatewayQuery gatewayQuery);
}
