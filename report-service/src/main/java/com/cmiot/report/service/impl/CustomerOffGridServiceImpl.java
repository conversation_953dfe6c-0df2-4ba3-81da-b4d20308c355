package com.cmiot.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.fgeo.taskmanager.enums.ExportStatusEnum;
import com.cmiot.report.bean.*;
import com.cmiot.report.common.enums.OffGridFactorNameEnum;
import com.cmiot.report.common.enums.OffGridFactorIndicatorEnum;
import com.cmiot.report.common.poi.ExcelUtils;
import com.cmiot.report.dto.*;
import com.cmiot.report.mapper.*;
import com.cmiot.report.service.CustomerOffGridService;
import com.cmiot.report.service.ExportService;
import com.cmiot.report.util.AesUtil;
import com.cmiot.report.util.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * author: ranwei
 * date: 2022/08/02 17:12
 * description:
 * 潜在离网客户分析
 * 1、获取离网因素权重和阈值
 * 2、获取离网因素详情
 * 3.设置离网因素指标权重
 * 4.设置综合离网指数阈值
 * 5.获取综合离网指数信息
 * 6.设置离网因素权重阈值
 * 7.获取客户状态
 * 8.获取企业价值分类
 * 9.查询离网预计信息统计值
 * 10.查询离网预警信息列表
 * 11.导出离网预计信息列表
 */

@Service
@Slf4j
public class CustomerOffGridServiceImpl implements CustomerOffGridService {

    @Autowired
    private CustomerOffGridFactorThresholdMapper thresholdMapper;

    @Autowired
    private CustomerOffGridoverallFactorThresholdMapper overallFactorThresholdMapper;

    @Autowired
    private DictBassStdMapper dictBassStdMapper;

    @Autowired
    private OffGridCustomerMapper offGridCustomerMapper;

    @Autowired
    private ExportService exportService;

    @Autowired
    private CustomerAllMapper customerAllMapper;

    @Autowired
    private EnterpriseComprehensiveMapper enterpriseComprehensiveMapper;

    /**
     * 临时目录
     */
    @Value("${export.tmp.local}")
    private String tmpPath;

    /**
     * 1 使用内网地址，否则使用外网地址
     */
    @Value("${export.use-pri-url}")
    private Integer usePriUrl;


    /**
     * 1、获取离网因素权重和阈值
     *
     * @return
     */
    @Override
    public List getOffGridFactorThreshold() {
        log.info("潜在离网客户分析 - 获取离网因素权重和阈值接口查询.");

        List result = new ArrayList();

        try {
            CustomerOffGridFactorThresholdExample example = new CustomerOffGridFactorThresholdExample();

            result = thresholdMapper.selectFactorByExample(example);

        } catch (Exception e) {
            log.info("潜在离网客户分析 - 获取离网因素权重和阈值接口查询异常: {}", e.getMessage(), e);
        }

        log.info("潜在离网客户分析 - 获取离网因素权重和阈值接口方法出参= {}", JSON.toJSONString(result));

        return result;
    }


    /**
     * 2、获取离网因素详情
     *
     * @param factorKey
     * @return
     */
    @Override
    public OffGridFactorThresholdDetail getOffGridFactorThresholdDetail(String factorKey) {
        log.info("潜在离网客户分析 - 获取离网因素详情接口方法入参factorKey: {}", factorKey);

        OffGridFactorThresholdDetail result = new OffGridFactorThresholdDetail();

        try {
            if (StringUtils.isBlank(factorKey)) return null;

            CustomerOffGridFactorThresholdExample example = new CustomerOffGridFactorThresholdExample();
            CustomerOffGridFactorThresholdExample.Criteria criteria = example.createCriteria();
            criteria.andFactorTypeEqualTo(Byte.parseByte(factorKey));

            List<CustomerOffGridFactorThreshold> list = thresholdMapper.selectFactorDetailByExample(example);

            if (!CollectionUtils.isEmpty(list)) {
                List<OffGridFactorIndicator> indicators = new ArrayList<>();
                int size = list.size();
                for (int i = 0; i < size; i++) {
                    CustomerOffGridFactorThreshold item = list.get(i);

                    OffGridFactorIndicator indicator = new OffGridFactorIndicator();
                    String indicateDesc = item.getFactorIndicateDesc();
                    // 11,12,...,51,52..
                    String indicatorKey = String.valueOf(item.getFactorIndicateType());
                    String indicateWeight = String.valueOf(item.getFactorIndicateWeight());
                    String indicatorName = "指标" + indicatorKey.substring(1);
                    indicator.setIndicatorDesc(indicateDesc);
                    indicator.setIndicatorKey(indicatorKey);
                    indicator.setWeight(indicateWeight);
                    indicator.setIndicatorName(indicatorName);
                    indicators.add(indicator);

                    if (i == 0) {
                        String factorName = item.getFactorName();
                        double factorWeight = item.getFactorWeight();
                        int factorThreshold = item.getFactorThreshold();
                        result.setFactorName(factorName);
                        result.setFactorKey(factorKey);
                        result.setWeight(factorWeight);
                        result.setThreshold(factorThreshold);
                    }
                }

                // 排序
                indicators.stream().sorted(Comparator.comparing(OffGridFactorIndicator::getIndicatorKey)).
                        collect(Collectors.toList());
                result.setIndicators(indicators);
            } else return null;

        } catch (Exception e) {
            log.info("潜在离网客户分析 - 获取离网因素详情接口查询异常: {}", e.getMessage(), e);
        }

        log.info("潜在离网客户分析 - 获取离网因素详情接口方法出参= {}", JSON.toJSONString(result));

        return result;
    }


    /**
     * 3.设置离网因素指标权重
     *
     * @param factorSet
     * @return
     */
    @Override
    public void setOffGridFactorIndicaterThreshold(OffGridFactorIndicatorThresholdSet factorSet) {
        log.info("潜在离网客户分析 - 设置离网因素指标权重接口方法入参: {}", JSON.toJSONString(factorSet));

        try {
            long timestamp = System.currentTimeMillis() / 1000;
            Date date = new Date();
            long gdate = Long.parseLong(new SimpleDateFormat("yyyyMMdd").format(date));

            List<CustomerOffGridFactorThreshold> list = new ArrayList<>();

            String factorKey = factorSet.getFactorKey();
            List<OffGridFactorIndicatorThreshold> indicators = factorSet.getIndicators();

            if (!CollectionUtils.isEmpty(indicators)) {
                OffGridFactorThresholdDetail factorThresholdDetail = getOffGridFactorThresholdDetail(factorKey);
                String factorName = factorThresholdDetail.getFactorName();
                // 因素
                int threshold = factorThresholdDetail.getThreshold();
                double weight = factorThresholdDetail.getWeight();

                // 指标
                indicators.forEach(item -> {
                    int indicaterKey = item.getIndicatorKey();
                    double indicaterWeight = item.getWeight();

                    for (int i = 1; i <= 5; i++) {
                        CustomerOffGridFactorThreshold record = new CustomerOffGridFactorThreshold();

                        String indicatorDesc = OffGridFactorIndicatorEnum.getFactorIndicatorDesc(indicaterKey);
                        if (StringUtils.isNotBlank(indicatorDesc)) {
                            record.setTimestamp(timestamp);
                            record.setFactorType(Byte.parseByte(factorKey));
                            record.setFactorName(factorName);
                            record.setFactorThreshold(threshold);
                            record.setFactorWeight(weight);
                            record.setFactorIndicateType(Byte.parseByte(indicaterKey + ""));
                            record.setFactorIndicateDesc(indicatorDesc);
                            record.setFactorIndicateWeight(indicaterWeight);
                            record.setSampleTime(date);
                            record.setGdate(gdate);
                            list.add(record);
                        }
                    }

                });

                thresholdMapper.insertFactorByBatch(list);
                log.info("潜在离网客户分析 - 设置离网因素指标权重成功写入表的数据: {}", JSON.toJSONString(list));
            }

        } catch (Exception e) {
            log.info("潜在离网客户分析 - 设置离网因素指标权重接口方法异常: {}", e.getMessage(), e);
        }

        log.info("潜在离网客户分析 - 设置离网因素指标权重接口方法结束. ");
    }


    /**
     * 4.设置综合离网指数阈值
     *
     * @param threshold
     */
    @Override
    public void setOffGridOverallFactorThreshold(Object threshold) {
        log.info("潜在离网客户分析 - 设置综合离网指数阈值接口方法入参threshold: {}", threshold);

        try {
            CustomerOffGridoverallFactorThreshold record = new CustomerOffGridoverallFactorThreshold();
            record.setTimestamp(System.currentTimeMillis() / 1000);
            Map map = (Map) threshold;
            record.setFactorThreshold((Integer) map.get("threshold"));
            record.setSampleTime(new Date());

            overallFactorThresholdMapper.insert(record);
            log.info("潜在离网客户分析 - 设置综合离网指数阈值成功写入表的数据: {}", record);
        } catch (Exception e) {
            log.info("潜在离网客户分析 - 设置综合离网指数阈值接口方法异常: {}", e.getMessage(), e);
        }

        log.info("潜在离网客户分析 - 设置综合离网指数阈值接口方法结束. ");
    }


    /**
     * 5.获取综合离网指数信息
     *
     * @return
     */
    @Override
    public int getOffGridOverallFactorThreshold() {
        log.info("潜在离网客户分析 - 获取综合离网指数信息接口查询.");

        int threshold = 0;

        try {
            CustomerOffGridoverallFactorThresholdExample example = new CustomerOffGridoverallFactorThresholdExample();
            threshold = overallFactorThresholdMapper.selectNewestByExample(example);
        } catch (Exception e) {
            log.info("潜在离网客户分析 - 获取综合离网指数信息接口方法异常: {}", e.getMessage(), e);
        }

        log.info("潜在离网客户分析 - 获取综合离网指数信息接口方法出参= {}", threshold);
        return threshold;
    }


    /**
     * 6.设置离网因素权重阈值
     *
     * @param request
     */
    @Override
    public void setOffGridFactorThreshold(SetOffGridFactorThresholdRequest request) {
        log.info("潜在离网客户分析 - 设置离网因素权重阈值接口方法入参factors: {}", JSON.toJSONString(request));

        try {
            long timestamp = System.currentTimeMillis() / 1000;
            Date date = new Date();
            long gdate = Long.parseLong(new SimpleDateFormat("yyyyMMdd").format(date));

            List<CustomerOffGridFactorThreshold> list = new ArrayList<>();

            List<OffGridFactorThresholdSet> factors = request.getFactors();
            if (!CollectionUtils.isEmpty(factors)) {
                CustomerOffGridFactorThresholdExample example = new CustomerOffGridFactorThresholdExample();
                List<CustomerOffGridFactorThreshold> thresholdList = thresholdMapper.selectFactorDetailByExample(example);
                if (!CollectionUtils.isEmpty(thresholdList)) {
                    // (indicatorKey,indicatorWeight)
                    Map<Byte, Double> indicateTypeWightMap = thresholdList.stream().collect(Collectors.toMap(CustomerOffGridFactorThreshold::getFactorIndicateType, CustomerOffGridFactorThreshold::getFactorIndicateWeight));
                    factors.forEach(item -> {
                        String factorKey = item.getFactorKey();
                        double weight = item.getWeight();
                        int threshold = item.getThreshold();
                        String factorName = OffGridFactorNameEnum.getFactorName(Integer.parseInt(factorKey));

                        for (int i = 1; i <= 5; i++) {
                            CustomerOffGridFactorThreshold record = new CustomerOffGridFactorThreshold();
                            int factorIndicateType = Integer.parseInt(factorKey + i);

                            String indicatorDesc = OffGridFactorIndicatorEnum.getFactorIndicatorDesc(factorIndicateType);
                            if (StringUtils.isNotBlank(indicatorDesc)) {
                                double indicatorWeight = indicateTypeWightMap.get(Byte.parseByte(factorIndicateType + ""));
                                record.setTimestamp(timestamp);
                                record.setFactorType(Byte.parseByte(factorKey));
                                record.setFactorName(factorName);
                                record.setFactorThreshold(threshold);
                                record.setFactorWeight(weight);
                                record.setFactorIndicateType(Byte.parseByte(factorIndicateType + ""));
                                record.setFactorIndicateDesc(indicatorDesc);
                                record.setFactorIndicateWeight(indicatorWeight);
                                record.setSampleTime(date);
                                record.setGdate(gdate);
                                list.add(record);
                            }
                        }

                    });

                    thresholdMapper.insertFactorByBatch(list);
                    log.info("潜在离网客户分析 - 设置离网因素权重阈值成功写入表的数据: {}", JSON.toJSONString(list));
                }
            }

        } catch (Exception e) {
            log.info("潜在离网客户分析 - 设置离网因素权重阈值接口方法异常: {}", e.getMessage(), e);
        }

        log.info("潜在离网客户分析 - 设置离网因素权重阈值接口方法结束. ");
    }


    /**
     * 7.获取客户状态
     *
     * @return
     */
    @Override
    public List<BassType> getCustomerStatus() {
        log.info("潜在离网客户分析 - 获取客户状态接口查询.");

        List<BassType> result = new ArrayList<>();

        try {
            DictBassStdExample example = new DictBassStdExample();
            DictBassStdExample.Criteria criteria = example.createCriteria();
            example.setOrderByClause("code DESC");
            // bass_type='BASS_STD1_0028' 客户状态 (不在网-10,离网-11,未入网-12,在网-20)
            criteria.andBassTypeEqualTo("BASS_STD1_0028");

            List<DictBassStd> stdList = dictBassStdMapper.selectByExample(example);
            stdList.forEach(item -> {
                BassType bt = new BassType();
                String name = item.getName();
                String code = item.getCode();
                bt.setName(name);
                bt.setValue(code);
                result.add(bt);
            });

        } catch (Exception e) {
            log.info("潜在离网客户分析 - 获取客户状态方法异常: {}", e.getMessage(), e);
        }

        log.info("潜在离网客户分析 - 获取客户状态接口方法出参= {}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 8.获取企业价值分类
     *
     * @return
     */
    @Override
    public List<BassType> getEnterpriseValueCategory() {
        log.info("潜在离网客户分析 - 获取企业价值分类接口查询.");

        List<BassType> result = new ArrayList<>();

        try {
            DictBassStdExample example = new DictBassStdExample();
            DictBassStdExample.Criteria criteria = example.createCriteria();
            example.setOrderByClause("code ASC");
            // bass_type='BASS_STD1_0018' 客户企业价值分类 (A1类-4,A2类-5,B1类-6,B2类-7,C类-8,D类-9)
            criteria.andBassTypeEqualTo("BASS_STD1_0018");

            List<DictBassStd> stdList = dictBassStdMapper.selectByExample(example);
            stdList.forEach(item -> {
                BassType bt = new BassType();
                String name = item.getName();
                String code = item.getCode();
                bt.setName(name);
                bt.setValue(code);
                result.add(bt);
            });
        } catch (Exception e) {
            log.info("潜在离网客户分析 - 获取企业价值分类方法异常: {}", e.getMessage(), e);
        }
        log.info("潜在离网客户分析 - 获取企业价值分类接口方法出参= {}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 9.查询离网预计信息统计值
     *
     * @param request
     * @return
     */
    @Override
    public OffGridCustStatisticResult getOffGridCustStatistic(OffGridCustStatisticRequest request) {
        log.info("潜在离网客户分析—离网预警信息统计接口方法入参: {}", JSON.toJSONString(request));

        OffGridCustStatisticResult result = new OffGridCustStatisticResult();

        try {
            List<String> provList = request.getProvince();
            List<String> cityList = request.getCity();

            OffGridCustomerExample example = new OffGridCustomerExample();
            OffGridCustomerExample.Criteria criteria = resetCriteria(example, provList, cityList);

            // 占比中,分母为筛选条件下,且能算出对分数的的全部客户数量
            // 有潜在离网因素的客户数
            long allCount = offGridCustomerMapper.countByExample(example);

            // 1.综合离网指数超阈值的客户数,占比
            int overall = getOffGridOverallFactorThreshold();
            criteria.andOffGridScoreGreaterThan(overall);
            long overallCount = offGridCustomerMapper.countByExample(example);
            String overallRatio = getPercent(overallCount, allCount);
            result.setSyntheticalValue(String.valueOf(overallCount));
            result.setSyntheticalRatio(overallRatio);

            // 各个离网因素及阈值
            List<OffGridFactorThresholdResult> factorThresholds = getOffGridFactorThreshold();
            factorThresholds.forEach(item -> {
                int factorKey = item.getFactorKey();
                int threshold = item.getThreshold();
                OffGridCustomerExample.Criteria criteriaExt = resetCriteria(example, provList, cityList);
                switch (factorKey) {
                    case 1:
                        // 2.终端质量因素分超阈值的客户数
                        criteriaExt.andTerminalScoreGreaterThan(threshold);
                        long terminalCount = offGridCustomerMapper.countByExample(example);
                        String terminalRatio = getPercent(terminalCount, allCount);
                        result.setTerminalQualityValue(String.valueOf(terminalCount));
                        result.setTerminalQualityRatio(terminalRatio);
                        break;
                    case 2:
                        // 3.网络质量因素分超阈值的客户数,占比
                        criteriaExt.andNetworkScoreGreaterThan(threshold);
                        long networkCount = offGridCustomerMapper.countByExample(example);
                        String networkRatio = getPercent(networkCount, allCount);
                        result.setNetworkQualityValue(String.valueOf(networkCount));
                        result.setNetworkQualityRatio(networkRatio);
                        break;
                    case 3:
                        // 4.客户服务因素分超阈值的客户数,占比
                        criteriaExt.andServiceScoreGreaterThan(threshold);
                        long serviceCount = offGridCustomerMapper.countByExample(example);
                        String serviceRatio = getPercent(serviceCount, allCount);
                        result.setCustomerServiceValue(String.valueOf(serviceCount));
                        result.setCustomerServiceRatio(serviceRatio);
                        break;
                    case 4:
                        // 4.业务订购因素分超阈值的客户数,占比
                        criteriaExt.andBusinessScoreGreaterThan(threshold);
                        long businessCount = offGridCustomerMapper.countByExample(example);
                        String businessRatio = getPercent(businessCount, allCount);
                        result.setBusinessOrderValue(String.valueOf(businessCount));
                        result.setBusinessOrderRatio(businessRatio);
                        break;
                    case 5:
                        // 5.业装维服务分超阈值的客户数,占比
                        criteriaExt.andMaintenanceScoreGreaterThan(threshold);
                        long maintenanceCount = offGridCustomerMapper.countByExample(example);
                        String maintenanceRatio = getPercent(maintenanceCount, allCount);
                        result.setInstallationAndMaintenanceServiceValue(String.valueOf(maintenanceCount));
                        result.setInstallationAndMaintenanceServiceRatio(maintenanceRatio);
                        break;
                    default:
                        break;
                }
            });

        } catch (Exception e) {
            log.info("潜在离网客户分析—离网预警信息统计接口方法异常: {}", e.getMessage(), e);
        }

        log.info("潜在离网客户分析—离网预警信息统计接口方法出参= {}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 10.查询离网预警信息列表
     *
     * @param request
     * @return
     */
    @Override
    public OffGridCustListResult getOffGridCustList(OffGridCustListRequest request) {
        log.info("潜在离网客户分析—离网预警信息列表查询接口方法入参: {}", JSON.toJSONString(request));

        OffGridCustListResult result = new OffGridCustListResult();

        try {
            List<String> provList = request.getProvince();
            List<String> cityList = request.getCity();
            String customerName = request.getBusiness();
            String customerId = request.getGroupCustomer();
            String valueCategory = request.getEnterpriseValueCategory();
            String customerStatus = request.getCustomerStatus();
            Integer overallStart = request.getSyntheticalStart();
            Integer overallEnd = request.getSyntheticalEnd();
            Integer terminalQualityStart = request.getTerminalQualityStart();
            Integer terminalQualityEnd = request.getTerminalQualityEnd();
            Integer networkQualityStart = request.getNetworkQualityStart();
            Integer networkQualityEnd = request.getNetworkQualityEnd();
            Integer customerServiceStart = request.getCustomerServiceStart();
            Integer customerServiceEnd = request.getCustomerServiceEnd();
            Integer businessOrderStart = request.getBusinessOrderStart();
            Integer businessOrderEnd = request.getBusinessOrderEnd();
            Integer maintenanceServiceStart = request.getInstallationAndMaintenanceServiceStart();
            Integer maintenanceServiceEnd = request.getInstallationAndMaintenanceServiceEnd();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();

            OffGridCustomerExample example = new OffGridCustomerExample();
            OffGridCustomerExample.Criteria criteria = example.createCriteria();
            example.setDistinct(true);
            example.setOrderByClause(" off_grid_score DESC ");

            if (!CollectionUtils.isEmpty(provList)) criteria.andProvinceCodeIn(provList);
            if (!CollectionUtils.isEmpty(cityList)) criteria.andCityCodeIn(cityList);

            // 模糊查询
            if (StringUtils.isNotBlank(customerName)) criteria.andCustomerNameLike("%" + customerName + "%");
            if (StringUtils.isNotBlank(customerId)) criteria.andCustomerIdLike("%" + customerId + "%");

            if (StringUtils.isNotBlank(valueCategory)) criteria.andValueCategoryEqualTo(valueCategory);
            if (StringUtils.isNotBlank(customerStatus))
                criteria.andCustomerStatusEqualTo(Integer.parseInt(customerStatus));

            // 综合离网指数
            //OffGridCustomerExample.Criteria criteria = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus, overallStart, overallEnd);
            if (overallStart != null && overallEnd != null && (overallStart >= 0 && overallStart <= 100) && (overallEnd >= 0 && overallEnd <= 100) && overallStart <= overallEnd) {
                criteria.andOffGridScoreBetween(overallStart, overallEnd);
                //example.or(criteria);
            }

            // 终端质量因素
            if (terminalQualityStart != null && terminalQualityEnd != null && (terminalQualityStart >= 0 && terminalQualityStart <= 100) && (terminalQualityEnd >= 0 && terminalQualityEnd <= 100) && terminalQualityStart <= terminalQualityEnd) {
                //OffGridCustomerExample.Criteria criteria1 = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus,overallStart,overallEnd);
                criteria.andTerminalScoreBetween(terminalQualityStart, terminalQualityEnd);
                //example.or(criteria1);
            }

            // 网络质量因素
            if (networkQualityStart != null && networkQualityEnd != null && (networkQualityStart >= 0 && networkQualityStart <= 100) && (networkQualityEnd >= 0 && networkQualityEnd <= 100) && networkQualityStart <= networkQualityEnd) {
                //OffGridCustomerExample.Criteria criteria2 = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus,overallStart,overallEnd);
                criteria.andNetworkScoreBetween(networkQualityStart, networkQualityEnd);
                //example.or(criteria2);
            }

            // 客服服务因素
            if (customerServiceStart != null && customerServiceEnd != null && (customerServiceStart >= 0 && customerServiceStart <= 100) && (customerServiceEnd >= 0 && customerServiceEnd <= 100) && customerServiceStart <= customerServiceEnd) {
                //OffGridCustomerExample.Criteria criteria3 = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus,overallStart,overallEnd);
                criteria.andServiceScoreBetween(customerServiceStart, customerServiceEnd);
                //example.or(criteria3);
            }

            // 业务订购因素
            if (businessOrderStart != null && businessOrderEnd != null && (businessOrderStart >= 0 && businessOrderStart <= 100) && (businessOrderEnd >= 0 && businessOrderEnd <= 100) && businessOrderStart <= businessOrderEnd) {
                //OffGridCustomerExample.Criteria criteria4 = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus,overallStart,overallEnd);
                criteria.andBusinessScoreBetween(businessOrderStart, businessOrderEnd);
                //example.or(criteria4);
            }

            // 装维服务因素
            if (maintenanceServiceStart != null && maintenanceServiceEnd != null && (maintenanceServiceStart >= 0 && maintenanceServiceStart <= 100) && (maintenanceServiceEnd >= 0 && maintenanceServiceEnd <= 100) && maintenanceServiceStart <= maintenanceServiceEnd) {
                //OffGridCustomerExample.Criteria criteria5 = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus,overallStart,overallEnd);
                criteria.andMaintenanceScoreBetween(maintenanceServiceStart, maintenanceServiceEnd);
                //example.or(criteria5);
            }

            Page<OffGridCustList> doPage = PageMethod.startPage(page, pageSize)
                    .doSelectPage(() -> offGridCustomerMapper.selectListByExample(example));

            List<OffGridCustList> list = doPage.getResult();
            long total = doPage.getTotal();
            int size = doPage.getPageSize();
            int pages = doPage.getPages();

            result.setList(CollectionUtils.isEmpty(list) ? new ArrayList<>() : list);
            result.setTotal(total);
            result.setPagaSize(size);
            result.setPage(pages);
        } catch (Exception e) {
            log.info("潜在离网客户分析—离网预警信息列表查询接口方法异常: {}", e.getMessage(), e);
        }

        log.info("潜在离网客户分析—离网预警信息列表查询接口方法出参= {}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 11.离网预警信息列表明细导出
     *
     * @param request
     */
    @Override
    public String exportOffGridCustomer(OffGridCustExportRequest request) {
        log.info("潜在离网客户分析—离网预警信息列表明细导出接口方法开始. ");

        String exportTaskName = "";

        try {
            List<String> provList = request.getProvince();
            List<String> cityList = request.getCity();
            String customerName = request.getBusiness();
            String customerId = request.getGroupCustomer();
            String valueCategory = request.getEnterpriseValueCategory();
            String customerStatus = request.getCustomerStatus();
            Integer overallStart = request.getSyntheticalStart();
            Integer overallEnd = request.getSyntheticalEnd();
            Integer terminalQualityStart = request.getTerminalQualityStart();
            Integer terminalQualityEnd = request.getTerminalQualityEnd();
            Integer networkQualityStart = request.getNetworkQualityStart();
            Integer networkQualityEnd = request.getNetworkQualityEnd();
            Integer customerServiceStart = request.getCustomerServiceStart();
            Integer customerServiceEnd = request.getCustomerServiceEnd();
            Integer businessOrderStart = request.getBusinessOrderStart();
            Integer businessOrderEnd = request.getBusinessOrderEnd();
            Integer maintenanceServiceStart = request.getInstallationAndMaintenanceServiceStart();
            Integer maintenanceServiceEnd = request.getInstallationAndMaintenanceServiceEnd();
            long uid = Long.parseLong(request.getUid());

            OffGridCustomerExample example = new OffGridCustomerExample();
            OffGridCustomerExample.Criteria criteria = example.createCriteria();
            example.setDistinct(true);
            example.setOrderByClause(" off_grid_score DESC ");

            if (!CollectionUtils.isEmpty(provList)) criteria.andProvinceCodeIn(provList);
            if (!CollectionUtils.isEmpty(cityList)) criteria.andCityCodeIn(cityList);

            // 模糊查询
            if (StringUtils.isNotBlank(customerName)) criteria.andCustomerNameLike("%" + customerName + "%");
            if (StringUtils.isNotBlank(customerId)) criteria.andCustomerIdLike("%" + customerId + "%");

            if (StringUtils.isNotBlank(valueCategory)) criteria.andValueCategoryEqualTo(valueCategory);
            if (StringUtils.isNotBlank(customerStatus))
                criteria.andCustomerStatusEqualTo(Integer.parseInt(customerStatus));

            // 综合离网指数
            //OffGridCustomerExample.Criteria criteria = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus, overallStart, overallEnd);
            if (overallStart != null && overallEnd != null && (overallStart >= 0 && overallStart <= 100) && (overallEnd >= 0 && overallEnd <= 100) && overallStart <= overallEnd) {
                criteria.andOffGridScoreBetween(overallStart, overallEnd);
                //example.or(criteria);
            }

            // 终端质量因素
            if (terminalQualityStart != null && terminalQualityEnd != null && (terminalQualityStart >= 0 && terminalQualityStart <= 100) && (terminalQualityEnd >= 0 && terminalQualityEnd <= 100) && terminalQualityStart <= terminalQualityEnd) {
                //OffGridCustomerExample.Criteria criteria1 = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus,overallStart,overallEnd);
                criteria.andTerminalScoreBetween(terminalQualityStart, terminalQualityEnd);
                //example.or(criteria1);
            }

            // 网络质量因素
            if (networkQualityStart != null && networkQualityEnd != null && (networkQualityStart >= 0 && networkQualityStart <= 100) && (networkQualityEnd >= 0 && networkQualityEnd <= 100) && networkQualityStart <= networkQualityEnd) {
                //OffGridCustomerExample.Criteria criteria2 = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus,overallStart,overallEnd);
                criteria.andNetworkScoreBetween(networkQualityStart, networkQualityEnd);
                //example.or(criteria2);
            }

            // 客服服务因素
            if (customerServiceStart != null && customerServiceEnd != null && (customerServiceStart >= 0 && customerServiceStart <= 100) && (customerServiceEnd >= 0 && customerServiceEnd <= 100) && customerServiceStart <= customerServiceEnd) {
                //OffGridCustomerExample.Criteria criteria3 = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus,overallStart,overallEnd);
                criteria.andServiceScoreBetween(customerServiceStart, customerServiceEnd);
                //example.or(criteria3);
            }

            // 业务订购因素
            if (businessOrderStart != null && businessOrderEnd != null && (businessOrderStart >= 0 && businessOrderStart <= 100) && (businessOrderEnd >= 0 && businessOrderEnd <= 100) && businessOrderStart <= businessOrderEnd) {
                //OffGridCustomerExample.Criteria criteria4 = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus,overallStart,overallEnd);
                criteria.andBusinessScoreBetween(businessOrderStart, businessOrderEnd);
                //example.or(criteria4);
            }

            // 装维服务因素
            if (maintenanceServiceStart != null && maintenanceServiceEnd != null && (maintenanceServiceStart >= 0 && maintenanceServiceStart <= 100) && (maintenanceServiceEnd >= 0 && maintenanceServiceEnd <= 100) && maintenanceServiceStart <= maintenanceServiceEnd) {
                //OffGridCustomerExample.Criteria criteria5 = resetListCriteria(example, provList, cityList, customerName, customerId, valueCategory, customerStatus,overallStart,overallEnd);
                criteria.andMaintenanceScoreBetween(maintenanceServiceStart, maintenanceServiceEnd);
                //example.or(criteria5);
            }

            long count = offGridCustomerMapper.countByExample(example);
            if (count > 0) {
                ExecutorService service = Executors.newSingleThreadExecutor();
                String exportType = "离网预警客户明细导出";
                String exportName = "离网预警客户明细_" + DateUtil.dateToString(new Date(), DateUtil.UNSIGNED_DATE_TIME_PATTERN);
                ExportInfoDto record = exportService.record(uid, exportType, exportName);
                exportTaskName = record.getExportName();
                service.execute(() -> offGridCustExportTask(example, record, exportName));
            } else {
                log.error("潜在离网客户分析—离网预警信息列表明细导出，查询数据为空.");
            }

        } catch (Exception e) {
            log.info("潜在离网客户分析—离网预警信息列表明细导出接口方法异常: {}", e.getMessage(), e);
        }

        Map<String, String> map = new HashMap<>();
        map.put("name", exportTaskName);

        log.info("潜在离网客户分析—离网预警信息列表明细导出，接口方法出参：{}", JSON.toJSONString(map));
        return JSON.toJSONString(map);
    }

    @Override
    public Page<GatewayAnomalyRecord> queryAnomalyRecordList(GatewayAnomalyRecordQuery query) {
        Page<GatewayAnomalyRecord> pageData = PageHelper.startPage(query.getPage(), query.getPageSize())
                .doSelectPage(() -> offGridCustomerMapper.queryAnomalyRecordList(query));
        return pageData;
    }

    @Override
    public Page<GatewayNotOnline> queryNotOnline(GatewayAnomalyRecordQuery query) {
        Page<GatewayNotOnline> pageData = PageHelper.startPage(query.getPage(), query.getPageSize())
                .doSelectPage(() -> offGridCustomerMapper.queryNotOnline(query));

        //如果有数据则查询对应企业地址、订购实例Id
        if (CollectionUtil.isNotEmpty(pageData.getResult())) {
            List<String> customerIds = new ArrayList<>();
            Map<String, GatewayNotOnline> gatewayNotOnlineMap = new HashMap<>();
            for (GatewayNotOnline gatewayNotOnline : pageData.getResult()) {
                gatewayNotOnlineMap.put(gatewayNotOnline.getCustomerId(), gatewayNotOnline);
                customerIds.add(gatewayNotOnline.getCustomerId());
            }
            if (CollectionUtil.isNotEmpty(customerIds)) {
                CustomerAllExample customerAllExample = new CustomerAllExample();
                customerAllExample.createCriteria()
                        .andCustomerIdIn(customerIds)
                        .andSampleTimeToYYYYMMDDEqualTo(String.valueOf(query.getLastday()));
                List<CustomerAll> customerAlls = customerAllMapper.selectByExample(customerAllExample);
                for (CustomerAll customerAll : customerAlls) {
                    String tempCId = customerAll.getCustomerId();
                    if (gatewayNotOnlineMap.containsKey(tempCId)) {
                        if (StringUtils.isNotBlank(customerAll.getAddress())) {
                          try {
                              gatewayNotOnlineMap.get(tempCId).setAddress(AesUtil.decrypt(customerAll.getAddress()));
                          } catch (Exception e) {
                              log.info("获取企业地址信息异常: {}", e.getMessage(), e);
                          }
                        }
                    }
                }

                //获取订购实例id
                List<CustomerOrderAll> customerOrderAlls =
                        this.enterpriseComprehensiveMapper.getOrdersByCustomerIds(customerIds, String.valueOf(query.getLastday()));
                for (CustomerOrderAll customerOrderAll : customerOrderAlls) {
                    String tempCId = customerOrderAll.getCustomerId();
                    if (gatewayNotOnlineMap.containsKey(tempCId)) {
                        List<String> businessProductIds = gatewayNotOnlineMap.get(tempCId).getBusinessProductIds();
                        if (businessProductIds == null) {
                            businessProductIds = new ArrayList<>();
                            gatewayNotOnlineMap.get(tempCId).setBusinessProductIds(businessProductIds);
                        }
                        businessProductIds.add(customerOrderAll.getBusinessProductId());
                    }
                }
            }
        }
        return pageData;
    }
    public static void main(String[] args) {
        Date yestoday = DateUtil.getBeforeDayDate(1);
        System.out.println(yestoday);
    }

    @Override
    public Page<CustomerUneffect> queryUneffect(GatewayAnomalyRecordQuery query) {
        Page<CustomerUneffect> pageData = PageHelper.startPage(query.getPage(), query.getPageSize())
                .doSelectPage(() -> offGridCustomerMapper.queryUneffect(query));
        List<CustomerUneffect> customerUneffects = pageData.getResult();
        if (CollectionUtil.isNotEmpty(customerUneffects)) {
            for (CustomerUneffect customerUneffect : customerUneffects) {
                try {
                    String managerNumber = customerUneffect.getManagerNumber();
                    if (StringUtils.isNotBlank(managerNumber)) {
                        managerNumber = AesUtil.decrypt(managerNumber);
                        customerUneffect.setManagerNumber(managerNumber);
                    }
                } catch (Exception e) {
                    log.info("解析客户经理电话号码信息: {}", e.getMessage(), e);
                }
            }
        }

        return pageData;
    }

    public void offGridCustExportTask(OffGridCustomerExample example, ExportInfoDto record, String exportName) {
        log.info("潜在离网客户分析—离网预警信息列表明细导出任务开始~");
        List<OffGridCustExportVO> voList = offGridCustomerMapper.selectExportByExample(example);

        String localFilePath = tmpPath + "/" + exportName.replace(" ", "_") + ".xlsx";
        File dataFile = ExcelUtils.writeExcel(voList, OffGridCustExportVO.class, localFilePath);

        String downLoadUrl = "";
        String failedDesc = "SUCCESS";
        try {
            Map<?, ?> uploadFile = exportService.uploadFile(record.getCreatorId(), dataFile);
            // 获取内网地址
            if (usePriUrl != null && usePriUrl == 1) {
                if (uploadFile.containsKey("privateUrl")) {
                    downLoadUrl = uploadFile.get("privateUrl").toString();
                } else {
                    failedDesc = "上传文件响应无内网地址";
                }
            } else {
                if (uploadFile.containsKey("internetUrl")) {
                    downLoadUrl = uploadFile.get("internetUrl").toString();
                } else {
                    failedDesc = "上传文件响应无公网地址";
                }
            }
        } catch (IOException ex) {
            failedDesc = "文件上传异常:" + ex.getMessage();
            log.error("潜在离网客户分析—离网预警信息列表明细数据导出任务异常={}", ex.getMessage(), ex);
        }

        // 上传记录设置
        record.setFailedDesc(failedDesc);
        record.setDownloadUrl(downLoadUrl);
        record.setStatus("SUCCESS".equalsIgnoreCase(failedDesc) ? ExportStatusEnum.SUCCESS : ExportStatusEnum.FAILED);
        exportService.updateRecord(record);

        log.info("潜在离网客户分析—离网预警信息列表明细导出任务结束~");
    }


    private OffGridCustomerExample.Criteria resetCriteria(OffGridCustomerExample example, List<String> provList, List<String> cityList) {
        example.getOredCriteria().clear();
        OffGridCustomerExample.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(provList)) criteria.andProvinceCodeIn(provList);
        if (!CollectionUtils.isEmpty(cityList)) criteria.andCityCodeIn(cityList);
        return criteria;
    }

    private OffGridCustomerExample.Criteria resetListCriteria(OffGridCustomerExample example, List<String> provList, List<String> cityList, String customerName, String customerId, String valueCategory, String customerStatus, int overallStart, int overallEnd) {
        example.getOredCriteria().clear();
        OffGridCustomerExample.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(provList)) criteria.andProvinceCodeIn(provList);
        if (!CollectionUtils.isEmpty(cityList)) criteria.andCityCodeIn(cityList);

        // 综合离网指数
        if ((overallStart > 0 && overallStart <= 100) && (overallEnd > 0 && overallEnd <= 100) && overallStart <= overallEnd) {
            criteria.andOffGridScoreBetween(overallStart, overallEnd);
            //example.or(criteria);
        }

        // 模糊查询
        if (StringUtils.isNotBlank(customerName)) criteria.andCustomerNameLike(customerName);
        if (StringUtils.isNotBlank(customerId)) criteria.andCustomerIdLike(customerId);

        if (StringUtils.isNotBlank(valueCategory)) criteria.andValueCategoryEqualTo(valueCategory);
        if (StringUtils.isNotBlank(customerStatus))
            criteria.andCustomerStatusEqualTo(Integer.parseInt(customerStatus));
        return criteria;
    }


    private String decimalFormat(String num) {
        String result;
        if (StringUtils.isNotBlank(num) && num.contains(".")) {
            DecimalFormat df = new DecimalFormat("0.00");
            result = df.format(Double.parseDouble(num));
        } else {
            result = num;
        }

        return result;
    }

    private String getPercent(Long num, Long total) {
        String perp;
        try {
            if (Objects.equals(num, 0L)) {
                perp = "0";
            } else if (Objects.equals(num, total)) {
                perp = "100";
            } else {
                perp = decimalFormat(String.valueOf(new BigDecimal((float) num / total).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() * 100));
            }
        } catch (Exception e) {
            perp = "0";
        }

        return perp;
    }
}

