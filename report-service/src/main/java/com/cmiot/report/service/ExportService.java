package com.cmiot.report.service;

import java.io.File;
import java.io.IOException;
import java.util.Map;

import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;

/**
 * 导出文件相关操作。
 * @version v103
 * <AUTHOR>
 */
public interface ExportService {
	/**
	 * 插入导出记录
	 */
	public ExportInfoDto record(Long userId, String exportType, String exportName);
	
	/**
	 * 更新导出记录
	 */
	public Integer updateRecord(ExportInfoDto infoDto);
	
	/**
	 * 上传文件。
	 */
	public Map<?, ?> uploadFile(Long userId, File inputFile) throws IOException;
	
}
