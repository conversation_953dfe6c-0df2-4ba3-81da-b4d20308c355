package com.cmiot.report.service.impl;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cmiot.e.apiservice.facade.ApiServiceFeignClient;
import com.cmiot.e.apiservice.facade.vo.DeviceClassifyResponse;
import com.cmiot.e.apiservice.facade.vo.DeviceInfoVo;
import com.cmiot.e.apiservice.facade.vo.DeviceListRequest;
import com.cmiot.e.apiservice.facade.vo.DeviceSearchRequest;
import com.cmiot.fgeo.common.error.FGEOException;
import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.report.bean.GatewayPeriodAll;
import com.cmiot.report.dto.*;
import com.cmiot.report.enums.GroupTypeEnum;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.facade.dto.gateway.GatewaySubDeviceInfoDto;
import com.cmiot.report.mapper.GatewayMapper;
import com.cmiot.report.mapper.GatewayPeriodAllMapper;
import com.cmiot.report.mapper.NetworkDeviceAllMapper;
import com.cmiot.report.otherserverfeign.ApiServerDeviceFeignClient;
import com.cmiot.report.service.GatewayService;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.MathUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
public class GatewayServiceImpl implements GatewayService {

    @Autowired
    GatewayMapper gatewayMapper;

    @Autowired
    NetworkDeviceAllMapper networkDeviceAllMapper;

    @Autowired
    private ApiServiceFeignClient apiServiceFeignClient;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ApiServerDeviceFeignClient apiServerDeviceFeignClient;

    @Autowired
    private GatewayPeriodAllMapper gatewayPeriodAllMapper;


    // 网关设备超时,单位小时
    @Value("${device.list.mac-overtime}")
    private Integer mac_overtime;

    // 组网设备超时,单位小时
    @Value("${device.list.sub-overtime}")
    private Integer sub_overtime;


    @Value("${device.list.limitNum:500}")
    private Integer limitNum;

    @Value("${period.detail.query.limit.hour:12}")
    private String periodDetailQueryLimitHour;


    @Override
    public GatewayDetailResult getDetail(GatewayQuery gatewayQuery) {
        //List<GatewayDetailResult> gatewayDetailDtoList = gatewayMapper.getGatewayBaseDetail(gatewayQuery);
        return null;
    }

    @Override
    public GatewayDetailResult getGatewayRunDataDetail(GatewayQuery gatewayQuery) {
        List<GatewayDetailResult> getGatewayRunDataDetails = new ArrayList<>();
        GatewayDetailResult gatewayDetailResult = new GatewayDetailResult();
        try {
            getGatewayRunDataDetails = gatewayMapper.getGatewayRunDataDetail(gatewayQuery);
            if (!CollectionUtils.isEmpty(getGatewayRunDataDetails)) {
                gatewayDetailResult = getGatewayRunDataDetails.get(0);
            }
        } catch (Exception e) {
            log.error("网关详情运行数据getGatewayRunDataDetail异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        return gatewayDetailResult;

    }

    @Override
    public List<GatewayVersionResult> getGatewayVersionStatistics(GatewayQuery gatewayQuery) {
        List<GatewayVersionResult> result = new ArrayList<>();
        try {
            result = gatewayMapper.getGatewayVersionStatistics(gatewayQuery);
        } catch (Exception e) {
            log.error("网关版本统计getGatewayVersionStatistics异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        return result;
    }

    @Override
    public Map getGatewayFlowListByPage(GatewayQuery gatewayQuery) {
        Page<GatewayFlowListResult> gatewayListResultPage = new Page<>();
        try {
            gatewayListResultPage = PageHelper.startPage(gatewayQuery.getPage(),
                            gatewayQuery.getPageSize())
                    .doSelectPage(() -> gatewayMapper.getGatewayFlowList(gatewayQuery));
        } catch (Exception e) {
            log.error("网关流量分析列表分页查询getGatewayFlowListByPage异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        Map result = new HashMap();
        result.put("list", gatewayListResultPage.getResult());
        result.put("page", gatewayListResultPage.getPageNum());
        result.put("pageSize", gatewayListResultPage.getPageSize());
        result.put("total", gatewayListResultPage.getTotal());
        return result;
    }

    @Override
    public List<GatewayFlowListResult> getGatewayFlowList(GatewayQuery gatewayQuery) {
        List<GatewayFlowListResult> gatewayFlowListResults = new ArrayList<>();
        try {
            gatewayFlowListResults = gatewayMapper.getGatewayFlowList(gatewayQuery);
        } catch (Exception e) {
            log.error("网关流量分析getGatewayFlowList异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        return gatewayFlowListResults;
    }

    @Override
    public GatewayFlowTrendResult getGatewayFlowOverview(GatewayQuery gatewayQuery) {
        GatewayFlowTrendResult gatewayFlowTrendResult = null;
        try {
            List<GatewayFlowTrendResult> queryResult = gatewayMapper.getGatewayFlowOverview(gatewayQuery);
            gatewayFlowTrendResult = queryResult.stream().findFirst().orElse(new GatewayFlowTrendResult(null));
            if (gatewayFlowTrendResult.getAverRxrate().equals(Double.NaN)) {
                gatewayFlowTrendResult.setAverRxrate(0d);
            }
            if (gatewayFlowTrendResult.getAverTxrate().equals(Double.NaN)) {
                gatewayFlowTrendResult.setAverTxrate(0d);
            }
        } catch (Exception e) {
            log.error("网关流量概览getGatewayFlowOverview异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        return gatewayFlowTrendResult;
    }

    @Override
    public List<GatewayFlowTrendResult> getGatewayFlowTrend(GatewayQuery gatewayQuery) {
        List<GatewayFlowTrendResult> gatewayFlowTrendResults = new ArrayList<>();
        try {
            List<String> days = DateUtil.getDaysByDuring(gatewayQuery.getStartTime(), gatewayQuery.getEndTime());
            List<GatewayFlowTrendResult> queryResult = gatewayMapper.getGatewayFlowTrend(gatewayQuery);
            for (String day : days) {
                GatewayFlowTrendResult item = queryResult.stream().filter(i -> i.getRecordTime().equals(day)).findFirst().orElse(new GatewayFlowTrendResult(day));
                gatewayFlowTrendResults.add(item);
            }
        } catch (Exception e) {
            log.error("网关流量趋势图getGatewayFlowTrend异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        return gatewayFlowTrendResults;
    }

    @Override
    public GatewayCpuAndRamResult getGatewayCpuAndRamOverview(GatewayQuery gatewayQuery) {
        GatewayCpuAndRamResult result = null;
        try {
            List<GatewayCpuAndRamResult> gatewayCpuAndRamResults = gatewayMapper.getGatewayCpuAndRamOverview(gatewayQuery);
            result = gatewayCpuAndRamResults.stream().findFirst().orElse(new GatewayCpuAndRamResult(null));
            if (result.getRamAvg().equals(Double.NaN)) {
                result.setRamAvg(0d);
            }
            if (result.getCpuAvg().equals(Double.NaN)) {
                result.setCpuAvg(0d);
            }
        } catch (Exception e) {
            log.error("网关cpu、内存概览getGatewayCpuAndRamOverview异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        return result;
    }

    @Override
    public Map getGatewayCpuAndRamStatistics(GatewayQuery gatewayQuery) {
        List<GatewayCpuAndRamResult> gatewayCpuAndRamResults = new ArrayList<>();
        List<String> xAxis = new ArrayList<>();
        List<Double> cpuAvg = new ArrayList<>();
        List<Double> ramAvg = new ArrayList<>();
        try {
            gatewayCpuAndRamResults = gatewayMapper.getGatewayCpuAndRamStatistics(gatewayQuery);
            if (GroupTypeEnum.province.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayCpuAndRamResults.stream().map(i -> i.getProvince()).collect(Collectors.toList());
            } else if (GroupTypeEnum.city.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayCpuAndRamResults.stream().map(i -> i.getCity()).collect(Collectors.toList());
            } else if (GroupTypeEnum.vendor.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayCpuAndRamResults.stream().map(i -> i.getGatewayVendor()).collect(Collectors.toList());
            } else if (GroupTypeEnum.model.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayCpuAndRamResults.stream().map(i -> i.getDeviceModel()).collect(Collectors.toList());
            } else if (GroupTypeEnum.day.getTypeValue().equals(gatewayQuery.getGroupType())) {
                List<String> days = DateUtil.getDaysByDuring(gatewayQuery.getStartTime(), gatewayQuery.getEndTime());
                List<GatewayCpuAndRamResult> tmp = new ArrayList<>();
                for (String day : days) {
                    GatewayCpuAndRamResult item = gatewayCpuAndRamResults.stream().filter(i -> i.getRecordTime().equals(day)).findFirst().orElse(new GatewayCpuAndRamResult(day));
                    tmp.add(item);
                }
                gatewayCpuAndRamResults = tmp;
                xAxis = gatewayCpuAndRamResults.stream().map(i -> i.getRecordTime()).collect(Collectors.toList());
            }
            cpuAvg = gatewayCpuAndRamResults.stream().map(i -> i.getCpuAvg()).collect(Collectors.toList());
            ramAvg = gatewayCpuAndRamResults.stream().map(i -> i.getRamAvg()).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("网关cpu、内存分析getGatewayCpuAndRamStatistics异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        Map result = new HashMap();
        result.put("xAxis", xAxis);
        result.put("cpuAverage", cpuAvg);
        result.put("ramAverage", ramAvg);
        return result;
    }

    @Override
    public Map rangeCpuAndRamOverLevelRatio(GatewayQuery gatewayQuery) {
        List<GatewayCpuAndRamResult> gatewayCpuAndRamResults = new ArrayList<>();
        List<String> xAxis = new ArrayList<>();
        List<Double> cpuAvg = new ArrayList<>();
        List<Double> ramAvg = new ArrayList<>();
        try {
            gatewayCpuAndRamResults = gatewayMapper.rangeCpuAndRamOverLevelRatio(gatewayQuery);
            if (GroupTypeEnum.province.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayCpuAndRamResults.stream().map(i -> i.getProvince()).collect(Collectors.toList());
            } else if (GroupTypeEnum.city.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayCpuAndRamResults.stream().map(i -> i.getCity()).collect(Collectors.toList());
            } else if (GroupTypeEnum.vendor.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayCpuAndRamResults.stream().map(i -> i.getGatewayVendor()).collect(Collectors.toList());
            } else if (GroupTypeEnum.model.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayCpuAndRamResults.stream().map(i -> i.getDeviceModel()).collect(Collectors.toList());
            } else if (GroupTypeEnum.day.getTypeValue().equals(gatewayQuery.getGroupType())) {
                List<String> days = DateUtil.getDaysByDuring(gatewayQuery.getStartTime(), gatewayQuery.getEndTime());
                List<GatewayCpuAndRamResult> tmp = new ArrayList<>();
                for (String day : days) {
                    GatewayCpuAndRamResult item = gatewayCpuAndRamResults.stream().filter(i -> i.getRecordTime().equals(day)).findFirst().orElse(new GatewayCpuAndRamResult(day));
                    tmp.add(item);
                }
                gatewayCpuAndRamResults = tmp;
                xAxis = gatewayCpuAndRamResults.stream().map(i -> i.getRecordTime()).collect(Collectors.toList());
            }
            cpuAvg = gatewayCpuAndRamResults.stream().map(i -> i.getCpuAvg()).collect(Collectors.toList());
            ramAvg = gatewayCpuAndRamResults.stream().map(i -> i.getRamAvg()).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("网关cpu、内存分析rangeCpuAndRamOverLevelRatio异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        Map result = new HashMap();
        result.put("xAxis", xAxis);
        result.put("cpuAverage", cpuAvg);
        result.put("ramAverage", ramAvg);
        return result;
    }

    @Override
    public List<GatewayCpuAndRamPieDataDto> getGatewayCpuGradStatistics(GatewayQuery gatewayQuery) {
        List<GatewayCpuOrRamRateResult> cpuRates = new ArrayList<>();
        List<GatewayCpuAndRamPieDataDto> gatewayCpuAndRamPieDataDtos = new ArrayList<>();
        try {
            cpuRates = gatewayMapper.getGatewayCpuGradStatistics(gatewayQuery);
            if (!CollectionUtils.isEmpty(cpuRates)) {
                Integer total = cpuRates.get(0).total();
                GatewayCpuAndRamPieDataDto itemv1 = new GatewayCpuAndRamPieDataDto();
                itemv1.setRange("0~10%");
                itemv1.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv1() * 100 / Double.valueOf(total))) + "%");
                itemv1.setNum(cpuRates.get(0).getLv1());
                gatewayCpuAndRamPieDataDtos.add(itemv1);
                GatewayCpuAndRamPieDataDto itemV2 = new GatewayCpuAndRamPieDataDto();
                itemV2.setRange("10%~20%");
                itemV2.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv2() * 100 / Double.valueOf(total))) + "%");
                itemV2.setNum(cpuRates.get(0).getLv2());
                gatewayCpuAndRamPieDataDtos.add(itemV2);
                GatewayCpuAndRamPieDataDto itemV3 = new GatewayCpuAndRamPieDataDto();
                itemV3.setRange("20%~30%");
                itemV3.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv3() * 100 / Double.valueOf(total))) + "%");
                itemV3.setNum(cpuRates.get(0).getLv3());
                gatewayCpuAndRamPieDataDtos.add(itemV3);
                GatewayCpuAndRamPieDataDto itemV4 = new GatewayCpuAndRamPieDataDto();
                itemV4.setRange("30%~40%");
                itemV4.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv4() * 100 / Double.valueOf(total))) + "%");
                itemV4.setNum(cpuRates.get(0).getLv4());
                gatewayCpuAndRamPieDataDtos.add(itemV4);
                GatewayCpuAndRamPieDataDto itemV5 = new GatewayCpuAndRamPieDataDto();
                itemV5.setRange("40%~50%");
                itemV5.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv5() * 100 / Double.valueOf(total))) + "%");
                itemV5.setNum(cpuRates.get(0).getLv5());
                gatewayCpuAndRamPieDataDtos.add(itemV5);
                GatewayCpuAndRamPieDataDto itemV6 = new GatewayCpuAndRamPieDataDto();
                itemV6.setRange("50%~60%");
                itemV6.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv6() * 100 / Double.valueOf(total))) + "%");
                itemV6.setNum(cpuRates.get(0).getLv6());
                gatewayCpuAndRamPieDataDtos.add(itemV6);
                GatewayCpuAndRamPieDataDto itemV7 = new GatewayCpuAndRamPieDataDto();
                itemV7.setRange("60%~70%");
                itemV7.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv7() * 100 / Double.valueOf(total))) + "%");
                itemV7.setNum(cpuRates.get(0).getLv7());
                gatewayCpuAndRamPieDataDtos.add(itemV7);
                GatewayCpuAndRamPieDataDto itemV8 = new GatewayCpuAndRamPieDataDto();
                itemV8.setRange("70%~80%");
                itemV8.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv8() * 100 / Double.valueOf(total))) + "%");
                itemV8.setNum(cpuRates.get(0).getLv8());
                gatewayCpuAndRamPieDataDtos.add(itemV8);
                GatewayCpuAndRamPieDataDto itemV9 = new GatewayCpuAndRamPieDataDto();
                itemV9.setRange("80%~90%");
                itemV9.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv9() * 100 / Double.valueOf(total))) + "%");
                itemV9.setNum(cpuRates.get(0).getLv9());
                gatewayCpuAndRamPieDataDtos.add(itemV9);
                GatewayCpuAndRamPieDataDto itemV10 = new GatewayCpuAndRamPieDataDto();
                itemV10.setRange("90%~100%");
                itemV10.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv10() * 100 / Double.valueOf(total))) + "%");
                itemV10.setNum(cpuRates.get(0).getLv10());
                gatewayCpuAndRamPieDataDtos.add(itemV10);
                GatewayCpuAndRamPieDataDto itemV0 = new GatewayCpuAndRamPieDataDto();
                itemV0.setRange("其他");
                itemV0.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(cpuRates.get(0).getLv0() * 100 / Double.valueOf(total))) + "%");
                itemV0.setNum(cpuRates.get(0).getLv0());
                gatewayCpuAndRamPieDataDtos.add(itemV0);
            }
        } catch (Exception e) {
            log.error("网关cpu占比统计分析getGatewayCpuGradStatistics异常: ", e);
            throw new FGEOException.InternalServerError();
        }


        return gatewayCpuAndRamPieDataDtos;
    }

    @Override
    public List<GatewayCpuAndRamPieDataDto> getGatewayRamGradStatistics(GatewayQuery gatewayQuery) {
        List<GatewayCpuOrRamRateResult> ramRates = new ArrayList<>();
        List<GatewayCpuAndRamPieDataDto> gatewayCpuAndRamPieDataDtos = new ArrayList<>();
        try {
            ramRates = gatewayMapper.getGatewayRamGradStatistics(gatewayQuery);
            if (!CollectionUtils.isEmpty(ramRates)) {
                Integer total = ramRates.get(0).total();
                GatewayCpuAndRamPieDataDto itemv1 = new GatewayCpuAndRamPieDataDto();
                itemv1.setRange("0~10%");
                itemv1.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv1() * 100 / Double.valueOf(total))) + "%");
                itemv1.setNum(ramRates.get(0).getLv1());
                gatewayCpuAndRamPieDataDtos.add(itemv1);
                GatewayCpuAndRamPieDataDto itemV2 = new GatewayCpuAndRamPieDataDto();
                itemV2.setRange("10%~20%");
                itemV2.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv2() * 100 / Double.valueOf(total))) + "%");
                itemV2.setNum(ramRates.get(0).getLv2());
                gatewayCpuAndRamPieDataDtos.add(itemV2);
                GatewayCpuAndRamPieDataDto itemV3 = new GatewayCpuAndRamPieDataDto();
                itemV3.setRange("20%~30%");
                itemV3.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv3() * 100 / Double.valueOf(total))) + "%");
                itemV3.setNum(ramRates.get(0).getLv3());
                gatewayCpuAndRamPieDataDtos.add(itemV3);
                GatewayCpuAndRamPieDataDto itemV4 = new GatewayCpuAndRamPieDataDto();
                itemV4.setRange("30%~40%");
                itemV4.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv4() * 100 / Double.valueOf(total))) + "%");
                itemV4.setNum(ramRates.get(0).getLv4());
                gatewayCpuAndRamPieDataDtos.add(itemV4);
                GatewayCpuAndRamPieDataDto itemV5 = new GatewayCpuAndRamPieDataDto();
                itemV5.setRange("40%~50%");
                itemV5.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv5() * 100 / Double.valueOf(total))) + "%");
                itemV5.setNum(ramRates.get(0).getLv5());
                gatewayCpuAndRamPieDataDtos.add(itemV5);
                GatewayCpuAndRamPieDataDto itemV6 = new GatewayCpuAndRamPieDataDto();
                itemV6.setRange("50%~60%");
                itemV6.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv6() * 100 / Double.valueOf(total))) + "%");
                itemV6.setNum(ramRates.get(0).getLv6());
                gatewayCpuAndRamPieDataDtos.add(itemV6);
                GatewayCpuAndRamPieDataDto itemV7 = new GatewayCpuAndRamPieDataDto();
                itemV7.setRange("60%~70%");
                itemV7.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv7() * 100 / Double.valueOf(total))) + "%");
                itemV7.setNum(ramRates.get(0).getLv7());
                gatewayCpuAndRamPieDataDtos.add(itemV7);
                GatewayCpuAndRamPieDataDto itemV8 = new GatewayCpuAndRamPieDataDto();
                itemV8.setRange("70%~80%");
                itemV8.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv8() * 100 / Double.valueOf(total))) + "%");
                itemV8.setNum(ramRates.get(0).getLv8());
                gatewayCpuAndRamPieDataDtos.add(itemV8);
                GatewayCpuAndRamPieDataDto itemV9 = new GatewayCpuAndRamPieDataDto();
                itemV9.setRange("80%~90%");
                itemV9.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv9() * 100 / Double.valueOf(total))) + "%");
                itemV9.setNum(ramRates.get(0).getLv9());
                gatewayCpuAndRamPieDataDtos.add(itemV9);
                GatewayCpuAndRamPieDataDto itemV10 = new GatewayCpuAndRamPieDataDto();
                itemV10.setRange("90%~100%");
                itemV10.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv10() * 100 / Double.valueOf(total))) + "%");
                itemV10.setNum(ramRates.get(0).getLv10());
                gatewayCpuAndRamPieDataDtos.add(itemV10);
                GatewayCpuAndRamPieDataDto itemV0 = new GatewayCpuAndRamPieDataDto();
                itemV0.setRange("其他");
                itemV0.setRatio((total == 0 ? 0 : MathUtils.get2NumDot(ramRates.get(0).getLv0() * 100 / Double.valueOf(total))) + "%");
                itemV0.setNum(ramRates.get(0).getLv0());
                gatewayCpuAndRamPieDataDtos.add(itemV0);
            }
        } catch (Exception e) {
            log.error("网关内存占比统计分析getGatewayRamGradStatistics异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        return gatewayCpuAndRamPieDataDtos;
    }

//    @Override
//    public List<GatewayCpuAndRamMaxTimeResult> getGatewayMaxCpuRamTime(GatewayQuery gatewayQuery) {
//
//        return null;
//    }

    @Override
    public List<GatewayCurrentResult> getGatewayLatestFlow(GatewayQuery gatewayQuery) {
        return null;
    }

    @Override
    public Map getGatewayCpuRamListByPage(GatewayQuery gatewayQuery) {
        Page<GatewayCpuRamListResult> gatewayListResultPage = new Page<>();
        Map result = new HashMap();
        try {
            gatewayListResultPage = PageHelper.startPage(gatewayQuery.getPage(),
                            gatewayQuery.getPageSize())
                    .doSelectPage(() -> gatewayMapper.getGatewayCpuRamList(gatewayQuery));
            result.put("list", gatewayListResultPage.getResult());
            result.put("page", gatewayListResultPage.getPageNum());
            result.put("pageSize", gatewayListResultPage.getPageSize());
            result.put("total", gatewayListResultPage.getTotal());
        } catch (Exception e) {
            log.error("网关cpu内存统计分析列表分页查询getGatewayCpuRamListByPage异常: ", e);
            throw new FGEOException.InternalServerError();
        }


        return result;
    }

    @Override
    public List<GatewayCpuRamListResult> getGatewayCpuRamList(GatewayQuery gatewayQuery) {
        List<GatewayCpuRamListResult> result = new ArrayList<>();
        try {
            result = gatewayMapper.getGatewayCpuRamList(gatewayQuery);
        } catch (Exception e) {
            log.error("网关cpu内存统计分析列表getGatewayCpuRamList异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        return result;
    }

    @Override
    public GatewayRunTimeResult getGatewayRunTimeData(GatewayQuery gatewayQuery) {
        GatewayRunTimeResult gatewayRunTimeResult = new GatewayRunTimeResult();
        try {
            gatewayQuery.setOverTime(mac_overtime);
            List<Integer> queryResult = gatewayMapper.getGatewayRunTime(gatewayQuery);
            if (!CollectionUtils.isEmpty(queryResult)) {
                // 运行的网关数
                gatewayRunTimeResult.setGatewayNumberRunning(queryResult.size());
                // 平均运行时长,单位小时
                gatewayRunTimeResult.setRunDurationAverage(new Double(queryResult.stream().mapToInt(i -> i).average().getAsDouble()).intValue());
                // 网关超长阈值,单位小时
                //gatewayRunTimeResult.setRunDuration(queryResult.stream().reduce(Integer::sum).orElse(0));
                gatewayRunTimeResult.setRunDuration(mac_overtime);
                // 组网设备超长阈值,单位小时
                gatewayRunTimeResult.setNetRunDuration(sub_overtime);

                // 网关时间超长数量
                long macOverCount = gatewayMapper.getGatewayRunTimeOverCount(gatewayQuery);
                gatewayRunTimeResult.setMacOverCount(macOverCount);

                // 组网设备时间超长数量
                gatewayQuery.setOverTime(sub_overtime);
                long subOverCount = networkDeviceAllMapper.networkDeviceCount(gatewayQuery);
                gatewayRunTimeResult.setSubOverCount(subOverCount);

            } else {
                gatewayRunTimeResult.setGatewayNumberRunning(0);
                gatewayRunTimeResult.setRunDurationAverage(0);
                gatewayRunTimeResult.setRunDuration(0);
                gatewayRunTimeResult.setNetRunDuration(0);
            }
        } catch (Exception e) {
            log.error("网关运行报告getGatewayRunTimeData异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        return gatewayRunTimeResult;
    }

    @Override
    public List<GatewayRunTimeDetail> getGatewayRunTimeList(GatewayQuery gatewayQuery) {
        log.info("网关运行时长超时getGatewayRunTimeList列表查询接口入参: {}", JSON.toJSONString(gatewayQuery));
        List<GatewayRunTimeDetail> gatewayRunTimeDetails = new ArrayList<>();
        try {
            gatewayQuery.setOverTime(mac_overtime);
            gatewayRunTimeDetails = gatewayMapper.getGatewayRunTimeOver(gatewayQuery);
        } catch (Exception e) {
            log.error("网关运行时长超时getGatewayRunTimeList异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        log.info("网关运行时长超时getGatewayRunTimeList列表查询接口出参= {}", JSON.toJSONString(gatewayRunTimeDetails));
        return gatewayRunTimeDetails;
    }

    @Override
    public List<GatewayWanBandwidthDetail> getGatewayWanBandwidthList(GatewayQuery gatewayQuery) {
        log.info("网关速率报告getGatewayWanBandwidthList列表查询接口入参: {}", JSON.toJSONString(gatewayQuery));
        List<GatewayWanBandwidthDetail> orderByAscList;
        try {
            orderByAscList = gatewayMapper.getGatewayWanBandwidth(gatewayQuery);
            if (orderByAscList != null && orderByAscList.size() > 1) {
                //如果是查看近一个月的数据，将缺失的数据填充为null
                if (gatewayQuery.getTimeType() == 2) {
                    LocalDate before31Day = LocalDate.now().minusDays(31);
                    LocalDateTime before31DayTime = LocalDateTime.of(before31Day, LocalTime.of(0, 0, 0));
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    //循环生成一个时间，去orderByAscList中查找，如果没有就填充，如果有就跳过
                    List<GatewayWanBandwidthDetail> paddingList = new ArrayList<>();
                    while (before31DayTime.isBefore(LocalDateTime.now())) {
                        String recordTime = formatter.format(before31DayTime);
                        if (orderByAscList.stream().noneMatch(detail -> detail.getRecordTime().equals(recordTime))) {
                            GatewayWanBandwidthDetail gatewayWanBandwidthDetail = new GatewayWanBandwidthDetail();
                            gatewayWanBandwidthDetail.setRecordTime(recordTime);
                            gatewayWanBandwidthDetail.setUpload(null);
                            gatewayWanBandwidthDetail.setDownload(null);
                            paddingList.add(gatewayWanBandwidthDetail);
                        }
                        before31DayTime = before31DayTime.plusMinutes(30);
                    }
                    orderByAscList.addAll(paddingList);
                }
                //按时间从小到达排序
                orderByAscList = orderByAscList.stream().sorted(Comparator.comparing(GatewayWanBandwidthDetail::getRecordTime)).collect(Collectors.toList());
            }
        }catch (Exception e) {
            log.error("网关速率报告getGatewayWanBandwidthList异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        log.info("网关速率报告getGatewayWanBandwidthList列表查询接口出参= {}", JSON.toJSONString(orderByAscList));
        return orderByAscList;
    }

/*    @Override
    public List<Object> getGatewayFlowVelocityListByEnterprise(GatewayQuery gatewayQuery) {
        String enterpriseId = gatewayQuery.getEid();
        DeviceSearchRequest deviceSearchRequest = new DeviceSearchRequest();
        deviceSearchRequest.setEnterpriseId(Long.parseLong(enterpriseId));
        deviceSearchRequest.setDeviceType(1);
        List<DeviceInfoVo> deviceInfoVos = apiServiceFeignClient.deviceESearch(deviceSearchRequest);
        List<String> macList = deviceInfoVos.stream().map(DeviceInfoVo::getMac).collect(Collectors.toList());
        this.redisTemplate.setKeySerializer(new StringRedisSerializer());
        this.redisTemplate.setValueSerializer(new StringRedisSerializer());
        this.redisTemplate.afterPropertiesSet();
        Map<String, Boolean> onlineMap = new HashMap<>();
        for (String mac : macList) {
            String key = "GATEWAY_ONLINE_UDP:"+mac;
            Object o = this.redisTemplate.opsForValue().get(key);
            String value = (String) o;
            if (StringUtils.hasText(value)) {
                onlineMap.put(mac, true);
            } else {
                onlineMap.put(mac, false);
            }
        }
        log.info("gatewayFlowVelocityList-onlineMap==>{}", onlineMap);
        List<Object> flowList = new ArrayList<>();
        for (DeviceInfoVo deviceInfoVo : deviceInfoVos) {
            if (StringUtils.hasText(deviceInfoVo.getMac())) {
                gatewayQuery.setGatewayMac(deviceInfoVo.getMac());
                List<GatewayWanBandwidthDetail> gatewayFlowVelocity = gatewayMapper.getGatewayFlowVelocity(gatewayQuery);
                Map<String, Object> viewMap = null;
                if (!CollectionUtils.isEmpty(gatewayFlowVelocity)) {
                    viewMap = new HashMap<>();
                    List<String> xAxis = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getRecordTime).collect(Collectors.toList());
                    viewMap.put("times", xAxis);
                    List<Double> topFlow = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getUpload).collect(Collectors.toList());
                    viewMap.put("upFlow", topFlow);
                    List<Double> downFlow = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getDownload).collect(Collectors.toList());
                    viewMap.put("downFlow", downFlow);
                }
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("gatewayName", deviceInfoVo.getName());
                resultMap.put("mac", deviceInfoVo.getMac());
                resultMap.put("sn", deviceInfoVo.getSn());
                resultMap.put("online", onlineMap.get(deviceInfoVo.getMac()));
                resultMap.put("flowVelocity", viewMap);
                flowList.add(resultMap);
            }
        }
        //将flowVelocity为空的排到后面
        flowList.sort(Comparator.comparing(o -> {
            Map<String, Object> map = (Map<String, Object>) o;
            Object value = map.get("flowVelocity");
            return value == null ? "1" : "0";
        }));
        return flowList;
    }*/

    @Override
    public List<Object> getGatewayFlowVelocityListByEnterprise(GatewayQuery gatewayQuery) {
        String enterpriseId = gatewayQuery.getEid();
        DeviceSearchRequest deviceSearchRequest = new DeviceSearchRequest();
        deviceSearchRequest.setEnterpriseId(Long.parseLong(enterpriseId));
        deviceSearchRequest.setDeviceType(1);
        List<DeviceInfoVo> deviceInfoVos = apiServiceFeignClient.deviceESearch(deviceSearchRequest);
        List<String> macList = deviceInfoVos.stream().map(DeviceInfoVo::getMac).collect(Collectors.toList());
        try {
            CompletableFuture<Map<String, Boolean>> future1 = CompletableFuture.supplyAsync(() -> {
                this.redisTemplate.setKeySerializer(new StringRedisSerializer());
                this.redisTemplate.setValueSerializer(new StringRedisSerializer());
                this.redisTemplate.afterPropertiesSet();
                Map<String, Boolean> onlineMap = new HashMap<>();
                for (String mac : macList) {
                    String key = "GATEWAY_ONLINE_UDP:"+mac;
                    Object o = this.redisTemplate.opsForValue().get(key);
                    String value = (String) o;
                    if (StringUtils.hasText(value)) {
                        onlineMap.put(mac, true);
                    } else {
                        onlineMap.put(mac, false);
                    }
                }
                return onlineMap;
            });

            CompletableFuture<List<GatewayWanBandwidthDetail>> future2 = CompletableFuture.supplyAsync(() -> {
                gatewayQuery.setGatewayMacList(macList);
                return gatewayMapper.getGatewayFlowVelocity(gatewayQuery);
            });
            Map<String, Boolean> onlineMap = future1.get();
            log.info("gatewayFlowVelocityList-onlineMap==>{}", onlineMap);
            List<GatewayWanBandwidthDetail> gatewayFlowVelocityList = future2.get();
            log.info("不分页gatewayFlowVelocityList==>{}", gatewayFlowVelocityList);

            //对gatewayFlowVelocityList按gatewayMac分组
            Map<String, List<GatewayWanBandwidthDetail>> gatewayFlowVelocityMap = gatewayFlowVelocityList.stream().collect(Collectors.groupingBy(GatewayWanBandwidthDetail::getGatewayMac));
            List<Object> flowList = new ArrayList<>();
            for (DeviceInfoVo deviceInfoVo : deviceInfoVos) {
                if (StringUtils.hasText(deviceInfoVo.getMac())) {
                    List<GatewayWanBandwidthDetail> gatewayFlowVelocity = gatewayFlowVelocityMap.get(deviceInfoVo.getMac());
                    Map<String, Object> viewMap = null;
                    if (!CollectionUtils.isEmpty(gatewayFlowVelocity)) {
                        viewMap = new HashMap<>();
                        List<String> xAxis = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getRecordTime).collect(Collectors.toList());
                        viewMap.put("times", xAxis);
                        List<Double> topFlow = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getUpload).collect(Collectors.toList());
                        viewMap.put("upFlow", topFlow);
                        List<Double> downFlow = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getDownload).collect(Collectors.toList());
                        viewMap.put("downFlow", downFlow);
                    }
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("gatewayName", deviceInfoVo.getName());
                    resultMap.put("mac", deviceInfoVo.getMac());
                    resultMap.put("sn", deviceInfoVo.getSn());
                    resultMap.put("online", onlineMap.get(deviceInfoVo.getMac()));
                    resultMap.put("flowVelocity", viewMap);
                    flowList.add(resultMap);
                }
            }
            //将flowVelocity为空的排到后面
            flowList.sort(Comparator.comparing(o -> {
                Map<String, Object> map = (Map<String, Object>) o;
                Object value = map.get("flowVelocity");
                return value == null ? "1" : "0";
            }));
            return flowList;
        } catch (Exception e) {
            log.error("网关速率报告getGatewayWanBandwidthList异常: {}", e.getMessage(), e);
        }
        return null;
    }

/*    @Override
    public PageRes<Map<String, Object>> getPageGatewayFlowVelocity(GatewayQuery gatewayQuery) {
        EnterpriseDeviceRequest request = new EnterpriseDeviceRequest();
        if (StringUtils.hasText(gatewayQuery.getGatewayMac())) {
            request.setMac(gatewayQuery.getGatewayMac());
        }
        if (StringUtils.hasText(gatewayQuery.getSn())) {
            request.setSn(gatewayQuery.getSn());
        }
        int page = gatewayQuery.getPage() == null ? 1 : gatewayQuery.getPage();
        int pageSize = gatewayQuery.getPageSize() == null ? 10 : gatewayQuery.getPageSize();
        request.setPage(page);
        request.setPageSize(pageSize);
        request.setDeviceType(1);
        log.info("getPageGatewayFlowVelocity查询网关流速条件:{}", JSONUtil.toJSONString(request));
        EnterpriseDeviceResponse enterpriseDeviceResponse = apiServerDeviceFeignClient.enterpriseDeviceList(request, String.valueOf(gatewayQuery.getUid()), Long.parseLong(gatewayQuery.getEid()));
        log.info("enterpriseDeviceResponse==>:{}", JSONUtil.toJSONString(enterpriseDeviceResponse));
        PageRes<Map<String, Object>> result = new PageRes<>();
        if (enterpriseDeviceResponse != null && !CollectionUtils.isEmpty(enterpriseDeviceResponse.getList())) {
            List<EnterpriseDeviceVo> deviceVoList = enterpriseDeviceResponse.getList();
            log.info("deviceVoList==>:{}", JSONUtil.toJSONString(deviceVoList));
            List<String> deviceIdList = deviceVoList.stream().map(EnterpriseDeviceVo::getId).collect(Collectors.toList());
            DeviceListRequest var1 = new DeviceListRequest();
            var1.setUserId(String.valueOf(gatewayQuery.getUid()));
            var1.setIds(deviceIdList);
            log.info("var1==>:{}", JSONUtil.toJSONString(var1));
            DeviceClassifyResponse deviceListEInfo = apiServiceFeignClient.getDeviceListEInfo(var1);
            log.info("deviceListEInfo==>:{}", JSONUtil.toJSONString(deviceListEInfo));

            String startTime = gatewayQuery.getStartTime();
            String endTime = gatewayQuery.getEndTime();
            if (!StringUtils.hasText(startTime) || !StringUtils.hasText(endTime)) {
                //默认查询近6个自然月的数据
                LocalDate now = LocalDate.now();
                startTime = now.minusMonths(5).with(TemporalAdjusters.firstDayOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                gatewayQuery.setStartTime(startTime);
                gatewayQuery.setEndTime(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }

            //并行计算每天的平均值
            List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>(deviceVoList.size());
            for (EnterpriseDeviceVo deviceInfoVo : deviceVoList) {
                if (StringUtils.hasText(deviceInfoVo.getMac())) {
                    try {
                        CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                            GatewayQuery query = new GatewayQuery();
                            BeanUtils.copyProperties(gatewayQuery, query);
                            query.setGatewayMac(deviceInfoVo.getMac());
                            List<GatewayWanBandwidthDetail> gatewayFlowVelocity = gatewayMapper.getGatewayFlowVelocityByParam(query);
                            Map<String, Object> viewMap = null;
                            if (!CollectionUtils.isEmpty(gatewayFlowVelocity)) {
                                viewMap = new HashMap<>();
                                List<String> xAxis = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getRecordTime).map(time -> time.substring(0, 16)).collect(Collectors.toList());
                                viewMap.put("times", xAxis);
                                List<Double> topFlow = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getUpload).collect(Collectors.toList());
                                viewMap.put("upFlow", topFlow);
                                List<Double> downFlow = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getDownload).collect(Collectors.toList());
                                viewMap.put("downFlow", downFlow);
                            }

                            Map<String, Object> resultMap = new HashMap<>();
                            if (deviceListEInfo == null || deviceListEInfo.getDevices() == null || deviceListEInfo.getDevices().isEmpty()) {
                                resultMap.put("imgUrl", "");
                                resultMap.put("gatewayName", "");
                            } else {
                                DeviceInfoVo deviceInfoVo1 = deviceListEInfo.getDevices().stream().filter(o -> o.getId().equals(deviceInfoVo.getId())).findFirst().orElse(null);
                                resultMap.put("imgUrl", deviceInfoVo1 == null ? "" : deviceInfoVo1.getIconUrl());
                                resultMap.put("gatewayName", deviceInfoVo1 == null ? "" : deviceInfoVo1.getName());
                            }
                            resultMap.put("mac", deviceInfoVo.getMac());
                            resultMap.put("sn", deviceInfoVo.getSn());
                            resultMap.put("model", deviceInfoVo.getDeviceModel());
                            resultMap.put("online", deviceInfoVo.getIsOnline());
                            resultMap.put("flowVelocity", viewMap);
                            return resultMap;
                        });
                        futures.add(future);
                    } catch (Exception e) {
                        log.error("网关分页流速查询异常: {}", e.getMessage(), e);
                    }
                }
            }
            // 使用allOf方法来表示所有的并行任务
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            // 获得所有子任务的处理结果
            CompletableFuture<List<Map<String, Object>>> finalResults = allFutures.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
            List<Map<String, Object>> joinList = finalResults.join();
            log.info("并行查询计算结果: {}", joinList);
            //将flowVelocity为空的排到后面
            joinList.sort(Comparator.comparing(o -> o.get("flowVelocity") == null ? "1" : "0"));

            result.setList(joinList);
            result.setPage(page);
            result.setPageSize(pageSize);
            result.setTotal((long)enterpriseDeviceResponse.getTotal());
        } else {
            result.setList(new ArrayList<>());
            result.setPage(page);
            result.setPageSize(pageSize);
            result.setTotal(0L);
        }
        log.info("getPageGatewayFlowVelocity查询网关流速结果:{}", result);
        return result;
    }*/


    @Override
    public PageRes<Map<String, Object>> getPageGatewayFlowVelocity(GatewayQuery gatewayQuery) {
        EnterpriseDeviceRequest request1 = new EnterpriseDeviceRequest();
        if (StringUtils.hasText(gatewayQuery.getGatewayMac())) {
            request1.setMac(gatewayQuery.getGatewayMac());
        }
        if (StringUtils.hasText(gatewayQuery.getSn())) {
            request1.setSn(gatewayQuery.getSn());
        }
        int page = gatewayQuery.getPage() == null ? 1 : gatewayQuery.getPage();
        int pageSize = gatewayQuery.getPageSize() == null ? 10 : gatewayQuery.getPageSize();
        request1.setPage(page);
        request1.setPageSize(pageSize);
        request1.setDeviceType(1);
        log.info("getPageGatewayFlowVelocity查询网关流速条件:{}", JSONUtil.toJSONString(request1));
        EnterpriseDeviceResponse enterpriseDeviceResponse = apiServerDeviceFeignClient.enterpriseDeviceList(request1, String.valueOf(gatewayQuery.getUid()), Long.parseLong(gatewayQuery.getEid()));
        log.info("enterpriseDeviceResponse==>:{}", JSONUtil.toJSONString(enterpriseDeviceResponse));
        PageRes<Map<String, Object>> result = new PageRes<>();
        if (enterpriseDeviceResponse != null && !CollectionUtils.isEmpty(enterpriseDeviceResponse.getList())) {
            List<EnterpriseDeviceVo> deviceVoList = enterpriseDeviceResponse.getList();
            log.info("deviceVoList==>:{}", JSONUtil.toJSONString(deviceVoList));
            try {
                CompletableFuture<DeviceClassifyResponse> future1 = CompletableFuture.supplyAsync(() -> {
                    List<String> deviceIdList = deviceVoList.stream().map(EnterpriseDeviceVo::getId).collect(Collectors.toList());
                    DeviceListRequest request2 = new DeviceListRequest();
                    request2.setUserId(String.valueOf(gatewayQuery.getUid()));
                    request2.setIds(deviceIdList);
                    log.info("request2==>:{}", JSONUtil.toJSONString(request2));
                    return apiServiceFeignClient.getDeviceListEInfo(request2);
                });

                CompletableFuture<List<GatewayWanBandwidthDetail>> future2 = CompletableFuture.supplyAsync(() -> {
                    String startTime = gatewayQuery.getStartTime();
                    String endTime = gatewayQuery.getEndTime();
                    if (!StringUtils.hasText(startTime) || !StringUtils.hasText(endTime)) {
                        //默认查询近6个自然月的数据
                        LocalDate now = LocalDate.now();
                        startTime = now.minusMonths(5).with(TemporalAdjusters.firstDayOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        gatewayQuery.setStartTime(startTime);
                        gatewayQuery.setEndTime(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                    gatewayQuery.setStartTime(gatewayQuery.getStartTime() + " 00:00:00");
                    gatewayQuery.setEndTime(gatewayQuery.getEndTime() + " 23:59:59");
                    List<String> macList = deviceVoList.stream().map(EnterpriseDeviceVo::getMac).collect(Collectors.toList());
                    gatewayQuery.setGatewayMacList(macList);
                    log.info("gatewayQuery==>:{}", JSONUtil.toJSONString(gatewayQuery));
                    return gatewayMapper.getGatewayFlowVelocityByParam(gatewayQuery);
                });
                DeviceClassifyResponse deviceListEInfo = future1.get();
                log.info("deviceListEInfo==>:{}", JSONUtil.toJSONString(deviceListEInfo));
                List<GatewayWanBandwidthDetail> gatewayFlowVelocityList = future2.get();
                log.info("分页gatewayFlowVelocityList==>:{}", JSONUtil.toJSONString(gatewayFlowVelocityList));

                //对gatewayFlowVelocityList按gatewayMac分组
                Map<String, List<GatewayWanBandwidthDetail>> gatewayFlowVelocityMap = gatewayFlowVelocityList.stream().collect(Collectors.groupingBy(GatewayWanBandwidthDetail::getGatewayMac));
                List<Map<String, Object>> resultList = new ArrayList<>();
                for (EnterpriseDeviceVo deviceInfoVo : deviceVoList) {
                    if (StringUtils.hasText(deviceInfoVo.getMac())) {
                        List<GatewayWanBandwidthDetail> gatewayFlowVelocity = gatewayFlowVelocityMap.get(deviceInfoVo.getMac());
                        Map<String, Object> viewMap = null;
                        if (!CollectionUtils.isEmpty(gatewayFlowVelocity)) {
                            viewMap = new HashMap<>();
                            List<String> xAxis = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getRecordTime).map(time -> time.substring(0, 16)).collect(Collectors.toList());
                            viewMap.put("times", xAxis);
                            List<Double> topFlow = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getUpload).collect(Collectors.toList());
                            viewMap.put("upFlow", topFlow);
                            List<Double> downFlow = gatewayFlowVelocity.stream().map(GatewayWanBandwidthDetail::getDownload).collect(Collectors.toList());
                            viewMap.put("downFlow", downFlow);
                        }

                        Map<String, Object> resultMap = new HashMap<>();
                        if (deviceListEInfo == null || deviceListEInfo.getDevices() == null || deviceListEInfo.getDevices().isEmpty()) {
                            resultMap.put("imgUrl", "");
                            resultMap.put("gatewayName", "");
                        } else {
                            DeviceInfoVo deviceInfoVo1 = deviceListEInfo.getDevices().stream().filter(o -> o.getId().equals(deviceInfoVo.getId())).findFirst().orElse(null);
                            resultMap.put("imgUrl", deviceInfoVo1 == null ? "" : deviceInfoVo1.getIconUrl());
                            resultMap.put("gatewayName", deviceInfoVo1 == null ? "" : deviceInfoVo1.getName());
                        }
                        resultMap.put("mac", deviceInfoVo.getMac());
                        resultMap.put("sn", deviceInfoVo.getSn());
                        resultMap.put("model", deviceInfoVo.getDeviceModel());
                        resultMap.put("online", deviceInfoVo.getIsOnline());
                        resultMap.put("flowVelocity", viewMap);
                        resultList.add(resultMap);
                    }
                }
                //将flowVelocity为空的排到后面
                resultList.sort(Comparator.comparing(o -> o.get("flowVelocity") == null ? "1" : "0"));

                result.setList(resultList);
                result.setPage(page);
                result.setPageSize(pageSize);
                result.setTotal((long) enterpriseDeviceResponse.getTotal());
                log.info("getPageGatewayFlowVelocity查询网关流速结果:{}", result);
            } catch (Exception e) {
                log.error("网关分页流速查询异常: {}", e.getMessage(), e);
            }
        } else {
            result.setList(new ArrayList<>());
            result.setPage(page);
            result.setPageSize(pageSize);
            result.setTotal(0L);
        }
        return result;
    }

    @Override
    public RealtimeFlowVelocityMessageVO getPollingRealtimeFlowVelocityMonitorReport(GatewayQuery gatewayQuery) {
        this.redisTemplate.setKeySerializer(new StringRedisSerializer());
        this.redisTemplate.setValueSerializer(new StringRedisSerializer());
        this.redisTemplate.afterPropertiesSet();
        String Heartbeatkey = "device-manager:realtimeFlowVelocityMonitorReport:" + gatewayQuery.getGatewayMac();
        //每次心跳都刷新任务存活标记，从头开始计时
        Boolean hasKey = this.redisTemplate.opsForValue().getOperations().hasKey(Heartbeatkey);
        if (hasKey) {//对于开启失败不接受心跳
            this.redisTemplate.opsForValue().set(Heartbeatkey, "open", 20, TimeUnit.SECONDS);
        }
        String date = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now());
        String key = "ekit-portal:realtimeFlowVelocityMonitorReport:" + date + ":" + gatewayQuery.getEid() + ":" + gatewayQuery.getGatewayMac();
        String value = String.valueOf(this.redisTemplate.opsForValue().get(key));
        log.info("getPollingRealtimeFlowVelocityMonitorReport从redis读取到数据: {}", value);
        RealtimeFlowVelocityMessageVO realtimeFlowVelocityMessageVO = new RealtimeFlowVelocityMessageVO();
        if (StringUtils.hasText(value) && !"null".equals(value)) {
            RealtimeFlowVelocityMessage realtimeFlowVelocityMessage = JSONUtil.toObject(value, RealtimeFlowVelocityMessage.class);
            realtimeFlowVelocityMessageVO.setEnterpriseId(realtimeFlowVelocityMessage.getEnterpriseId());
            realtimeFlowVelocityMessageVO.setMac(realtimeFlowVelocityMessage.getMac());
            List<RealtimeFlowVelocityMessageVO.Velocity> velocityVOList = new ArrayList<>();
            List<RealtimeFlowVelocityMessage.Velocity> velocityList = realtimeFlowVelocityMessage.getVelocity();
            for (RealtimeFlowVelocityMessage.Velocity velocity : velocityList) {
                RealtimeFlowVelocityMessageVO.Velocity velocityVO = new RealtimeFlowVelocityMessageVO.Velocity();
                double download = new BigDecimal(Double.toString((double)velocity.getDownload()/1024)).setScale(2, RoundingMode.HALF_UP).doubleValue();
                double upload = new BigDecimal(Double.toString((double)velocity.getUpload()/1024)).setScale(2, RoundingMode.HALF_UP).doubleValue();
                velocityVO.setDownload(download);
                velocityVO.setUpload(upload);
                Long timestamp = velocity.getTimestamp();
                // 创建SimpleDateFormat对象，指定日期时间格式
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                // 将10位时间戳转换为Date对象 并格式化日期
                String formattedDate = sdf.format(new Date(timestamp * 1000));
                // 提取时分秒
                String timePart = formattedDate.substring(formattedDate.indexOf(" ") + 1);
                velocityVO.setTimestamp(timePart);
                velocityVOList.add(velocityVO);
            }
            realtimeFlowVelocityMessageVO.setVelocity(velocityVOList);
        }
        return realtimeFlowVelocityMessageVO;
    }

    @Override
    public List<Map<String, Object>> getGatewayWanConnNumByEnterprise(Long eid) {
        DeviceSearchRequest deviceSearchRequest = new DeviceSearchRequest();
        deviceSearchRequest.setEnterpriseId(eid);
        deviceSearchRequest.setDeviceType(1);
        List<DeviceInfoVo> deviceInfoVos = apiServiceFeignClient.deviceESearch(deviceSearchRequest);
        List<String> macList = deviceInfoVos.stream().map(DeviceInfoVo::getMac).collect(Collectors.toList());
        try {
            CompletableFuture<Map<String, Boolean>> future1 = CompletableFuture.supplyAsync(() -> {
                this.redisTemplate.setKeySerializer(new StringRedisSerializer());
                this.redisTemplate.setValueSerializer(new StringRedisSerializer());
                this.redisTemplate.afterPropertiesSet();
                Map<String, Boolean> onlineMap = new HashMap<>();
                for (String mac : macList) {
                    String key = "GATEWAY_ONLINE_UDP:"+mac;
                    Object o = this.redisTemplate.opsForValue().get(key);
                    String value = (String) o;
                    if (StringUtils.hasText(value)) {
                        onlineMap.put(mac, true);
                    } else {
                        onlineMap.put(mac, false);
                    }
                }
                return onlineMap;
            });

            CompletableFuture<List<GatewayWanConnDetail>> future2 = CompletableFuture.supplyAsync(() -> {
                GatewayQuery gatewayQuery = new GatewayQuery();
                if (!StringUtils.hasText(gatewayQuery.getStartTime()) || !StringUtils.hasText(gatewayQuery.getEndTime())) {
                    //默认查询近30天的数据
                    LocalDate now = LocalDate.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    gatewayQuery.setStartTime(now.minusDays(30).format(formatter));
                    gatewayQuery.setEndTime(now.format(formatter));
                }

                gatewayQuery.setGatewayMacList(macList);
                return gatewayMapper.getGatewayWanConnByParam(gatewayQuery);
            });
            Map<String, Boolean> onlineMap = future1.get();
            log.info("getGatewayWanConnNumByEnterprise-onlineMap==>{}", onlineMap);
            List<GatewayWanConnDetail> gatewayWanConnList = future2.get();
            log.info("getGatewayWanConnNumByEnterprise-gatewayWanConnList==>{}", gatewayWanConnList);

            //根据gatewayMac进行分组
            Map<String, List<GatewayWanConnDetail>> gatewayWanConnMap = gatewayWanConnList.stream().collect(Collectors.groupingBy(GatewayWanConnDetail::getGatewayMac));
            List<Map<String, Object>> viewList = new ArrayList<>();
            for (DeviceInfoVo deviceInfoVo : deviceInfoVos) {
                String mac = deviceInfoVo.getMac();
                List<GatewayWanConnDetail> gatewayWanConnDetails = gatewayWanConnMap.get(mac);
                Map<String, Object> viewMap = null;
                if (gatewayWanConnDetails != null && !gatewayWanConnDetails.isEmpty()) {
                    viewMap = new HashMap<>();
                    List<String> recordTime = gatewayWanConnDetails.stream().map(GatewayWanConnDetail::getRecordTime).collect(Collectors.toList());
                    viewMap.put("times", recordTime);
                    List<Double> avgNum = gatewayWanConnDetails.stream().map(GatewayWanConnDetail::getAvgConnectNumber).collect(Collectors.toList());
                    viewMap.put("avgNum", avgNum);
                    List<Integer> maxNum = gatewayWanConnDetails.stream().map(GatewayWanConnDetail::getMaxConnectNumber).collect(Collectors.toList());
                    viewMap.put("maxNum", maxNum);
                }
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("mac", mac);
                resultMap.put("sn", deviceInfoVo.getSn());
                resultMap.put("gatewayName", deviceInfoVo.getName());
                resultMap.put("online", onlineMap.get(mac));
                resultMap.put("flowVelocity", viewMap);
                viewList.add(resultMap);
            }

            //将flowVelocity为空的排到后面
            viewList.sort(Comparator.comparing(o -> o.get("flowVelocity") == null ? "1" : "0"));
            return viewList;
        } catch (Exception e) {
            log.error("网关WAN连接数查询异常: {}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    @Override
    public PageRes<Map<String, Object>> getPageGatewayWanConnNum(GatewayQuery gatewayQuery) {
        EnterpriseDeviceRequest request1 = new EnterpriseDeviceRequest();
        if (StringUtils.hasText(gatewayQuery.getGatewayMac())) {
            request1.setMac(gatewayQuery.getGatewayMac());
        }
        if (StringUtils.hasText(gatewayQuery.getSn())) {
            request1.setSn(gatewayQuery.getSn());
        }
        int page = gatewayQuery.getPage() == null ? 1 : gatewayQuery.getPage();
        int pageSize = gatewayQuery.getPageSize() == null ? 10 : gatewayQuery.getPageSize();
        request1.setPage(page);
        request1.setPageSize(pageSize);
        request1.setDeviceType(1);
        log.info("ggetPageGatewayWanConnNum查询网关连接数条件:{}", JSONUtil.toJSONString(request1));
        EnterpriseDeviceResponse enterpriseDeviceResponse = apiServerDeviceFeignClient.enterpriseDeviceList(request1, String.valueOf(gatewayQuery.getUid()), Long.parseLong(gatewayQuery.getEid()));
        log.info("enterpriseDeviceResponse==>:{}", JSONUtil.toJSONString(enterpriseDeviceResponse));

        PageRes<Map<String, Object>> result = new PageRes<>();
        if (enterpriseDeviceResponse != null && !CollectionUtils.isEmpty(enterpriseDeviceResponse.getList())) {
            List<EnterpriseDeviceVo> deviceVoList = enterpriseDeviceResponse.getList();
            log.info("deviceVoList==>:{}", JSONUtil.toJSONString(deviceVoList));
            try {
                CompletableFuture<DeviceClassifyResponse> future1 = CompletableFuture.supplyAsync(() -> {
                    DeviceListRequest request2 = new DeviceListRequest();
                    request2.setUserId(String.valueOf(gatewayQuery.getUid()));
                    request2.setIds(deviceVoList.stream().map(EnterpriseDeviceVo::getId).collect(Collectors.toList()));
                    return apiServiceFeignClient.getDeviceListEInfo(request2);
                });
                CompletableFuture<List<GatewayWanConnDetail>> future2 = CompletableFuture.supplyAsync(() -> {
                    if (!StringUtils.hasText(gatewayQuery.getStartTime()) || !StringUtils.hasText(gatewayQuery.getEndTime())) {
                        //默认查询近30天的数据
                        LocalDate now = LocalDate.now();
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        gatewayQuery.setStartTime(now.minusDays(30).format(formatter));
                        gatewayQuery.setEndTime(now.format(formatter));
                    }
                    //抽取deviceVoList中的mac生成集合
                    List<String> macs = deviceVoList.stream().map(EnterpriseDeviceVo::getMac).collect(Collectors.toList());
                    gatewayQuery.setGatewayMacList(macs);
                    return gatewayMapper.getGatewayWanConnByParam(gatewayQuery);
                });

                DeviceClassifyResponse deviceListEInfo = future1.get();
                log.info("deviceListEInfo==>:{}", JSONUtil.toJSONString(deviceListEInfo));
                List<GatewayWanConnDetail> gatewayWanConnList = future2.get();
                log.info("getGatewayWanConnNum-gatewayWanConnList==>{}", gatewayWanConnList);
                //根据gatewayMac进行分组
                Map<String, List<GatewayWanConnDetail>> gatewayWanConnMap = gatewayWanConnList.stream().collect(Collectors.groupingBy(GatewayWanConnDetail::getGatewayMac));
                List<Map<String, Object>> viewList = new ArrayList<>();
                for (EnterpriseDeviceVo deviceVo : deviceVoList) {
                    String mac = deviceVo.getMac();
                    List<GatewayWanConnDetail> gatewayWanConnDetails = gatewayWanConnMap.get(mac);
                    Map<String, Object> viewMap = null;
                    if (!CollectionUtils.isEmpty(gatewayWanConnDetails)) {
                        viewMap = new HashMap<>();
                        List<String> recordTime = gatewayWanConnDetails.stream().map(GatewayWanConnDetail::getRecordTime).collect(Collectors.toList());
                        viewMap.put("times", recordTime);
                        List<Double> avgNum = gatewayWanConnDetails.stream().map(GatewayWanConnDetail::getAvgConnectNumber).collect(Collectors.toList());
                        viewMap.put("avgNum", avgNum);
                        List<Integer> maxNum = gatewayWanConnDetails.stream().map(GatewayWanConnDetail::getMaxConnectNumber).collect(Collectors.toList());
                        viewMap.put("maxNum", maxNum);
                    }
                    Map<String, Object> resultMap = new HashMap<>();
                    if (deviceListEInfo == null || deviceListEInfo.getDevices() == null || deviceListEInfo.getDevices().isEmpty()) {
                        resultMap.put("imgUrl", "");
                        resultMap.put("gatewayName", "");
                    } else {
                        DeviceInfoVo deviceInfoVo = deviceListEInfo.getDevices().stream().filter(o -> o.getId().equals(deviceVo.getId())).findFirst().orElse(null);
                        resultMap.put("imgUrl", deviceInfoVo == null ? "" : deviceInfoVo.getIconUrl());
                        resultMap.put("gatewayName", deviceInfoVo == null ? "" : deviceInfoVo.getName());
                    }
                    resultMap.put("mac", mac);
                    resultMap.put("sn", deviceVo.getSn());
                    resultMap.put("model", deviceVo.getDeviceModel());
                    resultMap.put("online", deviceVo.getIsOnline());
                    resultMap.put("flowVelocity", viewMap);
                    viewList.add(resultMap);
                }

                //将flowVelocity为空的排到后面
                viewList.sort(Comparator.comparing(o -> o.get("flowVelocity") == null ? "1" : "0"));

                result.setList(viewList);
                result.setPage(page);
                result.setPageSize(pageSize);
                result.setTotal((long)enterpriseDeviceResponse.getTotal());
            } catch (Exception e) {
                log.error("网关WAN连接数查询异常: {}", e.getMessage(), e);
            }
        } else {
            result.setList(new ArrayList<>());
            result.setPage(page);
            result.setPageSize(pageSize);
            result.setTotal(0L);
        }
        log.info("getPageGatewayWanConnNum查询网关连接数结果:{}", result);
        return result;
    }

    @Override
    public RealtimeWanConnMessageVO getPollingRealtimeWanConnReport(GatewayQuery gatewayQuery) {
        this.redisTemplate.setKeySerializer(new StringRedisSerializer());
        this.redisTemplate.setValueSerializer(new StringRedisSerializer());
        this.redisTemplate.afterPropertiesSet();
        String Heartbeatkey = "device-manager:realtimeWanConnMonitorReport:" + gatewayQuery.getGatewayMac();
        //每次心跳都刷新任务存活标记，从头开始计时
        Boolean hasKey = this.redisTemplate.opsForValue().getOperations().hasKey(Heartbeatkey);
        if (hasKey) {//对于开启失败不接受心跳
            this.redisTemplate.opsForValue().set(Heartbeatkey, "open", 20, TimeUnit.SECONDS);
        }
        String date = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now());
        String key = "ekit-portal:realtimeWanConnMonitorReport:" + date + ":" + gatewayQuery.getEid() + ":" + gatewayQuery.getGatewayMac();
        String value = String.valueOf(this.redisTemplate.opsForValue().get(key));
        log.info("getPollingRealtimeWanConnReport从redis读取到数据: {}", value);
        RealtimeWanConnMessageVO realtimeMessageVO = new RealtimeWanConnMessageVO();
        if (StringUtils.hasText(value) && !"null".equals(value)) {
            RealtimeWanConnMessage realtimeMessage = JSONUtil.toObject(value, RealtimeWanConnMessage.class);
            realtimeMessageVO.setEnterpriseId(realtimeMessage.getEnterpriseId());
            realtimeMessageVO.setMac(realtimeMessage.getMac());
            List<RealtimeWanConnMessageVO.Velocity> velocityVOList = new ArrayList<>();
            List<RealtimeWanConnMessage.Velocity> velocityList = realtimeMessage.getVelocity();
            for (RealtimeWanConnMessage.Velocity velocity : velocityList) {
                RealtimeWanConnMessageVO.Velocity velocityVO = new RealtimeWanConnMessageVO.Velocity();
                velocityVO.setConnCount(velocity.getConnectionCount());
                Long timestamp = velocity.getTimestamp();
                // 将13位时间戳转换为LocalDatetime对象
                LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
                // 取出时分秒并格式化成HH:mm:ss
                String time = localDateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
                velocityVO.setTimestamp(time);
                velocityVOList.add(velocityVO);
            }
            realtimeMessageVO.setVelocity(velocityVOList);
        }
        return realtimeMessageVO;
    }
/*
    @Override
    public Map<String, List<ConnectNumberDataDto>> getGatewayConnectNumberStatistics(GatewayQuery gatewayQuery) {
        List<Integer> factoryIdList = getIdList(gatewayQuery.getVendor());
        List<Integer> deviceModelIdList = getIdList(gatewayQuery.getModel());
        List<GateWayConnectNumberStatisticsResult> connectNumberStatistics = gatewayMapper.getGatewayConnectNumberStatistics(gatewayQuery, factoryIdList, deviceModelIdList);
        log.info("getGatewayConnectNumberStatistics result: {}", connectNumberStatistics);
        //计算总的数据
        long sumTotal = connectNumberStatistics.stream().mapToLong(GateWayConnectNumberStatisticsResult::getTotal).sum();
        //连接数范围
        long total_0_100 = 0;
        long total_101_200 = 0;
        long total_201_300 = 0;
        long total_301_400 = 0;
        long total_401_500 = 0;
        long total_501_600 = 0;
        long total_601_700 = 0;
        long total_701_800 = 0;
        long total_801_900 = 0;
        long total_901_1000 = 0;
        long total_1000_up = 0;
        for (GateWayConnectNumberStatisticsResult numberStatistic : connectNumberStatistics) {
            Long total = numberStatistic.getTotal();
            Long connectNumber = numberStatistic.getConnectNumber();
            if (connectNumber != null && 0 <= connectNumber && connectNumber <= 100) {
                total_0_100 = total_0_100 + total;
            }
            if (connectNumber != null && 100 < connectNumber && connectNumber <= 200) {
                total_101_200 = total_101_200 + total;
            }
            if (connectNumber != null && 200 < connectNumber && connectNumber <= 300) {
                total_201_300 = total_201_300 + total;
            }
            if (connectNumber != null && 300 < connectNumber && connectNumber <= 400) {
                total_301_400 = total_301_400 + total;
            }
            if (connectNumber != null && 400 < connectNumber && connectNumber <= 500) {
                total_401_500 = total_401_500 + total;
            }
            if (connectNumber != null && 500 < connectNumber && connectNumber <= 600) {
                total_501_600 = total_501_600 + total;
            }
            if (connectNumber != null && 600 < connectNumber && connectNumber <= 700) {
                total_601_700 = total_601_700 + total;
            }
            if (connectNumber != null && 700 < connectNumber && connectNumber <= 800) {
                total_701_800 = total_701_800 + total;
            }
            if (connectNumber != null && 800 < connectNumber && connectNumber <= 900) {
                total_801_900 = total_801_900 + total;
            }
            if (connectNumber != null && 900 < connectNumber && connectNumber <= 1000) {
                total_901_1000 = total_901_1000 + total;
            }
            if (connectNumber != null && 1000 < connectNumber) {
                total_1000_up = total_1000_up + total;
            }
        }
        List<ConnectNumberDataDto> connectNumberDataDtos = new ArrayList<>();
        ConnectNumberDataDto dto1 = new ConnectNumberDataDto();
        dto1.setRange("0-100");
        dto1.setNum(total_0_100);
//        dto1.setRatio(String.format("%.2f", (double) total_0_100 / sumTotal * 100));
//        dto1.setRatio(String.format("%d", (int) total_0_100 / sumTotal * 100));
        dto1.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_0_100 / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto1);
        ConnectNumberDataDto dto2 = new ConnectNumberDataDto();
        dto2.setRange("101-200");
        dto2.setNum(total_101_200);
        dto2.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_101_200 / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto2);
        ConnectNumberDataDto dto3 = new ConnectNumberDataDto();
        dto3.setRange("201-300");
        dto3.setNum(total_201_300);
        dto3.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_201_300 / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto3);
        ConnectNumberDataDto dto4 = new ConnectNumberDataDto();
        dto4.setRange("301-400");
        dto4.setNum(total_301_400);
        dto4.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_301_400 / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto4);
        ConnectNumberDataDto dto5 = new ConnectNumberDataDto();
        dto5.setRange("401-500");
        dto5.setNum(total_401_500);
        dto5.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_401_500 / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto5);
        ConnectNumberDataDto dto6 = new ConnectNumberDataDto();
        dto6.setRange("501-600");
        dto6.setNum(total_501_600);
        dto6.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_501_600 / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto6);
        ConnectNumberDataDto dto7 = new ConnectNumberDataDto();
        dto7.setRange("601-700");
        dto7.setNum(total_601_700);
        dto7.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_601_700 / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto7);
        ConnectNumberDataDto dto8 = new ConnectNumberDataDto();
        dto8.setRange("701-800");
        dto8.setNum(total_701_800);
        dto8.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_701_800 / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto8);
        ConnectNumberDataDto dto9 = new ConnectNumberDataDto();
        dto9.setRange("801-900");
        dto9.setNum(total_801_900);
        dto9.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_801_900 / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto9);
        ConnectNumberDataDto dto10 = new ConnectNumberDataDto();
        dto10.setRange("901-1000");
        dto10.setNum(total_901_1000);
        dto10.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_901_1000 / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto10);
        ConnectNumberDataDto dto11 = new ConnectNumberDataDto();
        dto11.setRange("1001以上");
        dto11.setNum(total_1000_up);
        dto11.setRatio("" + String.format("%.2f", (sumTotal <= 0 ? 0 : (double)total_1000_up / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto11);
        Map<String, List<ConnectNumberDataDto>> map = new HashMap<>(1);
        map.put("connectNumberData", connectNumberDataDtos);
        return map;
    }
*/

    @Override
    public Map<String, List<ConnectNumberDataDto>> getGatewayConnectNumberStatistics(GatewayQuery gatewayQuery) {
        List<Integer> factoryIdList = getIdList(gatewayQuery.getVendor());
        List<Integer> deviceModelIdList = getIdList(gatewayQuery.getModel());
        GateWayConnectNumberStatisticsResult connectNumberStatistics = gatewayMapper.getGatewayConnectNumberStatistics(gatewayQuery, factoryIdList, deviceModelIdList);
        log.info("getGatewayConnectNumberStatistics result: {}", connectNumberStatistics);
//        //计算总的数据
//        long sumTotal = connectNumberStatistics.stream().mapToLong(GateWayConnectNumberStatisticsResult::getTotal).sum();
//        //连接数范围
//        long total_0_512 = 0;
//        long total_513_1024 = 0;
//        long total_1025_1537 = 0;
//        long total_1538_2048 = 0;
//        long total_2049_up = 0;
//        for (GateWayConnectNumberStatisticsResult numberStatistic : connectNumberStatistics) {
//            Long total = numberStatistic.getTotal();
//            Long connectNumber = numberStatistic.getConnectNumber();
//            if (connectNumber != null && 0 <= connectNumber && connectNumber <= 512) {
//                total_0_512 = total_0_512 + total;
//            }
//            if (connectNumber != null && 512 < connectNumber && connectNumber <= 1024) {
//                total_513_1024 = total_513_1024 + total;
//            }
//            if (connectNumber != null && 1024 < connectNumber && connectNumber <= 1537) {
//                total_1025_1537 = total_1025_1537 + total;
//            }
//            if (connectNumber != null && 1537 < connectNumber && connectNumber <= 2048) {
//                total_1538_2048 = total_1538_2048 + total;
//            }
//            if (connectNumber != null && 2048 < connectNumber) {
//                total_2049_up = total_2049_up + total;
//            }
//        }
        long sumTotal = connectNumberStatistics.getTotal();
        List<ConnectNumberDataDto> connectNumberDataDtos = new ArrayList<>();
        ConnectNumberDataDto dto1 = new ConnectNumberDataDto();
        dto1.setRange("1-512");
        dto1.setNum(connectNumberStatistics.getCount1to512());
        dto1.setRatio(String.format("%.2f", (sumTotal <= 0 ? 0 : (double)connectNumberStatistics.getCount1to512() / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto1);

        ConnectNumberDataDto dto2 = new ConnectNumberDataDto();
        dto2.setRange("513-1024");
        dto2.setNum(connectNumberStatistics.getCount513to1024());
        dto2.setRatio(String.format("%.2f", (sumTotal <= 0 ? 0 : (double)connectNumberStatistics.getCount513to1024() / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto2);

        ConnectNumberDataDto dto3 = new ConnectNumberDataDto();
        dto3.setRange("1025-1537");
        dto3.setNum(connectNumberStatistics.getCount1025to1537());
        dto3.setRatio(String.format("%.2f", (sumTotal <= 0 ? 0 : (double)connectNumberStatistics.getCount1025to1537() / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto3);

        ConnectNumberDataDto dto4 = new ConnectNumberDataDto();
        dto4.setRange("1538-2048");
        dto4.setNum(connectNumberStatistics.getCount1538to2048());
        dto4.setRatio(String.format("%.2f", (sumTotal <= 0 ? 0 : (double)connectNumberStatistics.getCount1538to2048() / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto4);
        ConnectNumberDataDto dto5 = new ConnectNumberDataDto();
        dto5.setRange("2048以上");
        dto5.setNum(connectNumberStatistics.getCount2049toup());
        dto5.setRatio(String.format("%.2f", (sumTotal <= 0 ? 0 : (double)connectNumberStatistics.getCount2049toup() / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto5);

        Map<String, List<ConnectNumberDataDto>> map = new HashMap<>(1);
        map.put("connectNumberData", connectNumberDataDtos);
        return map;
    }


    @Override
    public Map<String, List<ConnectNumberDataDto>> getGatewayConnectNumberStatistics95(GatewayQuery gatewayQuery) {
        List<Integer> factoryIdList = getIdList(gatewayQuery.getVendor());
        List<Integer> deviceModelIdList = getIdList(gatewayQuery.getModel());
        GateWayConnectNumberStatisticsResult connectNumberStatistics = gatewayMapper.getGatewayConnectNumberStatistics95(gatewayQuery, factoryIdList, deviceModelIdList);
        log.info("getGatewayConnectNumberStatistics95 result: {}", connectNumberStatistics);
        long sumTotal = connectNumberStatistics.getTotal();
        List<ConnectNumberDataDto> connectNumberDataDtos = new ArrayList<>();
        ConnectNumberDataDto dto1 = new ConnectNumberDataDto();
        dto1.setRange("1-512");
        dto1.setNum(connectNumberStatistics.getCount1to512());
        dto1.setRatio(String.format("%.2f", (sumTotal <= 0 ? 0 : (double)connectNumberStatistics.getCount1to512() / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto1);

        ConnectNumberDataDto dto2 = new ConnectNumberDataDto();
        dto2.setRange("513-1024");
        dto2.setNum(connectNumberStatistics.getCount513to1024());
        dto2.setRatio(String.format("%.2f", (sumTotal <= 0 ? 0 : (double)connectNumberStatistics.getCount513to1024() / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto2);

        ConnectNumberDataDto dto3 = new ConnectNumberDataDto();
        dto3.setRange("1025-1537");
        dto3.setNum(connectNumberStatistics.getCount1025to1537());
        dto3.setRatio(String.format("%.2f", (sumTotal <= 0 ? 0 : (double)connectNumberStatistics.getCount1025to1537() / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto3);

        ConnectNumberDataDto dto4 = new ConnectNumberDataDto();
        dto4.setRange("1538-2048");
        dto4.setNum(connectNumberStatistics.getCount1538to2048());
        dto4.setRatio(String.format("%.2f", (sumTotal <= 0 ? 0 : (double)connectNumberStatistics.getCount1538to2048() / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto4);
        ConnectNumberDataDto dto5 = new ConnectNumberDataDto();
        dto5.setRange("2048以上");
        dto5.setNum(connectNumberStatistics.getCount2049toup());
        dto5.setRatio(String.format("%.2f", (sumTotal <= 0 ? 0 : (double)connectNumberStatistics.getCount2049toup() / sumTotal)*100) + "%");
        connectNumberDataDtos.add(dto5);

        Map<String, List<ConnectNumberDataDto>> map = new HashMap<>(1);
        map.put("connectNumberData", connectNumberDataDtos);
        return map;
    }

    @Override
    public Map<String, Object> getPageConnectNumberDetail(GatewayQuery gatewayQuery) {
        Page<GateWayConnectNumberResult> gatewayListResultPage = new Page<>();
        try {
            List<Integer> factoryIdList = getIdList(gatewayQuery.getVendor());
            List<Integer> deviceModelIdList = getIdList(gatewayQuery.getModel());
            gatewayListResultPage = PageHelper.startPage(gatewayQuery.getPage(), gatewayQuery.getPageSize())
                    .doSelectPage(() -> gatewayMapper.getGatewayConnectNumberList(gatewayQuery, factoryIdList, deviceModelIdList));
        } catch (Exception e) {
            log.error("分页查询连接数监控列表查询getPageConnectNumberDetail异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        Map<String, Object> result = new HashMap<>();
        result.put("list", gatewayListResultPage.getResult());
        result.put("page", gatewayListResultPage.getPageNum());
        result.put("pageSize", gatewayListResultPage.getPageSize());
        result.put("total", gatewayListResultPage.getTotal());

        return result;
    }

    @Override
    public Map<String, Object> getGatewayConnectNumberBySn(GatewayQuery gatewayQuery) {
        if (gatewayQuery == null || gatewayQuery.getTimeType() == null) {
            throw new FGEOException.BadRequest("查询参数不能为空！");
        }
        String ipVersion = gatewayQuery.getIpVersion();
        String wanConnType = gatewayQuery.getWanConnType();
        if (StrUtil.isBlank(ipVersion)) {
            gatewayQuery.setIpVersion(null);
        }
        if (StrUtil.isBlank(wanConnType)) {
            gatewayQuery.setWanConnType(null);
        }
        List<GateWayConnectNumberStaticResult> gatewayConnectNumberBySn = gatewayMapper.getGatewayConnectNumberBySn(gatewayQuery);
        Map<String, Object> mapResult = new HashMap<>();
        List<String> xAxis = gatewayConnectNumberBySn.stream().map(GateWayConnectNumberStaticResult::getTimeType).collect(Collectors.toList());
        Integer timeType = gatewayQuery.getTimeType();
        if (timeType == 2) {
            // 202421->2024-21周
            List<String> xAxiNewList = new ArrayList<>();
            for (String xAxi : xAxis) {
                String xAxiNew =xAxi.substring(0, 4) + "-" +  xAxi.substring(4, xAxi.length()) + "周";
                xAxiNewList.add(xAxiNew);
            }
            xAxis = xAxiNewList;
        }
        if (timeType == 3) {
            // 202406->2024年06月
            List<String> xAxiNewList = new ArrayList<>();
            for (String xAxi : xAxis) {
                String xAxiNew =xAxi.substring(0, 4) + "年" +  xAxi.substring(4, xAxi.length()) + "月";
                xAxiNewList.add(xAxiNew);
            }
            xAxis = xAxiNewList;
        }
        List<Integer> average = gatewayConnectNumberBySn.stream().map(GateWayConnectNumberStaticResult::getAvgConnectNumber).collect(Collectors.toList());
        List<Integer> maximum = gatewayConnectNumberBySn.stream().map(GateWayConnectNumberStaticResult::getMaxConnectNumber).collect(Collectors.toList());
        mapResult.put("xAxis", xAxis);//日期数组
        mapResult.put("average", average);//平均连接数数组
        mapResult.put("maximum", maximum);//最大连接数数组
        return mapResult;
    }

    @Override
    public List<Map<String, String>> getGatewayWanConnServiceList(GatewayQuery gatewayQuery) {
        if (gatewayQuery == null ||  StrUtil.isBlank(gatewayQuery.getGatewayMac())) {
            throw new FGEOException.BadRequest("查询参数不能为空！");
        }
        List<String> gatewayWanConnServiceList = gatewayMapper.getGatewayWanConnServiceList(gatewayQuery);
        if (gatewayWanConnServiceList != null && gatewayWanConnServiceList.size() > 0) {
            List<Map<String, String>> result = new ArrayList<>();
            for (String gatewayWanConnService : gatewayWanConnServiceList) {
                Map<String, String> map = new HashMap<>();
                map.put("label", gatewayWanConnService);
                map.put("value", gatewayWanConnService);
                result.add(map);
            }
            return result;
        }
        return null;
    }

    @Override
    public List<GateWayConnectNumberResult> getGatewayConnectNumberList(GatewayQuery gatewayQuery) {
        List<GateWayConnectNumberResult> gatewayConnectNumberList;
        try {
            List<Integer> factoryIdList = getIdList(gatewayQuery.getVendor());
            List<Integer> deviceModelIdList = getIdList(gatewayQuery.getModel());
            gatewayConnectNumberList = gatewayMapper.getGatewayConnectNumberList(gatewayQuery, factoryIdList, deviceModelIdList);
        } catch (Exception e) {
            log.error("网关连接数列表getGatewayConnectNumberList异常: ", e);
            throw new FGEOException.InternalServerError();
        }
        return gatewayConnectNumberList;
    }

    @Override
    public PageRes<AccessNetworkAnalysisResult> getPageAccessNetworkAnalysis(GatewayQuery gatewayQuery) {
        try {
            Page<AccessNetworkAnalysisResult> pageData = PageHelper.startPage(gatewayQuery.getPage(), gatewayQuery.getPageSize())
                    .doSelectPage(() -> gatewayMapper.accessNetworkAnalysisList(gatewayQuery));

            PageRes<AccessNetworkAnalysisResult> result = new PageRes<>();
            result.setList(pageData.getResult());
            result.setPage(pageData.getPageNum());
            result.setPageSize(pageData.getPageSize());
            result.setTotal(pageData.getTotal());
            return result;
        } catch (Exception e) {
            log.error("分页查询用户访问网络分析getPageAccessNetworkAnalysis异常: ", e);
            throw new FGEOException.InternalServerError();
        }
    }

    @Override
    public List<GatewaySubDeviceInfoDto> getGatewaySubDeviceOnlineNum(List<String> gatewaySns) {
        if (gatewaySns == null || gatewaySns.isEmpty()) {
            return new ArrayList<>();
        }
        if (gatewaySns.size() > limitNum) {
            gatewaySns = gatewaySns.subList(0, limitNum);
        }
        String finalperiodDetailQueryLimitHour = periodDetailQueryLimitHour;
        if (finalperiodDetailQueryLimitHour == null) {
            finalperiodDetailQueryLimitHour = "12";
        }
        //通过mac查询下挂设备数量
        List<GatewayPeriodAll> gatewayPeriodAlls = this.gatewayPeriodAllMapper.getLastPeriodInfo(gatewaySns, finalperiodDetailQueryLimitHour);
        if (gatewayPeriodAlls == null || gatewayPeriodAlls.isEmpty()) {
            return new ArrayList<>();
        }
        List<GatewaySubDeviceInfoDto> gatewaySubDeviceInfoDtos = new ArrayList<>();
        for (GatewayPeriodAll gatewayPeriodAll : gatewayPeriodAlls) {
            GatewaySubDeviceInfoDto gatewaySubDeviceInfoDto = new GatewaySubDeviceInfoDto();
            gatewaySubDeviceInfoDto.setMac(gatewayPeriodAll.getGatewayMac());
            gatewaySubDeviceInfoDto.setSn(gatewayPeriodAll.getGatewaySn());
            gatewaySubDeviceInfoDto.setDeviceNum(gatewayPeriodAll.getDeviceNum());
            gatewaySubDeviceInfoDto.setOnlineDeviceNum(gatewayPeriodAll.getOnlineDeviceNum());
            gatewaySubDeviceInfoDtos.add(gatewaySubDeviceInfoDto);
        }
        return gatewaySubDeviceInfoDtos;
    }


    private List<Integer> getIdList(String ids) {
        //为null和空以及空格
        if (StrUtil.isBlank(ids)) {
            return null;
        }
        //去掉首位空格和中间空格
        ids = ids.trim().replaceAll(" ", "");
        //全是逗号
        if (ids.matches("^,+$")) {
            return null;
        }
        List<Integer> idList = new ArrayList<>();
        if (ids.contains(",")) {
            String[] split = ids.split(",");
            for (String s : split) {
                if (StrUtil.isNotBlank(s.trim())) {
                    idList.add(Integer.parseInt(s.trim()));
                }
            }
        } else {
            idList.add(Integer.parseInt(ids));
        }
        return idList;
    }

}
