package com.cmiot.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.cmiot.fgeo.ams.dto.enterprise.EntUserInfo;
import com.cmiot.fgeo.ams.facade.feign.EnterpriseFeignClient;
import com.cmiot.fgeo.common.dto.PageInfoParam;
import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.fgeo.devicemanager.dto.GatewayDetailDto;
import com.cmiot.fgeo.devicemanager.gatewayapi.GatewayApiServiceFeignClient;
import com.cmiot.fgeo.pluginmanager.facade.feign.PluginInfoFeignClient;
import com.cmiot.qyzw.analyse.api.feignclient.BlinkPluginStatFeignClient;
import com.cmiot.qyzw.analyse.api.request.BlinkPluginRTStatRequest;
import com.cmiot.qyzw.analyse.api.response.BlinkPluginRTStatVo;
import com.cmiot.report.bean.DicAreaInfo;
import com.cmiot.report.bean.GatewayRebootDetail;
import com.cmiot.report.bean.GatewayRebootDetailExample;
import com.cmiot.report.bean.plugin.EkitRestartAlertAnalysisDto;
import com.cmiot.report.bean.plugin.EkitRestartAlertAnalysisParam;
import com.cmiot.report.dto.plugin.*;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.facade.dto.plugin.*;
import com.cmiot.report.mapper.*;
import com.cmiot.report.service.DicDataService;
import com.cmiot.report.service.PluginReportService;
import com.cmiot.report.util.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PluginReportServiceImpl implements PluginReportService {

    @Autowired
    private PluginInstallMapper pluginInstallMapper;
    @Autowired
    private GatewayRebootMapper gatewayRebootMapper;
    @Autowired
    private GatewayModelYesterdayMapper gatewayModelYesterdayMapper;
    @Autowired
    private GatewayRebootDetailMapper gatewayRebootDetailMapper;

    @Autowired
    private PluginInfoStatisticMapper pluginInfoStatisticMapper;

    @Autowired
    private GatewayApiServiceFeignClient gatewayApiServiceFeignClient;

    @Autowired
    private PluginInfoFeignClient pluginInfoFeignClient;

    @Autowired
    private BlinkPluginStatFeignClient blinkPluginStatFeignClient;

    @Autowired
    private DicAreaInfoMapper dicAreaInfoMapper;

    //#{'${name.name.name}'.trim().split(',')}
    @Value("${gateway.model.int}")
    private List<String> modelkeyList;
    @Value("${gateway.model.str}")
    private List<String> modelValueList;

    //计算活跃时间值，单位分钟。在该时间值内的才算活跃
    @Value("${plugininfo.use.active.time:10}")
    private Integer pluginUseActiveTime;


    @Value("${plugininfo.blink.bundleId:2001}")
    private String blinkBundleId;

    @Autowired
    private DicDataService dicDataService;

    @Autowired
    private EnterpriseFeignClient enterpriseFeignClient;


    @Override
    public PluginInstallAnalysisRes getInstallationAnalysis(PluginInstallAnalysisParam param, LocalDate localDate) {
        List<PluginModelCount> installList = this.pluginInstallMapper.getInstallationByModelQuery(param.getVendor()
                , param.getModel(), param.getPlugin(), param.getVersion());
        //获取gateway_productclass统计
        List<PluginModelCount> modelCountList = this.gatewayModelYesterdayMapper.getCountByModelQuery(param.getVendor()
                , param.getModel());
        PluginInstallAnalysisRes res = new PluginInstallAnalysisRes();
        Map<Long, PluginModelCount> modelCoutMap = modelCountList.stream().collect(Collectors.toMap(PluginModelCount::getModelId, k -> k, (k1, k2) -> k1));
        //Map<Long, PluginModelCount> installMap = installList.stream().collect(Collectors.toMap(PluginModelCount::getModelId, k->k, (k1, k2) -> k1));
        List<PluginInstallAnalysisVo> voList = new ArrayList<>();
        Long installOther = 0L;
        for (PluginModelCount mc : installList) {
            if (modelCoutMap.containsKey(mc.getModelId())) {
                PluginInstallAnalysisVo pa = new PluginInstallAnalysisVo();
                String gatewayModel = modelCoutMap.get(mc.getModelId()).getGatewayModel();
                // res.getYaxis().add(gatewayModel);
                pa.setModel(gatewayModel);
                Long modelCount = modelCoutMap.get(mc.getModelId()).getModelCount();
                //res.getInstalledNumber().add(mc.getModelCount());
                //res.getTotalQuantity().add(modelCount);
                pa.setModelCount(modelCount);
                pa.setInstallCount(mc.getModelCount());
                if (param.getStatisticType().equals(2)) {
                    String coverage = getPercent(mc.getModelCount(), modelCount);
                    // res.getCoverage().add(coverage);
                    pa.setCoverage(coverage);
                }
                voList.add(pa);
                modelCoutMap.remove(mc.getModelId());
            } else {
                installOther += mc.getModelCount();
            }
        }
        for (Map.Entry<Long, PluginModelCount> entry : modelCoutMap.entrySet()) {
            PluginModelCount value = entry.getValue();
            PluginInstallAnalysisVo pa = new PluginInstallAnalysisVo();
            pa.setModel(value.getGatewayModel());
            pa.setModelCount(value.getModelCount());
            pa.setInstallCount(0L);
            if (param.getStatisticType().equals(2)) {
                pa.setCoverage("0");
            }
            voList.add(pa);
        }

        if (installOther > 0) {
            PluginInstallAnalysisVo pa = new PluginInstallAnalysisVo();
            pa.setModel("未知型号");
            pa.setModelCount(0L);
            pa.setInstallCount(installOther);
            if (param.getStatisticType().equals(2)) {
                pa.setCoverage("0");
            }
            voList.add(pa);
        }
        if (param.getStatisticType().equals(1)) {
            voList.sort(new Comparator<PluginInstallAnalysisVo>() {
                @Override
                public int compare(PluginInstallAnalysisVo o1, PluginInstallAnalysisVo o2) {
                    return o2.getInstallCount().compareTo(o1.getInstallCount());
                }
            });
        } else {
            voList.sort(new Comparator<PluginInstallAnalysisVo>() {
                @Override
                public int compare(PluginInstallAnalysisVo o1, PluginInstallAnalysisVo o2) {
                    return new BigDecimal(o2.getCoverage()).compareTo(new BigDecimal(o1.getCoverage()));
                }
            });
        }
        for (PluginInstallAnalysisVo vo : voList) {
            res.getYaxis().add(vo.getModel());
            res.getInstalledNumber().add(vo.getInstallCount());
            res.getTotalQuantity().add(vo.getModelCount());
            res.getCoverage().add(vo.getCoverage());
        }

        return res;
    }

    @Override
    public PageRes<EkitRestartAlertAnalysisRes> ekitRestartAlertAnalysis(EkitRestartAlertAnalysisParam param) {
        PageRes<EkitRestartAlertAnalysisRes> res = new PageRes<>();
        res.setList(new ArrayList<>());
        Page<Object> page = PageHelper.startPage(param.getPage(), param.getPageSize());
        // Page<EkitRestartAlertAnalysisDto> doSelectPage = page.doSelectPage(() -> gatewayRebootMapper.ekitRestartAlertAnalysis(param));
        String time = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<EkitRestartAlertAnalysisDto> list = gatewayRebootMapper.ekitRestartAlertAnalysis(param, time);
        List<String> macList = list.stream().map(EkitRestartAlertAnalysisDto::getMac).collect(Collectors.toList());
        List<GatewayDetailDto> gatewayList = new ArrayList<>();
        try {
            gatewayList = gatewayApiServiceFeignClient.getGatewayDetailInfoByMacList(macList);
        } catch (Exception e) {
            log.info("ekitRestartAlertAnalysis gatewayApiServiceFeignClient.getGatewayDetailInfoByMacList exception:", e);
        }
        Map<String, GatewayDetailDto> gatewayMacMap = gatewayList.stream().collect(Collectors.toMap(GatewayDetailDto::getMac, e -> e, (k1, k2) -> k2));
        for (EkitRestartAlertAnalysisDto dto : list) {
            EkitRestartAlertAnalysisRes r = new EkitRestartAlertAnalysisRes();
            r.setAlarmLevel(dto.getAlarmLevel());
            r.setRestartsNumber(dto.getRestartsNumber());
            r.setAlarmTime(dto.getAlarmTime());
            r.setProvince(dto.getProvince());
            r.setBusinessName(dto.getBusinessName());
            r.setVendor(dto.getVendor());
            r.setModel(dto.getModel());
            r.setSn(dto.getSn());
            r.setMac(dto.getMac());
            if (gatewayMacMap.containsKey(r.getMac())) {
                r.setGatewayId(gatewayMacMap.get(r.getMac()).getId());
            } else {
                r.setGatewayId(null);
            }
            if (modelkeyList.contains(dto.getGatewayModelInt().toString())) {
                int i = modelkeyList.indexOf(dto.getGatewayModelInt().toString());
                r.setDeviceType(modelValueList.get(i));
                res.getList().add(r);
            } else {
                // r.setDeviceType("未知型号");
                log.warn("ekitRestartAlertAnalysis find unknown 设备型号：sn={},型号={}", dto.getSn(), dto.getGatewayModelInt());
            }


        }

        res.setPage(param.getPage());
        res.setPageSize(param.getPageSize());
        res.setTotal(page.getTotal());
        return res;
    }

    @Override
    public List<String> ekitRestartNumberList(EkitRestartNumberListParam param) {
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDate localDate = param.getAlarmTime().toInstant().atZone(zoneId).toLocalDate();
        LocalDateTime endTime = localDate.atTime(23, 59, 59, 999999999);
        LocalDateTime startTime = localDate.atTime(0, 0, 0, 0);
        Date startDate = Date.from(startTime.atZone(ZoneId.of("GMT+8")).toInstant());
        Date endDate = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());
        GatewayRebootDetailExample example = new GatewayRebootDetailExample();
        example.setOrderByClause(" reboot_time desc");
        example.createCriteria()
                .andSampleDateGreaterThanOrEqualTo(startDate)
                .andSampleDateLessThanOrEqualTo(endDate)
                .andGatewaySnEqualTo(param.getSn());
        RowBounds rowBounds = new RowBounds(0, 10);
        List<GatewayRebootDetail> list = gatewayRebootDetailMapper.selectByExampleWithRowbounds(example, rowBounds);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> list1 = list.stream().map(e -> format.format(e.getRebootTime())).collect(Collectors.toList());
        return list1;
    }

    @Override
    public PageRes<PluginCompositeOverview> getPluginInfoOverview(Integer column,
                                                                  Integer order,
                                                                  Integer page,
                                                                  Integer pageSize) {
        if (column == null || (column < 1 || column > 5)) {
            column = 1;
        }
        if (order == null || (order != 1 && order != 2)) {
            order = 1;
        }
        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 1;
        }
        PageRes<PluginCompositeOverview> pageRes = new PageRes<>();
        pageRes.setPage(page);
        pageRes.setPageSize(pageSize);
        pageRes.setTotal(0L);
        pageRes.setList(new ArrayList<>());

        PageInfoParam pageInfoParam = new PageInfoParam();
        pageInfoParam.setPage(page);
        pageInfoParam.setPageSize(pageSize);
        //获取全部插件列表
        List<PluginBaseInfo> pluginInfoBaseDtos = this.pluginInfoStatisticMapper.getPluginBaseInfo();
        if (pluginInfoBaseDtos == null || pluginInfoBaseDtos.isEmpty()) {
            return pageRes;
        }
        pageRes.setTotal((long) pluginInfoBaseDtos.size());
        List<PluginCompositeOverview> pluginCompositeOverviewList = new ArrayList<>();
        List<String> bundleIds = new ArrayList<>();
        Map<String, PluginCompositeOverview> bundleOverviewMap = new HashMap<>();
        for (PluginBaseInfo pluginInfoBaseDto : pluginInfoBaseDtos) {
            PluginCompositeOverview pluginCompositeOverview = new PluginCompositeOverview();
            pluginCompositeOverview.setId(pluginInfoBaseDto.getId());
            pluginCompositeOverview.setPluginName(pluginInfoBaseDto.getPluginName());
            pluginCompositeOverview.setManufacture(pluginInfoBaseDto.getManufacture());
            pluginCompositeOverview.setPluginDes(pluginInfoBaseDto.getPluginDes());
            pluginCompositeOverview.setVersion(pluginInfoBaseDto.getVersion());

            pluginCompositeOverview.setInstallTotal(0L);
            pluginCompositeOverview.setMonthAddNum(0L);
            pluginCompositeOverview.setMonthAddRate("0");
            pluginCompositeOverview.setUseNum(0L);
            pluginCompositeOverview.setUseRate("0%");
            pluginCompositeOverview.setMonthActiveNum(0L);
            pluginCompositeOverview.setMonthActiveRate("0%");
            pluginCompositeOverview.setDeprecatedNum(0L);

            pluginCompositeOverview.setBundleId(pluginInfoBaseDto.getBundleId());

            pluginCompositeOverviewList.add(pluginCompositeOverview);

            if (StringUtils.isNotBlank(pluginInfoBaseDto.getBundleId())) {
                bundleIds.add(pluginInfoBaseDto.getBundleId());
                bundleOverviewMap.put(pluginInfoBaseDto.getBundleId(), pluginCompositeOverview);
            }
        }

        //获取安全猫/等统计
        if (!bundleIds.isEmpty()) {

            //获取blink统计 通过blink接口获取
            try {
                if (bundleOverviewMap.containsKey(blinkBundleId)) {
                    PluginCompositeOverview pluginCompositeOverview = bundleOverviewMap.get(blinkBundleId);
                    BlinkPluginRTStatRequest blinkPluginRTStatRequest = new BlinkPluginRTStatRequest();
                    //blinkPluginRTStatRequest.setModelList();
                    //blinkPluginRTStatRequest.setProvinceList();
                    List<BlinkPluginRTStatVo> blinkPluginRTStatVos = getBlinkRtInfo(blinkPluginRTStatRequest);
                    if (blinkPluginRTStatVos != null && !blinkPluginRTStatVos.isEmpty()) {
                        long intallTotal = 0L;
                        long activeNum = 0L;
                        for (BlinkPluginRTStatVo blinkPluginRTStatVo : blinkPluginRTStatVos) {
                            intallTotal += blinkPluginRTStatVo.getInstallNum();
                            activeNum += blinkPluginRTStatVo.getActiveNum();
                        }
                        pluginCompositeOverview.setInstallTotal(intallTotal);
                        pluginCompositeOverview.setUseNum(activeNum);
                        pluginCompositeOverview.setUseRate(getPercentWithSuffix(activeNum, intallTotal));
                    }
                }
            } catch (Exception e) {
                log.info("获取组网实时数据");
            }


            //获取前30天活跃数量、百分比、 疑似弃用数
            String lastDay = DateUtil.getYesterday();
            List<PluginInfoDayCount> pluginInfoDayCounts =
                    this.pluginInfoStatisticMapper.getPluginInfoDayOverview(lastDay, bundleIds, null, null);
            if (pluginInfoDayCounts != null && !pluginInfoDayCounts.isEmpty()) {
                for (PluginInfoDayCount pluginInfoDayCount : pluginInfoDayCounts) {
                    String currentBundleId = pluginInfoDayCount.getBundleId();
                    if (bundleOverviewMap.containsKey(currentBundleId)) {
                        PluginCompositeOverview pluginCompositeOverview = bundleOverviewMap.get(currentBundleId);
                        pluginCompositeOverview.setDeprecatedNum(pluginInfoDayCount.getNoUseNum());

                        //百分比
                        if (pluginInfoDayCount.getThirtyDayActiveNum() <= 0) {
                            pluginCompositeOverview.setMonthActiveNum(pluginInfoDayCount.getThirtyDayActiveNum());
                            pluginCompositeOverview.setMonthActiveRate("0%");
                        } else if (pluginInfoDayCount.getThirtyDayActiveNum() >= pluginInfoDayCount.getTotalNum()) {
                            pluginCompositeOverview.setMonthActiveRate("100%");
                            pluginCompositeOverview.setMonthActiveNum(pluginInfoDayCount.getTotalNum());
                        } else {
                            pluginCompositeOverview.setMonthActiveRate(getPercentWithSuffix(pluginInfoDayCount.getThirtyDayActiveNum(), pluginInfoDayCount.getTotalNum()));
                            pluginCompositeOverview.setMonthActiveNum(pluginInfoDayCount.getThirtyDayActiveNum());
                        }
                    }
                }
            }


            String lastMonth = DateUtil.getLastMonth();
            //获取新增数量、新增百分比
            List<PluginInfoMonthCount> pluginInfoMonthCountList =
                    this.pluginInfoStatisticMapper.getPluginInfoOverview(lastMonth, bundleIds, null, null);
            if (pluginInfoMonthCountList != null && !pluginInfoMonthCountList.isEmpty()) {
                for (PluginInfoMonthCount pluginInfoMonthCount : pluginInfoMonthCountList) {
                    String currentBundleId = pluginInfoMonthCount.getBundleId();
                    if (bundleOverviewMap.containsKey(currentBundleId)) {
                        PluginCompositeOverview pluginCompositeOverview = bundleOverviewMap.get(currentBundleId);
                        Long monthIncrNum = pluginInfoMonthCount.getIncrNum();
                        pluginCompositeOverview.setMonthAddNum(monthIncrNum);
                        pluginCompositeOverview.setMonthAddRate(getPercent2(monthIncrNum, 1L));

                    }
                }
                String lastTwoMonth = null;
                try {
                    lastTwoMonth = DateUtil.getOffsetMonth(lastMonth, -1);
                } catch (ParseException e) {
                    log.info("获取时间异常: {}", e.getMessage(), e);
                }
                if (StringUtils.isNotBlank(lastTwoMonth)) {
                    List<PluginInfoMonthCount> pluginInfoMonthCountLastTwoMonthList =
                            this.pluginInfoStatisticMapper.getPluginInfoOverview(lastTwoMonth, bundleIds, null, null);
                    if (pluginInfoMonthCountLastTwoMonthList != null && !pluginInfoMonthCountLastTwoMonthList.isEmpty()) {
                        for (PluginInfoMonthCount pluginInfoMonthCount : pluginInfoMonthCountLastTwoMonthList) {
                            String currentBundleId = pluginInfoMonthCount.getBundleId();
                            if (bundleOverviewMap.containsKey(currentBundleId)) {
                                PluginCompositeOverview pluginCompositeOverview = bundleOverviewMap.get(currentBundleId);
                                Long incrNum = pluginCompositeOverview.getMonthAddNum();
                                Long totalNum = pluginInfoMonthCount.getTotalNum();
                                pluginCompositeOverview.setMonthAddRate(getPercent2(incrNum, totalNum));
                            }
                        }
                    }
                }
            }

            //实时接口查询总数
            List<PluginInfoTotalOverviewCount> pluginInfoOverviewTotalCounts =
                    this.pluginInfoStatisticMapper.getPluginOverviewTotalCount(bundleIds, null, null);
            if (pluginInfoOverviewTotalCounts != null && !pluginInfoOverviewTotalCounts.isEmpty()) {
                for (PluginInfoTotalOverviewCount pluginInfoOverviewTotalCount : pluginInfoOverviewTotalCounts) {
                    String currentBundleId = pluginInfoOverviewTotalCount.getBundleId();
                    if (bundleOverviewMap.containsKey(currentBundleId)) {
                        PluginCompositeOverview pluginCompositeOverview = bundleOverviewMap.get(currentBundleId);
                        pluginCompositeOverview.setInstallTotal(pluginInfoOverviewTotalCount.getTotalNum());
                    }
                }
            }

            //、启用率
            Integer activeTime = pluginUseActiveTime;
            List<PluginInfoTotalOverviewCount> pluginInfoOverviewActiveCounts =
                    this.pluginInfoStatisticMapper.getPluginOverviewActiveCount(activeTime, bundleIds, null, null);

            if (pluginInfoOverviewActiveCounts != null && !pluginInfoOverviewActiveCounts.isEmpty()) {
                for (PluginInfoTotalOverviewCount pluginInfoOverviewActiveCount : pluginInfoOverviewActiveCounts) {
                    String currentBundleId = pluginInfoOverviewActiveCount.getBundleId();
                    if (bundleOverviewMap.containsKey(currentBundleId)) {
                        PluginCompositeOverview pluginCompositeOverview = bundleOverviewMap.get(currentBundleId);
                        Long installTotal = pluginCompositeOverview.getInstallTotal();
                        Long usingNum = pluginInfoOverviewActiveCount.getUsingNum();
                        if (usingNum >= installTotal) {
                            usingNum = installTotal;
                        }
                        pluginCompositeOverview.setUseNum(usingNum);
                        pluginCompositeOverview.setUseRate(getPercentWithSuffix(usingNum, installTotal));
                    }
                }
            }
        }




        //排序
        //column 1 总安装量 2 环比新增 3 插件启用率 4月活数量 5 疑似启用
        //order 1 降序 2 升序

        Integer finalColumn = column;
        int finalOrder;
        if (order == 2) {
            finalOrder = 1;
        } else {
            finalOrder = -1;
        }

        log.info("插件概览原始统计数据: {}", JSONUtil.toJSONString(pluginCompositeOverviewList));

        pluginCompositeOverviewList.sort(new Comparator<PluginCompositeOverview>() {
            @Override
            public int compare(PluginCompositeOverview o1, PluginCompositeOverview o2) {
                switch (finalColumn) {
                    case 2: {
                        long o1Num = o1.getMonthAddNum() != null ? o1.getMonthAddNum() : 0L;
                        long o2Num = o2.getMonthAddNum() != null ? o2.getMonthAddNum() : 0L;
                        if (o1Num > o2Num) {
                            return finalOrder;
                        } else if (o1Num == o2Num) {
                            return 0;
                        } else {
                            return -1 * finalOrder;
                        }
                    }
                    case 3: {
                        long o1Num = o1.getUseNum() != null ? o1.getUseNum() : 0L;
                        long o2Num = o2.getUseNum() != null ? o2.getUseNum() : 0L;
                        if (o1Num > o2Num) {
                            return finalOrder;
                        } else if (o1Num == o2Num) {
                            return 0;
                        } else {
                            return -1 * finalOrder;
                        }
                    }
                    case 4: {
                        long o1Num = o1.getMonthActiveNum() != null ? o1.getMonthActiveNum() : 0L;
                        long o2Num = o2.getMonthActiveNum() != null ? o2.getMonthActiveNum() : 0L;
                        if (o1Num > o2Num) {
                            return finalOrder;
                        } else if (o1Num == o2Num) {
                            return 0;
                        } else {
                            return -1 * finalOrder;
                        }
                    }
                    case 5: {
                        long o1Num = o1.getDeprecatedNum() != null ? o1.getDeprecatedNum() : 0L;
                        long o2Num = o2.getDeprecatedNum() != null ? o2.getDeprecatedNum() : 0L;
                        if (o1Num > o2Num) {
                            return finalOrder;
                        } else if (o1Num == o2Num) {
                            return 0;
                        } else {
                            return -1 * finalOrder;
                        }
                    }
                    default: {
                        long o1InstallTotal = o1.getInstallTotal() != null ? o1.getInstallTotal() : 0L;
                        long o2InstallTotal = o2.getInstallTotal() != null ? o2.getInstallTotal() : 0L;
                        if (o1InstallTotal > o2InstallTotal) {
                            return finalOrder;
                        } else if (o1InstallTotal == o2InstallTotal) {
                            return 0;
                        } else {
                            return -1 * finalOrder;
                        }
                    }
                }
            }
        });


        log.info("插件概览排序后统计数据: {}", JSONUtil.toJSONString(pluginCompositeOverviewList));


        //分页
        pluginCompositeOverviewList =
                pluginCompositeOverviewList.subList((int) pageInfoParam.getOffset(),
                        Math.min((int) (pageInfoParam.getOffset() + pageInfoParam.getSize()), pluginCompositeOverviewList.size()));
        pageRes.setList(pluginCompositeOverviewList);
        return pageRes;
    }

    @Override
    public PluginDetailOverview getPluginDetailOverview(GetPluginDetailOverviewPar getPluginDetailOverviewPar) {

        //初始化数据
        PluginDetailOverview pluginDetailOverview = new PluginDetailOverview();
        pluginDetailOverview.setInstallTotal(0L);


        pluginDetailOverview.setDeprecatedNum(0L);
        pluginDetailOverview.setList(new ArrayList<>());

        Integer pluginId = getPluginDetailOverviewPar.getId();
        if (pluginId == null) {
            pluginDetailOverview.setMonthAddNum(0L);
            pluginDetailOverview.setMonthAddRate("0%");
            pluginDetailOverview.setUseNum(0L);
            pluginDetailOverview.setUseRate("0%");
            pluginDetailOverview.setMonthActiveNum(0L);
            pluginDetailOverview.setMonthActiveRate("0%");
            return pluginDetailOverview;
        }
        PluginBaseInfo pluginInfoBaseDto = this.pluginInfoStatisticMapper.getPluginBaseInfoById(pluginId);
        if (pluginInfoBaseDto == null) {
            pluginDetailOverview.setMonthAddNum(0L);
            pluginDetailOverview.setMonthAddRate("0%");
            pluginDetailOverview.setUseNum(0L);
            pluginDetailOverview.setUseRate("0%");
            pluginDetailOverview.setMonthActiveNum(0L);
            pluginDetailOverview.setMonthActiveRate("0%");
            return pluginDetailOverview;
        }

        List<String> province = getPluginDetailOverviewPar.getProvince();
        if (province != null && province.isEmpty()) {
            province = null;
        }
        List<String> model = getPluginDetailOverviewPar.getModel();
        if (model != null && model.isEmpty()) {
            model = null;
        }
        String startTime = getPluginDetailOverviewPar.getStartTime();
        String endTime = getPluginDetailOverviewPar.getEndTime();

        boolean haveTimeSelect = false;
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            haveTimeSelect = true;
        }

        if (!haveTimeSelect) {
            pluginDetailOverview.setMonthAddNum(0L);
            pluginDetailOverview.setMonthAddRate("0%");
            pluginDetailOverview.setUseNum(0L);
            pluginDetailOverview.setUseRate("0%");
            pluginDetailOverview.setMonthActiveNum(0L);
            pluginDetailOverview.setMonthActiveRate("0%");
        }

        String bundleId = pluginInfoBaseDto.getBundleId();
        if (StringUtils.isBlank(bundleId)) {
            return pluginDetailOverview;
        }

        List<String> bundleIds = new ArrayList<>();
        bundleIds.add(bundleId);

        if (haveTimeSelect) {
            //设置周期类新增总数， 各省新增数量
            //pluginDetailOverview.setList();
            //pluginDetailOverview.setInstallTotal();
            getProinvceIncrMap(pluginDetailOverview, bundleIds, province, model, startTime, endTime);

            //如果有筛选时间，疑似弃用数获取
            //pluginDetailOverview.setDeprecatedNum(pluginInfoDayCount.getNoUseNum());
            getDeprecatedNumByTime(pluginDetailOverview, endTime, bundleIds, province, model);

            //其余数据项不需要
            pluginDetailOverview.setMonthAddNum(null);
            pluginDetailOverview.setMonthAddRate(null);
            pluginDetailOverview.setUseNum(null);
            pluginDetailOverview.setUseRate(null);
            pluginDetailOverview.setMonthActiveNum(null);
            pluginDetailOverview.setMonthActiveRate(null);
        } else {
            //从日统计中获取30天活跃数量（及百分比）、疑似弃用数量
            //pluginDetailOverview.setDeprecatedNum();
            //pluginDetailOverview.setMonthActiveNum();
            //pluginDetailOverview.setMonthActiveRate();
            getFromDayCountOverview(pluginDetailOverview, bundleIds, province, model);
            String lastMonth = DateUtil.getLastMonth();
            String lastTwoMonth = null;
            try {
                lastTwoMonth = DateUtil.getOffsetMonth(lastMonth, -1);
            } catch (ParseException e) {
                log.info("获取时间异常: {}", e.getMessage(), e);
            }
            //从月数据中获取月新增数量（及百分比）
            //pluginDetailOverview.setMonthAddNum();
            //pluginDetailOverview.setMonthAddRate();
            getFromMonthCountOverview(pluginDetailOverview, bundleIds, lastMonth, lastTwoMonth, province, model);
            //     * 获取实时总数、启用率。
            //     * 获取分省安装总数，上月活跃率
            getInstallTotalAndRTCount(pluginDetailOverview, bundleIds, lastMonth, lastTwoMonth, province, model, bundleId);

        }

        if (pluginDetailOverview.getList() != null && !pluginDetailOverview.getList().isEmpty()) {
            Map<String, String> dicProvMap = this.dicDataService.getDicAreaMap();
            for (PluginDetailOverviewProvinceInfo pluginDetailOverviewProvinceInfo : pluginDetailOverview.getList()) {
                String areaCode = pluginDetailOverviewProvinceInfo.getName();
                if (StringUtils.isBlank(areaCode) || "-100".equals(areaCode)) {
                    pluginDetailOverviewProvinceInfo.setName("未知");
                } else {
                    if (StringUtils.isNumeric(areaCode)) {
                        String areaName = dicProvMap.getOrDefault(areaCode, areaCode);
                        if (StringUtils.isNumeric(areaName)) {
                            areaName = "未知";
                        }
                        pluginDetailOverviewProvinceInfo.setName(areaName);
                    }
                    if (StringUtils.isBlank(pluginDetailOverviewProvinceInfo.getName()) || "-100".equals(pluginDetailOverviewProvinceInfo.getName())) {
                        pluginDetailOverviewProvinceInfo.setName("未知");
                    }
                }

            }
        }
        return pluginDetailOverview;
    }

    /**
     * 获取实时总数、启用率。
     * 获取分省安装总数，上月活跃率
     * @param pluginDetailOverview
     * @param bundleIds
     * @param lastMonth
     * @param lastTwoMonth
     * @param province
     * @param model
     * @param bundleId
     */
    private void getInstallTotalAndRTCount(PluginDetailOverview pluginDetailOverview,
                                           List<String> bundleIds,
                                           String lastMonth,
                                           String lastTwoMonth,
                                           List<String> province,
                                           List<String> model, String bundleId) {

        long blinkNowUseNum = 0L;
        long allTotal = 0L;
        List<BlinkPluginRTStatVo> blinkPluginRTStatVos = null;
        List<PluginInfoTotalOverviewCount> pluginInfoOverviewTotalCounts = null;

        if (blinkBundleId.equals(bundleId)) {
            //统计blink实时数据 总数
            BlinkPluginRTStatRequest blinkPluginRTStatRequest = new BlinkPluginRTStatRequest();
            blinkPluginRTStatRequest.setProvinceList(province);
            blinkPluginRTStatRequest.setModelList(model);
            blinkPluginRTStatVos = getBlinkRtInfo(blinkPluginRTStatRequest);
            log.info("查询blink实时数据返回: {}", JSONUtil.toJSONString(blinkPluginRTStatVos));
            if (blinkPluginRTStatVos != null && !blinkPluginRTStatVos.isEmpty()) {
                for (BlinkPluginRTStatVo blinkPluginRTStatVo : blinkPluginRTStatVos) {
                    allTotal += blinkPluginRTStatVo.getInstallNum();
                    blinkNowUseNum += blinkPluginRTStatVo.getActiveNum();
                }
            }
        } else {
            //ekit 实时
            pluginInfoOverviewTotalCounts =
                    this.pluginInfoStatisticMapper.getPluginOverviewTotalCountGroupProvince(bundleIds, province, model);
            if (pluginInfoOverviewTotalCounts != null && !pluginInfoOverviewTotalCounts.isEmpty()) {
                for (PluginInfoTotalOverviewCount pluginInfoOverviewTotalCount : pluginInfoOverviewTotalCounts) {
                    allTotal += pluginInfoOverviewTotalCount.getTotalNum();
                }
            }
        }
        List<PluginDetailOverviewProvinceInfo> provinceInfoList = new ArrayList<>();
        pluginDetailOverview.setList(provinceInfoList);
        Map<String, PluginInfoMonthCount> lastMonthProviceMap = getMonthProvinceCountMap(lastMonth, bundleIds, province, model);
        Map<String, PluginInfoMonthCount> lastTwoMonthProviceMap = getMonthProvinceCountMap(lastTwoMonth, bundleIds, province, model);

        //没有时间，计算累计安装量
        //使用实时接口查询总数
        if (blinkBundleId.equalsIgnoreCase(bundleId)) {
            pluginDetailOverview.setInstallTotal(allTotal);

            //列表
            if (blinkPluginRTStatVos != null && !blinkPluginRTStatVos.isEmpty()) {
                blinkPluginRTStatVos.sort(new Comparator<BlinkPluginRTStatVo>() {
                    @Override
                    public int compare(BlinkPluginRTStatVo o1, BlinkPluginRTStatVo o2) {
                        return Integer.compare(o2.getInstallNum(), o1.getInstallNum());
                    }
                });
                for (BlinkPluginRTStatVo blinkPluginRTStatVo : blinkPluginRTStatVos) {
                    PluginDetailOverviewProvinceInfo pluginDetailOverviewProvinceInfo = new PluginDetailOverviewProvinceInfo();
                    String currentProvince = blinkPluginRTStatVo.getProvince();
                    pluginDetailOverviewProvinceInfo.setName(currentProvince);
                    pluginDetailOverviewProvinceInfo.setValue((long) blinkPluginRTStatVo.getInstallNum());
                    pluginDetailOverviewProvinceInfo.setAddNum(0L);
                    pluginDetailOverviewProvinceInfo.setAddRate("0%");

                    if (lastMonthProviceMap.containsKey(currentProvince)) {
                        Long monthProinvceIncr = lastMonthProviceMap.get(currentProvince).getIncrNum();
                        pluginDetailOverviewProvinceInfo.setAddNum(monthProinvceIncr);
                        pluginDetailOverviewProvinceInfo.setAddRate(getPercentWithSuffix(monthProinvceIncr, 1L));
                        if (lastTwoMonthProviceMap.containsKey(currentProvince)) {
                            Long monthProinvceTotal = lastTwoMonthProviceMap.get(currentProvince).getTotalNum();
                            pluginDetailOverviewProvinceInfo.setAddRate(getPercentWithSuffix(monthProinvceIncr, monthProinvceTotal));
                        }
                        lastMonthProviceMap.remove(currentProvince);
                    } else if ("-100".equals(currentProvince)) {
                        if (lastMonthProviceMap.containsKey("")) {
                            Long monthProinvceIncr = lastMonthProviceMap.get("").getIncrNum();
                            pluginDetailOverviewProvinceInfo.setAddNum(monthProinvceIncr);
                            pluginDetailOverviewProvinceInfo.setAddRate(getPercentWithSuffix(monthProinvceIncr, 1L));
                            if (lastTwoMonthProviceMap.containsKey("")) {
                                Long monthProinvceTotal = lastTwoMonthProviceMap.get("").getTotalNum();
                                pluginDetailOverviewProvinceInfo.setAddRate(getPercentWithSuffix(monthProinvceIncr, monthProinvceTotal));
                            }
                            lastMonthProviceMap.remove("");
                        }
                    }
                    provinceInfoList.add(pluginDetailOverviewProvinceInfo);
                }
                try {
                    if (!lastMonthProviceMap.isEmpty()) {
                        for (Map.Entry<String, PluginInfoMonthCount> stringPluginInfoMonthCountEntry :
                                lastMonthProviceMap.entrySet()) {
                            PluginDetailOverviewProvinceInfo pluginDetailOverviewProvinceInfo = new PluginDetailOverviewProvinceInfo();
                            PluginInfoMonthCount pluginInfoMonthCount = stringPluginInfoMonthCountEntry.getValue();
                            pluginDetailOverviewProvinceInfo.setName(pluginInfoMonthCount.getArea());
                            pluginDetailOverviewProvinceInfo.setValue(0L);
                            pluginDetailOverviewProvinceInfo.setAddNum(pluginInfoMonthCount.getIncrNum());

                            if (pluginInfoMonthCount.getIncrNum() < 0) {
                                pluginDetailOverviewProvinceInfo.setAddRate("-100%");
                            } else if (pluginInfoMonthCount.getIncrNum() == 0) {
                                break;
                            } else {
                                pluginDetailOverviewProvinceInfo.setAddRate(getPercentWithSuffix(pluginInfoMonthCount.getIncrNum(), 1L));
                            }

                            if (lastTwoMonthProviceMap.containsKey(pluginInfoMonthCount.getArea())) {
                                Long monthProinvceTotal = lastTwoMonthProviceMap.get(pluginInfoMonthCount.getArea()).getTotalNum();
                                pluginDetailOverviewProvinceInfo.setAddRate(getPercentWithSuffix(pluginInfoMonthCount.getIncrNum(), monthProinvceTotal));
                            }

                            provinceInfoList.add(pluginDetailOverviewProvinceInfo);
                        }
                    }
                } catch (Exception e) {
                    log.info("处理省份列表统计失败: {}", e.getMessage(), e);
                }
            }
        } else {
            //列表
            pluginDetailOverview.setInstallTotal(allTotal);

            if (pluginInfoOverviewTotalCounts != null && !pluginInfoOverviewTotalCounts.isEmpty()) {
                pluginInfoOverviewTotalCounts.sort(new Comparator<PluginInfoTotalOverviewCount>() {
                    @Override
                    public int compare(PluginInfoTotalOverviewCount o1, PluginInfoTotalOverviewCount o2) {
                        return Long.compare(o2.getTotalNum(), o1.getTotalNum());
                    }
                });
                for (PluginInfoTotalOverviewCount pluginInfoTotalOverviewCount : pluginInfoOverviewTotalCounts) {
                    PluginDetailOverviewProvinceInfo pluginDetailOverviewProvinceInfo = new PluginDetailOverviewProvinceInfo();
                    String currentProvince = pluginInfoTotalOverviewCount.getProvince();
                    pluginDetailOverviewProvinceInfo.setName(currentProvince);
                    pluginDetailOverviewProvinceInfo.setValue(pluginInfoTotalOverviewCount.getTotalNum());
                    pluginDetailOverviewProvinceInfo.setAddNum(0L);
                    pluginDetailOverviewProvinceInfo.setAddRate("0%");

                    if (lastMonthProviceMap.containsKey(currentProvince)) {
                        Long monthProinvceIncr = lastMonthProviceMap.get(currentProvince).getIncrNum();
                        pluginDetailOverviewProvinceInfo.setAddNum(monthProinvceIncr);
                        pluginDetailOverviewProvinceInfo.setAddRate(getPercentWithSuffix(monthProinvceIncr, 1L));
                        if (lastTwoMonthProviceMap.containsKey(currentProvince)) {
                            Long monthProinvceTotal = lastTwoMonthProviceMap.get(currentProvince).getTotalNum();
                            pluginDetailOverviewProvinceInfo.setAddRate(getPercentWithSuffix(monthProinvceIncr, monthProinvceTotal));
                        }
                        lastMonthProviceMap.remove(currentProvince);
                    }
                    provinceInfoList.add(pluginDetailOverviewProvinceInfo);
                }
                try {
                    if (!lastMonthProviceMap.isEmpty()) {
                        for (Map.Entry<String, PluginInfoMonthCount> stringPluginInfoMonthCountEntry :
                                lastMonthProviceMap.entrySet()) {
                            PluginDetailOverviewProvinceInfo pluginDetailOverviewProvinceInfo = new PluginDetailOverviewProvinceInfo();
                            PluginInfoMonthCount pluginInfoMonthCount = stringPluginInfoMonthCountEntry.getValue();
                            pluginDetailOverviewProvinceInfo.setName(pluginInfoMonthCount.getArea());
                            pluginDetailOverviewProvinceInfo.setValue(0L);
                            pluginDetailOverviewProvinceInfo.setAddNum(pluginInfoMonthCount.getIncrNum());
                            if (pluginInfoMonthCount.getIncrNum() < 0) {
                                pluginDetailOverviewProvinceInfo.setAddRate("-100%");
                            } else if (pluginInfoMonthCount.getIncrNum() == 0) {
                                break;
                            } else {
                                pluginDetailOverviewProvinceInfo.setAddRate(getPercentWithSuffix(pluginInfoMonthCount.getIncrNum(), 1L));
                            }

                            if (lastTwoMonthProviceMap.containsKey(pluginInfoMonthCount.getArea())) {
                                Long monthProinvceTotal = lastTwoMonthProviceMap.get(pluginInfoMonthCount.getArea()).getTotalNum();
                                pluginDetailOverviewProvinceInfo.setAddRate(getPercentWithSuffix(pluginInfoMonthCount.getIncrNum(), monthProinvceTotal));
                            }
                            provinceInfoList.add(pluginDetailOverviewProvinceInfo);
                        }
                    }
                } catch (Exception e) {
                    log.info("处理省份统计列表失败: {}", e.getMessage(), e);
                }
            }
        }

        //启用率/和时间无关
        if (blinkBundleId.equals(bundleId)) {
            //如果是blink
            pluginDetailOverview.setUseNum(blinkNowUseNum);
            pluginDetailOverview.setUseRate(getPercentWithSuffix(blinkNowUseNum, allTotal));
        } else {
            Integer activeTime = pluginUseActiveTime;
            List<PluginInfoTotalOverviewCount> pluginInfoOverviewActiveCounts =
                    this.pluginInfoStatisticMapper.getPluginOverviewActiveCount(activeTime, bundleIds, province, model);

            if (pluginInfoOverviewActiveCounts != null && !pluginInfoOverviewActiveCounts.isEmpty()) {
                PluginInfoTotalOverviewCount pluginInfoOverviewActiveCount = CollectionUtil.getFirst(pluginInfoOverviewActiveCounts);
                Long installTotal = pluginDetailOverview.getInstallTotal();
                Long usingNum = pluginInfoOverviewActiveCount.getUsingNum();
                if (usingNum >= installTotal) {
                    usingNum = installTotal;
                }
                pluginDetailOverview.setUseNum(usingNum);
                pluginDetailOverview.setUseRate(getPercentWithSuffix(usingNum, installTotal));
            }
        }
    }

    private List<BlinkPluginRTStatVo> getBlinkRtInfo(BlinkPluginRTStatRequest blinkPluginRTStatRequest) {
        List<BlinkPluginRTStatVo> pluginRTStatVos =
                this.blinkPluginStatFeignClient.getBlinkPluginRTStat(blinkPluginRTStatRequest);
        Map<String, BlinkPluginRTStatVo> pluginRTStatVoMap = new HashMap<>();
        if (pluginRTStatVos != null && !pluginRTStatVos.isEmpty()) {
            Map<String, String> dicProvMap = this.dicDataService.getDicAreaMap();
            for (BlinkPluginRTStatVo pluginRTStatVo : pluginRTStatVos) {
                String province = pluginRTStatVo.getProvince();
                if (!dicProvMap.containsKey(province)) {
                    province = "";
                    pluginRTStatVo.setProvince(province);
                }
                if (pluginRTStatVoMap.containsKey(province)) {
                    BlinkPluginRTStatVo oldVo = pluginRTStatVoMap.get(province);
                    oldVo.setInstallNum(oldVo.getInstallNum() + pluginRTStatVo.getInstallNum());
                    oldVo.setActiveNum(oldVo.getActiveNum() + pluginRTStatVo.getActiveNum());
                } else {
                    pluginRTStatVoMap.put(province, pluginRTStatVo);
                }
            }
        }
        return new ArrayList<>(pluginRTStatVoMap.values());
    }

    private Map<String, PluginInfoMonthCount> getMonthProvinceCountMap(String lastMonth,
                                                                       List<String> bundleIds,
                                                                       List<String> province,
                                                                       List<String> model) {
        List<PluginInfoMonthCount> areaPluginInfoLastMonthCounts =
                this.pluginInfoStatisticMapper.getPluginInfoOverviewGroupProvince(lastMonth, bundleIds, province, model);
        Map<String, PluginInfoMonthCount> monthProviceMap = new HashMap<>();
        if (areaPluginInfoLastMonthCounts != null && !areaPluginInfoLastMonthCounts.isEmpty()) {
            for (PluginInfoMonthCount areaPluginInfoLastMonthCount : areaPluginInfoLastMonthCounts) {
                if (areaPluginInfoLastMonthCount != null) {
                    monthProviceMap.put(areaPluginInfoLastMonthCount.getArea(), areaPluginInfoLastMonthCount);
                }
            }
        }
        return monthProviceMap;
    }

    /**
     * 从月统计中获取统计数据
     * 月新增数量，环比新增百分比
     * @param pluginDetailOverview
     * @param bundleIds
     * @param lastMonth
     * @param lastTwoMonth
     * @param province
     * @param model
     */
    private void getFromMonthCountOverview(PluginDetailOverview pluginDetailOverview,
                                           List<String> bundleIds,
                                           String lastMonth,
                                           String lastTwoMonth,
                                           List<String> province,
                                           List<String> model) {
        //获取月新增数量、新增百分比
        List<PluginInfoMonthCount> pluginInfoMonthCountList =
                this.pluginInfoStatisticMapper.getPluginInfoOverview(lastMonth, bundleIds, province, model);
        if (pluginInfoMonthCountList != null && !pluginInfoMonthCountList.isEmpty()) {
            for (PluginInfoMonthCount pluginInfoMonthCount : pluginInfoMonthCountList) {
                Long monthIncrNum = pluginInfoMonthCount.getIncrNum();
                pluginDetailOverview.setMonthAddNum(monthIncrNum);
                pluginDetailOverview.setMonthAddRate(getPercentWithSuffix(monthIncrNum, 1L));
            }

            if (StringUtils.isNotBlank(lastTwoMonth)) {
                List<PluginInfoMonthCount> pluginInfoMonthCountLastTwoMonthList =
                        this.pluginInfoStatisticMapper.getPluginInfoOverview(lastTwoMonth, bundleIds, province, model);
                if (pluginInfoMonthCountLastTwoMonthList != null && !pluginInfoMonthCountLastTwoMonthList.isEmpty()) {
                    PluginInfoMonthCount pluginInfoMonthCount = CollectionUtil.getFirst(pluginInfoMonthCountLastTwoMonthList);
                    Long incrNum = pluginDetailOverview.getMonthAddNum();
                    Long totalNum = pluginInfoMonthCount.getTotalNum();
                    pluginDetailOverview.setMonthAddRate(getPercentWithSuffix(incrNum, totalNum));
                }
            }
        }
    }

    /**
     * 从日统计中获取数据，30天活跃数量（和百分比），疑似弃用数量
     * @param pluginDetailOverview
     * @param bundleIds
     * @param province
     * @param model
     */
    private void getFromDayCountOverview(PluginDetailOverview pluginDetailOverview,
                                         List<String> bundleIds,
                                         List<String> province,
                                         List<String> model) {

        //获取前30天活跃数量、百分比、疑似弃用数
        String lastDay = DateUtil.getYesterday();
        List<PluginInfoDayCount> pluginInfoDayCounts =
                this.pluginInfoStatisticMapper.getPluginInfoDayOverview(lastDay, bundleIds, province, model);
        if (pluginInfoDayCounts != null && !pluginInfoDayCounts.isEmpty()) {
            PluginInfoDayCount pluginInfoDayCount = CollectionUtil.getFirst(pluginInfoDayCounts);
            //弃用数量
            pluginDetailOverview.setDeprecatedNum(pluginInfoDayCount.getNoUseNum());
            //30天活跃数量，和时间筛选无关
            pluginDetailOverview.setMonthActiveNum(pluginInfoDayCount.getThirtyDayActiveNum());
            //百分比
            if (pluginInfoDayCount.getThirtyDayActiveNum() <= 0) {
                pluginDetailOverview.setMonthActiveRate("0%");
            } else if (pluginInfoDayCount.getThirtyDayActiveNum() >= pluginInfoDayCount.getTotalNum()) {
                pluginDetailOverview.setMonthActiveRate("100%");
            } else {
                pluginDetailOverview.setMonthActiveRate(getPercentWithSuffix(pluginInfoDayCount.getThirtyDayActiveNum(), pluginInfoDayCount.getTotalNum()));
            }
        }

    }

    /**
     * 获取截止日期的疑似弃用总数
     * @param pluginDetailOverview
     * @param endTime
     * @param bundleIds
     * @param province
     * @param model
     */
    private void getDeprecatedNumByTime(PluginDetailOverview pluginDetailOverview,
                                        String endTime,
                                        List<String> bundleIds,
                                        List<String> province,
                                        List<String> model) {
        try {
            String queryEndDay = "";
            if (DateUtil.stringToDateFormat(endTime + " 23:59:59").after(new Date())) {
                queryEndDay = DateUtil.getBeforeDay(1, DateUtil.MONTH_DATE_PATTERN);
            } else {
                queryEndDay = DateUtil.formatFromTo(endTime, DateUtil.DATE_PATTERN, DateUtil.MONTH_DATE_PATTERN);
            }

            List<PluginInfoDayCount> pluginInfoDayCountsNoUse =
                    this.pluginInfoStatisticMapper.getPluginInfoDayOverview(queryEndDay, bundleIds, province, model);
            if (pluginInfoDayCountsNoUse != null && !pluginInfoDayCountsNoUse.isEmpty()) {
                PluginInfoDayCount pluginInfoDayCount = CollectionUtil.getFirst(pluginInfoDayCountsNoUse);
                pluginDetailOverview.setDeprecatedNum(pluginInfoDayCount.getNoUseNum());
            }
        } catch (Exception e) {
            pluginDetailOverview.setDeprecatedNum(0L);
        }
    }

    /**
     * 获取周期内新增总数
     * 各省新增数量
     * @param pluginDetailOverview
     * @param bundleIds
     * @param province
     * @param model
     * @param startTime
     * @param endTime
     */
    private void getProinvceIncrMap(PluginDetailOverview pluginDetailOverview,
                                    List<String> bundleIds,
                                    List<String> province,
                                    List<String> model,
                                    String startTime,
                                    String endTime) {
        //如果有筛选时间，计算周期内的新增数量
        long allIncr = 0L;
        List<PluginInfoDayCount> areaPluginInfoDayCountList = null;
        try {
            areaPluginInfoDayCountList =
                    this.pluginInfoStatisticMapper.getPluginInfoIncrOverview(startTime, endTime, bundleIds, province, model);
        } catch (Exception ignore) {
        }
        List<PluginDetailOverviewProvinceInfo> provinceInfoList = new ArrayList<>();
        if (areaPluginInfoDayCountList != null && !areaPluginInfoDayCountList.isEmpty()) {
            areaPluginInfoDayCountList.sort(new Comparator<PluginInfoDayCount>() {
                @Override
                public int compare(PluginInfoDayCount o1, PluginInfoDayCount o2) {
                    return Long.compare(o2.getIncrNum(), o1.getIncrNum());
                }
            });
            for (PluginInfoDayCount pluginInfoDayCount : areaPluginInfoDayCountList) {
                allIncr += pluginInfoDayCount.getIncrNum();
                if (pluginInfoDayCount.getIncrNum() != 0) {
                    //列表
                    PluginDetailOverviewProvinceInfo pluginDetailOverviewProvinceInfo = new PluginDetailOverviewProvinceInfo();
                    String currentProvince = pluginInfoDayCount.getArea();
                    pluginDetailOverviewProvinceInfo.setName(currentProvince);
                    pluginDetailOverviewProvinceInfo.setValue(pluginInfoDayCount.getIncrNum());
                    pluginDetailOverviewProvinceInfo.setAddNum(null);
                    pluginDetailOverviewProvinceInfo.setAddRate(null);

                    provinceInfoList.add(pluginDetailOverviewProvinceInfo);
                }
            }
        }
        pluginDetailOverview.setList(provinceInfoList);
        pluginDetailOverview.setInstallTotal(allIncr);
    }


    private static String getPercent(Long c1, Long c2) {
        String s = "--";
        try {
            if (c1 == null || c2 == null || c2.intValue() == 0) {
                return "--";
            }
            s = NumberUtil.decimalFormat("#.##%", NumberUtil.div(new BigDecimal(c1), new BigDecimal(c2), 4)).replace("%", "");
        } catch (Exception e) {
            log.info("exception:", e);
        }
        return s;
    }

    private static String getPercent2(Long c1, Long c2) {
        String s = "0";
        try {
            if (c1 == null || c2 == null || c2.intValue() == 0) {
                return "0";
            }
            s = NumberUtil.decimalFormat("#.##%", NumberUtil.div(new BigDecimal(c1), new BigDecimal(c2), 4)).replace("%", "");
        } catch (Exception e) {
            log.info("exception:", e);
        }
        return s;
    }

    private static String getPercent3(Long c1, Long c2) {
        String s = "0";
        try {
            if (c1 == null || c2 == null || c2 == 0) {
                return "";
            }
            s = NumberUtil.decimalFormat("#.##%", NumberUtil.div(new BigDecimal(c1), new BigDecimal(c2), 4)).replace("%", "");
        } catch (Exception e) {
            log.info("getPercent3 exception:", e);
        }
        return s + "%";
    }


    private static String getPercent4(Long c1, Long c2) {
        String s = "0";
        try {
            if (c1 == null || c2 == null || c2 == 0) {
                return "";
            }
            //(c1-c2)/c2   （8月-7月）/7月
            BigDecimal sub = NumberUtil.sub(new BigDecimal(c1), new BigDecimal(c2));
            s = NumberUtil.decimalFormat("#.##%", NumberUtil.div(sub, new BigDecimal(c2), 4)).replace("%", "");
        } catch (Exception e) {
            log.info("getPercent4 exception:", e);
        }
        return s + "%";
    }


    private static String getPercentWithSuffix(Long c1, Long c2) {
        return getPercent2(c1, c2) + "%";
    }

    public static void main(String[] args) {
        List<Integer> s = new ArrayList<>();
        for (int i = 0; i <= 99; i++) {
            s.add(i);
        }
        for (int i = 1; i <= 10; i++) {
            PageInfoParam pageInfoParam = new PageInfoParam();
            pageInfoParam.setPage(i);
            pageInfoParam.setPageSize(10);
            System.out.println(s.subList((int) pageInfoParam.getOffset(),
                    Math.min((int) (pageInfoParam.getOffset() + pageInfoParam.getSize()), s.size())));
        }
/*        System.out.println(getPercent(0L,1L));
        System.out.println(getPercent(1L,1L));
        System.out.println(getPercent(100L,100L));
        System.out.println(getPercent(100L,1L));*/

  /*      for (int i = 0; i < 1000; i++)  {
            System.out.println(i + ": " + getPercent((long) i,10000L));
        }*/
    }


    @Override
    public PageRes<PluginCompositeOverview> getPluginInfoStatistic(Integer column, Integer order, Integer page, Integer pageSize) {
        if (column == null || (column < 1 || column > 5)) {
            column = 1;
        }
        if (order == null || (order != 1 && order != 2)) {
            order = 1;
        }

        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 1;
        }
        PageRes<PluginCompositeOverview> pageRes = new PageRes<>();
        pageRes.setPage(page);
        pageRes.setPageSize(pageSize);
        pageRes.setTotal(0L);
        pageRes.setList(new ArrayList<>());

        PageInfoParam pageInfoParam = new PageInfoParam();
        pageInfoParam.setPage(page);
        pageInfoParam.setPageSize(pageSize);
        //获取全部OSGi插件列表  目前就只有一个插件，且上报数据并未关联插件类型
        List<PluginBaseInfo> pluginInfoBaseDtos = this.pluginInfoStatisticMapper.getPluginOsgiBaseInfo(null);
        if (pluginInfoBaseDtos == null || pluginInfoBaseDtos.isEmpty()) {
            return pageRes;
        }
        List<PluginStatisticInfo> pluginOsgiInstallCountGroupVersion = this.pluginInfoStatisticMapper.getPluginOsgiInstallCountGroupVersion();
        if (pluginOsgiInstallCountGroupVersion == null || pluginOsgiInstallCountGroupVersion.isEmpty()) {
            return pageRes;
        }
        List<PluginCompositeOverview> pluginCompositeOverviewList = new ArrayList<>();
        Set<String> pluginNameSet = pluginOsgiInstallCountGroupVersion.stream().map(PluginStatisticInfo::getPluginName).collect(Collectors.toSet());
        pageRes.setTotal((long)pluginNameSet.size());
        for (String pluginName : pluginNameSet) {
            for (PluginBaseInfo pluginBaseInfo : pluginInfoBaseDtos) {
                if (StrUtil.isNotBlank(pluginName) && pluginName.equals(pluginBaseInfo.getPluginNum())) {
                    PluginCompositeOverview overview = createOverview(pluginBaseInfo);
                    pluginCompositeOverviewList.add(overview);
                }
            }
        }

        List<PluginStatisticInfo> pluginOsgiInstallCount = this.pluginInfoStatisticMapper.getPluginOsgiInstallCount(null, null, null, null, null, null);
        List<PluginStatisticInfo> pluginOsgiInstallCountLastMoth = this.pluginInfoStatisticMapper.getPluginOsgiInstallCount(null, null, null, null, null, 1);
        List<PluginStatisticInfo> pluginOsgiInstallCountTwoMonth = this.pluginInfoStatisticMapper.getPluginOsgiInstallCount(null, null, null, null, null, 2);
        List<PluginStatisticInfo> pluginOsgiDayLiveNum = this.pluginInfoStatisticMapper.getPluginOsgiDayLiveNum(null);
        List<PluginStatisticInfo> pluginOsgiMonthLiveNum = this.pluginInfoStatisticMapper.getPluginOsgiMonthLiveNum(null);
        for (PluginCompositeOverview pluginCompositeOverview : pluginCompositeOverviewList) {
            //统计插件当前安装总量
            if (pluginOsgiInstallCount != null && !pluginOsgiInstallCount.isEmpty()) {
                for (PluginStatisticInfo pluginStatisticInfo : pluginOsgiInstallCount) {
                    if (pluginCompositeOverview.getPluginNum().equals(pluginStatisticInfo.getPluginName())) {
                        long total = pluginStatisticInfo.getTotal().longValue();
                        pluginCompositeOverview.setInstallTotal(total);
                    }
                }
            }
            //环比和同比主要区别如下：
            //1、环比是将当期数据与上期数据做对比；而同比是将当期数据与上年同期数据做对比；
            //2、环比常用在月、日上进行对比，而同比常用在相邻两年的相同月份之间进行对比。
            //环比和同比两者所反映的虽然都是变化速度，但由于采用基期的不同，其反映的内涵是完全不同的。环比和同比的计算公式具体如下：
            //环比增长率=(本期某指标数值-上期该指标数值)÷上期该指标数值×100%；
            //同比增长率=(当年某指标数值-上年同期该指标数值)÷上年同期该指标数值×100%。

            //统计环比新增
            long lastMothInstallCount = 0L;
            if (pluginOsgiInstallCountLastMoth != null && !pluginOsgiInstallCountLastMoth.isEmpty()) {
                for (PluginStatisticInfo statisticInfo : pluginOsgiInstallCountLastMoth) {
                    if (pluginCompositeOverview.getPluginNum().equals(statisticInfo.getPluginName())) {
                        lastMothInstallCount = statisticInfo.getTotal().longValue();
                    }
                }
            }
            long twoMonthInstallCount = 0L;
            if (pluginOsgiInstallCountTwoMonth != null && !pluginOsgiInstallCountTwoMonth.isEmpty()) {
                for (PluginStatisticInfo statisticInfo : pluginOsgiInstallCountTwoMonth) {
                    if (pluginCompositeOverview.getPluginNum().equals(statisticInfo.getPluginName())) {
                        twoMonthInstallCount = statisticInfo.getTotal().longValue();
                    }
                }
            }
            pluginCompositeOverview.setMonthAddNum(lastMothInstallCount);
            pluginCompositeOverview.setMonthAddRate(getPercent4(lastMothInstallCount, twoMonthInstallCount));

            Long installTotal = pluginCompositeOverview.getInstallTotal();
            //统计日活数量、比例 = 前1天的活跃数量/总量
            if (pluginOsgiDayLiveNum != null && !pluginOsgiDayLiveNum.isEmpty()) {
                for (PluginStatisticInfo pluginStatisticInfo : pluginOsgiDayLiveNum) {
                    if (pluginCompositeOverview.getPluginNum().equals(pluginStatisticInfo.getPluginName())) {
                        long total = pluginStatisticInfo.getTotal().longValue();
                        pluginCompositeOverview.setDayActiveNum(total);
                        pluginCompositeOverview.setDayActiveRate(getPercent3(total, installTotal));
                    }
                }
            }
            //统计月活数量、比例 = 前30天的活跃数量/总量
            if (pluginOsgiMonthLiveNum != null && !pluginOsgiMonthLiveNum.isEmpty()) {
                for (PluginStatisticInfo pluginStatisticInfo : pluginOsgiMonthLiveNum) {
                    if (pluginCompositeOverview.getPluginNum().equals(pluginStatisticInfo.getPluginName())) {
                        long total = pluginStatisticInfo.getTotal().longValue();
                        pluginCompositeOverview.setMonthActiveNum(total);
                        pluginCompositeOverview.setMonthActiveRate(getPercent3(total, installTotal));
                    }
                }
            }

            // 每个版本的统计数据
            long sum = pluginOsgiInstallCountGroupVersion.stream().filter(o -> o.getPluginName().equals(pluginCompositeOverview.getPluginNum()))
                    .mapToLong(o -> o.getTotal().longValue()).sum();
            List<PluginDetailOverviewProvinceInfo> list = new ArrayList<>();
            for (PluginStatisticInfo pluginOsgiInstallCountByParam : pluginOsgiInstallCountGroupVersion) {
                if (pluginCompositeOverview.getPluginNum().equals(pluginOsgiInstallCountByParam.getPluginName())) {
                    PluginDetailOverviewProvinceInfo pluginDetailOverviewProvinceInfo = new PluginDetailOverviewProvinceInfo();
                    pluginDetailOverviewProvinceInfo.setName(StrUtil.isNotBlank(pluginOsgiInstallCountByParam.getPluginVersion()) ?  pluginOsgiInstallCountByParam.getPluginVersion() : "其它");
                    long total = pluginOsgiInstallCountByParam.getTotal().longValue();
                    pluginDetailOverviewProvinceInfo.setValue(total);
                    pluginDetailOverviewProvinceInfo.setAddRate(getPercent3(total, sum));
                    list.add(pluginDetailOverviewProvinceInfo);
                }
            }
            pluginCompositeOverview.setList(list);
        }
        log.info("插件概览原始统计数据: {}", JSONUtil.toJSONString(pluginCompositeOverviewList));

        //排序
        //column 1 总安装量 2 环比新增 3 插件启用率 4月活数量 5 疑似启用
        //order 1 降序 2 升序

        Integer finalColumn = column;
        int finalOrder;
        if (order == 2) {
            finalOrder = 1;
        } else {
            finalOrder = -1;
        }

        log.info("插件概览原始统计数据: {}", JSONUtil.toJSONString(pluginCompositeOverviewList));

        pluginCompositeOverviewList.sort(new Comparator<PluginCompositeOverview>() {
            @Override
            public int compare(PluginCompositeOverview o1, PluginCompositeOverview o2) {
                switch (finalColumn) {
                    case 3: {
                        long o1Num = o1.getMonthAddNum() != null ? o1.getMonthAddNum() : 0L;
                        long o2Num = o2.getMonthAddNum() != null ? o2.getMonthAddNum() : 0L;
                        if (o1Num > o2Num) {
                            return finalOrder;
                        } else if (o1Num == o2Num) {
                            return 0;
                        } else {
                            return -1 * finalOrder;
                        }
                    }
                    case 4: {
                        long o1Num = o1.getMonthActiveNum() != null ? o1.getMonthActiveNum() : 0L;
                        long o2Num = o2.getMonthActiveNum() != null ? o2.getMonthActiveNum() : 0L;
                        if (o1Num > o2Num) {
                            return finalOrder;
                        } else if (o1Num == o2Num) {
                            return 0;
                        } else {
                            return -1 * finalOrder;
                        }
                    }
                    case 5: {
                        long o1Num = o1.getDayActiveNum() != null ? o1.getDayActiveNum() : 0L;
                        long o2Num = o2.getDayActiveNum() != null ? o2.getDayActiveNum() : 0L;
                        if (o1Num > o2Num) {
                            return finalOrder;
                        } else if (o1Num == o2Num) {
                            return 0;
                        } else {
                            return -1 * finalOrder;
                        }
                    }
                    default: {
                        long o1InstallTotal = o1.getInstallTotal() != null ? o1.getInstallTotal() : 0L;
                        long o2InstallTotal = o2.getInstallTotal() != null ? o2.getInstallTotal() : 0L;
                        if (o1InstallTotal > o2InstallTotal) {
                            return finalOrder;
                        } else if (o1InstallTotal == o2InstallTotal) {
                            return 0;
                        } else {
                            return -1 * finalOrder;
                        }
                    }
                }
            }
        });


        log.info("插件概览排序后统计数据: {}", JSONUtil.toJSONString(pluginCompositeOverviewList));


        //分页
        pluginCompositeOverviewList = pluginCompositeOverviewList.subList((int) pageInfoParam.getOffset(),
                Math.min((int) (pageInfoParam.getOffset() + pageInfoParam.getSize()), pluginCompositeOverviewList.size()));
            pageRes.setList(pluginCompositeOverviewList);

        return pageRes;
    }


    private PluginCompositeOverview createOverview(PluginBaseInfo pluginBaseInfo) {
        PluginCompositeOverview pluginCompositeOverview = new PluginCompositeOverview();
        pluginCompositeOverview.setId(pluginBaseInfo.getId());
        pluginCompositeOverview.setPluginName(pluginBaseInfo.getPluginName());
        pluginCompositeOverview.setPluginNum(pluginBaseInfo.getPluginNum());
        pluginCompositeOverview.setManufacture(pluginBaseInfo.getManufacture());
        pluginCompositeOverview.setVersion(pluginBaseInfo.getVersion());
        pluginCompositeOverview.setIsPreset(pluginBaseInfo.getIsPreset() == null ? null : "1".equals(pluginBaseInfo.getIsPreset()) ? "是" : "否");
        List<Long> ids = new ArrayList<>();
        ids.add(Long.parseLong(pluginBaseInfo.getCreateId()));
//        List<EntUserInfo> enterpriseUserByIds = enterpriseFeignClient.findEnterpriseUserByIds(ids);
//        if (enterpriseUserByIds != null && !enterpriseUserByIds.isEmpty()) {
//            EntUserInfo entUserInfo = enterpriseUserByIds.get(0);
//            pluginCompositeOverview.setCreator(entUserInfo.getUserName());
//        }
        pluginCompositeOverview.setPluginDes(pluginBaseInfo.getPluginDes());
        //设置默认值
        pluginCompositeOverview.setInstallTotal(0L);
        pluginCompositeOverview.setMonthAddNum(0L);
        pluginCompositeOverview.setMonthAddRate("0");
        pluginCompositeOverview.setMonthActiveNum(0L);
        pluginCompositeOverview.setMonthActiveRate("0%");
        pluginCompositeOverview.setDayActiveNum(0L);
        pluginCompositeOverview.setDayActiveRate("0%");
        return pluginCompositeOverview;
    }



    @Override
    public PluginDetailOverview getPluginInfoStatisticDetail(GetPluginDetailOverviewPar pluginInfoStatisticDetailPar) {
        //初始化数据
        PluginDetailOverview pluginDetailOverview = new PluginDetailOverview();
        pluginDetailOverview.setInstallTotal(0L);
        pluginDetailOverview.setDayActiveNum(0L);
        pluginDetailOverview.setDayActiveRate("0%");
        pluginDetailOverview.setMonthActiveNum(0L);
        pluginDetailOverview.setMonthActiveRate("0%");
        pluginDetailOverview.setMonthAddNum(0L);
        pluginDetailOverview.setMonthAddRate("0%");
        pluginDetailOverview.setVersionUpdateNum(0L);
        pluginDetailOverview.setVersionUpdateRate("0%");
        pluginDetailOverview.setList(new ArrayList<>());

        Integer pluginId = pluginInfoStatisticDetailPar.getId();
        if (pluginId == null) {
            return pluginDetailOverview;
        }
        List<PluginBaseInfo> pluginInfoBaseDtos = this.pluginInfoStatisticMapper.getPluginOsgiBaseInfo(pluginId);
        if (pluginInfoBaseDtos == null || pluginInfoBaseDtos.size() == 0) {
            return pluginDetailOverview;
        }

        List<String> province = pluginInfoStatisticDetailPar.getProvince();
        if (province != null && province.isEmpty()) {
            province = null;
        }
        List<String> model = pluginInfoStatisticDetailPar.getModel();
        if (model != null && model.isEmpty()) {
            model = null;
        }
        List<String> factory = pluginInfoStatisticDetailPar.getFactory();
        if (factory != null && factory.isEmpty()) {
            factory = null;
        }
        String startTime = pluginInfoStatisticDetailPar.getStartTime();
        String endTime = pluginInfoStatisticDetailPar.getEndTime();
        String version = pluginInfoStatisticDetailPar.getVersion();
        PluginBaseInfo pluginBaseInfo = pluginInfoBaseDtos.get(0);
        String pluginNum = pluginBaseInfo.getPluginNum();

        Map<String, String> codeNames = new HashMap<>();
        List<DicAreaInfo> dicAreaInfos = dicAreaInfoMapper.selectByExample(null);
        for (DicAreaInfo dicAreaInfo : dicAreaInfos) {
            codeNames.put(dicAreaInfo.getGcode(), dicAreaInfo.getGname());
        }
        //插件安装总量|日活数量|月活数量|环比新增|版本更新占比：已更新到插件最新版本的设备数量/当前已安装插件的设备数量
        //选择时间段后，日活、月活、同比、更新占比不显示

        //弹框展示字段：
        //省份名称
        //插件安装总量
        //插件覆盖率（选择时间段后不显示）
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            //设置插件安装总量， 各省插件安装总量
            List<PluginStatisticInfo> pluginOsgiInstallCountByParams = this.pluginInfoStatisticMapper.getPluginOsgiInstallCountByParams(pluginNum, province, factory, model, version, startTime, endTime);
            long sum = pluginOsgiInstallCountByParams.stream().mapToLong(o -> o.getTotal().longValue()).sum();
            List<PluginDetailOverviewProvinceInfo> list = new ArrayList<>();
            for (PluginStatisticInfo pluginOsgiInstallCountByParam : pluginOsgiInstallCountByParams) {
                PluginDetailOverviewProvinceInfo pluginDetailOverviewProvinceInfo = new PluginDetailOverviewProvinceInfo();
                pluginDetailOverviewProvinceInfo.setName(codeNames.getOrDefault(pluginOsgiInstallCountByParam.getProvince(), "其它"));
                pluginDetailOverviewProvinceInfo.setValue(pluginOsgiInstallCountByParam.getTotal().longValue());
                list.add(pluginDetailOverviewProvinceInfo);
            }
            pluginDetailOverview.setInstallTotal(sum);
            pluginDetailOverview.setList(list); //插件启用省份排名
            pluginDetailOverview.setDayActiveNum(null);
            pluginDetailOverview.setDayActiveRate(null);
            pluginDetailOverview.setMonthActiveNum(null);
            pluginDetailOverview.setMonthActiveRate(null);
            pluginDetailOverview.setMonthAddNum(null);
            pluginDetailOverview.setMonthAddRate(null);
            pluginDetailOverview.setVersionUpdateNum(null);
            pluginDetailOverview.setVersionUpdateRate(null);
        } else {
            //每个省的网关数量
            List<PluginStatisticInfo> gatewayCountByGroupProvince = this.pluginInfoStatisticMapper.getGatewayCountByGroupProvince(province);
            //设置插件安装总量， 各省插件安装总量
            List<PluginStatisticInfo> pluginOsgiInstallCountByParams = this.pluginInfoStatisticMapper.getPluginOsgiInstallCountByParams(pluginNum, province, factory, model, version, null, null);
            long sum = pluginOsgiInstallCountByParams.stream().mapToLong(o -> o.getTotal().longValue()).sum();
            List<PluginDetailOverviewProvinceInfo> list = new ArrayList<>();
            for (PluginStatisticInfo pluginOsgiInstallCountByParam : pluginOsgiInstallCountByParams) {
                PluginDetailOverviewProvinceInfo pluginDetailOverviewProvinceInfo = new PluginDetailOverviewProvinceInfo();
                pluginDetailOverviewProvinceInfo.setName(codeNames.getOrDefault(pluginOsgiInstallCountByParam.getProvince(), "其它"));
                pluginDetailOverviewProvinceInfo.setValue(pluginOsgiInstallCountByParam.getTotal().longValue());
                //插件覆盖率 = 各个省的插件安装总量/各个省政企网关总量
                for (PluginStatisticInfo gatewayCount : gatewayCountByGroupProvince) {
                    if (gatewayCount.getProvince().equals(pluginOsgiInstallCountByParam.getProvince())) {
                        long installTotal = pluginOsgiInstallCountByParam.getTotal().longValue();
                        long gatewayTotal = gatewayCount.getTotal().longValue();
                        pluginDetailOverviewProvinceInfo.setAddRate(getPercent3(installTotal, gatewayTotal));
                    }
                }
                list.add(pluginDetailOverviewProvinceInfo);
            }
            pluginDetailOverview.setInstallTotal(sum);
            pluginDetailOverview.setList(list);
            LocalDate now = LocalDate.now();
            LocalDate yesterday = now.minusDays(1);
            LocalDate lastMoth = now.minusMonths(1);
            //日活数量：近一天插件活跃设备数量/当前插件安装总量
            Long lastDayActiveNum = this.pluginInfoStatisticMapper.getPluginOsgiLiveNumByParams(pluginNum, province, factory, model, version);
            pluginDetailOverview.setDayActiveNum(lastDayActiveNum);
            pluginDetailOverview.setDayActiveRate(getPercent3(lastDayActiveNum, sum));
            //月活数量：近一月插件活跃设备数量/当前插件安装总量
            Long lastMonthActiveNum = this.pluginInfoStatisticMapper.getPluginOsgiMonthLiveNumByParams(pluginNum, province, factory, model, version);
            pluginDetailOverview.setMonthActiveNum(lastMonthActiveNum);
            pluginDetailOverview.setMonthActiveRate(getPercent3(lastMonthActiveNum, sum));
            //环比增长率=(本期某指标数值-上期该指标数值)÷上期该指标数值×100%；
            List<PluginStatisticInfo> pluginOsgiInstallCountLastMoth = this.pluginInfoStatisticMapper.getPluginOsgiInstallCount(pluginNum, province, factory, model, version, 1);
            long lastMothInstallCount = 0L;
            if (pluginOsgiInstallCountLastMoth != null && !pluginOsgiInstallCountLastMoth.isEmpty()) {
                PluginStatisticInfo pluginStatisticInfo = pluginOsgiInstallCountLastMoth.get(0);
                lastMothInstallCount = pluginStatisticInfo.getTotal().longValue();
            }
            long twoMonthInstallCount = 0L;
            List<PluginStatisticInfo> pluginOsgiInstallCountTwoMonth = this.pluginInfoStatisticMapper.getPluginOsgiInstallCount(pluginNum, province, factory, model, version,2);
            if (pluginOsgiInstallCountTwoMonth != null && !pluginOsgiInstallCountTwoMonth.isEmpty()) {
                PluginStatisticInfo pluginStatisticInfo = pluginOsgiInstallCountTwoMonth.get(0);
                twoMonthInstallCount = pluginStatisticInfo.getTotal().longValue();
            }
            pluginDetailOverview.setMonthAddNum(lastMothInstallCount);
            pluginDetailOverview.setMonthAddRate(getPercent4(lastMothInstallCount, twoMonthInstallCount));
            //版本占比：选中版本的设备数量/当前已安装插件的设备数量
            List<PluginStatisticInfo> pluginOsgiInstallCount = this.pluginInfoStatisticMapper.getPluginOsgiInstallCountByParams(pluginNum, province, factory, model, null, null, null);
            long allVersionSum = pluginOsgiInstallCount.stream().mapToLong(o -> o.getTotal().longValue()).sum();
            List<PluginStatisticInfo> lastVersionInstallCount = this.pluginInfoStatisticMapper.getPluginOsgiInstallCount(pluginNum, province, factory, model, version,null);
            long updateNum = 0L;
            if (lastVersionInstallCount != null && !lastVersionInstallCount.isEmpty()) {
                PluginStatisticInfo pluginStatisticInfo = lastVersionInstallCount.get(0);
                updateNum = pluginStatisticInfo.getTotal().longValue();
            }
            pluginDetailOverview.setVersionUpdateNum(updateNum);
            pluginDetailOverview.setVersionUpdateRate(getPercent3(updateNum, allVersionSum));
        }

        if (pluginDetailOverview.getList() != null && !pluginDetailOverview.getList().isEmpty()) {
            Map<String, String> dicProvMap = this.dicDataService.getDicAreaMap();
            for (PluginDetailOverviewProvinceInfo pluginDetailOverviewProvinceInfo : pluginDetailOverview.getList()) {
                String areaCode = pluginDetailOverviewProvinceInfo.getName();
                if (StringUtils.isBlank(areaCode) || "-100".equals(areaCode)) {
                    pluginDetailOverviewProvinceInfo.setName("未知");
                } else {
                    if (StringUtils.isNumeric(areaCode)) {
                        String areaName = dicProvMap.getOrDefault(areaCode, areaCode);
                        if (StringUtils.isNumeric(areaName)) {
                            areaName = "未知";
                        }
                        pluginDetailOverviewProvinceInfo.setName(areaName);
                    }
                    if (StringUtils.isBlank(pluginDetailOverviewProvinceInfo.getName()) || "-100".equals(pluginDetailOverviewProvinceInfo.getName())) {
                        pluginDetailOverviewProvinceInfo.setName("未知");
                    }
                }
            }
        }
        return pluginDetailOverview;
    }

    @Override
    public PluginBaseInfo getPluginInfo(Integer pluginId) {
        List<PluginBaseInfo> pluginInfoBaseDtos = this.pluginInfoStatisticMapper.getPluginOsgiBaseInfo(pluginId);
        if (pluginInfoBaseDtos != null && pluginInfoBaseDtos.size() > 0) {
            PluginBaseInfo pluginBaseInfo = pluginInfoBaseDtos.get(0);
            List<String> idList = pluginInfoBaseDtos.stream().map(PluginBaseInfo::getCreateId).collect(Collectors.toList());
            List<Long> ids = new ArrayList<>();
            for (String s : idList) {
                ids.add(Long.parseLong(s));
            }
            List<EntUserInfo> enterpriseUserByIds = enterpriseFeignClient.findEnterpriseUserByIds(ids);
            if (enterpriseUserByIds != null && enterpriseUserByIds.size() > 0) {
                EntUserInfo entUserInfo = enterpriseUserByIds.get(0);
                pluginBaseInfo.setCreator(entUserInfo.getUserName());
            }
            return pluginBaseInfo;
        }
        return null;
    }

    @Override
    public List<String> getAllPluginVersion(Integer pluginId) {
        List<PluginStatisticInfo> pluginOsgiInstallCountGroupVersion = this.pluginInfoStatisticMapper.getPluginOsgiInstallCountGroupVersion();
        List<PluginBaseInfo> pluginInfoBaseDtos = this.pluginInfoStatisticMapper.getPluginOsgiBaseInfo(pluginId);
        if (pluginInfoBaseDtos != null && !pluginInfoBaseDtos.isEmpty()) {
            PluginBaseInfo pluginBaseInfo = pluginInfoBaseDtos.get(0);
            String pluginNum = pluginBaseInfo.getPluginNum();
            pluginOsgiInstallCountGroupVersion = pluginOsgiInstallCountGroupVersion.stream().filter(o -> o.getPluginName().equals(pluginNum)).collect(Collectors.toList());
            //升序
            List<String> collect = pluginOsgiInstallCountGroupVersion.stream().map(PluginStatisticInfo::getPluginVersion).sorted().collect(Collectors.toList());
            //降序
            Collections.reverse(collect);
            return collect;
        } else {
            return null;
        }
    }


}
