package com.cmiot.report.service;

import com.cmiot.report.dto.customerOrder.*;

import java.util.List;
import java.util.Map;

public interface CustomerOrderService {

    OverviewPieChartData customerOrderOverviewPieChart(OverviewPieChartRequest request);

    List<OverviewHistogramData> customerOrderOverviewHistogram(OverviewHistogramRequest request);

    List<OrderTrendLineChartData> customerOrderTrendLineChart(OrderTrendLineChartRequest request);

    Map<String, Object> cumulativePackageSubscription(PackageSubscriptionRequest request);

    Map<String, Object> newPackageSubscription(PackageSubscriptionRequest request);

    Map<String, Object> SinglePackageSubscription(PackageSubscriptionRequestExtend request);

    List<PackageGrowthTrendLineChartData> packageGrowthTrendLineChart(PackageGrowthTrendRequest request);

    // 2.4 套餐增长趋势柱状图
    List<PackageGrowthTrendHistogramData> packageGrowthTrendHistogram(PackageGrowthTrendRequest request);

    List<BandwidthStatisticalData> bandwidthStatisticalAnalysis(BandwidthStatisticalRequest request);
}
