package com.cmiot.report.service;

import com.cmiot.report.dto.enterprise.DeviceCpuRamDataResult;
import com.cmiot.report.dto.enterprise.DeviceFlowDataResult;
import com.cmiot.report.facade.enterprise.DeviceGraphDataResult;
import com.cmiot.report.facade.enterprise.DeviceOverviewResult;
import com.cmiot.report.facade.enterprise.SubDeivceGraphDataResult;

import java.util.List;

/**
 * @Classname EnterpriseDeviceStatisticService
 * @Description
 * @Date 2023/8/1 9:22
 * @Created by lei
 */
public interface EnterpriseDeviceStatisticService {
    DeviceOverviewResult getDeviceOverview(Long eid);

    List<DeviceGraphDataResult> getDeviceGraphData(Long eid);

    SubDeivceGraphDataResult getStaDeivceGraphData(Long eid, Integer dateType);

    DeviceFlowDataResult deviceFlow(Long eid, String startTime, String endTime);

    DeviceCpuRamDataResult getCpuRam(Long eid, String startTime, String endTime);
}
