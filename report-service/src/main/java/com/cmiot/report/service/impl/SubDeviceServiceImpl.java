package com.cmiot.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.cmiot.fgeo.common.dto.PageInfoParam;
import com.cmiot.fgeo.common.error.FGEOException;
import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.fgeo.taskmanager.enums.ExportStatusEnum;
import com.cmiot.report.InitSystemConfig;
import com.cmiot.report.bean.*;
import com.cmiot.report.common.poi.ExcelUtils;
import com.cmiot.report.dto.*;
import com.cmiot.report.dto.SubDeviceStatisticResult.HangingDeviceRange;
import com.cmiot.report.mapper.DeviceAllMapper;
import com.cmiot.report.mapper.DeviceCumCountMapper;
import com.cmiot.report.mapper.DeviceCumulativeAllMapper;
import com.cmiot.report.mapper.DeviceDayCountMapper;
import com.cmiot.report.service.DicDataService;
import com.cmiot.report.service.ExportService;
import com.cmiot.report.service.SubDeviceService;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.PermissionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * author: ranwei
 * date: 2022/06/17 10:55
 * description:
 * 下挂设备统计-1.0.3需求
 */

@Service
public class SubDeviceServiceImpl implements SubDeviceService {
    private final static Logger logger = LoggerFactory.getLogger(SubDeviceServiceImpl.class);

    @Autowired
    private DeviceCumCountMapper deviceCumCountMapper;

    @Autowired
    private DeviceAllMapper deviceAllMapper;

    @Autowired
    private DeviceDayCountMapper deviceDayCountMapper;

    @Autowired
    private DeviceCumulativeAllMapper deviceCumulativeAllMapper;

    /**
     * 导出工具。
     */
    @Autowired
    private ExportService exportService;

    /**
     * 临时目录
     */
    @Value("${export.tmp.local}")
    private String tmpPath;

    /**
     * 1 使用内网地址,否则使用外网地址
     */
    @Value("${export.use-pri-url}")
    private Integer usePriUrl;

    @Autowired
    private DicDataService dicDataService;


    @Override
    public SubOverviewResult getSubOverview(SubOverviewRequest request, String province) {
        logger.info("下挂设备分析-指标概览，接口方法入参=：{}, province:{}", LocalDate.now(), province);

        SubOverviewResult result = new SubOverviewResult();

        try {
            List<String> provinces = request.getProvince();
            List<String> citys = request.getCity();

            // 用户区域权限与查询区域取交集
            if (org.springframework.util.StringUtils.isEmpty(province)) {
                logger.error("getSubOverview user provice is null");
                return result;
            }
            List<String> permission = PermissionUtils.getResult(province, provinces);
            if (!CollectionUtils.isEmpty(permission)) {
                provinces.clear();
                provinces.addAll(permission);
            }
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(citys)) {
                    if (!ls.containsAll(citys)) {
                        citys.clear();
                        citys.addAll(ls);
                    }
                } else {
                    citys.clear();
                    citys.addAll(ls);
                }
            }
            logger.info("getSubOverview area, provList: {}, cityList:{}", provinces, citys);
            DeviceCumCountExample example = new DeviceCumCountExample();
            DeviceCumCountExample.Criteria criteria = example.createCriteria();

            if (!CollectionUtils.isEmpty(provinces)) criteria.andProvinceCodeIn(provinces);
            if (!CollectionUtils.isEmpty(citys)) criteria.andCityCodeIn(citys);
            criteria.andPdateEqualTo(DateUtil.getYesterday());

            List<DeviceCumCount> list = deviceCumCountMapper.selectByExample(example);
            long dayActive = list.stream().mapToLong(DeviceCumCount::getDeviceDayAlive).sum();
            long weekActive = list.stream().mapToLong(DeviceCumCount::getDeviceWeekAlive).sum();
            long monthActive = list.stream().mapToLong(DeviceCumCount::getDeviceMonthAlive).sum();
            long wired = list.stream().mapToLong(DeviceCumCount::getDeviceAllWired).sum();
            long wireless = list.stream().mapToLong(DeviceCumCount::getDeviceAllWireless).sum();
            long total = list.stream().mapToLong(DeviceCumCount::getDeviceAllCount).sum();
            if (!CollectionUtils.isEmpty(list)) {
                result.setDayActiveNum(dayActive);
                result.setWeekActiveNum(weekActive);
                result.setMonthActiveNum(monthActive);
                result.setAddUpWiredNum(wired);
                result.setAddUpWirelessNum(wireless);
                result.setTotal(total);
            } else {
                result.setDayActiveNum(0);
                result.setWeekActiveNum(0);
                result.setMonthActiveNum(0);
                result.setAddUpWiredNum(0);
                result.setAddUpWirelessNum(0);
                result.setTotal(0);
                logger.info("下挂设备分析-指标概览，查询结果为空，设置默认值.");
            }
        } catch (Exception ex) {
            logger.error("下挂设备分析-指标概览，异常={}", ex.getMessage(), ex);
        }

        logger.info("下挂设备分析-指标概览，接口方法出参={}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public SubDeviceStatisticResult getStatisticIndex(SubDeviceStatisticRequest request, String province) {
        logger.info("下挂设备分析-数量统计，接口方法入参=：{}, province:{}", JSON.toJSONString(request), province);

        SubDeviceStatisticResult result = new SubDeviceStatisticResult();

        try {
            List<String> provinces = request.getProvince();
            List<String> vendors = request.getVendor();

            // 用户区域权限与查询区域取交集
            if (org.springframework.util.StringUtils.isEmpty(province)) {
                logger.error("getStatisticIndex user provice is null");
                return result;
            }
            List<String> permission = PermissionUtils.getResult(province, provinces);
            if (!CollectionUtils.isEmpty(permission)) {
                provinces.clear();
                provinces.addAll(permission);
            }
            logger.info("getStatisticIndex area, provList: {}", provinces);
            String startTime = request.getStartTime();
            String endTime = request.getEndTime();
            // if(startTime == null || startTime.length() == 0) startTime = DateUtil.getBefore30DayString();
            // if(endTime == null || endTime.length() == 0) endTime = DateUtil.getNowDayString();
            
            // 厂商编码转换为long
            List<Long> vendorsIdList = vendors.stream()
            		.mapToLong(x -> NumberUtils.toLong(x, 0))
            		.filter(x -> x > 0).boxed()
            		.collect(Collectors.toList());


            // yyyy-MM-dd HH:mm:ss
            List<String> daysByDuringStartEnd = new ArrayList<>();
            if(startTime != null && endTime != null 
            		&& endTime.length() > 1 && startTime.length() > 1) {
	            Date startTimeDate = DateUtil.stringToDate(startTime + " 00:00:00", DateUtil.DATE_TIME_PATTERN);
	            Date endTimeDate = DateUtil.stringToDate(endTime + " 23:59:59", DateUtil.DATE_TIME_PATTERN);
	            daysByDuringStartEnd.addAll(DateUtil.getDaysByDuring(startTime, endTime));
            }
            String qStartTime = StringUtils.isNotBlank(startTime) ? startTime.replace("-", "") : "";
            String qEndTime = StringUtils.isNotBlank(endTime) ? endTime.replace("-", "") : "";
            List<DeviceAllByCount> countList =
                    deviceDayCountMapper.selectDeviceDayCount(provinces, vendors, qStartTime, qEndTime);

            // 不根据时间分区 pdate
            List<DeviceAllByCount> countListNotByPdate =
                    deviceDayCountMapper.selectDeviceAllCount(provinces, vendors, startTime, endTime);
            
            // 结果子集
            // 如果省份为空（默认所有省份）或者长度大于1，根据省份分组统计
            // 如果省份只有一个，根据地市分组统计
            List<SubDeviceStatisticResult.HangingDeviceRange> hangingDeviceRange = toHangingDeviceRange(countListNotByPdate, provinces);
            
            // 指定省份指定地市下的按天分析
            Map<Long, DeviceAllByCount> collect = countList.stream()
            		.filter(x -> x != null)
            		.collect(Collectors.groupingBy(x -> x.getPdate()))
            		.entrySet().stream()
            		.map(x -> from(x.getValue()))
            		.filter(x -> x != null)
            		.collect(Collectors.toMap(DeviceAllByCount::getPdate, o -> o));
            // x轴
            List<String> xAxis = daysByDuringStartEnd.stream().map(x -> x.replaceAll("-", "")).collect(Collectors.toList());
            List<Long> activeTotal = xAxis.stream()
            		.map(x -> collect.get(Long.valueOf(x)))
            		.mapToLong(x -> (x == null) ? 0 : x.getDeviceAllCount())
            		.boxed()
            		.collect(Collectors.toList());
            List<Long> wirelessActiveNum = xAxis.stream()
            		.map(x -> collect.get(Long.valueOf(x)))
            		.mapToLong(x -> (x == null) ? 0 : x.getDeviceAllWireless())
            		.boxed()
            		.collect(Collectors.toList());
            List<Long> wiredActiveNum = xAxis.stream()
            		.map(x -> collect.get(Long.valueOf(x)))
            		.mapToLong(x -> (x == null) ? 0 : x.getDeviceAllWired())
            		.boxed()
            		.collect(Collectors.toList());
            
            // 结果组合
            result.setHangingDeviceRange(hangingDeviceRange);
            result.setxAxis(xAxis);
            result.setActiveTotal(activeTotal);
            result.setWirelessActiveNum(wirelessActiveNum);
            result.setWiredActiveNum(wiredActiveNum);

        } catch (Exception ex) {
            logger.info("下挂设备分析-数量统计，异常={}", ex.getMessage(), ex);
        }
        
        logger.info("下挂设备分析-数量统计，接口方法出参={}", JSON.toJSONString(result));
        return result;
    }
    
    /**
     * 三个指标求累计值。
     */
    private DeviceAllByCount from(List<DeviceAllByCount> countList) {
    	if(countList == null || countList.isEmpty()) {
    		return null;
    	}
    	
    	// 求和
    	long deviceAllCount = countList.stream().mapToLong(DeviceAllByCount::getDeviceAllCount).sum();
    	long deviceAllWired = countList.stream().mapToLong(DeviceAllByCount::getDeviceAllWired).sum();
    	long deviceAllWireless = countList.stream().mapToLong(DeviceAllByCount::getDeviceAllWireless).sum();
    	
    	DeviceAllByCount ans = new DeviceAllByCount();
    	ans.setPdate(countList.get(0).getPdate());
    	ans.setProvinceCode(countList.get(0).getProvinceCode());
    	ans.setCityCode(countList.get(0).getCityCode());
    	ans.setDeviceAllCount(deviceAllCount);
    	ans.setDeviceAllWired(deviceAllWired);
    	ans.setDeviceAllWireless(deviceAllWireless);
    	return ans;
    }
    
    /**
     * 根据省份或者地市统计分析，累积量。
     */
    private List<SubDeviceStatisticResult.HangingDeviceRange> toHangingDeviceRange(List<DeviceAllByCount> countList, List<String> provinces) {
    	// 1、先讲数据转换为 key 与 value 形式; type == 1 根据省份分组统计, type == 2 根据地市分组统计
    	boolean isByProvince = (provinces == null || provinces.isEmpty() || provinces.size() > 1);
    	Map<String, LongSummaryStatistics> collect = countList.stream()
    			.filter(x -> x != null)
    			.collect(Collectors.groupingBy(x -> isByProvince ? x.getProvinceCode() : (StringUtils.isNotBlank(x.getCityCode()) ? x.getCityCode() : "其他"),
    					Collectors.summarizingLong(x -> x.getDeviceAllCount())));
    	// 2、获取所有的省份或者地市信息
    	List<String> keyList = new ArrayList<>();
    	// (1)如果省份为空，查询全部省份的地址
    	if(provinces == null || provinces.isEmpty()) {
    		keyList.addAll(InitSystemConfig.AREA_INFO_LIST.stream()
    				.filter(x -> x.getLevel() == 1)
    				.map(x -> x.getGcode())
    				.collect(Collectors.toList()));
    	}
    	// (2)如果省份不为空
    	else if(provinces.size() > 1) {
    		keyList.addAll(provinces);
    	} 
    	// (3)如果省份只有一个，查询省份下面所有地市
    	else if(provinces.size() == 1){
    		keyList.addAll(InitSystemConfig.AREA_INFO_LIST.stream()
    				.filter(x -> (x.getPid().intValue() == Integer.valueOf(provinces.get(0)).intValue()))
    				.map(x -> x.getGcode())
    				.collect(Collectors.toList()));
            keyList.add("其他");
    		keyList.remove(provinces.get(0));
    	}
    	logger.info("keyList : {}", keyList.size());
    	// 3、根据关键字组合数据
    	return keyList.stream()
    			.map(x -> from(x, collect.get(x)))
    			.sorted(Comparator.comparingLong(HangingDeviceRange::getNum).reversed())
    			.collect(Collectors.toList());
    }
    
    /**
     * 根据自定字段，进行对象装换。
     * type ： 
     * 1 根据省份编码装换
     * 2 根据地市编码装换
     */
    private SubDeviceStatisticResult.HangingDeviceRange from(String key, LongSummaryStatistics lssInfo) {
    	SubDeviceStatisticResult.HangingDeviceRange ans = new SubDeviceStatisticResult.HangingDeviceRange();
		ans.setName(InitSystemConfig.AREA_CODE_NAME.getOrDefault(key, key));
		ans.setNum((lssInfo == null) ? 0 : lssInfo.getSum());
    	return ans;
    }

    @Override
    public SubDeviceTrendResult getTrendStatistic(SubDeviceTrendRequest request, String province) {
        logger.info("下挂设备分析-趋势统计，接口方法入参=：{}, province:{}", JSON.toJSONString(request), province);

        SubDeviceTrendResult result = new SubDeviceTrendResult();

        try {
            List<String> provinces = request.getProvince();
            List<String> citys = request.getCity();
            // 用户区域权限与查询区域取交集
            if (org.springframework.util.StringUtils.isEmpty(province)) {
                logger.error("getTrendStatistic user provice is null");
                return result;
            }
            List<String> permission = PermissionUtils.getResult(province, provinces);
            if (!CollectionUtils.isEmpty(permission)) {
                provinces.clear();
                provinces.addAll(permission);
            }

            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(citys)) {
                    if (!ls.containsAll(citys)) {
                        citys.clear();
                        citys.addAll(ls);
                    }
                } else {
                    citys.clear();
                    citys.addAll(ls);
                }
            }

            logger.info("getTrendStatistic area, provList: {}, cityList:{}", provinces, citys);
            List<String> vendors = request.getVendor();
            // 1有线 2无线
            int accessMode = request.getAccessMode();
            // time,area,vendor
            String type = request.getType();
            String startTime = request.getStartTime();
            String endTime = request.getEndTime();
            if(startTime == null || startTime.length() == 0) startTime = DateUtil.getBefore30DayString();
            if(endTime == null || endTime.length() == 0) endTime = DateUtil.getNowDayString();
            // 日期横坐标
            List<String> daysByDuringStartEnd = new ArrayList<>();
            daysByDuringStartEnd.addAll(DateUtil.getDaysByDuring(startTime, endTime));
            
            // 厂商编码转换为long
            List<Long> vendorsIdList = vendors.stream()
            		.mapToLong(x -> NumberUtils.toLong(x, 0))
            		.filter(x -> x > 0).boxed()
            		.collect(Collectors.toList());
            // 无线状态
            List<String> wirelessList = new ArrayList<String>();
            wirelessList.add("2.4G");
            wirelessList.add("5G");
            
            // 查询条件组合
            DeviceCumulativeAllExample example = new DeviceCumulativeAllExample();
            DeviceCumulativeAllExample.Criteria criteria = example.createCriteria();
            if (!CollectionUtils.isEmpty(provinces)) criteria.andProvinceCodeIn(provinces);
            if (!CollectionUtils.isEmpty(citys)) criteria.andCityCodeIn(citys);
            if (!CollectionUtils.isEmpty(vendorsIdList)) criteria.andFactoryIdIn(vendorsIdList);
            // 无线设置
            if(2 == accessMode) {
            	criteria.andWlanRadioTypeIn(wirelessList);
            } else if(1 == accessMode) {
            	criteria.andWlanRadioTypeNotIn(wirelessList);
            }
            // yyyy-MM-dd HH:mm:ss
            String yesterday = DateUtil.getYesterday();
            Date startTimeDate = DateUtil.stringToDate(yesterday + "000000", DateUtil.UNSIGNED_DATE_TIME_PATTERN);
            Date endTimeDate = DateUtil.stringToDate(yesterday + "232359", DateUtil.UNSIGNED_DATE_TIME_PATTERN);
            if(startTimeDate != null && endTimeDate != null) {
            	criteria.andPdateBetween(startTimeDate, endTimeDate);
            }
            
            // 统计新增量
            List<DeviceCumulativeAllCount> allCountList = deviceCumulativeAllMapper.countDistinctMacByExample(example);
            
            // time,area,vendor
            // String type = request.getType();
            List<String> xAxis = new ArrayList<String>();
            List<Long> addNumList = new ArrayList<>();
            List<Long> currentNumList = new ArrayList<>();
            
            // 日期维度处理
            long startLongTime = NumberUtils.toLong(startTime.replaceAll("-", ""));
            long endLongTime = NumberUtils.toLong(endTime.replaceAll("-", ""));
            if("time".equalsIgnoreCase(type)) {
            	// 时间维度查询
            	Map<Long, Long> collect = allCountList.stream()
            			.collect(Collectors.groupingBy(DeviceCumulativeAllCount::getPdate, 
            					Collectors.summingLong(DeviceCumulativeAllCount::getNewMacCount)));
            	// 补全时间维度坐标
            	for(String str : daysByDuringStartEnd) {
            		Long dateKey = Long.valueOf(str.replaceAll("-", ""));
            		if(!collect.containsKey(dateKey)) {
            			collect.put(dateKey, 0L);
            		}
            	}
            	// 时间很坐标
            	List<Long> xLongList = collect.entrySet().stream()
            			.mapToLong(x -> x.getKey())
            			.sorted().boxed()
            			.collect(Collectors.toList());
            	// 时间很坐标转换字符串
            	xAxis.addAll(xLongList.stream()
            			.filter(x -> (x >= startLongTime && x <= endLongTime))
            			.map(x -> String.valueOf(x))
            			.collect(Collectors.toList()));
            	// 增量数据
            	long total = 0;
            	for(Long pdate : xLongList) {
            		Long curr = collect.getOrDefault(pdate, 0L);
            		total += curr;
            		if(pdate >= startLongTime && pdate <= endLongTime) {
            			addNumList.add(curr);
            			currentNumList.add(total);
            		}
            	}
            }
            // 区域维度处理
            else if("area".equalsIgnoreCase(type)) {
            	boolean isByProvince = (provinces == null || provinces.isEmpty() || provinces.size() > 1);
            	// 省份维度查询
            	Map<String, Long> areaCountMap = new HashMap<>();
            	if(isByProvince) {
            		areaCountMap.putAll(allCountList.stream()
            			.collect(Collectors.groupingBy(DeviceCumulativeAllCount::getProvinceCode, 
            					Collectors.summingLong(DeviceCumulativeAllCount::getNewMacCount))));
            	} else {
            		areaCountMap.putAll(allCountList.stream()
                			.collect(Collectors.groupingBy(DeviceCumulativeAllCount::getCityCode, 
                					Collectors.summingLong(DeviceCumulativeAllCount::getNewMacCount))));
                }
            	
            	// 2、获取所有的省份或者地市信息
            	List<String> keyList = new ArrayList<>();
            	// (1)如果省份为空，查询全部省份的地址
            	if(provinces == null || provinces.isEmpty()) {
            		keyList.addAll(InitSystemConfig.AREA_INFO_LIST.stream()
            				.filter(x -> x.getLevel() == 1)
            				.map(x -> x.getGcode())
            				.collect(Collectors.toList()));
            	}
            	// (2)如果省份不为空
            	else if(provinces.size() > 1) {
            		keyList.addAll(provinces);
            	} 
            	// (3)如果省份只有一个，查询省份下面所有地市
            	else if(provinces.size() == 1){
            		keyList.addAll(InitSystemConfig.AREA_INFO_LIST.stream()
            				.filter(x -> (x.getPid().intValue() == Integer.valueOf(provinces.get(0)).intValue()))
            				.map(x -> x.getGcode())
            				.collect(Collectors.toList()));
            		keyList.remove(provinces.get(0));
            	}
            	
            	// 补全省份维度坐标
            	for(String str : keyList) {
            		if(!areaCountMap.containsKey(str)) {
            			areaCountMap.put(str, 0L);
            		}
            	}
            	// 横坐标
            	List<String> xAreaList = areaCountMap.entrySet().stream()
            			.map(x -> x.getKey())
            			.filter(x -> (x != null && x.length() > 0))
            			.collect(Collectors.toList());
            	xAxis.addAll(xAreaList.stream()
            			.map(x -> InitSystemConfig.AREA_CODE_NAME.getOrDefault(x, x))
            			.collect(Collectors.toList()));
            	// 当前总量
            	for(String garea : xAreaList) {
            		currentNumList.add(areaCountMap.getOrDefault(garea, 0L));
            	}
            	// 新增量
				Map<String, Long> areaCountNewMap = allCountList.stream()
						.filter(x -> (x.getPdate() >= startLongTime && x.getPdate() <= endLongTime))
						.collect(Collectors.groupingBy(DeviceCumulativeAllCount::getProvinceCode,
								Collectors.summingLong(DeviceCumulativeAllCount::getNewMacCount)));
				for(String garea : xAreaList) {
					addNumList.add(areaCountNewMap.getOrDefault(garea, 0L));
            	}
            }
            // 厂商维度处理
            else if("vendor".equalsIgnoreCase(type)) {
            	Map<String, Long> vendorCountMap = allCountList.stream()
            			.collect(Collectors.groupingBy(DeviceCumulativeAllCount::getGatewayVendor, 
            					Collectors.summingLong(DeviceCumulativeAllCount::getNewMacCount)));
            	// 横坐标
            	List<String> xVendorList = vendorCountMap.entrySet().stream()
            			.map(x -> x.getKey())
            			.filter(x -> (x != null && x.length() > 0))
            			.collect(Collectors.toList());
            	xAxis.addAll(xVendorList.stream()
            			.map(x -> InitSystemConfig.FACTORY_CODE_NAME.getOrDefault(x, x))
            			.collect(Collectors.toList()));
            	// 当前总量
            	for(String xVendor : xVendorList) {
            		currentNumList.add(vendorCountMap.getOrDefault(xVendor, 0L));
            	}
            	// 新增量
				Map<String, Long> xVendorNewMap = allCountList.stream()
						.filter(x -> (x.getPdate() >= startLongTime && x.getPdate() <= endLongTime))
						.collect(Collectors.groupingBy(DeviceCumulativeAllCount::getGatewayVendor,
								Collectors.summingLong(DeviceCumulativeAllCount::getNewMacCount)));
				for(String xVendor : xVendorList) {
					addNumList.add(xVendorNewMap.getOrDefault(xVendor, 0L));
            	}
            }
            
            result.setxAxis(xAxis);
            result.setCurrentNum(currentNumList);
            result.setAddNum(addNumList);
            
        } catch (Exception ex) {
            logger.info("下挂设备分析-趋势统计，异常={}", ex.getMessage(), ex);
        }

        logger.info("下挂设备分析-趋势统计，接口方法出参={}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public SubDeviceListResult getSubDeviceList(SubDeviceListRequest request, String province) {
        logger.info("下挂设备分析-列表明细，接口方法入参=：{}, province:{}", JSON.toJSONString(request), province);

        SubDeviceListResult result = new SubDeviceListResult();

        try {
            List<String> provinces = request.getProvince();
            List<String> citys = request.getCity();
            // 用户区域权限与查询区域取交集
            if (org.springframework.util.StringUtils.isEmpty(province)) {
                logger.error("getSubDeviceList user provice is null");
                return result;
            }
            List<String> permission = PermissionUtils.getResult(province, provinces);
            if (!CollectionUtils.isEmpty(permission)) {
                provinces.clear();
                provinces.addAll(permission);
            }
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(citys)) {
                    if (!ls.containsAll(citys)) {
                        citys.clear();
                        citys.addAll(ls);
                    }
                } else {
                    citys.clear();
                    citys.addAll(ls);
                }
            }
            logger.info("getSubDeviceList area, provList: {}, cityList:{}", provinces, citys);
            List<String> vendors = request.getVendor();
            String gatewayMac = request.getGatewayMac();
            String gatewaySn = request.getGatewaySn();
            String deviceMac = request.getHangingDeviceMac();
            int page = request.getPage();
            int pageSize = request.getPageSize();

            DeviceCumulativeAllExample example = new DeviceCumulativeAllExample();
            DeviceCumulativeAllExample.Criteria criteria = example.createCriteria();

            if (!CollectionUtils.isEmpty(provinces)) criteria.andProvinceCodeIn(provinces);
            if (!CollectionUtils.isEmpty(citys)) criteria.andCityCodeIn(citys);
            if (!CollectionUtils.isEmpty(vendors)) {
                List<Long> vendorIdList = vendors.stream().map(Long::valueOf).collect(Collectors.toList());
                criteria.andFactoryIdIn(vendorIdList);
            }
            if (StringUtils.isNotBlank(gatewayMac)) criteria.andGatewayMacEqualTo(gatewayMac);
            if (StringUtils.isNotBlank(gatewaySn)) criteria.andGatewaySnEqualTo(gatewaySn);
            if (StringUtils.isNotBlank(deviceMac)) criteria.andMacEqualTo(deviceMac);

/*            Page<SubDeviceList> listPage = PageMethod.startPage(page, pageSize)
                    .doSelectPage(() -> deviceCumulativeAllMapper.selectListByExample(example));*/

            PageInfoParam pageInfoParam = new PageInfoParam();
            pageInfoParam.setPage(page);
            pageInfoParam.setPageSize(pageSize);
            example.setLimit(pageInfoParam.getPageSize());
            example.setOffset(pageInfoParam.getOffset());

            List<SubDeviceList> subDeviceLists = deviceCumulativeAllMapper.selectListByExample(example);

            if (!CollectionUtils.isEmpty(subDeviceLists)) {
                result.setList(subDeviceLists);
                result.setPage(page);
                result.setPageSize(pageSize);
                //result.setTotal(listPage.getTotal());
            } else {
                result.setList(new ArrayList());
                logger.info("下挂设备分析-列表明细，查询数据为空.");
            }

        } catch (Exception ex) {
            logger.info("下挂设备分析-列表明细，异常={}", ex.getMessage(), ex);
        }

        logger.info("下挂设备分析-列表明细，接口方法出参={}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public String subDeviceExport(SubDeviceListRequest request, String province) {
        logger.info("下挂设备分析-列表明细导出，接口方法入参=：{}", JSON.toJSONString(request));

        String subExportName = "";

        try {
            List<String> provinces = request.getProvince();
            List<String> citys = request.getCity();
            // 用户区域权限与查询区域取交集
            if (org.springframework.util.StringUtils.isEmpty(province)) {
                logger.error("getSubDeviceList user provice is null");
                throw new FGEOException.NotAcceptable("用户没有区域权限无法导出数据");
            }
            List<String> permission = PermissionUtils.getResult(province, provinces);
            if (!CollectionUtils.isEmpty(permission)) {
                provinces.clear();
                provinces.addAll(permission);
            }
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(citys)) {
                    if (!ls.containsAll(citys)) {
                        citys.clear();
                        citys.addAll(ls);
                    }
                } else {
                    citys.clear();
                    citys.addAll(ls);
                }
            }


            List<String> vendors = request.getVendor();
            String gatewayMac = request.getGatewayMac();
            String gatewaySn = request.getGatewaySn();
            String deviceMac = request.getHangingDeviceMac();
            String uid = request.getUid();

            DeviceCumulativeAllExample example = new DeviceCumulativeAllExample();
            DeviceCumulativeAllExample.Criteria criteria = example.createCriteria();

            if (!CollectionUtils.isEmpty(provinces)) criteria.andProvinceCodeIn(provinces);
            if (!CollectionUtils.isEmpty(citys)) criteria.andCityCodeIn(citys);
            if (!CollectionUtils.isEmpty(vendors)) {
                List<Long> vendorIdList = vendors.stream().map(Long::valueOf).collect(Collectors.toList());
                criteria.andFactoryIdIn(vendorIdList);
            }
            if (StringUtils.isNotBlank(gatewayMac)) criteria.andGatewayMacEqualTo(gatewayMac);
            if (StringUtils.isNotBlank(gatewaySn)) criteria.andGatewaySnEqualTo(gatewaySn);
            if (StringUtils.isNotBlank(deviceMac)) criteria.andMacEqualTo(deviceMac);

            long count = deviceCumulativeAllMapper.countByExample(example);
            if (count > 0L) {
                ExecutorService service = Executors.newSingleThreadExecutor();
                String exportType = "下挂设备列表明细导出";
                String exportName = "下挂设备列表明细导出_" + DateUtil.dateToString(new Date(), DateUtil.UNSIGNED_DATE_TIME_PATTERN);
                ExportInfoDto record = exportService.record(Long.valueOf(uid), exportType, exportName);
                subExportName = record.getExportName();
                service.execute(() -> this.subDeviceExportTask(example, record, exportName));
            }

        } catch (Exception ex) {
            logger.info("下挂设备分析-列表明细导出，异常={}", ex.getMessage(), ex);
        }

        Map<String, String> map = new HashMap<>();
        map.put("name", subExportName);

        logger.info("下挂设备分析-列表明细导出，接口方法出参={}", JSON.toJSONString(map));
        return JSON.toJSONString(map);
    }

    public void subDeviceExportTask(DeviceCumulativeAllExample example, ExportInfoDto record, String exportName) {
        logger.info("下挂设备分析-列表明细导出任务开始~");
        List<SubDeviceList> voList = deviceCumulativeAllMapper.selectListByExample(example);

        String localFilePath = tmpPath + "/" + exportName.replace(" ", "_") + ".xlsx";
        File dataFile = ExcelUtils.writeExcel(voList, SubDeviceList.class, localFilePath);

        String downLoadUrl = "";
        String failedDesc = "SUCCESS";

        try {
            Map<?, ?> uploadFile = exportService.uploadFile(record.getCreatorId(), dataFile);
            if (usePriUrl != null && usePriUrl == 1) {
                if (uploadFile.containsKey("privateUrl")) {
                    downLoadUrl = uploadFile.get("privateUrl").toString();
                } else {
                    failedDesc = "上传文件响应无内网地址";
                }
            } else if (uploadFile.containsKey("internetUrl")) {
                downLoadUrl = uploadFile.get("internetUrl").toString();
            } else {
                failedDesc = "上传文件响应无公网地址";
            }
        } catch (Exception ex) {
            failedDesc = "文件上传异常:" + ex.getMessage();
            logger.error("下挂设备分析-列表明细导出任务异常={}", ex.getMessage(), ex);
        }

        record.setFailedDesc(failedDesc);
        record.setDownloadUrl(downLoadUrl);
        record.setStatus("SUCCESS".equalsIgnoreCase(failedDesc) ? ExportStatusEnum.SUCCESS : ExportStatusEnum.FAILED);

        exportService.updateRecord(record);
        logger.info("下挂设备分析-列表明细导出任务结束~");
    }
}
