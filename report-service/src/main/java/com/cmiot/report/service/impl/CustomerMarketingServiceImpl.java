package com.cmiot.report.service.impl;

import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.report.bean.DictBassStd;
import com.cmiot.report.bean.DictBassStdExample;
import com.cmiot.report.bean.customer.*;
import com.cmiot.report.dto.ExportDataResult;
import com.cmiot.report.dto.customer.*;
import com.cmiot.report.mapper.CustomerMarketingDayCountAllMapper;
import com.cmiot.report.mapper.CustomerMarketingScoreAllMapper;
import com.cmiot.report.mapper.DictBassStdMapper;
import com.cmiot.report.service.CustomerMarketingService;
import com.cmiot.report.service.ExportService;
import com.cmiot.report.thread.task.CustomerMarketingExportTask;
import com.cmiot.report.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Classname CustomerMarketingServiceImpl
 * @Description
 * @Date 2022/8/25 14:13
 * @Created by lei
 */
@Service
public class CustomerMarketingServiceImpl implements CustomerMarketingService {

    private final static Logger logger = LoggerFactory.getLogger(CustomerMarketingServiceImpl.class);

    @Autowired
    private ExportService exportService;

    @Autowired
    private CustomerMarketingDayCountAllMapper customerMarketingDayCountAllMapper;

    @Autowired
    private CustomerMarketingScoreAllMapper customerMarketingScoreAllMapper;

    @Autowired
    private DictBassStdMapper dictBassStdMapper;

    /**
     * 临时目录
     */
    @Value("${export.tmp.local}")
    private String tmpPath;
    @Value("${customer.marketing.score:80}")
    private Integer marketingScore;
    /**
     * 1 使用内网地址，否则使用外网地址
     */
    @Value("${export.use-pri-url}")
    private Integer usePriUrl;

    private static final Map<Integer, String> scoreSectionMap = new HashMap<>();

    static {
        //[0,20), [20,40) ,[40,60) [60,80) [80,100]
        scoreSectionMap.put(0, "[0,20)");
        scoreSectionMap.put(20, "[20,40)");
        scoreSectionMap.put(40, "[40,60)");
        scoreSectionMap.put(60, "[60,80)");
        scoreSectionMap.put(80, "[80,100]");
        scoreSectionMap.put(100, "[80,100]");
    }

    /**
     * 单线程池。用于文件导出。
     */
    public static final ExecutorService SINGLE_THREAD_EXECUTOR = Executors.newSingleThreadExecutor();

    @Override
    public CustomerMarketingDistributionData targetCustomerDistribution(TargetCustomerDistributionRequest request) {
        CustomerMarketingDistributionData customerMarketingDistributionData = new CustomerMarketingDistributionData();

        CustomerMarketingDayCountAllExample example = new CustomerMarketingDayCountAllExample();
        CustomerMarketingDayCountAllExample.Criteria criteria = example.createCriteria();
        String time = request.getTime();
        if (StringUtils.isBlank(time)) {
            time = DateUtil.getYesterday();
        } else {
            time = time.replace("-", "");
        }
        criteria.andGdateEqualTo(Integer.valueOf(time));

        String provinceCode = request.getProvince();
        List<String> provList = new ArrayList<>();
        if (StringUtils.isNotBlank(provinceCode)) {
            provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provinceCode, "\\,"));
            criteria.andProvinceCodeIn(provList);
        }

        String cityCode = request.getCity();
        List<String> cityList = new ArrayList<>();
        if (StringUtils.isNotBlank(cityCode)) {
            cityList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCode, "\\,"));
            criteria.andCityCodeIn(cityList);
        }
        List<CustomerMarketingDistributionAreaInfo> customersDistribution = new ArrayList<>();
        long totalTargetCustomers = 0L;
        long yesterdayNewCustomers = 0L;
        long yesterdayReduceCustomers = 0L;
        long lastWeekNewCustomers = 0L;
        long lastWeekReduceCustomers = 0L;
        long lastMonthNewCustomers = 0L;
        long lastMonthReduceCustomers = 0L;

        if (marketingScore == null) {
            marketingScore = 80;
        }
        if (provList.size() != 1) {
            //按省统计
            List<CustomerMarketingDayCountAll> customerMarketingDayCountAlls =
                    this.customerMarketingDayCountAllMapper.selectByExampleByProvince(example);
            for (CustomerMarketingDayCountAll data : customerMarketingDayCountAlls) {
                CustomerMarketingDistributionAreaInfo temp = new CustomerMarketingDistributionAreaInfo();
                temp.setAreaCode(data.getProvinceCode());
                temp.setName(data.getAreaName());
                temp.setTotalTargetCustomers(data.getTotalNum());
                temp.setYesterdayNewCustomers(data.getLastDayIncr());
                temp.setYesterdayReduceCustomers(data.getLastDayDecr());
                customersDistribution.add(temp);

                totalTargetCustomers += data.getTotalNum();
                yesterdayNewCustomers += data.getLastDayIncr();
                yesterdayReduceCustomers += data.getLastDayDecr();
                lastWeekNewCustomers += data.getLastWeekIncr();
                lastWeekReduceCustomers += data.getLastWeekDecr();
                lastMonthNewCustomers += data.getLastMonthIncr();
                lastMonthReduceCustomers += data.getLastMonthDecr();
            }
        } else {
            //按市统计
            List<CustomerMarketingDayCountAll> customerMarketingDayCountAlls =
                    this.customerMarketingDayCountAllMapper.selectByExampleByCity(example);
            for (CustomerMarketingDayCountAll data : customerMarketingDayCountAlls) {
                CustomerMarketingDistributionAreaInfo temp = new CustomerMarketingDistributionAreaInfo();
                temp.setAreaCode(data.getCityCode());
                temp.setName(data.getAreaName());
                temp.setTotalTargetCustomers(data.getTotalNum());
                temp.setYesterdayNewCustomers(data.getLastDayIncr());
                temp.setYesterdayReduceCustomers(data.getLastDayDecr());
                customersDistribution.add(temp);

                totalTargetCustomers += data.getTotalNum();
                yesterdayNewCustomers += data.getLastDayIncr();
                yesterdayReduceCustomers += data.getLastDayDecr();
                lastWeekNewCustomers += data.getLastWeekIncr();
                lastWeekReduceCustomers += data.getLastWeekDecr();
                lastMonthNewCustomers += data.getLastMonthIncr();
                lastMonthReduceCustomers += data.getLastMonthDecr();
            }
        }
        customerMarketingDistributionData.setCustomersDistribution(customersDistribution);
        CustomerMarketingDistributionStatistics statistics = new CustomerMarketingDistributionStatistics();
        statistics.setTotalTargetCustomers(totalTargetCustomers);
        statistics.setYesterdayNewCustomers(yesterdayNewCustomers);
        statistics.setYesterdayReduceCustomers(yesterdayReduceCustomers);
        statistics.setLastWeekNewCustomers(lastWeekNewCustomers);
        statistics.setLastWeekReduceCustomers(lastWeekReduceCustomers);
        statistics.setLastMonthNewCustomers(lastMonthNewCustomers);
        statistics.setLastMonthReduceCustomers(lastMonthReduceCustomers);
        customerMarketingDistributionData.setStatistics(statistics);

        List<CustomerMarketingScoreDistribution> customerMarketingScoreDistributionList =
                this.customerMarketingDayCountAllMapper.selectScoreDistribution(time, provList, cityList);

        long totalTargetCustomersOverLimit = 0L;
        Map<String, Long> sectionScore = new LinkedHashMap<>();
        for (CustomerMarketingScoreDistribution data : customerMarketingScoreDistributionList) {
            String key = scoreSectionMap.get(data.getScoreFloor());
            if (StringUtils.isBlank(key)) {
                continue;
            }
            if (sectionScore.containsKey(key)) {
                sectionScore.computeIfPresent(key, (s, aLong) -> aLong + data.getCnum());
            } else {
                sectionScore.put(key, data.getCnum());
            }

            if (data.getScoreFloor() >= marketingScore) {
                totalTargetCustomersOverLimit += data.getCnum();
            }
        }
        List<CustomerMarketingDistributionRecommendationRate> recommendationRate = new ArrayList<>();
        for (Map.Entry<String, Long> entry : sectionScore.entrySet()) {
            CustomerMarketingDistributionRecommendationRate temp = new CustomerMarketingDistributionRecommendationRate();
            temp.setName(entry.getKey());
            temp.setValue(entry.getValue());
            recommendationRate.add(temp);
        }
        customerMarketingDistributionData.setTotalTargetCustomers(String.valueOf(totalTargetCustomersOverLimit));

        customerMarketingDistributionData.setRecommendationRate(recommendationRate);

        return customerMarketingDistributionData;
    }

    @Override
    public ChangesInTargetUsersData changesInTargetUsers(ChangesInTargetUsersRequest request) {
        CustomerMarketingDayCountAllExample example = new CustomerMarketingDayCountAllExample();
        CustomerMarketingDayCountAllExample.Criteria criteria = example.createCriteria();
        String startTime = request.getStartTime();
        if (StringUtils.isBlank(startTime)) {
            startTime = DateUtil.getBeforeDay(30, DateUtil.MONTH_DATE_PATTERN);
        } else {
            startTime = startTime.replace("-", "");
        }

        String endTime = request.getEndTime();
        if (StringUtils.isBlank(endTime)) {
            endTime = DateUtil.getBeforeDay(1, DateUtil.MONTH_DATE_PATTERN);
        } else {
            endTime = endTime.replace("-", "");
        }
        criteria.andGdateBetween(Integer.valueOf(startTime), Integer.valueOf(endTime));

        String provinceCode = request.getProvince();
        List<String> provList = new ArrayList<>();
        if (StringUtils.isNotBlank(provinceCode)) {
            provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provinceCode, "\\,"));
            criteria.andProvinceCodeIn(provList);
        }

        String cityCode = request.getCity();
        List<String> cityList = new ArrayList<>();
        if (StringUtils.isNotBlank(cityCode)) {
            cityList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCode, "\\,"));
            criteria.andCityCodeIn(cityList);
        }
        ChangesInTargetUsersData changesInTargetUsersData = new ChangesInTargetUsersData();
        List<String> xAxis = new ArrayList<>();
        List<Long> yesterdayTargetCustomers = new ArrayList<>();
        List<Long> yesterdayNewCustomers = new ArrayList<>();
        List<Long> yesterdayReduceCustomers = new ArrayList<>();
        List<CustomerMarketingDayCountAll> customerMarketingDayCountAlls =
                this.customerMarketingDayCountAllMapper.selectByExampleByDate(example);
        for (CustomerMarketingDayCountAll data : customerMarketingDayCountAlls) {
            xAxis.add(DateUtil.formatInput(String.valueOf(data.getGdate())));
            yesterdayTargetCustomers.add(data.getTotalNum());
            yesterdayNewCustomers.add(data.getLastDayIncr());
            yesterdayReduceCustomers.add(data.getLastDayDecr());
        }
        changesInTargetUsersData.setXAxis(xAxis);
        changesInTargetUsersData.setYesterdayTargetCustomers(yesterdayTargetCustomers);
        changesInTargetUsersData.setYesterdayNewCustomers(yesterdayNewCustomers);
        changesInTargetUsersData.setYesterdayReduceCustomers(yesterdayReduceCustomers);
        return changesInTargetUsersData;
    }

    @Override
    public MarketingCustomerListData precisionMarketingCustomerList(MarketingCustomerListRequest request) {
        String time = DateUtil.getYesterday();

        String provinceCode = request.getProvince();
        List<String> provList = null;
        if (StringUtils.isNotBlank(provinceCode)) {
            provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provinceCode, "\\,"));
        }

        String cityCode = request.getCity();
        List<String> cityList = null;
        if (StringUtils.isNotBlank(cityCode)) {
            cityList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCode, "\\,"));
        }
        String customerName = request.getBusiness();
        String customerId = request.getGroupCustomer();
        List<String> valueCategoryList = null;
        if (StringUtils.isNotBlank(request.getEnterpriseValueCategory())) {
            valueCategoryList =
                    (List<String>) CollectionUtils
                            .arrayToList(StringUtils.split(request.getEnterpriseValueCategory(), "\\,"));
        }
        List<String> customerStatusList = null;
        if (StringUtils.isNotBlank(request.getCustomerStatus())) {
            customerStatusList =
                    (List<String>) CollectionUtils
                            .arrayToList(StringUtils.split(request.getCustomerStatus(), "\\,"));
        }
        String scoreStart = request.getMarketingExponentStart();
        String scoreEnd = request.getMarketingExponentEnd();

        Integer page = request.getPage();
        Integer pageSize = request.getPageSize();
        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        Long offset = (long) (page - 1) * Long.valueOf(pageSize);

        CustomerMarketingScoreQueryParam queryParam = new CustomerMarketingScoreQueryParam();
        queryParam.setTime(time);
        queryParam.setProvList(provList);
        queryParam.setCityList(cityList);
        queryParam.setCustomerName(customerName);
        queryParam.setCustomerId(customerId);
        queryParam.setValueCategoryList(valueCategoryList);
        queryParam.setCustomerStatusList(customerStatusList);
        queryParam.setScoreStart(scoreStart);
        queryParam.setScoreEnd(scoreEnd);
        queryParam.setOffset(offset);
        queryParam.setPageSize(pageSize);

        Long total = this.customerMarketingScoreAllMapper.countByParam(queryParam);

        MarketingCustomerListData resultData = new MarketingCustomerListData();
        resultData.setPage(page);
        resultData.setPageSize(pageSize);
        resultData.setTotal(total);
        if (total == 0) {
            resultData.setList(Collections.emptyList());
            return resultData;
        }


        List<CustomerMarketingScoreAll> customerMarketingScoreAlls =
                this.customerMarketingScoreAllMapper.selectByParam(queryParam);

        // bass_type='BASS_STD1_0028' 客户状态 (不在网-10,离网-11,未入网-12,在网-20)
        Map<String, String> customerStatusDict = getBassStd("BASS_STD1_0028");
        //价值分类
        Map<String, String> valueCategoryDict = getBassStd("BASS_STD1_0018");
        List<MarketingCustomerData> listData = new ArrayList<>();
        for (CustomerMarketingScoreAll temp : customerMarketingScoreAlls) {
            MarketingCustomerData data = new MarketingCustomerData();
            data.setId(String.valueOf(temp.getEnterpriseId()));
            data.setBusiness(temp.getCustomerName());
            String areaName = temp.getAreaName();
            if ("其他-其他".equals(areaName)) {
                areaName = "";
            }
            data.setAreaName(areaName);
            data.setGroupCustomer(temp.getCustomerId());
            data.setEnterpriseValueCategory(valueCategoryDict.getOrDefault(temp.getValueCategory(), ""));
            data.setCustomerStatus(customerStatusDict.getOrDefault(temp.getCustomerStatus(), ""));
            data.setMarketingExponent(String.valueOf(temp.getScore()));

            listData.add(data);
        }
        resultData.setList(listData);
        return resultData;
    }

    @Override
    public ExportDataResult precisionMarketingCustomerListExport(MarketingCustomerListRequest request) {

        String exportType = "精准营销客户列表导出";
        String exportName = "精准营销客户列表导出";
        ExportInfoDto record = exportService.record(request.getUid(), exportType, exportName);

        // 启动异步导出线程
        SINGLE_THREAD_EXECUTOR.submit(new CustomerMarketingExportTask(customerMarketingDayCountAllMapper,
                customerMarketingScoreAllMapper,
                dictBassStdMapper,
                request,
                exportService,
                record,
                tmpPath,
                usePriUrl));
        ExportDataResult exportDataResult = new ExportDataResult();
        exportDataResult.setName(record.getExportName());
        return exportDataResult;
    }

    private Map<String, String> getBassStd(String bassType) {
        DictBassStdExample example = new DictBassStdExample();
        DictBassStdExample.Criteria criteria = example.createCriteria();

        criteria.andBassTypeEqualTo(bassType);
        List<DictBassStd> stdList = dictBassStdMapper.selectByExample(example);
        Map<String, String> result = new HashMap<>();
        for (DictBassStd dictBassStd : stdList) {
            result.put(dictBassStd.getCode(), dictBassStd.getName());
        }
        return result;
    }


}
