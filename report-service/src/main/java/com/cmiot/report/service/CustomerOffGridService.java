package com.cmiot.report.service;


import com.cmiot.report.bean.CustomerUneffect;
import com.cmiot.report.bean.GatewayAnomalyRecord;
import com.cmiot.report.bean.GatewayNotOnline;
import com.cmiot.report.dto.*;
import com.github.pagehelper.Page;

import java.util.List;

public interface CustomerOffGridService {

    List getOffGridFactorThreshold();

    OffGridFactorThresholdDetail getOffGridFactorThresholdDetail(String factorKey);

    void setOffGridFactorIndicaterThreshold(OffGridFactorIndicatorThresholdSet factorSet);

    void setOffGridOverallFactorThreshold(Object threshold);

    int getOffGridOverallFactorThreshold();

    void setOffGridFactorThreshold(SetOffGridFactorThresholdRequest request);

    List<BassType> getCustomerStatus();

    List<BassType> getEnterpriseValueCategory();

    OffGridCustStatisticResult getOffGridCustStatistic(OffGridCustStatisticRequest request);

    OffGridCustListResult getOffGridCustList(OffGridCustListRequest request);

    String exportOffGridCustomer(OffGridCustExportRequest request);

    //路线质量不佳用户
    Page<GatewayAnomalyRecord> queryAnomalyRecordList(GatewayAnomalyRecordQuery query);

    //长期离线用户
    Page<GatewayNotOnline> queryNotOnline(GatewayAnomalyRecordQuery query);

    Page<CustomerUneffect> queryUneffect(GatewayAnomalyRecordQuery query);
}
