package com.cmiot.report.service.impl;

import com.cmiot.fgeo.common.dto.PageInfoDto;
import com.cmiot.fgeo.common.dto.PageInfoParam;
import com.cmiot.report.bean.CustomerOrderAll;
import com.cmiot.report.dto.enterprise.ComprehensiveGatewayInfo;
import com.cmiot.report.dto.enterprise.EnterpriseComprehensiveInfo;
import com.cmiot.report.facade.dto.enterprise.*;
import com.cmiot.report.mapper.CustomerOrderAllMapper;
import com.cmiot.report.mapper.EnterpriseComprehensiveMapper;
import com.cmiot.report.service.EnterpriseComprehensiveService;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Classname EnterpriseComprehensiveServiceImpl
 * @Description
 * @Date 2023/10/11 15:27
 * @Created by lei
 */
@Service
public class EnterpriseComprehensiveServiceImpl implements EnterpriseComprehensiveService {

    @Autowired
    private EnterpriseComprehensiveMapper enterpriseComprehensiveMapper;



    @Override
    public EnterpriseComprehensiveResult getComprehensive(Long eid) {
        String day = DateUtil.getYesterday();
        EnterpriseComprehensiveInfo enterpriseComprehensiveInfo =
                this.enterpriseComprehensiveMapper.getComprehensive(eid, day);
        if (enterpriseComprehensiveInfo == null) {
            return null;
        }
        EnterpriseComprehensiveResult result = new EnterpriseComprehensiveResult();
        result.setIndustry(enterpriseComprehensiveInfo.getIndustryName());
        String area = enterpriseComprehensiveInfo.getProvinceName();
        String cityName = enterpriseComprehensiveInfo.getCityName();
        if (StringUtils.isNotBlank(cityName)) {
            area += (StringUtils.isNotBlank(area) ? "-" : "") + cityName;
        }
        String countyName = enterpriseComprehensiveInfo.getCountyName();
        result.setArea(area);
        result.setCustomerStatus(enterpriseComprehensiveInfo.getCustomerStatusName());
        result.setBusinessCount(enterpriseComprehensiveInfo.getBusinessCount());
        result.setPackageCount(enterpriseComprehensiveInfo.getPackageCount());
        result.setGatewayCount(enterpriseComprehensiveInfo.getGatewayCount());
        result.setHighLoadingGatewayCount(enterpriseComprehensiveInfo.getHighLoadingGatewayCount());
        result.setLowLoadingGatewayCount(enterpriseComprehensiveInfo.getLowLoadingGatewayCount());
        result.setLongTimeIdleCount(enterpriseComprehensiveInfo.getLongTimeIdleCount());
        result.setOverLifeCount(enterpriseComprehensiveInfo.getOverLifeCount());
        result.setBandwidthNonsupport(enterpriseComprehensiveInfo.getBandwidthNonsupport());
        result.setNetworkSpeedExp(enterpriseComprehensiveInfo.getNetworkSpeedExp());
        result.setNetworkPingExp(enterpriseComprehensiveInfo.getNetworkPingExp());
        result.setCpuRamExp(enterpriseComprehensiveInfo.getCpuRamExp());

        return result;
    }

    @Override
    public PageInfoDto<ComprehensiveGatewayResult> getComprehensiveGateway(
            GetComprehensiveGatewayParam getComprehensiveGatewayParam) {
        PageInfoDto<ComprehensiveGatewayResult> pageInfoDto = new PageInfoDto<>();
        if (getComprehensiveGatewayParam == null) {
            return pageInfoDto;
        }
        if (getComprehensiveGatewayParam.getPage() == null || getComprehensiveGatewayParam.getPageSize() == null) {
            return pageInfoDto;
        }
        PageInfoParam pageInfoParam = new PageInfoParam();
        pageInfoParam.setPage(getComprehensiveGatewayParam.getPage());
        pageInfoParam.setPageSize(getComprehensiveGatewayParam.getPageSize());
        pageInfoParam.setEid(getComprehensiveGatewayParam.getEid());

        pageInfoDto.setPage(pageInfoParam.getPage());
        pageInfoDto.setPageSize(pageInfoParam.getPageSize());
        pageInfoDto.setTotal(0L);

        // highLoading 高负载网关;
        // lowLoading  低负载网关;
        // idle 空闲网关;
        // overLife 超期使用设备;
        // bandwidthNonsupport 带宽不匹配网关;
        // netBadPing ping网络质量差;
        // netBadSpeed 测速质量差;
        // cpuRam cpu内存告警网关
        String queryType = getComprehensiveGatewayParam.getQueryType();
        if (StringUtils.isBlank(queryType)) {
            return pageInfoDto;
        }
        switch (queryType) {
            case "base" : {
                return getGatewayBaseInfo(pageInfoParam, pageInfoDto);
            }
            case "highLoading" : {
                return getHighLoadingGateway(pageInfoParam, pageInfoDto);
            }
            case "lowLoading" : {
                return getLowLoadingGateway(pageInfoParam, pageInfoDto);
            }
            case "idle" : {
                return getIdleGateway(pageInfoParam, pageInfoDto);
            }
            case "overLife" : {
                return getOverLifeGateway(pageInfoParam, pageInfoDto);
            }
            case "bandwidthNonsupport" : {
                return getBandwidthNonsupportGateway(pageInfoParam, pageInfoDto);
            }
            case "netBadPing" : {
                return getNetBadPingGateway(pageInfoParam, pageInfoDto);
            }
            case "netBadSpeed" : {
                return pageInfoDto;
            }
            case "cpuRam" : {
                return getCpuRamGateway(pageInfoParam, pageInfoDto);
            }
            default: {
                return pageInfoDto;
            }
        }
    }

    private PageInfoDto<ComprehensiveGatewayResult> getGatewayBaseInfo(PageInfoParam pageInfoParam,
                                                                       PageInfoDto<ComprehensiveGatewayResult> pageInfoDto) {
        String day = DateUtil.getYesterday();
        Long eid = pageInfoParam.getEid();
        Long total = this.enterpriseComprehensiveMapper.countBaseGateway(eid, day);
        if (total == null || total == 0L) {
            pageInfoDto.setTotal(0L);
            return pageInfoDto;
        }
        pageInfoDto.setTotal(total);

        List<ComprehensiveGatewayInfo> gatewayInfoList =
                this.enterpriseComprehensiveMapper.getBaseGateway(eid, day,
                        pageInfoParam.getOffset(), pageInfoParam.getPageSize());

        List<ComprehensiveGatewayResult> resultList = buildGatewayResultInfo(gatewayInfoList);
        pageInfoDto.setList(resultList);
        return pageInfoDto;
    }

    @Override
    public PageInfoDto<ComprehensivePackageResult> getComprehensivePackage(
            GetComprehensivePackageParam getComprehensivePackageParam) {
        PageInfoDto<ComprehensivePackageResult> pageInfoDto = new PageInfoDto<>();
        if (getComprehensivePackageParam == null) {
            return pageInfoDto;
        }
        if (getComprehensivePackageParam.getPage() == null || getComprehensivePackageParam.getPageSize() == null) {
            return pageInfoDto;
        }
        PageInfoParam pageInfoParam = new PageInfoParam();
        pageInfoParam.setPage(getComprehensivePackageParam.getPage());
        pageInfoParam.setPageSize(getComprehensivePackageParam.getPageSize());
        pageInfoParam.setEid(getComprehensivePackageParam.getEid());

        pageInfoDto.setPage(pageInfoParam.getPage());
        pageInfoDto.setPageSize(pageInfoParam.getPageSize());
        pageInfoDto.setTotal(0L);
        String day = DateUtil.getYesterday();
        Long eid = pageInfoParam.getEid();
        Long total = this.enterpriseComprehensiveMapper.countPackage(eid, day);
        if (total == null || total == 0L) {
            pageInfoDto.setTotal(0L);
            return pageInfoDto;
        }
        pageInfoDto.setTotal(total);
        List<CustomerOrderAll> customerOrderAlls =
                this.enterpriseComprehensiveMapper.getPackage(eid, day,
                        pageInfoParam.getOffset(), pageInfoParam.getPageSize());

        List<ComprehensivePackageResult> resultList = new ArrayList<>();
        for (CustomerOrderAll customerOrder : customerOrderAlls) {
            ComprehensivePackageResult comprehensivePackageResult = new ComprehensivePackageResult();
            comprehensivePackageResult.setPackageCode(customerOrder.getProvincePackageCode());
            comprehensivePackageResult.setBandwidth(Objects.toString(customerOrder.getBandwidth(), ""));
            resultList.add(comprehensivePackageResult);
        }

        pageInfoDto.setList(resultList);
        return pageInfoDto;
    }

    private PageInfoDto<ComprehensiveGatewayResult> getCpuRamGateway(PageInfoParam pageInfoParam,
                                                                     PageInfoDto<ComprehensiveGatewayResult> pageInfoDto) {
        String day = DateUtil.getYesterday();
        Long eid = pageInfoParam.getEid();
        Long total = this.enterpriseComprehensiveMapper.countCpuRamGateway(eid, day);
        if (total == null || total == 0L) {
            pageInfoDto.setTotal(0L);
            return pageInfoDto;
        }
        pageInfoDto.setTotal(total);

        List<ComprehensiveGatewayInfo> gatewayInfoList =
                this.enterpriseComprehensiveMapper.getCpuRamGateway(eid, day,
                        pageInfoParam.getOffset(), pageInfoParam.getPageSize());

        List<ComprehensiveGatewayResult> resultList = buildGatewayResultInfo(gatewayInfoList);
        pageInfoDto.setList(resultList);
        return pageInfoDto;
    }

    private PageInfoDto<ComprehensiveGatewayResult> getNetBadPingGateway(PageInfoParam pageInfoParam,
                                                                         PageInfoDto<ComprehensiveGatewayResult> pageInfoDto) {
        String day = DateUtil.getYesterday();
        Long eid = pageInfoParam.getEid();
        Long total = this.enterpriseComprehensiveMapper.countNetBadPingGateway(eid, day);
        if (total == null || total == 0L) {
            pageInfoDto.setTotal(0L);
            return pageInfoDto;
        }
        pageInfoDto.setTotal(total);

        List<ComprehensiveGatewayInfo> gatewayInfoList =
                this.enterpriseComprehensiveMapper.getNetBadPingGateway(eid, day,
                        pageInfoParam.getOffset(), pageInfoParam.getPageSize());

        List<ComprehensiveGatewayResult> resultList = buildGatewayResultInfo(gatewayInfoList);
        pageInfoDto.setList(resultList);
        return pageInfoDto;
    }

    private PageInfoDto<ComprehensiveGatewayResult> getBandwidthNonsupportGateway(PageInfoParam pageInfoParam,
                                                                                  PageInfoDto<ComprehensiveGatewayResult> pageInfoDto) {
        String day = DateUtil.getYesterday();
        Long eid = pageInfoParam.getEid();
        Long total = this.enterpriseComprehensiveMapper.countBandwidthNonsupportGateway(eid, day);
        if (total == null || total == 0L) {
            pageInfoDto.setTotal(0L);
            return pageInfoDto;
        }
        pageInfoDto.setTotal(total);

        List<ComprehensiveGatewayInfo> gatewayInfoList =
                this.enterpriseComprehensiveMapper.getBandwidthNonsupportGateway(eid, day,
                        pageInfoParam.getOffset(), pageInfoParam.getPageSize());

        List<ComprehensiveGatewayResult> resultList = buildGatewayResultInfo(gatewayInfoList);
        pageInfoDto.setList(resultList);
        return pageInfoDto;
    }

    private PageInfoDto<ComprehensiveGatewayResult> getOverLifeGateway(PageInfoParam pageInfoParam,
                                                                       PageInfoDto<ComprehensiveGatewayResult> pageInfoDto) {
        String day = DateUtil.getYesterday();
        Long eid = pageInfoParam.getEid();
        Long total = this.enterpriseComprehensiveMapper.countOverLifeGateway(eid, day);
        if (total == null || total == 0L) {
            pageInfoDto.setTotal(0L);
            return pageInfoDto;
        }
        pageInfoDto.setTotal(total);

        List<ComprehensiveGatewayInfo> gatewayInfoList =
                this.enterpriseComprehensiveMapper.getOverLifeGateway(eid, day,
                        pageInfoParam.getOffset(), pageInfoParam.getPageSize());

        List<ComprehensiveGatewayResult> resultList = buildGatewayResultInfo(gatewayInfoList);
        pageInfoDto.setList(resultList);
        return pageInfoDto;
    }

    private PageInfoDto<ComprehensiveGatewayResult> getIdleGateway(PageInfoParam pageInfoParam,
                                                                   PageInfoDto<ComprehensiveGatewayResult> pageInfoDto) {
        String day = DateUtil.getYesterday();
        Long eid = pageInfoParam.getEid();
        Long total = this.enterpriseComprehensiveMapper.countIdleGateway(eid, day);
        if (total == null || total == 0L) {
            pageInfoDto.setTotal(0L);
            return pageInfoDto;
        }
        pageInfoDto.setTotal(total);

        List<ComprehensiveGatewayInfo> gatewayInfoList =
                this.enterpriseComprehensiveMapper.getIdleGateway(eid, day,
                        pageInfoParam.getOffset(), pageInfoParam.getPageSize());

        List<ComprehensiveGatewayResult> resultList = buildGatewayResultInfo(gatewayInfoList);
        pageInfoDto.setList(resultList);
        return pageInfoDto;
    }

    private PageInfoDto<ComprehensiveGatewayResult> getHighLoadingGateway(PageInfoParam pageInfoParam,
                                                                          PageInfoDto<ComprehensiveGatewayResult> pageInfoDto) {
        String month = DateUtil.getLastMonth();
        Long eid = pageInfoParam.getEid();
        Long total = this.enterpriseComprehensiveMapper.countHighLoadingGateway(eid, month);
        if (total == null || total == 0L) {
            pageInfoDto.setTotal(0L);
            return pageInfoDto;
        }
        pageInfoDto.setTotal(total);

        List<ComprehensiveGatewayInfo> gatewayInfoList =
                this.enterpriseComprehensiveMapper.getHighLoadingGateway(eid, month,
                        pageInfoParam.getOffset(), pageInfoParam.getPageSize());

        List<ComprehensiveGatewayResult> resultList = buildGatewayResultInfo(gatewayInfoList);
        pageInfoDto.setList(resultList);
        return pageInfoDto;
    }



    private PageInfoDto<ComprehensiveGatewayResult> getLowLoadingGateway(PageInfoParam pageInfoParam,
                                                                         PageInfoDto<ComprehensiveGatewayResult> pageInfoDto) {
        String month = DateUtil.getLastMonth();
        Long eid = pageInfoParam.getEid();
        Long total = this.enterpriseComprehensiveMapper.countLowLoadingGateway(eid, month);
        if (total == null || total == 0L) {
            pageInfoDto.setTotal(0L);
            return pageInfoDto;
        }
        pageInfoDto.setTotal(total);

        List<ComprehensiveGatewayInfo> gatewayInfoList =
                this.enterpriseComprehensiveMapper.getLowLoadingGateway(eid, month,
                        pageInfoParam.getOffset(), pageInfoParam.getPageSize());

        List<ComprehensiveGatewayResult> resultList = buildGatewayResultInfo(gatewayInfoList);
        pageInfoDto.setList(resultList);
        return pageInfoDto;
    }


/*
    public static void main(String[] args) {
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        System.out.println(decimalFormat.format(0.111));
        System.out.println(decimalFormat.format(11.111));
        System.out.println(decimalFormat.format(111.1));
        System.out.println(decimalFormat.format(0.1));
        System.out.println(decimalFormat.format(0));
        System.out.println(decimalFormat.format(111.124));
        System.out.println(decimalFormat.format(1111.115));
        System.out.println(decimalFormat.format(1.116));
    }
*/


    private List<ComprehensiveGatewayResult> buildGatewayResultInfo(List<ComprehensiveGatewayInfo> gatewayInfoList) {
        List<ComprehensiveGatewayResult> resultList = new ArrayList<>();
        for (ComprehensiveGatewayInfo data : gatewayInfoList) {
            ComprehensiveGatewayResult gatewayResult = new ComprehensiveGatewayResult();
            gatewayResult.setSn(data.getSn());
            gatewayResult.setMac(data.getMac());
            gatewayResult.setFactory(data.getFactoryName());
            gatewayResult.setDeviceModel(data.getDeviceModelName());
            String highTime = "";
            if (data.getHighLoadingTime() != null) {
                //格式化，按小时返回，保留两位小数
                try {
                    Double time = data.getHighLoadingTime() / 3600d;
                    DecimalFormat decimalFormat = new DecimalFormat("#.##");
                    String stime = decimalFormat.format(time);
                    highTime = stime;
                    gatewayResult.setHighLoadingTime(stime);
                } catch (Exception e) {
                    gatewayResult.setHighLoadingTime("");
                }
            }
            String runTime = "";
            if (data.getRunningTime() != null) {
                try {
                    Double time = data.getRunningTime() / 3600d;
                    DecimalFormat decimalFormat = new DecimalFormat("#.##");
                    String stime = decimalFormat.format(time);
                    runTime = stime;
                    gatewayResult.setRunningTime(stime);
                } catch (Exception e) {
                    gatewayResult.setRunningTime("");
                }
            }
            if (StringUtils.isNotBlank(data.getHighLoadingPercent())) {
                if (StringUtils.isNotBlank(highTime) && StringUtils.isNotBlank(runTime) && Objects.equals(highTime, runTime)) {
                    gatewayResult.setHighLoadingPercent("100");
                } else {
                    gatewayResult.setHighLoadingPercent(data.getHighLoadingPercent());
                }

            }
            String lowTime = "";
            if (data.getLowLoadingTime() != null) {
                try {
                    Double time = data.getLowLoadingTime() / 3600d;
                    DecimalFormat decimalFormat = new DecimalFormat("#.##");
                    String stime = decimalFormat.format(time);
                    lowTime = stime;
                    gatewayResult.setLowLoadingTime(stime);
                } catch (Exception e) {
                    gatewayResult.setLowLoadingTime("");
                }
            }
            if (StringUtils.isNotBlank(data.getLowLoadingPercent())) {
                if (StringUtils.isNotBlank(lowTime) && StringUtils.isNotBlank(runTime) && Objects.equals(lowTime, runTime)) {
                    gatewayResult.setLowLoadingPercent("100");
                } else {
                    gatewayResult.setLowLoadingPercent(data.getLowLoadingPercent());
                }

            }
            if (data.getIdleTime() != null) {
                gatewayResult.setIdleTime(Objects.toString(data.getIdleTime(), ""));
            }
            if (data.getUseTime() != null) {
                gatewayResult.setUseTime(Objects.toString(data.getUseTime(), ""));
            }
            if (data.getPlanUseTime() != null) {
                gatewayResult.setPlanUseTime(Objects.toString(data.getPlanUseTime(), ""));
            }
            if (data.getBandwidth() != null) {
                gatewayResult.setBandwidth(Objects.toString(data.getBandwidth(), ""));
            }
            if (data.getSupportBandwidth() != null) {
                gatewayResult.setSupportBandwidth(Objects.toString(data.getSupportBandwidth(), ""));
            }
            if (data.getPingTime() != null) {
                gatewayResult.setPingTime(Objects.toString(data.getPingTime(), ""));
            }
            if (data.getSpeedTestBandwidth() != null) {
                gatewayResult.setSpeedTestBandwidth(Objects.toString(data.getSpeedTestBandwidth(), ""));
            }
            if (data.getCpu() != null) {
                gatewayResult.setCpu(Objects.toString(data.getCpu(), ""));
            }
            if (data.getRam() != null) {
                gatewayResult.setRam(Objects.toString(data.getRam(), ""));
            }

            resultList.add(gatewayResult);
        }
        return resultList;
    }


}
