package com.cmiot.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.cmiot.report.bean.*;
import com.cmiot.report.dto.*;
import com.cmiot.report.dto.customerOrder.CustomerOrderCountData;
import com.cmiot.report.mapper.CustomerAllMapper;
import com.cmiot.report.mapper.CustomerOrderAggAllMapper;
import com.cmiot.report.mapper.DicAreaInfoMapper;
import com.cmiot.report.mapper.DictIndustryMapper;
import com.cmiot.report.service.CustomerAllViewService;
import com.cmiot.report.service.DicDataService;
import com.cmiot.report.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * author: ranwei
 * date: 2022/07/26 15:06
 * description:
 * 企业客户信息 - 企业客户全景视图
 * 1、企业客户区域分布
 * 2、新增企业客户统计
 * 3、存量企业客户统计
 * 4、企业行业统计
 */

@Service
@Slf4j
public class CustomerAllViewImpl implements CustomerAllViewService {

    @Autowired
    private DicAreaInfoMapper dicAreaInfoMapper;

    @Autowired
    private CustomerOrderAggAllMapper customerOrderAggAllMapper;

    @Autowired
    private CustomerAllMapper customerAllMapper;

    @Autowired
    private DicDataService dicDataService;

    @Autowired
    private DictIndustryMapper dictIndustryMapper;


    /**
     * 1、企业客户区域分布
     *
     * @param request
     * @return
     */
    @Override
    public EnterCustDistResult getEnterpriseCustomerDistribution(EnterCustomerRequest request) {
        log.info("企业客户全景视图 - 企业客户区域分布接口方法入参: {}", JSON.toJSONString(request));

        EnterCustDistResult result = new EnterCustDistResult();

        try {
            List<String> provCodes = request.getProvince();
            List<String> cityCodes = request.getCity();

            // 0：企业宽带 1：互联网专线
            String businessType = request.getBusinessType();

            // yyyy-MM-dd
            String startDate = request.getStartDate();
            String endDate = request.getEndDate();

            Date initDate = DateUtil.stringToDate("1970-01-01 08:00:00", DateUtil.DATE_TIME_PATTERN);

            // yyyy-MM-dd HH:mm:ss
            Date startTimeDate = DateUtil.stringToDate(startDate + " 00:00:00", DateUtil.DATE_TIME_PATTERN);
            Date endTimeDate = DateUtil.stringToDate(endDate + " 23:59:59", DateUtil.DATE_TIME_PATTERN);

            CustomerOrderAggAllExample example = new CustomerOrderAggAllExample();
            CustomerOrderAggAllExample.Criteria criteria = example.createCriteria();


            // 省份标识(1-省份,0-地市)
            int flag = 1;
            if (!CollectionUtils.isEmpty(provCodes)) {
                criteria.andProvinceCodeIn(provCodes);
            }
            if (!CollectionUtils.isEmpty(cityCodes)) {
                criteria.andCityCodeIn(cityCodes);
                flag = 0;
            }

            // 1171-互联网专线,1172-企业宽带,1173-商务快线,9999-其他
            if (StringUtils.isNotBlank(businessType)) {
                criteria.andBusinessTypeEqualTo(businessType);
            }

            CustBussQuery query = new CustBussQuery();
            if (!CollectionUtils.isEmpty(provCodes)) {
                query.setProvince(provCodes);
            }
            if (!CollectionUtils.isEmpty(cityCodes)) {
                query.setCity(cityCodes);
            }
            if (StringUtils.isNotBlank(businessType)) {
                query.setBusinessType(businessType);
            }
            query.setStartDate(startTimeDate);
            query.setEndDate(endTimeDate);
            query.setInitDate(initDate);

            // 存量客户数 - 客户建档日期小于等于endTimeDate
            long allCount = customerOrderAggAllMapper.countAllCustByQuery(query);

            // 分省&地市客户存量统计
            List<CustNameCount> allCustList = customerOrderAggAllMapper.countCustByQuery(query);


            // 业务生效中客户统计(周期内必须生效中) - 生效时间<=startTimeDate且(失效时间>endTimeDate或失效时间=1970-01-01 08:00:00)
            List<CustNameCount> onBussCustList = customerOrderAggAllMapper.countActiveCustByQuery(query);
            long onBussCount = onBussCustList.stream().mapToLong(CustNameCount::getCount).sum();
            String onBussPercent = getPercent(onBussCount, allCount);


            // 办理过业务客户统计(订购产品时间在指定时间范围内)
            criteria.andBusinessSubscribeTimeBetween(startTimeDate, endTimeDate);
            criteria.andBusinessSubscribeTimeGreaterThan(initDate);
            List<CustNameCount> bussCustList = customerOrderAggAllMapper.countBussCustByExample(example);

            // 办理过业务的客户数
            long bussCustCount = bussCustList.stream().mapToLong(CustNameCount::getCount).sum();
            String bussCustPercent = getPercent(bussCustCount, allCount);

            EnterCustDistOverView overView = new EnterCustDistOverView();


            // 数据组合
            List<EnterCustDistMapData> mapDataList = getEnterCustMapData(provCodes, cityCodes, allCustList, allCount, bussCustList, onBussCustList, flag);

            overView.setCustomerNum(allCount);
            overView.setOnBusinessCustomerRate(Float.parseFloat(onBussPercent));
            overView.setBusinessCustomerRate(Float.parseFloat(bussCustPercent));

            result.setOverView(overView);
            result.setMapData(CollectionUtils.isEmpty(mapDataList) ? new ArrayList<>() : mapDataList);
        } catch (Exception e) {
            log.info("企业客户全景视图 - 企业客户区域分布接口查询异常: {}", e.getMessage(), e);
        }

        log.info("企业客户全景视图 - 企业客户区域分布接口方法出参: {}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 2、新增企业客户统计
     *
     * @param request
     * @return
     */
    @Override
    public NewCustomerStatisticResult getNewCustomerStatistic(EnterCustomerRequest request) {
        log.info("企业客户全景视图 - 新增企业客户统计接口方法入参: {}", JSON.toJSONString(request));

        NewCustomerStatisticResult result = new NewCustomerStatisticResult();

        try {
            List<String> provCodes = request.getProvince();
            List<String> cityCodes = request.getCity();

            // 0：企业宽带 1：互联网专线
            String businessType = request.getBusinessType();

            // yyyy-MM-dd
            String startDate = request.getStartDate();
            String endDate = request.getEndDate();

            // 初始日期
            Date initDate = DateUtil.stringToDate("1970-01-01 08:00:00", DateUtil.DATE_TIME_PATTERN);

            // yyyy-MM-dd HH:mm:ss
            Date startTimeDate = DateUtil.stringToDate(startDate + " 00:00:00", DateUtil.DATE_TIME_PATTERN);
            Date endTimeDate = DateUtil.stringToDate(endDate + " 23:59:59", DateUtil.DATE_TIME_PATTERN);

            CustomerOrderAggAllExample example = new CustomerOrderAggAllExample();
            CustomerOrderAggAllExample.Criteria criteria = example.createCriteria();

            if (!CollectionUtils.isEmpty(provCodes)) {
                criteria.andProvinceCodeIn(provCodes);
            }
            if (!CollectionUtils.isEmpty(cityCodes)) {
                criteria.andCityCodeIn(cityCodes);
            }
            if (StringUtils.isNotBlank(businessType)) {
                criteria.andBusinessTypeEqualTo(businessType);
            }

            // 企业新增客户：集团客户建档时间(create_time)为指定时间内,则为新增客户
            criteria.andCreateTimeBetween(startTimeDate, endTimeDate);

            // 企业新增客户数统计
            long newCustCount = customerOrderAggAllMapper.countNewCustByExample(example);

            // 企业新增客户列表
            List<NewCustNameCount> newCustCountList = customerOrderAggAllMapper.countNewCustList(example);
            Map<String, Long> newCustCountMap = newCustCountList.stream().collect(Collectors.groupingBy(NewCustNameCount::getDate, Collectors.reducing(0L, NewCustNameCount::getCount, Long::sum)));


            // 首次新增客户且办理了业务的客户
            // 当天: 当天客户建档,且在当天办理了业务
            // 本月: 当月客户建档,且在当月内办理过业务(不限定当天建档当天办理业务)
            // 产品订购时间在周期内
            criteria.andBusinessSubscribeTimeGreaterThan(initDate);
            criteria.andBusinessSubscribeTimeBetween(startTimeDate, endTimeDate);

            // 新增客户且办理了业务的客户统计
            long newCustBussCount = customerOrderAggAllMapper.countNewCustBussByExample(example);

            // 新增客户且办理了业务的客户列表
            List<NewCustNameCount> newCustBussCountList = customerOrderAggAllMapper.countNewCustBussList(example);
            Map<String, Long> newCustBussCountMap = newCustBussCountList.stream().collect(Collectors.groupingBy(NewCustNameCount::getDate, Collectors.reducing(0L, NewCustNameCount::getCount, Long::sum)));

            List<String> dateList = DateUtil.splitDateList(startDate, endDate);

            List<NewCustomerChartData> chartDataList = new ArrayList<>();

            // date: yyyy-MM-dd
            dateList.forEach(date -> {
                NewCustomerChartData chartData = new NewCustomerChartData();
                chartData.setDate(date);
                long newCustDateCount = newCustCountMap.getOrDefault(date, 0L);
                long newCustBussDateCount = newCustBussCountMap.getOrDefault(date, 0L);

                chartData.setNewCustomer(newCustDateCount);
                chartData.setBusinnessNewCustomer(newCustBussDateCount);
                String newCustBussPercent = getPercent(newCustBussDateCount, newCustDateCount);
                chartData.setBusinnessNewCustomerRate(Float.parseFloat(newCustBussPercent));
                chartDataList.add(chartData);
            });

            NewCustomerOverView overView = new NewCustomerOverView();
            overView.setNewCustomerNum(newCustCount);
            overView.setFirstBusinessCustomer(newCustBussCount);

            result.setOverView(overView);
            result.setChartData(chartDataList);
        } catch (Exception e) {
            log.info("企业客户全景视图 - 新增企业客户统计接口查询异常: {}", e.getMessage(), e);
        }

        log.info("企业客户全景视图 - 新增企业客户统计接口方法出参: {}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 3、企业存量客户统计
     *
     * @param request
     * @return
     */
    @Override
    public CustomerStatisticResult getCustomerStatistic(EnterCustomerRequest request) {
        log.info("企业客户全景视图 - 存量企业客户统计接口方法入参: {}", JSON.toJSONString(request));
        long startTimeAll = System.currentTimeMillis();
        CustomerStatisticResult result = new CustomerStatisticResult();

        try {
            List<String> provCodes = request.getProvince();
            List<String> cityCodes = request.getCity();

            // 0：企业宽带 1：互联网专线
            String businessType = request.getBusinessType();

            // yyyy-MM-dd
            String startDate = request.getStartDate();
            String endDate = request.getEndDate();

            // yyyy-MM-dd HH:mm:ss
            Date startTimeDate = DateUtil.stringToDate(startDate + " 00:00:00", DateUtil.DATE_TIME_PATTERN);
            Date endTimeDate = DateUtil.stringToDate(endDate + " 23:59:59", DateUtil.DATE_TIME_PATTERN);

            // 默认时间
            Date initDate = DateUtil.stringToDate("1970-01-01 08:00:00", DateUtil.DATE_TIME_PATTERN);

            CustBussQuery query = new CustBussQuery();
            if (!CollectionUtils.isEmpty(provCodes)) {
                query.setProvince(provCodes);
            }
            if (!CollectionUtils.isEmpty(cityCodes)) {
                query.setCity(cityCodes);
            }
            if (StringUtils.isNotBlank(businessType)) {
                query.setBusinessType(businessType);
            }
            query.setInitDate(initDate);

            //并行执行

            CompletableFuture<CustomerOrderCountData> baseCount = CompletableFuture.supplyAsync(() -> {
                long startTime = System.currentTimeMillis();
                CustomerOrderCountData customerOrderCountData = customerOrderAggAllMapper.countBase(query);
                log.info("获取基础信息统计耗时: {}ms", (System.currentTimeMillis() - startTime));
                return customerOrderCountData;
            });


            // 存量客户数(不受时间筛选条件控制，仅总量),截止目前
            //long allCount = customerOrderAggAllMapper.countAllCustByQuery(query);


            // 未办理业务客户总数（不受时间筛选条件控制,仅总量）,截止目前,包含从未办理，已退订，已到期
            // 未办理业务客户: 不存在于产品订购状态business_status等于1,或者套餐订购状态package_status等于1
            //long allNoBussCount = customerOrderAggAllMapper.countCustNoBussByQuery(query);


            // 已办理客户总数（不受时间筛选条件控制，仅总量）,截止目前,包含已办理未生效、已生效
            // 已生效-(已办理未生效+已生效) 产品订购状态=1
            //long allExistBussCount = customerOrderAggAllMapper.countCustExistBussByQuery(query);

            // 业务办理率（已办理客户总数/存量客户总数）
            //float custExistBussPercent = Float.parseFloat(getPercent(allExistBussCount, allCount));

            query.setStartDate(startTimeDate);
            query.setEndDate(endTimeDate);

            // 周期内新办理业务的存量客户数：包含已办理未生效，已生效。（包含已退订、已到期但重新办理了）
            // 产品订购日期在周期内,且企业存在于订购日期小于周期起始日期的并办理过业务的企业
            //long allNewBussCustCount = customerOrderAggAllMapper.countCustNewBussByQuery(query);

            CompletableFuture<Long> allNewBussCustFuture = CompletableFuture.supplyAsync(() -> {
                long startTime = System.currentTimeMillis();
                Long re = customerOrderAggAllMapper.countCustNewBussByQuery(query);
                log.info("新办理数量统计耗时: {}ms", (System.currentTimeMillis() - startTime));
                return re;
            });

            // 周期内新办理业务的存量客户趋势图不包含退订,即: 周期内办理了业务且生效中,同首次办理
            // 订购时间在周期内,退订时间不在周期内
            //List<NewCustNameCount> newBussCustList = customerOrderAggAllMapper.countCustNewBussListByQuery(query);
            //Map<String, Long> newBussCustMap = newBussCustList.stream().collect(Collectors.groupingBy(NewCustNameCount::getDate, Collectors.reducing(0L, NewCustNameCount::getCount, Long::sum)));
            CompletableFuture<Map<String, Long> > newBussCustMapFuture = CompletableFuture.supplyAsync(() -> {
                long startTime = System.currentTimeMillis();
                List<NewCustNameCount> newBussCustList = customerOrderAggAllMapper.countCustNewBussListByQuery(query);
                Map<String, Long> newBussCustMap = newBussCustList.stream().collect(Collectors.groupingBy(NewCustNameCount::getDate, Collectors.reducing(0L, NewCustNameCount::getCount, Long::sum)));
                log.info("新办理趋势统计耗时: {}ms", (System.currentTimeMillis() - startTime));
                return newBussCustMap;
            });

            // 周期内首次办理业务的存量客户数：包含已办理未生效，已生效。（不包含已退订、已到期但重新办理了）
            // 即: 周期内办理了业务且生效中
            // 产品订购日期和套餐生效日期在周期内,或者产品订购日期在周期内和套餐生效日期为默认值(1970-01-01 08:00:00),且企业不存在于订购日期小于周期起始日期的企业
            //long firstBussCustCount = customerOrderAggAllMapper.queryCustFirstNewBuss(query);
            CompletableFuture<Long> firstBussCustCountFuture = CompletableFuture.supplyAsync(() -> {
                long startTime = System.currentTimeMillis();
                Long re = customerOrderAggAllMapper.queryCustFirstNewBussV2(query);
                log.info("首次办理数量统计耗时: {}ms", (System.currentTimeMillis() - startTime));
                return re;
            });


            // 分日期周期内首次办理业务存量计客户列表,不计算退订
            //List<NewCustNameCount> firstBussCustList = customerOrderAggAllMapper.queryCustFirstNewBussList(query);
           // Map<String, Long> firstBussCustMap = firstBussCustList.stream().collect(Collectors.groupingBy(NewCustNameCount::getDate, Collectors.reducing(0L, NewCustNameCount::getCount, Long::sum)));
            CompletableFuture<Map<String, Long> > firstBussCustMapFuture = CompletableFuture.supplyAsync(() -> {
                long startTime = System.currentTimeMillis();
                List<NewCustNameCount> firstBussCustList = customerOrderAggAllMapper.queryCustFirstNewBussListV2(query);
                Map<String, Long> firstBussCustMap = firstBussCustList.stream().collect(Collectors.groupingBy(NewCustNameCount::getDate, Collectors.reducing(0L, NewCustNameCount::getCount, Long::sum)));
                log.info("首次办理趋势统计耗时: {}ms", (System.currentTimeMillis() - startTime));
                return firstBussCustMap;
            });


            // 周期内退订业务的存量客户数：已退订,已到期
            //long unsubBussCustCount = customerOrderAggAllMapper.countUnsubBussByQuery(query);
            CompletableFuture<Long> unsubBussCustCountFuture = CompletableFuture.supplyAsync(() -> {
                long startTime = System.currentTimeMillis();
                Long re = customerOrderAggAllMapper.countUnsubBussByQuery(query);
                log.info("退订数量统计耗时: {}ms", (System.currentTimeMillis() - startTime));
                return re;
            });

            //List<NewCustNameCount> unsubBussCustList = customerOrderAggAllMapper.countCustUnsubBussListByQuery(query);
            //Map<String, Long> unsubBussCustMap = unsubBussCustList.stream().collect(Collectors.groupingBy(NewCustNameCount::getDate, Collectors.reducing(0L, NewCustNameCount::getCount, Long::sum)));
            CompletableFuture<Map<String, Long> > unsubBussCustMapFuture = CompletableFuture.supplyAsync(() -> {
                long startTime = System.currentTimeMillis();
                List<NewCustNameCount> unsubBussCustList = customerOrderAggAllMapper.countCustUnsubBussListByQuery(query);
                Map<String, Long> unsubBussCustMap = unsubBussCustList.stream().collect(Collectors.groupingBy(NewCustNameCount::getDate, Collectors.reducing(0L, NewCustNameCount::getCount, Long::sum)));
                log.info("退订趋势统计耗时: {}ms", (System.currentTimeMillis() - startTime));
                return unsubBussCustMap;
            });


            CustomerOrderCountData customerOrderCountData = baseCount.get();
            long allCount = customerOrderCountData.getAllCount();
            long allExistBussCount = customerOrderCountData.getAllExistBussCount();
            long allNoBussCount  = allCount - allExistBussCount;
            float custExistBussPercent = Float.parseFloat(getPercent(allExistBussCount, allCount));

            long allNewBussCustCount = allNewBussCustFuture.get();
            Map<String, Long> newBussCustMap = newBussCustMapFuture.get();
            long firstBussCustCount = firstBussCustCountFuture.get();
            Map<String, Long> firstBussCustMap = firstBussCustMapFuture.get();
            long unsubBussCustCount = unsubBussCustCountFuture.get();
            Map<String, Long> unsubBussCustMap = unsubBussCustMapFuture.get();

            List<String> dateList = DateUtil.splitDateList(startDate, endDate);

            List<CustomerChartData> chartDataList = new ArrayList<>();
            // yyyy-MM-DD
            dateList.forEach(date -> {
                CustomerChartData chartData = new CustomerChartData();
                chartData.setDate(date);
                long newBussCustCount = newBussCustMap.getOrDefault(date, 0L);
                long firstBusssCustCount = firstBussCustMap.getOrDefault(date, 0L);
                long unsubCustCount = unsubBussCustMap.getOrDefault(date, 0L);
                chartData.setNewCustomer(newBussCustCount);
                chartData.setFirstBusinessCustomer(firstBusssCustCount);
                chartData.setCancelBusinessCustomer(unsubCustCount);
                chartDataList.add(chartData);
            });

            CustomerStatisticData data = new CustomerStatisticData();
            data.setApprovalCustomerNum(allCount);
            data.setUnBusinessCustomerNum(allNoBussCount);
            data.setBusinessCustomerNum(allExistBussCount);
            data.setBusinessRate(custExistBussPercent);
            data.setNewBusinessCustomerNum(allNewBussCustCount);
            data.setFirstBusinessCustomerNum(firstBussCustCount);
            data.setCancelOderCustomerNum(unsubBussCustCount);

            result.setData(data);
            result.setChartData(CollectionUtils.isEmpty(chartDataList) ? new ArrayList<>() : chartDataList);

        } catch (Exception e) {
            log.info("企业客户全景视图 - 存量企业客户统计接口查询异常: {}", e.getMessage(), e);
        }

        log.info("企业客户全景视图 - 存量企业客户统计接口方法出参, 耗时: {}ms", (System.currentTimeMillis() - startTimeAll));
        return result;
    }


    /**
     * 4、企业行业新增客户统计
     *
     * @param request
     * @return
     */
    @Override
    public NewIndustryCustomerResult getNewIndustryCustomerStatistic(EnterCustomerRequest request) {
        log.info("企业客户全景视图 - 企业行业新增客户统计接口方法入参: {}", JSON.toJSONString(request));

        NewIndustryCustomerResult result = new NewIndustryCustomerResult();

        try {
            List<String> provCodes = request.getProvince();
            List<String> cityCodes = request.getCity();

            // 0：企业宽带 1：互联网专线
            String businessType = request.getBusinessType();

            // yyyy-MM-dd
            String startDate = request.getStartDate();
            String endDate = request.getEndDate();

            // yyyy-MM-dd HH:mm:ss
            Date startTimeDate = DateUtil.stringToDate(startDate + " 00:00:00", DateUtil.DATE_TIME_PATTERN);
            Date endTimeDate = DateUtil.stringToDate(endDate + " 23:59:59", DateUtil.DATE_TIME_PATTERN);

            CustomerOrderAggAllExample example = new CustomerOrderAggAllExample();
            CustomerOrderAggAllExample.Criteria criteria = example.createCriteria();

            if (!CollectionUtils.isEmpty(provCodes)) {
                criteria.andProvinceCodeIn(provCodes);
            }
            if (!CollectionUtils.isEmpty(cityCodes)) {
                criteria.andCityCodeIn(cityCodes);
            }
            if (StringUtils.isNotBlank(businessType)) {
                criteria.andBusinessTypeEqualTo(businessType);
            }

            // 企业新增客户：集团客户建档时间(create_time)为指定时间内,则为新增客户
            criteria.andCreateTimeBetween(startTimeDate, endTimeDate);

            List<NewIndusCustCount> list = customerOrderAggAllMapper.countNewIndusCustListByExample(example);
            long allCount = list.stream().mapToLong(NewIndusCustCount::getCount).sum();

            List<NewIndustryCustomer> dataList = new ArrayList<>();

            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(item -> {
                    NewIndustryCustomer customer = new NewIndustryCustomer();
                    String industry = item.getIndustry();
                    long count = item.getCount();
                    float rate = Float.parseFloat(getPercent(count, allCount));
                    customer.setIndustryName(industry);
                    customer.setNewCompanyNum(count);
                    customer.setRate(rate);
                    dataList.add(customer);
                });
            } /*else {
                listAll.forEach(item -> {
                    NewIndustryCustomer customer = new NewIndustryCustomer();
                    String industry_name = item.getIndustry_name();
                    customer.setIndustryName(industry_name);
                    customer.setNewCompanyNum(0L);
                    customer.setRate(0);
                    dataList.add(customer);
                });
                log.info("企业客户全景视图 - 企业行业新增客户统计结果为空, 设置默认值. ");
            }*/
            dataList.sort(Comparator.comparing(NewIndustryCustomer::getNewCompanyNum).reversed());
            result.setList(dataList);

        } catch (Exception e) {
            log.info("企业客户全景视图 - 企业行业新增客户统计接口查询异常: {}", e.getMessage(), e);
        }

        log.info("企业客户全景视图 - 企业行业新增统计接口方法出参: {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 查询客户名称
     *
     * @param eid
     * @return
     */
    @Override
    public String getCustomerName(long eid) {
        return customerAllMapper.selectNameByExample(eid);
    }


    private List<EnterCustDistMapData> getEnterCustMapData(List<String> provCodes, List<String> cityCodes, List<CustNameCount> allCustList, long allCount, List<CustNameCount> bussCustList, List<CustNameCount> onBussCustList, int flag) {

        List<EnterCustDistMapData> mapDataList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(allCustList)) {
            // 多个其他的存量客户数
            Map<String, Long> existOther = new HashMap<>();
            // 其他的办理业务客户数
            Map<String, Long> bussCustOther = new HashMap<>();
            // 其他的业务生效中的客户数
            Map<String, Long> onBussCustOther = new HashMap<>();

            // 分省
            if (flag == 1) {
                // 存量客户(provCode,count)
                Map<String, Long> allProvMap = allCustList.stream().collect(Collectors.groupingBy(CustNameCount::getProvince, Collectors.reducing(0L, CustNameCount::getCount, Long::sum)));
                // 办理过业务客户(provCode,count)
                Map<String, Long> bussCustProvMap = bussCustList.stream().collect(Collectors.groupingBy(CustNameCount::getProvince, Collectors.reducing(0L, CustNameCount::getCount, Long::sum)));
                // 业务生效中客户(provCode,count)
                Map<String, Long> onBussCustProvMap = onBussCustList.stream().collect(Collectors.groupingBy(CustNameCount::getProvince, Collectors.reducing(0L, CustNameCount::getCount, Long::sum)));
                Set<String> provSet = new HashSet<>();
                // (provCode,name)
                Map<String, String> provMap = getDicPtovMap();
                if (CollectionUtils.isEmpty(provCodes)) {
                    provSet.addAll(provMap.keySet());
                }
                provSet.addAll(allProvMap.keySet());
                provSet.forEach(provCode -> {
                    EnterCustDistMapData distMapData = new EnterCustDistMapData();
                    String provName = provMap.getOrDefault(provCode, "其他");
                    Long allProvCount = allProvMap.getOrDefault(provCode, 0L);
                    Long bussCustProvCount = bussCustProvMap.getOrDefault(provCode, 0L);
                    Long onBussCustProvCount = onBussCustProvMap.getOrDefault(provCode, 0L);

                    if (!provName.equals("其他")) {
                        String bussCustProvPercent = getPercent(bussCustProvCount, allProvCount);
                        String onBussCustProvPercent = getPercent(onBussCustProvCount, allProvCount);
                        String existProvPercent = getPercent(allProvCount, allCount);
                        distMapData.setExistPercent(Float.parseFloat(existProvPercent));

                        distMapData.setName(provName);
                        distMapData.setValue(allProvCount);
                        distMapData.setPercent(Float.parseFloat(bussCustProvPercent));
                        distMapData.setValidatePercent(Float.parseFloat(onBussCustProvPercent));
                        if (allProvCount > 0) {
                            mapDataList.add(distMapData);
                        }
                    } else {
                        if (existOther.isEmpty()) {
                            existOther.put("其他", allProvCount);
                            bussCustOther.put("其他", bussCustProvCount);
                            onBussCustOther.put("其他", onBussCustProvCount);
                        } else {
                            Long aLong = existOther.get("其他");
                            Long bLong = bussCustOther.get("其他");
                            Long oLong = onBussCustOther.get("其他");

                            existOther.put("其他", allProvCount + aLong);
                            bussCustOther.put("其他", bussCustProvCount + bLong);
                            onBussCustOther.put("其他", onBussCustProvCount + oLong);
                        }
                    }
                });
            } else {
                Map<String, Long> allCityMap = allCustList.stream().collect(Collectors.groupingBy(CustNameCount::getCity, Collectors.reducing(0L, CustNameCount::getCount, Long::sum)));
                Map<String, Long> bussCustCityMap = bussCustList.stream().collect(Collectors.groupingBy(CustNameCount::getCity, Collectors.reducing(0L, CustNameCount::getCount, Long::sum)));
                Map<String, Long> onBussCustCityMap = onBussCustList.stream().collect(Collectors.groupingBy(CustNameCount::getCity, Collectors.reducing(0L, CustNameCount::getCount, Long::sum)));
                Set<String> citySet = new HashSet<>();
                // (cityCode,name)
                Map<String, String> cityMap = getDicCityMap();
                if (CollectionUtils.isEmpty(cityCodes)) {
                    citySet.addAll(cityMap.keySet());
                }
                citySet.addAll(allCityMap.keySet());
                citySet.forEach(cityCode -> {
                    EnterCustDistMapData distMapData = new EnterCustDistMapData();
                    String provName = cityMap.getOrDefault(cityCode, "其他");
                    Long allCityCount = allCityMap.getOrDefault(cityCode, 0L);
                    Long bussCustProvCount = bussCustCityMap.getOrDefault(cityCode, 0L);
                    Long onBussCustProvCount = onBussCustCityMap.getOrDefault(cityCode, 0L);

                    if (!provName.equals("其他")) {
                        String bussCustProvPercent = getPercent(bussCustProvCount, allCityCount);
                        String onBussCustProvPercent = getPercent(onBussCustProvCount, allCityCount);
                        String existCityPercent = getPercent(allCityCount, allCount);
                        distMapData.setExistPercent(Float.parseFloat(existCityPercent));

                        distMapData.setName(provName);
                        distMapData.setValue(allCityCount);
                        distMapData.setPercent(Float.parseFloat(bussCustProvPercent));
                        distMapData.setValidatePercent(Float.parseFloat(onBussCustProvPercent));
                        if (allCityCount > 0) {
                            mapDataList.add(distMapData);
                        }
                    } else {
                        if (existOther.isEmpty()) {
                            existOther.put("其他", allCityCount);
                            bussCustOther.put("其他", bussCustProvCount);
                            onBussCustOther.put("其他", onBussCustProvCount);
                        } else {
                            Long aLong = existOther.get("其他");
                            Long bLong = bussCustOther.getOrDefault("其他", 0L);
                            Long oLong = onBussCustOther.getOrDefault("其他", 0L);

                            existOther.put("其他", allCityCount + aLong);
                            bussCustOther.put("其他", bussCustProvCount + bLong);
                            onBussCustOther.put("其他", onBussCustProvCount + oLong);
                        }
                    }
                });
            }
            EnterCustDistMapData otherMapData = new EnterCustDistMapData();
            if (!existOther.isEmpty()) {
                otherMapData.setName("其他");
                Long other = existOther.get("其他");
                otherMapData.setValue(other);
                otherMapData.setPercent(Float.parseFloat(getPercent(bussCustOther.getOrDefault("其他", 0L), other)));
                otherMapData.setValidatePercent(Float.parseFloat(getPercent(onBussCustOther.getOrDefault("其他", 0L), other)));
                otherMapData.setExistPercent(Float.parseFloat(getPercent(other, allCount)));
                mapDataList.add(otherMapData);
            }


            // 倒序排序
            mapDataList.sort(Comparator.comparing(EnterCustDistMapData::getValue).reversed());
        }

        return mapDataList;
    }


    private CustomerOrderAggAllExample.Criteria resetCriteria(CustomerOrderAggAllExample example, List<String> provCodes, List<String> cityCodes, String businessType) {
        example.getOredCriteria().clear();
        CustomerOrderAggAllExample.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(provCodes)) {
            criteria.andProvinceCodeIn(provCodes);
        }
        if (!CollectionUtils.isEmpty(cityCodes)) {
            criteria.andCityCodeIn(cityCodes);
        }
        if (StringUtils.isNotBlank(businessType)) {
            criteria.andBusinessTypeEqualTo(businessType);
        }
        criteria.andSampleTimeIsNotNull();
        return criteria;
    }


    private String decimalFormat(String num) {
        String result;
        if (StringUtils.isNotBlank(num) && num.contains(".")) {
            DecimalFormat df = new DecimalFormat("0.00");
            result = df.format(Double.parseDouble(num));
        } else {
            result = num;
        }

        return result;
    }

    private String getPercent(Long num, Long total) {
        String perp;
        try {
            if (Objects.equals(num, 0L)) {
                perp = "0";
            } else if (Objects.equals(num, total)) {
                perp = "100";
            } else {
                perp = decimalFormat(String.valueOf(new BigDecimal((float) num / total).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() * 100));
            }
        } catch (Exception e) {
            perp = "0";
        }

        return perp;
    }

    public Map<String, String> getDicPtovMap() {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andLevelEqualTo(1);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        return dicAreaInfoList.stream().distinct().collect(Collectors.toMap(DicAreaInfo::getGcode, DicAreaInfo::getGname))
                .entrySet().stream().collect(Collectors.toMap(m -> String.valueOf(m.getKey()), Map.Entry::getValue));
    }

    public Map<String, String> getDicAreaMap() {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andLevelEqualTo(1);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectSubProvNameByExample(example);
        return dicAreaInfoList.stream().distinct().collect(Collectors.toMap(DicAreaInfo::getGcode, DicAreaInfo::getGname))
                .entrySet().stream().collect(Collectors.toMap(m -> String.valueOf(m.getKey()), Map.Entry::getValue));
    }

    public Map<String, String> getDicCityMap() {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andLevelEqualTo(2);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        return dicAreaInfoList.stream().collect(Collectors.toMap(DicAreaInfo::getGcode, DicAreaInfo::getGname))
                .entrySet().stream().collect(Collectors.toMap(m -> String.valueOf(m.getKey()), Map.Entry::getValue));
    }

    public Map<Long, String> getDictIndustryMap() {
        DictIndustryExample example = new DictIndustryExample();
        List<DictIndustry> industryList = dictIndustryMapper.selectByExample(example);
        return industryList.stream().collect(Collectors.toMap(DictIndustry::getId, DictIndustry::getIndustryName));
    }

}
