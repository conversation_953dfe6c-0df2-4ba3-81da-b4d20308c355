package com.cmiot.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.fgeo.taskmanager.enums.ExportStatusEnum;
import com.cmiot.report.bean.*;
import com.cmiot.report.common.poi.ExcelUtils;
import com.cmiot.report.dto.*;
import com.cmiot.report.mapper.*;
import com.cmiot.report.service.DicDataService;
import com.cmiot.report.service.ExportService;
import com.cmiot.report.service.GatewayCountService;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.PermissionUtils;
import com.cmiot.report.util.TimerUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * author: ranwei
 * date: 2022/03/09 9:24
 * description:
 * 网关全量,概览,增量,在线离线查询;
 */

@Service
public class GatewayCountServiceImpl implements GatewayCountService {

    private final static Logger logger = LoggerFactory.getLogger(GatewayCountServiceImpl.class);

    @Autowired
    private GatewayOverviewCountMapper gatewayOverviewCountMapper;

    @Autowired
    private GatewayCountMapper gatewayCountMapper;

    @Autowired
    private GatewayIncrementCountMapper gatewayIncrementCountMapper;

    @Autowired
    private GatewayOnOfflineDetailMapper gatewayOnOfflineDetailMapper;

    @Autowired
    private DicAreaInfoMapper dicAreaInfoMapper;

    @Autowired
    private DicFactoryMapper dicFactoryMapper;

    @Autowired
    private DictIndustryMapper dictIndustryMapper;

    @Autowired
    private GatewayAlarmDetailMapper gatewayAlarmDetailMapper;

    @Autowired
    private AlarmThresholdMapper alarmThresholdMapper;

    @Autowired
    private PonPowerStatisticsDetailMapper ponPowerStatisticsDetailMapper;

    @Autowired
    private PonPowerListDetailMapper ponPowerListDetailMapper;

    @Autowired
    private GatewayTotalCountMapper gatewayTotalCountMapper;

    @Autowired
    private DictDeviceModelMapper dictDeviceModelMapper;

    @Autowired
    private DicDataService dicDataService;

    /**
     * 导出工具。
     */
    @Autowired
    private ExportService exportService;

    /**
     * 临时目录
     */
    @Value("${export.tmp.local}")
    private String tmpPath;

    /**
     * 1 使用内网地址，否则使用外网地址
     */
    @Value("${export.use-pri-url}")
    private Integer usePriUrl;


    /*
     * 前端请求为code,后端返回name为中文名称;
     * 如省份: 请求code为51000,返回name为江苏;
     */


    /**
     * 网关概览
     *
     * @return
     * @param provinceCode
     */
    @Override
    public GatewayOverviewResult getOverviewCount(List<String> provinceCode) {
        logger.info("网关分布全景视图概览-网关数据查询，接口方法入参=：{}, 日期: {}", JSON.toJSONString(provinceCode), LocalDate.now());

        GatewayOverviewResult overviewResult = new GatewayOverviewResult();

        try {
            LocalDate lastDayLocal = LocalDate.now().minusDays(1);

            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
            long lastday = Long.parseLong(lastDayLocal.format(df));

            List<GatewayOverviewCount> list = gatewayOverviewCountMapper.selectByDay(lastday, provinceCode);
            //logger.info("网关分布全景视图概览-网关数据查询，接口方法入参=：{},", JSON.toJSONString(list));

            if (!CollectionUtils.isEmpty(list)) {
                Long gatewayCount = 0L;
                Long gatewayIncCount = 0L;
                Long gatewayActiveCount = 0L;
                Long gatewayLostCount = 0L;
                Double gatewayYoyRatio = 0D;
                Double gatewayMomRatio = 0D;
                Long connectFamilyGatewayNum = 0L;
                Double connectFamilyGatewayRate = 0D;

                for (GatewayOverviewCount overviewCount : list) {
                    gatewayCount += overviewCount.getGatewayCount();
                    gatewayIncCount += overviewCount.getGatewayIncCount();
                    gatewayActiveCount += overviewCount.getGatewayActiveCount();
                    gatewayLostCount += overviewCount.getGatewayLostCount();
                    connectFamilyGatewayNum += overviewCount.getConnectFamilyGatewayNum();
                    connectFamilyGatewayRate += overviewCount.getConnectFamilyGatewayRate();
                }
                LocalDate lastMonthLocal = lastDayLocal.minusMonths(1);
                long lastMonth = Long.parseLong(lastMonthLocal.format(df));
                List<GatewayOverviewCount> lastMonthList = gatewayOverviewCountMapper.selectByDay(lastMonth, provinceCode);
                Long lastMonthGatewayCount = 0L;
                if (lastMonthList != null && !lastMonthList.isEmpty()) {
                    for (GatewayOverviewCount overviewCount : lastMonthList) {
                        lastMonthGatewayCount += overviewCount.getGatewayCount();
                    }
                }
                LocalDate lastYearLocal = lastDayLocal.minusYears(1);
                long lastYear = Long.parseLong(lastYearLocal.format(df));
                List<GatewayOverviewCount> lastYearList = gatewayOverviewCountMapper.selectByDay(lastYear, provinceCode);
                Long lastYearGatewayCount = 0L;
                if (lastYearList != null && !lastYearList.isEmpty()) {
                    for (GatewayOverviewCount overviewCount : lastYearList) {
                        lastYearGatewayCount += overviewCount.getGatewayCount();
                    }
                }
                //同比增长率=（本期数－同期数）÷ 同期数×100%
                if (gatewayCount != 0 && lastYearGatewayCount != 0) {
                    gatewayYoyRatio = Double.valueOf(((double)gatewayCount - (double)lastYearGatewayCount) /  (double)lastYearGatewayCount * 100);
                } else if (gatewayCount == 0) {
                    gatewayYoyRatio = 0D;
                } else {
                    gatewayYoyRatio = 100D;
                }
                //环比增长率=（本期数－上期数）÷ 上期数×100%
                if (gatewayCount != 0 && lastMonthGatewayCount != 0) {
                    gatewayMomRatio = Double.valueOf(((double)gatewayCount - (double)lastMonthGatewayCount) / (double)lastMonthGatewayCount * 100);
                } else if (gatewayCount == 0) {
                    gatewayMomRatio = 0D;
                } else {
                    gatewayMomRatio = 100D;
                }
                overviewResult.setTotalNum(convertDataType(gatewayCount));
                overviewResult.setAddNum(convertDataType(gatewayIncCount));
                overviewResult.setDayActiveNum(convertDataType(gatewayActiveCount));
                overviewResult.setRunOffNum(convertDataType(gatewayLostCount));
                overviewResult.setTotalMonthOnMonth(decimalFormat(convertDataType(gatewayMomRatio)));
                overviewResult.setTotalYearOnYear(decimalFormat(convertDataType(gatewayYoyRatio)));
                overviewResult.setConnectFamilyGatewayNum(convertDataType(connectFamilyGatewayNum));
                overviewResult.setConnectFamilyGatewayRate(decimalFormat(convertDataType(connectFamilyGatewayRate)));

            } else {
                String numZero = "0";
                overviewResult.setTotalNum(numZero);
                overviewResult.setAddNum(numZero);
                overviewResult.setDayActiveNum(numZero);
                overviewResult.setRunOffNum(numZero);
                overviewResult.setTotalMonthOnMonth(numZero);
                overviewResult.setTotalYearOnYear(numZero);
                overviewResult.setConnectFamilyGatewayNum(numZero);
                overviewResult.setConnectFamilyGatewayRate(numZero);
                logger.info("网关分布全景视图概览-网关数据查询，查询结果为空，设置默认值.");
            }

        } catch (Exception e) {
            logger.error("网关分布全景视图概览-网关数据查询，异常={}", e.getMessage(), e);
        }

        logger.info("网关分布全景视图概览-网关数据查询，接口方法出参={}", JSON.toJSONString(overviewResult));
        return overviewResult;
    }


    /**
     * 网关全景视图—网关设备总量
     *
     * @param request
     * @return
     */
    @Override
    public GatewayCountResult getGatewayCount(GatewayCountRequest request) {
        logger.info("网关全景视图—网关设备总量查询，接口方法入参=：{}", request);

        GatewayCountResult result = new GatewayCountResult();

        try {
            String provinces = request.getProvince();
            String vendorIds = request.getVendor();
            String industryCode = request.getIndustry();
            String date = request.getDate();
            Integer connectType = request.getConnectType();

            GatewayCountExample example = new GatewayCountExample();
            GatewayCountExample.Criteria criteria = example.createCriteria();
            criteria.andGdateEqualTo(Long.parseLong(DateUtil.formatInputs(date)));

            // (code,name)
            Map<String, String> dicAreaMap = getDicAreaMap();
            Map<String, String> dictIndustryMap = getDictIndustryMap();

            //List<String> industryNameList = new ArrayList<>();
            if (StringUtils.isNotBlank(industryCode)) {
                String industryName = dictIndustryMap.getOrDefault(industryCode, "其他");
                //industryNameList.add(industryName);
                criteria.andIndustryEqualTo(industryName);
            }

            if (StringUtils.isNotBlank(provinces)) {
                List<String> provCodeList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provinces, "\\,"));
                criteria.andProvinceCodeIn(provCodeList);
            }

            // (code,name)
            Map<String, String> dicFactoryMap = getDicFactoryMap();
            if (StringUtils.isNotBlank(vendorIds)) {
                List<String> vendorIdsList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(vendorIds, "\\,"));
                List<String> vendorCodesList = getDicFactoryIdCodeMap(vendorIdsList);
                criteria.andFactoryCodeIn(vendorCodesList);
            }

            if (connectType != null && connectType != -1) {
                criteria.andConnectTypeEqualTo(connectType);
            }


            List<GatewayCount> gtList = gatewayCountMapper.selectByExample(example);

            result = convertGatewayCountResultList(gtList, dicAreaMap, dicFactoryMap, dictIndustryMap);

        } catch (Exception e) {
            logger.error("网关全景视图—网关设备总量查询，异常={}", e.getMessage(), e);
        }

        logger.info("网关全景视图—网关设备总量查询，接口方法出参={}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 网关全景视图—新增网关设备
     *
     * @param request
     * @return
     */
    @Override
    public GatewayIncrementCountResult getGatewayIncrementCount(GatewayIncrementCountRequst request) {
        logger.info("网关全景视图—新增网关设备数量查询，接口方法入参=：{}", request);

        GatewayIncrementCountResult result = new GatewayIncrementCountResult();

        try {
            String province = request.getProvince();
            String vendorIds = request.getVendor();
            String industryCode = request.getIndustry();
            // yyyy-MM-dd
            String inputStartTime = request.getStartTime();
            // yyyy-MM-dd
            String inputEndTime = request.getEndTime();
            Integer connectType = request.getConnectType();

            long startTime = Long.parseLong(DateUtil.formatInputs(inputStartTime));
            long endTime = Long.parseLong(DateUtil.formatInputs(inputEndTime));

            GatewayIncrementCountExample example = new GatewayIncrementCountExample();
            GatewayIncrementCountExample.Criteria criteria = example.createCriteria();

            // (code,name)
            Map<String, String> dicAreaMap = getDicAreaMap();
            Map<String, String> dictIndustryMap = getDictIndustryMap();

            if (StringUtils.isNotBlank(industryCode)) {
                String industryName = dictIndustryMap.getOrDefault(industryCode, "其他");
                criteria.andIndustryEqualTo(industryName);
            }

            if (StringUtils.isNotBlank(province)) {
                List<String> provCodeList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(province, "\\,"));
                criteria.andProvinceCodeIn(provCodeList);
            }

            // (id,name)
            Map<String, String> dicFactoryMap = getDicFactoryMap();
            if (StringUtils.isNotBlank(vendorIds)) {
                List<String> vendorIdsList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(vendorIds, "\\,"));
                List<String> vendorCodesList = getDicFactoryIdCodeMap(vendorIdsList);
                criteria.andFactoryCodeIn(vendorCodesList);
            }

            if (connectType != null && connectType != -1) {
                criteria.andConnectTypeEqualTo(connectType);
            }

            criteria.andGdateBetween(startTime, endTime);
            List<GatewayIncrementCount> gtList = gatewayIncrementCountMapper.selectByExample(example);

            result = convertGatewayIncrementCountResultList(gtList, dicAreaMap, dicFactoryMap, dictIndustryMap);

        } catch (Exception e) {
            logger.error("网关全景视图—新增网关设备数量查询，异常={}", e.getMessage(), e);
        }

        logger.info("网关全景视图—新增网关设备数量查询，接口方法出参={}", JSON.toJSONString(result));


        return result;

    }


    /**
     * 网关运行分析—网关在线离线数量
     *
     * @param request
     * @return
     */
    @Override
    public List<GatewayOnOffLineNumResult> gatewayOnOfflineNumStatistic(GatewayOnOfflineCountRequest request, String province) {
        logger.info("网关运行分析-网关在线离线柱状图统计查询，接口方法入参=：{}, province:{}", request, province);


        List<GatewayOnOffLineNumResult> result = new ArrayList<>();

        try {
            // 单个省份支持地市多选,多个省份地市需为空
            // "provCode":"170000,130000,190000"
            String provCodes = request.getProvince();
            // "cityCodes":"170001,170002"
            String cityCodes = request.getCity();
            // 厂商单选或全部(为空)
            String vendorCodes = request.getVendorCode();
            // yyyy-MM-dd
            String inputStartTime = request.getStartTime();
            // yyyy-MM-dd
            String inputEndTime = request.getEndTime();

            // 1-厂商, 2-区域, 3-地市
            String type = request.getType();

            long startTime = Long.parseLong(DateUtil.formatInputs(inputStartTime));
            long endTime = Long.parseLong(DateUtil.formatInputs(inputEndTime));

            GatewayOnOfflineDetailExample example = new GatewayOnOfflineDetailExample();
            GatewayOnOfflineDetailExample.Criteria criteria = example.createCriteria();

            // (code,name)
            Map<String, String> dicAreaMap = getDicAreaMap(); // 省份code,省份名称
            Map<String, String> dicFactoryMap = getDicFactoryMap(); // 厂商code,厂商名称

            // code
            List<String> provList = new ArrayList<>();
            if (StringUtils.isNotBlank(provCodes)) {
                provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provCodes, "\\,"));
                provList = new ArrayList<>(provList);
                List<String> userPermission = PermissionUtils.getUserPermission(province, provList);
                if (CollectionUtils.isEmpty(userPermission)) {
                    List<String> ownPermission = PermissionUtils.getOwnPermission(province);
                    provList.clear();
                    provList.addAll(ownPermission);
                    criteria.andProvinceCodeIn(provList);
                } else {
                    provList.clear();
                    provList.addAll(userPermission);
                    criteria.andProvinceCodeIn(provList);
                }

            } else {
                if (PermissionUtils.userAllPermission(province)) {
                    provList.addAll(dicAreaMap.keySet());
                } else {
                    List<String> ownPermission = PermissionUtils.getOwnPermission(province);
                    provList.clear();
                    provList.addAll(ownPermission);
                    criteria.andProvinceCodeIn(provList);
                }

            }

            List<String> cityCodeList = new ArrayList<>();
            // 地市code,地市名称
            Map<String, String> dicCityMap = new HashMap<>();

            // 如何查询一个省市 需要判断地市是否是所属权限
            if(1 == provList.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(provList);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                cityCodeList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCodes, "\\,"));
                if (StringUtils.isNotBlank(cityCodes) && ls.containsAll(cityCodeList)) {
                    criteria.andCityCodeIn(cityCodeList);
                    dicCityMap = getDicCityMap(cityCodeList);
                } else {
                    cityCodeList = new ArrayList<>(cityCodeList);
                    cityCodeList.clear();
                    cityCodeList.addAll(ls);
                    criteria.andCityCodeIn(cityCodeList);
                    dicCityMap = getDicCityMap(cityCodeList);
                }
            }
            logger.info("gatewayOnOfflineNumStatistic ProvinceList:{},CityList:{}", provList, cityCodeList);
            // code
            List<String> vendorList = new ArrayList<>();
            if (StringUtils.isNotBlank(vendorCodes)) {
                vendorList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(vendorCodes, "\\,"));
                //List<String> finalVendorList = getDicFactoryCodeListById(vendorList);
                criteria.andFactoryCodeIn(vendorList);
            } else {
                vendorList.addAll(dicFactoryMap.keySet());
            }


            criteria.andGdateBetween(startTime, endTime);
            List<GatewayOnOfflineCount> list = new ArrayList<>();
            // 按天
            if (startTime == endTime) {
                list = gatewayOnOfflineDetailMapper.selectCountByExample(example);
            } else {
                // 周,月
                List<GatewayOnOfflineCount> onList = gatewayOnOfflineDetailMapper.selectWeekOrMonthOnCountByExample(example);
                List<GatewayOnOfflineCount> offList = gatewayOnOfflineDetailMapper.selectWeekOrMonthOffCountByExample(example);
                list.addAll(onList);
                list.addAll(offList);
            }


            if (!CollectionUtils.isEmpty(list)) {
                List<GatewayOnOfflineCount> uniqueList = list.stream().distinct().collect(Collectors.toList());
                if ("2".equals(type)) {
                    Map<String, Long> provOnMap = uniqueList.stream().filter(gatewayOnOfflineCount -> gatewayOnOfflineCount.getGtype() == 1).collect(Collectors.groupingBy(GatewayOnOfflineCount::getProvinceCode, Collectors.reducing(0L, GatewayOnOfflineCount::getCount, Long::sum)));
                    Map<String, Long> provOffMap = uniqueList.stream().filter(gatewayOnOfflineCount -> gatewayOnOfflineCount.getGtype() == 0).collect(Collectors.groupingBy(GatewayOnOfflineCount::getProvinceCode, Collectors.reducing(0L, GatewayOnOfflineCount::getCount, Long::sum)));
                    result = converOnOffLinetList(provList, provOnMap, provOffMap, dicAreaMap);
                } else if ("1".equals(type)) {
                    Map<String, Long> factoryOnMap = uniqueList.stream().filter(gatewayOnOfflineCount -> gatewayOnOfflineCount.getGtype() == 1).collect(Collectors.groupingBy(GatewayOnOfflineCount::getFactoryCode, Collectors.reducing(0L, GatewayOnOfflineCount::getCount, Long::sum)));
                    Map<String, Long> factoryOffMap = uniqueList.stream().filter(gatewayOnOfflineCount -> gatewayOnOfflineCount.getGtype() == 0).collect(Collectors.groupingBy(GatewayOnOfflineCount::getFactoryCode, Collectors.reducing(0L, GatewayOnOfflineCount::getCount, Long::sum)));
                    result = converOnOffLinetList(vendorList, factoryOnMap, factoryOffMap, dicFactoryMap);
                } else if ("3".equals(type)) {
                    Map<String, Long> cityOnMap = uniqueList.stream().filter(gatewayOnOfflineCount -> gatewayOnOfflineCount.getGtype() == 1).collect(Collectors.groupingBy(GatewayOnOfflineCount::getCityCode, Collectors.reducing(0L, GatewayOnOfflineCount::getCount, Long::sum)));
                    Map<String, Long> cityOffMap = uniqueList.stream().filter(gatewayOnOfflineCount -> gatewayOnOfflineCount.getGtype() == 0).collect(Collectors.groupingBy(GatewayOnOfflineCount::getCityCode, Collectors.reducing(0L, GatewayOnOfflineCount::getCount, Long::sum)));
                    result = converOnOffLinetList(cityCodeList, cityOnMap, cityOffMap, dicCityMap);
                }
            } else {
                // 没有数据默认补全
                List<GatewayOnOffLineNumResult> finalResult = new ArrayList<>();

                if (("2".equals(type)) && provList.size() > 0) {
                    provList.forEach(provCode -> {
                        GatewayOnOffLineNumResult numResult = new GatewayOnOffLineNumResult();
                        String provName = dicAreaMap.getOrDefault(provCode, "其他");
                        numResult.setName(provName);
                        numResult.setOnlineNum("0");
                        numResult.setOfflineNum("0");
                        finalResult.add(numResult);
                    });
                } else if (("1".equals(type)) && vendorList.size() > 0) {
                    vendorList.forEach(vendorCode -> {
                        GatewayOnOffLineNumResult numResult = new GatewayOnOffLineNumResult();
                        String vendorName = dicFactoryMap.getOrDefault(vendorCode, "其他");
                        numResult.setName(vendorName);
                        numResult.setOnlineNum("0");
                        numResult.setOfflineNum("0");
                        finalResult.add(numResult);
                    });
                } else if (("3".equals(type)) && cityCodeList.size() > 0) {
                    Map<String, String> finalDicCityMap = dicCityMap;
                    cityCodeList.forEach(cityCode -> {
                        GatewayOnOffLineNumResult numResult = new GatewayOnOffLineNumResult();
                        String cityName = finalDicCityMap.getOrDefault(cityCode, "其他");
                        numResult.setName(cityName);
                        numResult.setOnlineNum("0");
                        numResult.setOfflineNum("0");
                        finalResult.add(numResult);
                    });
                }

                result.addAll(finalResult);
                logger.info("网关运行分析-网关在线离线柱状图统计查询，查询结果为空,设置默认值.");
            }


        } catch (Exception e) {
            logger.error("网关运行分析-网关在线离线柱状图统计查询，异常={}", e.getMessage(), e);
        }


        logger.info("网关运行分析-网关在线离线柱状图统计查询，接口方法出参={}", JSON.toJSONString(result));
        return result;
    }


    public static void main(String[] args) {
        String provCodes = "";
        String province = "110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000";
        List<String> provList = new ArrayList<>();
        if (StringUtils.isNotBlank(provCodes)) {
            provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provCodes, "\\,"));
            provList = new ArrayList<>(provList);
            List<String> userPermission = PermissionUtils.getUserPermission(province, provList);
            if (CollectionUtils.isEmpty(userPermission)) {
                List<String> ownPermission = PermissionUtils.getOwnPermission(province);
                provList.clear();
                provList.addAll(ownPermission);
                System.out.println(provList);
            } else {
                provList.clear();
                provList.addAll(userPermission);
            }

        } else {
            if (PermissionUtils.userAllPermission(province)) {
                System.out.println("all");
               // provList.addAll(dicAreaMap.keySet());
            } else {
                List<String> ownPermission = PermissionUtils.getOwnPermission(province);
                provList.clear();
                provList.addAll(ownPermission);
            }
        }
        System.out.println(provList);
    }

    /**
     * 网关运行分析—网关在线离线数量概览 - unused
     *
     * @param request
     * @return
     */
    @Override
    public GatewayOnOffLineNumOverviewResult gatewayOnOfflineNumOverviewStatistic(GatewayOnOfflineCountRequest request) {
        logger.info("网关运行分析-网关在线离线数量概览查询，接口方法入参=：{}", request);

        GatewayOnOffLineNumOverviewResult result = new GatewayOnOffLineNumOverviewResult();

        try {
            // 单个省份支持地市多选,多个省份地市需为空
            // "province":"170000,130000,190000"
            String provinces = request.getProvince();
            // "city":"170001,170002"
            String citys = request.getCity();
            // 厂商单选或全部(为空)
            String vendors = request.getVendorCode();
            // yyyy-MM-dd
            String inputStartTime = request.getStartTime();
            // yyyy-MM-dd
            String inputEndTime = request.getEndTime();

            // 1-厂商, 2-区域
            String type = request.getType();

            long startTime = Long.parseLong(DateUtil.formatInputs(inputStartTime));
            long endTime = Long.parseLong(DateUtil.formatInputs(inputEndTime));

            GatewayOnOfflineDetailExample example = new GatewayOnOfflineDetailExample();
            GatewayOnOfflineDetailExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(provinces)) {
                List<String> provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provinces, "\\,"));
                criteria.andProvinceCodeIn(provList);
            }

            if (StringUtils.isNotBlank(citys)) {
                List<String> cityList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(citys, "\\,"));
                criteria.andCityCodeIn(cityList);
            }

            if (StringUtils.isNotBlank(vendors)) {
                List<String> vendorList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(vendors, "\\,"));
                criteria.andFactoryCodeIn(vendorList);
            }

            criteria.andGdateBetween(startTime, endTime);

            List<GatewayOnOffLineNumOverviewCount> overviewList = gatewayOnOfflineDetailMapper.selectOverviewCountByExample(example);

            AtomicLong sum = new AtomicLong();
            if (!CollectionUtils.isEmpty(overviewList)) {
                overviewList.forEach(item -> {
                    int gtype = item.getGtype();
                    long count = item.getCount();
                    if (gtype == 1) {
                        result.setOnlineDevice(String.valueOf(count));
                    } else if (gtype == 0) {
                        result.setOfflineDevice(String.valueOf(count));
                    }
                    sum.addAndGet(count);
                });

                result.setTotalDevice(String.valueOf(sum.get()));
            } else {
                result.setTotalDevice("0");
                result.setOnlineDevice("0");
                result.setTotalDevice("0");
                logger.info("网关运行分析-网关在线离线数量概览查询，查询结果为空,设置默认值为0.");
            }

        } catch (Exception e) {
            logger.error("网关运行分析-网关在线离线数量概览查询，异常={}", e.getMessage(), e);
        }


        logger.info("网关运行分析-网关在线离线数量概览查询，接口方法出参={}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 小程序-在线离线网关数据统计
     *
     * @param request
     * @return
     */
    @Override
    public MiniProgramOnOfflineCountResult gatewayOnOfflineTrendStatistic(MiniProgramOnOfflineCountRequest request) {
        logger.info("分析平台小程序端-在线离线网关趋势统计查询，接口方法入参=：{}", request);

        MiniProgramOnOfflineCountResult result = new MiniProgramOnOfflineCountResult();

        try {
            String enterpriseId = request.getEid();
            // yyyy-MM-dd
            String inputStartDate = request.getStartDate();
            String inputEndDate = request.getEndDate();

            if (StringUtils.isNotBlank(enterpriseId)) {
                long startTime = Long.parseLong(DateUtil.formatInputs(inputStartDate));
                long endTime = Long.parseLong(DateUtil.formatInputs(inputEndDate));

                GatewayOnOfflineDetailExample example = new GatewayOnOfflineDetailExample();
                GatewayOnOfflineDetailExample.Criteria criteria = example.createCriteria();

                criteria.andEnterpriseIdEqualTo(enterpriseId);
                criteria.andGdateBetween(startTime, endTime);

                List<GatewayOnOfflineMiniCount> countList = gatewayOnOfflineDetailMapper.selectMiniCountByExample(example);

                Map<Long, Long> dtOnlineMap = countList.stream().filter(miniCount -> "1".equals(miniCount.getGtype())).collect(Collectors.toMap(GatewayOnOfflineMiniCount::getGdate, GatewayOnOfflineMiniCount::getCount));
                Map<Long, Long> dtOfflineMap = countList.stream().filter(miniCount -> "0".equals(miniCount.getGtype())).collect(Collectors.toMap(GatewayOnOfflineMiniCount::getGdate, GatewayOnOfflineMiniCount::getCount));

                List<String> timeList = new ArrayList<>();
                List<Long> onlineList = new ArrayList<>();
                List<Long> offlineList = new ArrayList<>();

                List<String> dateList = DateUtil.splitDateList(inputStartDate, inputEndDate);
                if (!CollectionUtils.isEmpty(countList)) {
                    dateList.forEach(dt -> {
                        Long dateFormat = dateFormat(dt);
                        Long onlineCount = dtOnlineMap.getOrDefault(dateFormat, 0L);
                        onlineList.add(onlineCount);

                        Long offlineCount = dtOfflineMap.getOrDefault(dateFormat, 0L);
                        offlineList.add(offlineCount);

                        timeList.add(dt);

                    });
                } else {
                    // 没有数据默认补全
                    dateList.forEach(dt -> {
                        timeList.add(dt);
                        onlineList.add(0L);
                        offlineList.add(0L);
                    });

                    logger.info("分析平台小程序端-在线离线网关趋势统计查询，查询结果为空,设置默认值为0.");
                }

                result.setTime(timeList);
                result.setOnline(onlineList);
                result.setOffline(offlineList);
            } else {
                logger.info("分析平台小程序端-在线离线网关趋势统计查询，入参enterpriseId为空.");
            }


        } catch (Exception e) {
            logger.error("分析平台小程序端-在线离线网关趋势统计查询，异常={}", e.getMessage(), e);
        }


        logger.info("分析平台小程序端-在线离线网关趋势统计查询，接口方法出参={}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 每天凌晨零点1分查询前一天是否设置了阈值,没有阈值自动将最新阈值写入前一天的分区
     * 0 1 0 * * ?
     */
    @Scheduled(cron = "0 1 0 * * ?")
    public void autoSetAlarmThresholdTask() {
        logger.info("网关质量分析—网关告警阈值自动设置定时任务，设置日期=：{}", new Date());

        AlarmThresholdExample example = new AlarmThresholdExample();
        AlarmThresholdExample.Criteria criteria = example.createCriteria();

        long yesterday = (long) TimerUtils.getYesterday();
        criteria.andGdateEqualTo(yesterday);

        Date date = new Date();

        // 昨天阈值
        List<AlarmThreshold> alarmThresholdList = alarmThresholdMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(alarmThresholdList)) {
            // 最近阈值
            alarmThresholdList = alarmThresholdMapper.selectNewestValueByExample();
            List<AlarmThreshold> alarmList = new ArrayList<>();
            for (AlarmThreshold alarmThreshold : alarmThresholdList) {
                alarmThreshold.setSampleTime(date);
                alarmThreshold.setGdate(yesterday);
                alarmList.add(alarmThreshold);
            }
            int count = alarmThresholdMapper.insertBatch(alarmList);
            logger.info("网关质量分析—网关告警阈值自动设置定时任务，写入数据条数=：{}", count);
            logger.info("网关质量分析—网关告警阈值自动设置定时任务，写入数据=：{}", JSON.toJSONString(alarmList));
        } else {
            logger.info("网关质量分析—网关告警阈值自动设置定时任务，存在昨天 {} 日期的阈值数据，不自动设置.", yesterday);
        }
    }


    /**
     * 网关质量分析—网关告警阈值设置
     *
     * @param request
     * @return
     */
    @Override
    public void setAlarmThreshold(AlarmThresholdRequest request) {
        logger.info("网关质量分析—网关告警阈值设置，接口方法入参=：{}", request);

        try {
            BigDecimal dns = request.getDnsAnalysisDuration();
            BigDecimal tcp = request.getTcpHandshakeDuration();
            BigDecimal http = request.getHttpResponseDuration();
            BigDecimal cpuRam = request.getCpuRamHighUseNum();
            BigDecimal toTimes = request.getToAlarmTimes();

            // 1-DNS解析时延超长
            // 2-TCP连接访问的目标服务器域名货地址握手时延
            // 3-HTTP访问的URL请求响应时间过长
            // 4-CPU和内存使用率过高
            // 5-网关单一告警原因达到的告警条件
            long timestamp = System.currentTimeMillis() / 1000;
            Date date = new Date();

            int today = TimerUtils.getToday();

            List<AlarmThreshold> alarmThresholdList = new ArrayList<>();

            for (int i = 1; i <= 5; i++) {
                AlarmThreshold alarmThreshold = new AlarmThreshold();
                alarmThreshold.setTimestamp(timestamp);
                alarmThreshold.setSampleTime(date);
                alarmThreshold.setType(Byte.parseByte(String.valueOf(i)));
                alarmThreshold.setGdate((long) today);
                switch (i) {
                    case 1:
                        alarmThreshold.setValue(dns);
                        break;
                    case 2:
                        alarmThreshold.setValue(tcp);
                        break;
                    case 3:
                        alarmThreshold.setValue(http);
                        break;
                    case 4:
                        alarmThreshold.setValue(cpuRam);
                        break;
                    case 5:
                        alarmThreshold.setValue(toTimes);
                        break;
                }

                alarmThresholdList.add(alarmThreshold);
            }

            if (!CollectionUtils.isEmpty(alarmThresholdList)) {
                alarmThresholdMapper.insertBatch(alarmThresholdList);
                logger.info("网关质量分析—网关告警阈值设置，写入数据：{}", JSON.toJSONString(alarmThresholdList));
            }

        } catch (Exception e) {
            logger.error("网关质量分析—网关告警阈值设置，异常={}", e.getMessage(), e);
        }

        logger.info("网关质量分析—网关告警阈值设置完成.");
    }


    /**
     * 网关质量分析—网关告警阈值查询
     *
     * @return
     */
    @Override
    public AlarmThresholdResult getAlarmThreshold() {
        logger.info("网关质量分析—网关告警阈值查询日期: {}", new Date());

        AlarmThresholdResult result = new AlarmThresholdResult();

        try {

            AlarmThresholdExample example = new AlarmThresholdExample();
            AlarmThresholdExample.Criteria criteria = example.createCriteria();

            long yesterday = (long) TimerUtils.getYesterday();
            criteria.andGdateEqualTo(yesterday);
            List<AlarmThreshold> list = alarmThresholdMapper.selectByExample(example);

            if (CollectionUtils.isEmpty(list)) {
                list = alarmThresholdMapper.selectNewestValueByExample();
            }

            list.forEach(alarmThreshold -> {
                Byte type = alarmThreshold.getType();
                BigDecimal value = alarmThreshold.getValue();
                switch (type) {
                    case 1:
                        result.setDnsAnalysisDuration(value);
                        break;
                    case 2:
                        result.setTcpHandshakeDuration(value);
                        break;
                    case 3:
                        result.setHttpResponseDuration(value);
                        break;
                    case 4:
                        result.setCpuRamHighUseNum(value);
                        break;
                    case 5:
                        result.setToAlarmTimes(value);
                        break;
                }

            });

        } catch (Exception e) {
            logger.error("网关质量分析—网关告警阈值查询，异常={}", e.getMessage(), e);
        }

        logger.info("网关质量分析—网关告警阈值查询，接口方法出参=：{}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 网关质量分析—网关告警分析
     *
     * @param request
     * @return
     */
    @Override
    public GatewayAlarmCountResult getGatewayAlarmCount(GatewayAlarmRequst request, String province) {
        logger.info("网关质量分析—网关告警分析统计查询，接口方法入参=：{}, province: {}", request, province);

        GatewayAlarmCountResult result = new GatewayAlarmCountResult();

        try {
            String provCodes = request.getProvince();
            String cityCodes = request.getCity();
            String vendorIds = request.getVendor();
            // yyyy-MM-dd
            String inputStartDate = request.getStartTime();
            String inputEndDate = request.getEndTime();
            // area,vendor
            String type = request.getType();

            // yyyyMMdd
            long startTime = Long.parseLong(DateUtil.formatInputs(inputStartDate));
            long endTime = Long.parseLong(DateUtil.formatInputs(inputEndDate));

            // 分省、地市、厂商总网关数查询
            GatewayOnOfflineDetailExample onOffExample = new GatewayOnOfflineDetailExample();
            GatewayOnOfflineDetailExample.Criteria onOffCriteria = onOffExample.createCriteria();

            // 分省、地市、厂商告警网关数查询参数
            GatewayAlarmQuery query = new GatewayAlarmQuery();
            // 分省、地市、厂商下的每个类型告警网关数查询参数
            GatewayAlarmQuery typeQuery = new GatewayAlarmQuery();

            List<String> provList = null;
            if (StringUtils.isEmpty(province)) {
                logger.error("getGatewayAlarmCount user permisson is null");
                return result;
            }
            if (StringUtils.isNotBlank(provCodes)) {
                provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provCodes, "\\,"));
                List<String> userPermission = PermissionUtils.getResult(province, provList);
                provList = new ArrayList<>(provList);
                provList.clear();
                provList.addAll(userPermission);
                query.setProvList(provList);
                typeQuery.setProvList(provList);
                onOffCriteria.andProvinceCodeIn(provList);
            } else {
                if (!PermissionUtils.userAllPermission(province)) {
                    provList = PermissionUtils.getOwnPermission(province);
                    query.setProvList(provList);
                    typeQuery.setProvList(provList);
                    onOffCriteria.andProvinceCodeIn(provList);
                }
            }

            List<String> cityList = null;
            if (1 == provList.size()) {
                //city多选
                Map<String, String> dicCityMap = this.dicDataService.getDictCityMap(provList);
                Set<String> strings = dicCityMap.keySet();
                List<String> ls = new ArrayList(strings);
                if (StringUtils.isNotBlank(cityCodes)) {
                    cityList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCodes, "\\,"));
                    if (!ls.containsAll(cityList)) {
                        cityList = ls;
                    }
                    query.setCityList(cityList);
                    typeQuery.setCityList(cityList);
                    onOffCriteria.andCityCodeIn(cityList);

                } else {
                    cityList = ls;
                    query.setCityList(cityList);
                    typeQuery.setCityList(cityList);
                    onOffCriteria.andCityCodeIn(cityList);
                }

            }

            logger.info("getGatewayAlarmCount area, provList: {}, cityList:{}", provList, cityList);
            List<Long> vendorIdList = null;
            List<String> factCodeList = null;
            if (StringUtils.isNotBlank(vendorIds)) {
                List<String> vendorList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(vendorIds, "\\,"));
                vendorIdList = vendorList.stream().map(Long::valueOf).collect(Collectors.toList());
                query.setVendorIdList(vendorIdList);
                typeQuery.setVendorIdList(vendorIdList);

                // (code,name)
                Map<String, String> factoryCodes = getDictFactoryCodeByIds(vendorIdList);
                factCodeList = new ArrayList<>(factoryCodes.keySet());
                onOffCriteria.andFactoryCodeIn(factCodeList);
            }

            query.setEndTime(endTime);
            query.setStartTime(startTime);
            typeQuery.setStartTime(startTime);
            typeQuery.setEndTime(endTime);
            onOffCriteria.andGdateEqualTo(endTime);

            // 1-省份,2-地市,3-厂商
            int flag = 2;

            // 在线离线明细表查询全量网关统计
            List<GatewayOnOfflineAllCount> allCountList = null;

            // 告警网关统计
            List<GatewayAlarmQueryCount> list = null;
            List<GatewayAlarmTypeQueryCount> typList = null;
            if (type.equalsIgnoreCase("area")) {
                if (!CollectionUtils.isEmpty(provList) && provList.size() == 1) {
                    // 仅一个省份会显示该省的多个地市
                    list = gatewayAlarmDetailMapper.selectCountByCityQuery(query);
                    typList = gatewayAlarmDetailMapper.selectCountByCityTypeQuery(typeQuery);
                    allCountList = gatewayOnOfflineDetailMapper.selectAllCountByCity(onOffExample);
                } else if ((!CollectionUtils.isEmpty(provList) && provList.size() >= 2) || CollectionUtils.isEmpty(provList)) {
                    // 两个以上省份则显示省份维度
                    list = gatewayAlarmDetailMapper.selectCountByProvQuery(query);
                    typList = gatewayAlarmDetailMapper.selectCountByProvTypeQuery(typeQuery);
                    allCountList = gatewayOnOfflineDetailMapper.selectAllCountByProv(onOffExample);
                    flag = 1;
                }
            } else if (type.equalsIgnoreCase("vendor")) {
                list = gatewayAlarmDetailMapper.selectCountByVendorQuery(query);
                typList = gatewayAlarmDetailMapper.selectCountByVendorTypeQuery(typeQuery);
                allCountList = gatewayOnOfflineDetailMapper.selectAllCountByVendor(onOffExample);
                flag = 3;
            }

            logger.info("网关质量分析—网关告警分析统计查询，查询结果list:{}", JSON.toJSONString(list));
            logger.info("网关质量分析—网关告警分析统计查询，查询结果typList:{}", JSON.toJSONString(typList));
            logger.info("网关质量分析—网关告警分析统计查询，查询结果allCountList:{}", JSON.toJSONString(allCountList));
            logger.info("网关质量分析—网关告警分析统计查询，类型flag:{}", flag);
            if (!CollectionUtils.isEmpty(typList)) {
                result = getGatewayAlarmCount(list, typList, allCountList, flag);
            } else {
                List<GatewayAlarmCountAndPie> rsList = new ArrayList<>();
                result.setList(rsList);
            }

        } catch (Exception ex) {
            logger.error("网关质量分析—网关告警分析统计查询，异常={}", ex.getMessage(), ex);
        }

        logger.info("网关质量分析—网关告警分析统计查询，接口方法出参=：{}", JSON.toJSONString(result));

        return result;
    }


    /**
     * 网关质量分析—网关告警列表
     *
     * @param request
     * @return
     */
    @Override
    public GatewayAlarmListResult getGatewayAlarmList(GatewayAlarmListRequst request, String province) {
        logger.info("网关质量分析—网关告警列表查询，接口方法入参=：{}, province:{}", request, province);

        GatewayAlarmListResult result = new GatewayAlarmListResult();

        try {
            String provCodes = request.getProvince();
            String cityCodes = request.getCity();
            String vendorIds = request.getVendor();
            String inputStartDate = request.getStartTime();
            String inputEndDate = request.getEndTime();
            int page = request.getPage();
            int pageSize = request.getPageSize();

            // yyyyMMdd
            long startTime = Long.parseLong(DateUtil.formatInputs(inputStartDate));
            long endTime = Long.parseLong(DateUtil.formatInputs(inputEndDate));

            GatewayAlarmQuery query = new GatewayAlarmQuery();

            // 用户权限校验
            if (StringUtils.isEmpty(province)) {
                logger.error("getGatewayAlarmCount user permisson is null");
                return result;
            }
            List<String> provList = null;
            if (StringUtils.isNotBlank(provCodes)) {
                provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provCodes, "\\,"));
                List<String> userPermission = PermissionUtils.getResult(province, provList);
                provList = new ArrayList<>(provList);
                provList.clear();
                provList.addAll(userPermission);
                query.setProvList(provList);
            } else {
                if (!PermissionUtils.userAllPermission(province)) {
                    provList = PermissionUtils.getOwnPermission(province);
                    query.setProvList(provList);
                }
            }

            List<String> cityList = null;
            if (null != provList && 1 == provList.size()) {
                //city多选
                Map<String, String> dicCityMap = this.dicDataService.getDictCityMap(provList);
                Set<String> strings = dicCityMap.keySet();
                List<String> ls = new ArrayList(strings);
                if (StringUtils.isNotBlank(cityCodes)) {
                    cityList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCodes, "\\,"));
                    if (!ls.containsAll(cityList)) {
                        cityList = ls;
                    }
                    query.setCityList(cityList);

                } else {
                    cityList = ls;
                    query.setCityList(cityList);

                }

            }
            logger.info("getGatewayAlarmList area, provList: {}, cityList:{}", provList, cityList);
            if (StringUtils.isNotBlank(vendorIds)) {
                List<String> vendorList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(vendorIds, "\\,"));
                List<Long> vendorIdList = vendorList.stream().map(Long::valueOf).collect(Collectors.toList());
                query.setVendorIdList(vendorIdList);
            }

            query.setStartTime(startTime);
            query.setEndTime(endTime);

            Page<GatewayAlarmDetailVo> alarmDetailVoPage = PageMethod.startPage(page, pageSize)
                    .doSelectPage(() -> gatewayAlarmDetailMapper.selectAlarmDetailListByQuery(query));

            List<GatewayAlarmDetailVo> voList = alarmDetailVoPage.getResult();

            List<GatewayAlarmListDTO> dtoList = new ArrayList<>();
            voList.forEach(detailVo -> {
                GatewayAlarmListDTO dto = new GatewayAlarmListDTO();
                dto.setBelongArea(detailVo.getProvinceName());
                dto.setBelongBusiness(detailVo.getCustomerName());
                dto.setAccount(detailVo.getAdslAccount());
                dto.setSn(detailVo.getGatewaySn());
                dto.setMac(detailVo.getGatewayMac());
                dto.setManufacturer(detailVo.getFactoryName());
                dto.setModel(detailVo.getDeviceModel());
                dto.setType(detailVo.getAlarmName());
                dto.setChipTemperature(detailVo.getMainChipTemperature());
                dto.setAlarmTime(String.valueOf(detailVo.getAlarmTimes()));
                dtoList.add(dto);
            });

            result.setList(dtoList);
            result.setPage(alarmDetailVoPage.getPageNum());
            result.setPageSize(alarmDetailVoPage.getPageSize());
            result.setTotal(alarmDetailVoPage.getTotal());

        } catch (Exception ex) {
            logger.error("网关质量分析—网关告警列表查询，异常={}", ex.getMessage(), ex);
        }

        logger.info("网关质量分析—网关告警列表查询，接口方法出参=：{}", JSON.toJSONString(result));

        return result;
    }


    /**
     * 网关质量分析—网关PON口强、弱光分析
     *
     * @param request
     * @return
     */
    @Override
    public PonStatisticResult getGatewayPonStatistic(PonStatisticRequst request) {
        logger.info("网关质量分析—网关PON口强、弱光分析，接口方法入参=：{}", request);

        PonStatisticResult result = new PonStatisticResult();

        try {
            String provCodes = request.getProvince();
            String cityCodes = request.getCity();
            String vendorIds = request.getVendor();
            String modelIds = request.getModel();

            // yyyy-MM-dd
            String inputTime = request.getTime();

            // 区域-1,厂商-2,型号-3
            String type = request.getType();

            // yyyyMMdd
            long time = Long.parseLong(DateUtil.formatInputs(inputTime));

            PonPowerStatisticsDetailExample example = new PonPowerStatisticsDetailExample();
            PonPowerStatisticsDetailExample.Criteria criteria = example.createCriteria();

            GatewayTotalCountExample totalExample = new GatewayTotalCountExample();
            GatewayTotalCountExample.Criteria totalCriteria = totalExample.createCriteria();

            List<String> provList = new ArrayList<>();
            if (StringUtils.isNotBlank(provCodes)) {
                provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provCodes, "\\,"));
                criteria.andProvinceCodeIn(provList);
                totalCriteria.andProvinceCodeIn(provList);
            }

            List<String> cityCodeList = null;
            if (StringUtils.isNotBlank(cityCodes)) {
                cityCodeList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCodes, "\\,"));
                criteria.andCityCodeIn(cityCodeList);
                totalCriteria.andCityCodeIn(cityCodeList);
            }

            List<Long> vendorIdList = null;
            if (StringUtils.isNotBlank(vendorIds)) {
                List<String> vendorIdStrList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(vendorIds, "\\,"));
                vendorIdList = vendorIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                criteria.andFactoryIdIn(vendorIdList);
                totalCriteria.andFactoryIdIn(vendorIdList);
            }

            List<Long> modelIdList = null;
            if (StringUtils.isNotBlank(modelIds)) {
                List<String> modelIdStrList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(modelIds, "\\,"));
                modelIdList = modelIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                criteria.andFactoryModelIdIn(modelIdList);
                totalCriteria.andFactoryModelIdIn(modelIdList);
            }

            criteria.andGdateEqualTo(time);
            totalCriteria.andGdateEqualTo(time);

            // 1-省份,11-地市,2-厂商,3-厂商型号
            int flag = 11;

            // 横坐标
            List<String> xAxisList = new ArrayList<>();

            List<PonPowerStatisticsQ> ponList = new ArrayList<>();
            List<GatewayTotalCountDTO> totalList = new ArrayList<>();

            if (type.equalsIgnoreCase("1")) {
                if (!CollectionUtils.isEmpty(provList) && provList.size() == 1) {
                    // 仅一个省份会显示该省的多个地市
                    ponList = ponPowerStatisticsDetailMapper.selectByCityExample(example);
                    totalList = gatewayTotalCountMapper.selectByCityExample(totalExample);

                    if (CollectionUtils.isEmpty(cityCodeList)) {
                        // (cityCode,name)
                        Map<String, String> dicCityMap = getDictCityMap(provList);
                        xAxisList.addAll(dicCityMap.keySet());
                    } else {
                        xAxisList.addAll(cityCodeList);
                    }
                } else if ((!CollectionUtils.isEmpty(provList) && provList.size() >= 2)) {
                    // 两个以上省份则显示省份维度
                    ponList = ponPowerStatisticsDetailMapper.selectByProvExample(example);
                    totalList = gatewayTotalCountMapper.selectByProvExample(totalExample);

                    xAxisList.addAll(provList);
                    flag = 1;
                } else if (CollectionUtils.isEmpty(provList)) {
                    // 全部省份维度
                    ponList = ponPowerStatisticsDetailMapper.selectByProvExample(example);
                    totalList = gatewayTotalCountMapper.selectByProvExample(totalExample);

                    // (provCode,name)
                    Map<String, String> dicProvMap = getDicAreaMap();
                    xAxisList.addAll(dicProvMap.keySet());
                    flag = 1;
                }
            } else if (type.equalsIgnoreCase("2")) {
                ponList = ponPowerStatisticsDetailMapper.selectByFactoryExample(example);
                totalList = gatewayTotalCountMapper.selectByFactoryExample(totalExample);

                if (CollectionUtils.isEmpty(vendorIdList)) {
                    // (factCode,name)
                    Map<String, String> dicFactoryMap = getDicFactoryMap();
                    xAxisList.addAll(dicFactoryMap.values());
                } else {
                    // (factCode,name)
                    Map<String, String> dicFactoryMap = getDictFactoryCodeByIds(vendorIdList);
                    xAxisList.addAll(dicFactoryMap.values());
                }
                flag = 2;
            } else if (type.equalsIgnoreCase("3")) {
                ponList = ponPowerStatisticsDetailMapper.selectByFactoryModelExample(example);
                totalList = gatewayTotalCountMapper.selectByFactoryModelExample(totalExample);

                if (CollectionUtils.isEmpty(modelIdList)) {
                    // (modelId,factName_modelName)
                    if (!CollectionUtils.isEmpty(vendorIdList)) {
                        Map<Long, String> dicFactoryMap = getDictFactoryModelMap(vendorIdList);
                        xAxisList.addAll(dicFactoryMap.values());
                    } else {
                        Map<Long, String> dicFactoryMap = getDicFactoryModelMap();
                        xAxisList.addAll(dicFactoryMap.values());
                    }

                } else {
                    // (modelId,factName_modelName)
                    Map<Long, String> dicFactoryMap = getDicFactoryModelMap(modelIdList);
                    xAxisList.addAll(dicFactoryMap.values());
                }
                flag = 3;
            }

            logger.info("网关质量分析—网关PON口强、弱光分析，查询结果ponList:{}", JSON.toJSONString(ponList));
            logger.info("网关质量分析—网关PON口强、弱光分析，查询结果totalList:{}", JSON.toJSONString(totalList));
            logger.info("网关质量分析—网关PON口强、弱光分析，维度xAxisList:{}", JSON.toJSONString(xAxisList));
            logger.info("网关质量分析—网关PON口强、弱光分析，类型flag:{}", flag);
            if (!CollectionUtils.isEmpty(ponList) && !CollectionUtils.isEmpty(totalList)) {
                //xAxisList.addAll(getOtherNameList(xAxisList));
                result = ponStatistic(ponList, totalList, xAxisList, flag);
            } else {
                List<String> xAxisLists = new ArrayList<>();

                // 强光接收功率平均值
                List<String> receivePowerStrongAverage = new ArrayList<>();

                // 光接收功率强光占比,单位%
                List<String> receivePowerStrongRate = new ArrayList<>();

                // 弱光接收功率平均值
                List<String> receivePowerWeakAverage = new ArrayList<>();

                // 光接收功率弱光占比,单位%
                List<String> receivePowerWeakRate = new ArrayList<>();

                // (provCode,名称)
                Map<String, String> provMap = getDicAreaMap();
                // (cityCode,名称)
                Map<String, String> cityMap = getDicCityMap();

                for (String xAxis : xAxisList) {
                    String finalName = null;
                    switch (flag) {
                        // 省份(code->名称)
                        case 1:
                            finalName = provMap.getOrDefault(xAxis, "其他");
                            break;
                        // 地市(code->名称)
                        case 11:
                            finalName = cityMap.getOrDefault(xAxis, "其他");
                            break;
                        // 厂商,厂商型号
                        case 2:
                            finalName = xAxis;
                            break;
                        // 厂商型号
                        case 3:
                            finalName = xAxis;
                            break;
                    }

                    xAxisLists.add(finalName);
                    receivePowerStrongAverage.add("");
                    receivePowerStrongRate.add("0");
                    receivePowerWeakAverage.add("");
                    receivePowerWeakRate.add("0");
                }

                result.setxAxis(xAxisLists);
                result.setReceivePowerStrongRate(receivePowerStrongRate);
                result.setReceivePowerStrongAverage(receivePowerStrongAverage);
                result.setReceivePowerWeakAverage(receivePowerWeakAverage);
                result.setReceivePowerWeakRate(receivePowerWeakRate);
                logger.info("网关质量分析—网关PON口强、弱光分析，查询结果为空设置默认值.");
            }
        } catch (Exception ex) {
            logger.error("网关质量分析—网关PON口强、弱光分析，异常={}", ex.getMessage(), ex);
        }

        logger.info("网关质量分析—网关PON口强、弱光分析，接口方法出参=：{}", JSON.toJSONString(result));

        return result;
    }


    /**
     * 网关质量分析—网关PON口列表查询
     *
     * @param request
     * @return
     */
    @Override
    public PonListResult getGatewayPonList(PonListRequst request) {
        logger.info("网关质量分析—网关PON口列表查询，接口方法入参=：{}", request);

        PonListResult result = new PonListResult();

        try {
            String sns = request.getSn();
            String provCodes = request.getProvince();
            String cityCodes = request.getCity();
            String vendorIds = request.getVendor();
            String modelIds = request.getModel();
            int page = request.getPage();
            int pageSize = request.getPageSize();

            PonPowerListDetailExample example = new PonPowerListDetailExample();
            PonPowerListDetailExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(sns)) {
                List<String> snList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(sns, "\\,"));
                criteria.andGatewaySnIn(snList);
            }

            if (StringUtils.isNotBlank(provCodes)) {
                List<String> provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provCodes, "\\,"));
                criteria.andProvinceCodeIn(provList);
            }

            if (StringUtils.isNotBlank(cityCodes)) {
                List<String> cityCodeList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCodes, "\\,"));
                criteria.andCityCodeIn(cityCodeList);
            }

            if (StringUtils.isNotBlank(vendorIds)) {
                List<String> vendorIdStrList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(vendorIds, "\\,"));
                List<Long> vendorIdList = vendorIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                criteria.andFactoryIdIn(vendorIdList);
            }

            if (StringUtils.isNotBlank(modelIds)) {
                List<String> modelIdsStrList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(modelIds, "\\,"));
                List<Long> modelIdList = modelIdsStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                criteria.andDeviceModelIdIn(modelIdList);
            }


            long yesterday = (long) TimerUtils.getYesterday();
            criteria.andGdateEqualTo(yesterday);

            Page<PonListDTO> ponPowerListDetailVoPage = PageMethod.startPage(page, pageSize)
                    .doSelectPage(() -> ponPowerListDetailMapper.selectDetailByExample(example));

            List<PonListDTO> dtoList = ponPowerListDetailVoPage.getResult();

            if (!CollectionUtils.isEmpty(dtoList)) {
                result.setList(dtoList);
                result.setPage(ponPowerListDetailVoPage.getPageNum());
                result.setPageSize(ponPowerListDetailVoPage.getPageSize());
                result.setTotal(ponPowerListDetailVoPage.getTotal());
            } else {
                result.setList(new ArrayList<>());
                logger.info("网关质量分析—网关PON口列表查询结果为空.");
            }

        } catch (Exception ex) {
            logger.error("网关质量分析—网关PON口列表查询，异常={}", ex.getMessage(), ex);
        }

        logger.info("网关质量分析—网关PON口列表查询，接口方法出参=：{}", JSON.toJSONString(result));

        return result;
    }


    /**
     * 网关质量分析—网关PON口列表明细导出
     *
     * @param request
     * @return
     */
    @Override
    public String getGatewayPonListExport(PonListExportRequest request) {
        logger.info("网关质量分析—网关PON口列表明细导出，接口方法入参=：{}", request);

        String exportTaskName = "";

        try {
            String sns = request.getSn();
            String provCodes = request.getProvince();
            String cityCodes = request.getCity();
            String vendorIds = request.getVendor();
            String modelIds = request.getModel();
            long uid = Long.parseLong(request.getUid());

            PonPowerListDetailExample example = new PonPowerListDetailExample();
            PonPowerListDetailExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(sns)) {
                List<String> snList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(sns, "\\,"));
                criteria.andGatewaySnIn(snList);
            }

            if (StringUtils.isNotBlank(provCodes)) {
                List<String> provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provCodes, "\\,"));
                criteria.andProvinceCodeIn(provList);
            }

            if (StringUtils.isNotBlank(cityCodes)) {
                List<String> cityCodeList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCodes, "\\,"));
                criteria.andCityCodeIn(cityCodeList);
            }

            if (StringUtils.isNotBlank(vendorIds)) {
                List<String> vendorIdStrList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(vendorIds, "\\,"));
                List<Long> vendorIdList = vendorIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                criteria.andFactoryIdIn(vendorIdList);
            }

            if (StringUtils.isNotBlank(modelIds)) {
                List<String> modelIdStrList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(modelIds, "\\,"));
                List<Long> modelIdList = modelIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                criteria.andDeviceModelIdIn(modelIdList);
            }

            long yesterday = (long) TimerUtils.getYesterday();
            criteria.andGdateEqualTo(yesterday);

            long count = ponPowerListDetailMapper.countByExample(example);
            if (count > 0) {
                ExecutorService service = Executors.newSingleThreadExecutor();
                String exportType = "网关PON口光功率明细导出";
                String exportName = "网关PON口光功率明细_" + DateUtil.dateToString(new Date(), DateUtil.UNSIGNED_DATE_TIME_PATTERN);
                ExportInfoDto record = exportService.record(uid, exportType, exportName);
                exportTaskName = record.getExportName();
                //service.execute(() -> new PonExportTask(exportService, ponPowerListDetailMapper, example, record, tmpPath, exportName, usePriUrl));
                service.execute(() -> ponExportTask(example, record, exportName));
            } else {
                logger.error("网关质量分析—网关PON口列表明细导出，查询数据为空.");
            }


        } catch (Exception ex) {
            logger.error("网关质量分析—网关PON口列表明细导出，异常={}", ex.getMessage(), ex);
        }

        Map<String, String> map = new HashMap<>();
        map.put("name", exportTaskName);

        logger.info("网关质量分析—网关PON口列表明细导出，接口方法出参：{}", JSON.toJSONString(map));
        return JSON.toJSONString(map);
    }


    /**
     * 小程序运行报告告警网关数统计
     *
     * @param gatewayQuery
     * @return
     */
    @Override
    public int getAlarmGatewayCount(GatewayQuery gatewayQuery) {
        logger.info("小程序运行报告告警网关数统计，接口方法入参：{}", gatewayQuery);

        int result = 0;

        try {
            //String eid = gatewayQuery.getEid();
            //// yyyy-MM-dd
            //String startTime = gatewayQuery.getStartTime();
            //String endTime = gatewayQuery.getEndTime();

            // 告警网关数
            result = gatewayAlarmDetailMapper.appReportCount(gatewayQuery);
        } catch (Exception ex) {
            logger.error("小程序运行报告告警网关数统计，异常={}", ex.getMessage(), ex);
        }

        logger.info("小程序运行报告告警网关数统计，接口方法出参= {}", result);
        return result;
    }


    public void ponExportTask(PonPowerListDetailExample example, ExportInfoDto record, String exportName) {
        //ExcelUtils.writeExcel(voList, PonListDTO.class, exportTaskName + ".xlsx");
        logger.info("开始PON光功率明细数据导出任务~");
        List<PonListDTO> voList = ponPowerListDetailMapper.selectDetailByExample(example);
        String localFilePath = tmpPath + "/" + exportName.replace(" ", "_") + ".xlsx";
        File dataFile = ExcelUtils.writeExcel(voList, PonListDTO.class, localFilePath);

        String downLoadUrl = "";
        String failedDesc = "SUCCESS";
        try {
            Map<?, ?> uploadFile = exportService.uploadFile(record.getCreatorId(), dataFile);
            // 获取内网地址
            if (usePriUrl != null && usePriUrl == 1) {
                if (uploadFile.containsKey("privateUrl")) {
                    downLoadUrl = uploadFile.get("privateUrl").toString();
                } else {
                    failedDesc = "上传文件响应无内网地址";
                }
            } else {
                if (uploadFile.containsKey("internetUrl")) {
                    downLoadUrl = uploadFile.get("internetUrl").toString();
                } else {
                    failedDesc = "上传文件响应无公网地址";
                }
            }
        } catch (IOException ex) {
            failedDesc = "文件上传异常:" + ex.getMessage();
            logger.error("PON光功率明细异步线程数据导出任务异常={}", ex.getMessage(), ex);
        }

        // 上传记录设置
        record.setFailedDesc(failedDesc);
        record.setDownloadUrl(downLoadUrl);
        record.setStatus("SUCCESS".equalsIgnoreCase(failedDesc) ? ExportStatusEnum.SUCCESS : ExportStatusEnum.FAILED);
        exportService.updateRecord(record);
        logger.info("PON光功率明细数据导出任务结束~");
    }


    public PonStatisticResult ponStatistic(List<PonPowerStatisticsQ> ponList, List<GatewayTotalCountDTO> totalList, List<String> xAxisList, int flag) {

        PonStatisticResult result = new PonStatisticResult();

        Map<String, Long> totalMap = totalList.stream().collect(Collectors.toMap(GatewayTotalCountDTO::getName, GatewayTotalCountDTO::getCount));

        // 1:弱光,2:强光
        Map<String, Integer> lowGtwCountMap = ponList.stream().filter(pon -> pon.getPonRxPowerCurrType() == 1)
                .collect(Collectors.toMap(PonPowerStatisticsQ::getName, PonPowerStatisticsQ::getGtwCount));
        Map<String, Integer> lowAvgMap = ponList.stream().filter(pon -> pon.getPonRxPowerCurrType() == 1)
                .collect(Collectors.toMap(PonPowerStatisticsQ::getName, PonPowerStatisticsQ::getPonRxPowerCurrAvg));

        Map<String, Integer> hardGtwCountMap = ponList.stream().filter(pon -> pon.getPonRxPowerCurrType() == 2)
                .collect(Collectors.toMap(PonPowerStatisticsQ::getName, PonPowerStatisticsQ::getGtwCount));
        Map<String, Integer> hardAvgMap = ponList.stream().filter(pon -> pon.getPonRxPowerCurrType() == 2)
                .collect(Collectors.toMap(PonPowerStatisticsQ::getName, PonPowerStatisticsQ::getPonRxPowerCurrAvg));


        //Map<String, List<PonPowerStatisticsQ>> lowNameMap = ponList.stream().filter(pon -> pon.getPonRxPowerCurrType() == 1)
        //        .collect(Collectors.groupingBy(PonPowerStatisticsQ::getName));
        //Map<String, List<PonPowerStatisticsQ>> hardNameMap = ponList.stream().filter(pon -> pon.getPonRxPowerCurrType() == 2)
        //        .collect(Collectors.groupingBy(PonPowerStatisticsQ::getName));

        List<String> xAxisLists = new ArrayList<>();

        // 强光接收功率平均值
        List<String> receivePowerStrongAverage = new ArrayList<>();

        // 光接收功率强光占比,单位%
        List<String> receivePowerStrongRate = new ArrayList<>();

        // 弱光接收功率平均值
        List<String> receivePowerWeakAverage = new ArrayList<>();

        // 光接收功率弱光占比,单位%
        List<String> receivePowerWeakRate = new ArrayList<>();

        //Map<String, PonPowerStatisticsM> ponMap = new HashMap<>();

        // (provCode,名称)
        Map<String, String> provMap = getDicAreaMap();
        // (cityCode,名称)
        Map<String, String> cityMap = getDicCityMap();

        //String otherName = null;
        //String otherFactModelName = null;
        for (String xAxis : xAxisList) {
            String finalName = null;
            switch (flag) {
                // 省份(code->名称)
                case 1:
                    finalName = provMap.getOrDefault(xAxis, "其他");
                    break;
                // 地市(code->名称)
                case 11:
                    finalName = cityMap.getOrDefault(xAxis, "其他");
                    break;
                // 厂商,厂商型号
                case 2:
                    finalName = xAxis;
                    break;
                // 厂商型号
                case 3:
                    finalName = xAxis;
                    //String[] split = StringUtils.split(xAxis, "_");
                    //otherFactModelName = split[0] + "其他";
                    break;
            }

            Integer lowGtwCount = lowGtwCountMap.getOrDefault(xAxis, 0);
            long lowGtwTotalCount = totalMap.getOrDefault(xAxis, 0L);
            Integer lowAvg = lowAvgMap.getOrDefault(xAxis, null);
            Integer hardGtwCount = hardGtwCountMap.getOrDefault(xAxis, 0);
            long hardGtwTotalCount = totalMap.getOrDefault(xAxis, 0L);
            Integer hardAvg = hardAvgMap.getOrDefault(xAxis, null);
            String lowPercent = (lowGtwCount == 0) ? "0" : getPercent(Long.valueOf(lowGtwCount), lowGtwTotalCount);
            String hardPercent = (hardGtwCount == 0) ? "0" : getPercent(Long.valueOf(hardGtwCount), hardGtwTotalCount);
            xAxisLists.add(finalName);
            receivePowerStrongAverage.add(hardAvg == null ? "" : String.valueOf(hardAvg));
            receivePowerStrongRate.add(String.valueOf(hardPercent));
            receivePowerWeakAverage.add(lowAvg == null ? "" : String.valueOf(lowAvg));
            receivePowerWeakRate.add(String.valueOf(lowPercent));
        }


        result.setxAxis(xAxisLists);
        result.setReceivePowerStrongRate(receivePowerStrongRate);
        result.setReceivePowerStrongAverage(receivePowerStrongAverage);
        result.setReceivePowerWeakAverage(receivePowerWeakAverage);
        result.setReceivePowerWeakRate(receivePowerWeakRate);

        return result;
    }


    public List<GatewayOnOffLineNumResult> converOnOffLinetList(List<String> codeList, Map<String, Long> onMap, Map<String, Long> offMap, Map<String, String> dicMap) {
        List<GatewayOnOffLineNumResult> resultList = new ArrayList<>();

        codeList.forEach(code -> {
            GatewayOnOffLineNumResult gwtResult = new GatewayOnOffLineNumResult();
            String dicName = dicMap.getOrDefault(code, "其他");
            Long onlineNum = onMap.get(code);
            Long offlineNum = offMap.get(code);
            gwtResult.setName(dicName);
            gwtResult.setOnlineNum(onlineNum == null ? "-" : String.valueOf(onlineNum));
            gwtResult.setOfflineNum(offlineNum == null ? "-" : String.valueOf(offlineNum));
            resultList.add(gwtResult);
        });

        return resultList;
    }


    public List<GatewayCountDtoResult> convertList(List<GatewayCountDto> list) {
        List<GatewayCountDtoResult> resultList = new ArrayList<>();

        list.forEach(item -> {
            GatewayCountDtoResult dtoResult = new GatewayCountDtoResult();
            dtoResult.setName(item.getName());
            dtoResult.setValue(item.getValue());
            dtoResult.setPercent(item.getPercent());
            resultList.add(dtoResult);
        });

        return resultList;
    }


    private GatewayCountResult convertGatewayCountResultList(List<GatewayCount> gtList, Map<String, String> areaDictMap, Map<String, String> factoryDictMap, Map<String, String> industryDictMap) {
        GatewayCountResult result = new GatewayCountResult();
        List<GatewayCount> uniqList = gtList.stream().distinct().collect(Collectors.toList());
        // (code,num)
        Map<String, Long> provResultMap = uniqList.stream().collect(Collectors.groupingBy(GatewayCount::getProvinceCode, Collectors.reducing(0L, GatewayCount::getGatewayCount, Long::sum)));
        Map<String, Long> venderResultMap = uniqList.stream().collect(Collectors.groupingBy(GatewayCount::getFactoryCode, Collectors.reducing(0L, GatewayCount::getGatewayCount, Long::sum)));
        // (industryName,num)
        Map<String, Long> industryResultMap = uniqList.stream().collect(Collectors.groupingBy(GatewayCount::getIndustry, Collectors.reducing(0L, GatewayCount::getGatewayCount, Long::sum)));

        long total = uniqList.stream().mapToLong(GatewayCount::getGatewayCount).sum();

        List<GatewayCountDtoResult> listByProvince = new ArrayList<>();
        List<GatewayCountDtoResult> listByVendor = new ArrayList<>();
        List<GatewayCountDtoResult> listByIndustry = new ArrayList<>();

        GatewayCountDtoResult otherByProvince = new GatewayCountDtoResult();
        provResultMap.forEach((provCode, provNum) -> {
            if (areaDictMap.containsKey(provCode)) {
                GatewayCountDtoResult byProvince = new GatewayCountDtoResult();
                byProvince.setName(areaDictMap.get(provCode));
                byProvince.setValue(provNum);
                byProvince.setPercent(getPercent(provNum, total));
                listByProvince.add(byProvince);
            } else {
                String otherName = otherByProvince.getName();
                if ("其他".equals(otherName)) {
                    Long otherNum = otherByProvince.getValue();
                    otherByProvince.setValue(otherNum + provNum);
                } else {
                    otherByProvince.setName("其他");
                    otherByProvince.setValue(provNum);
                }
            }
        });

        long otherProvinceNum = otherByProvince.getValue() == null ? 0 : otherByProvince.getValue();
        if (otherProvinceNum > 0) {
            otherByProvince.setPercent(getPercent(otherProvinceNum, total));
            listByProvince.add(otherByProvince);
        }

        GatewayCountDtoResult otherByManufacturer = new GatewayCountDtoResult();
        venderResultMap.forEach((vendorCode, vendorNum) -> {
            if (factoryDictMap.containsKey(vendorCode)) {
                GatewayCountDtoResult byManufacturer = new GatewayCountDtoResult();
                String factoryName = factoryDictMap.get(vendorCode);
                byManufacturer.setName(factoryName);
                byManufacturer.setValue(vendorNum);
                byManufacturer.setPercent(getPercent(vendorNum, total));
                listByVendor.add(byManufacturer);
            } else {
                String otherName = otherByManufacturer.getName();
                if ("其他".equals(otherName)) {
                    Long otherNum = otherByManufacturer.getValue();
                    otherByManufacturer.setValue(otherNum + vendorNum);
                } else {
                    otherByManufacturer.setName("其他");
                    otherByManufacturer.setValue(vendorNum);
                }
            }
        });

        long otherManufacturerNum = otherByManufacturer.getValue() == null ? 0 : otherByManufacturer.getValue();
        if (otherManufacturerNum > 0) {
            otherByManufacturer.setPercent(getPercent(otherManufacturerNum, total));
            listByVendor.add(otherByManufacturer);
        }

        GatewayCountDtoResult otherIndustry = new GatewayCountDtoResult();
        industryResultMap.forEach((industryName, industryNum) -> {
            GatewayCountDtoResult byIndustry = new GatewayCountDtoResult();
            if (!"其他".equals(industryName) && industryDictMap.containsValue(industryName)) {
                byIndustry.setName(industryName);
                byIndustry.setValue(industryNum);
                byIndustry.setPercent(getPercent(industryNum, total));
                listByIndustry.add(byIndustry);
            } else {
                String otherName = otherIndustry.getName();
                if ("其他".equals(otherName)) {
                    Long otherNum = otherIndustry.getValue();
                    otherIndustry.setValue(otherNum + industryNum);
                } else {
                    otherIndustry.setName("其他");
                    otherIndustry.setValue(industryNum);
                }
            }
        });

        long otherIndustryNum = otherIndustry.getValue() == null ? 0 : otherIndustry.getValue();
        if (otherIndustryNum > 0) {
            otherIndustry.setPercent(getPercent(otherIndustryNum, total));
            listByIndustry.add(otherIndustry);
        }

        listByProvince.sort(Comparator.comparing(GatewayCountDtoResult::getValue).reversed());
        listByVendor.sort(Comparator.comparing(GatewayCountDtoResult::getValue).reversed());
        listByIndustry.sort(Comparator.comparing(GatewayCountDtoResult::getValue).reversed());


        result.setListByProvince(listByProvince);
        result.setListByVendor(listByVendor);
        result.setListByIndustry(listByIndustry);
        result.setTotalGatewayNum(String.valueOf(total));
        return result;
    }


    private GatewayIncrementCountResult convertGatewayIncrementCountResultList(List<GatewayIncrementCount> gtList, Map<String, String> areaDictMap, Map<String, String> factoryDictMap, Map<String, String> industryDictMap) {
        GatewayIncrementCountResult result = new GatewayIncrementCountResult();
        List<GatewayIncrementCount> uniqList = gtList.stream().distinct().collect(Collectors.toList());
        // (code,num)
        Map<String, Long> provResultMap = uniqList.stream().collect(Collectors.groupingBy(GatewayIncrementCount::getProvinceCode, Collectors.reducing(0L, GatewayIncrementCount::getGatewayIncCount, Long::sum)));
        Map<String, Long> venderResultMap = uniqList.stream().collect(Collectors.groupingBy(GatewayIncrementCount::getFactoryCode, Collectors.reducing(0L, GatewayIncrementCount::getGatewayIncCount, Long::sum)));
        // (industryName,num)
        Map<String, Long> industryResultMap = uniqList.stream().collect(Collectors.groupingBy(GatewayIncrementCount::getIndustry, Collectors.reducing(0L, GatewayIncrementCount::getGatewayIncCount, Long::sum)));

        long total = uniqList.stream().mapToLong(GatewayIncrementCount::getGatewayIncCount).sum();

        List<GatewayCountDtoResult> listByProvince = new ArrayList<>();
        List<GatewayCountDtoResult> listByVendor = new ArrayList<>();
        List<GatewayCountDtoResult> listByIndustry = new ArrayList<>();

        GatewayCountDtoResult otherByProvince = new GatewayCountDtoResult();
        provResultMap.forEach((provCode, provNum) -> {
            if (areaDictMap.containsKey(provCode)) {
                GatewayCountDtoResult byProvince = new GatewayCountDtoResult();
                byProvince.setName(areaDictMap.get(provCode));
                byProvince.setValue(provNum);
                byProvince.setPercent(getPercent(provNum, total));
                listByProvince.add(byProvince);
            } else {
                String otherName = otherByProvince.getName();
                if ("其他".equals(otherName)) {
                    Long otherNum = otherByProvince.getValue();
                    otherByProvince.setValue(otherNum + provNum);
                } else {
                    otherByProvince.setName("其他");
                    otherByProvince.setValue(provNum);
                }
            }
        });

        long otherProvinceNum = otherByProvince.getValue() == null ? 0 : otherByProvince.getValue();
        if (otherProvinceNum > 0) {
            otherByProvince.setPercent(getPercent(otherProvinceNum, total));
            listByProvince.add(otherByProvince);
        }

        GatewayCountDtoResult otherByManufacturer = new GatewayCountDtoResult();
        venderResultMap.forEach((vendorCode, vendorNum) -> {
            if (factoryDictMap.containsKey(vendorCode)) {
                GatewayCountDtoResult byManufacturer = new GatewayCountDtoResult();
                String factoryName = factoryDictMap.get(vendorCode);
                byManufacturer.setName(factoryName);
                byManufacturer.setValue(vendorNum);
                byManufacturer.setPercent(getPercent(vendorNum, total));
                listByVendor.add(byManufacturer);
            } else {
                String otherName = otherByManufacturer.getName();
                if ("其他".equals(otherName)) {
                    Long otherNum = otherByManufacturer.getValue();
                    otherByManufacturer.setValue(otherNum + vendorNum);
                } else {
                    otherByManufacturer.setName("其他");
                    otherByManufacturer.setValue(vendorNum);
                }
            }
        });

        long otherManufacturerNum = otherByManufacturer.getValue() == null ? 0 : otherByManufacturer.getValue();
        if (otherManufacturerNum > 0) {
            otherByManufacturer.setPercent(getPercent(otherManufacturerNum, total));
            listByVendor.add(otherByManufacturer);
        }

        GatewayCountDtoResult otherIndustry = new GatewayCountDtoResult();
        industryResultMap.forEach((industryName, industryNum) -> {
            GatewayCountDtoResult byIndustry = new GatewayCountDtoResult();
            if (!"其他".equals(industryName) && industryDictMap.containsValue(industryName)) {
                byIndustry.setName(industryName);
                byIndustry.setValue(industryNum);
                byIndustry.setPercent(getPercent(industryNum, total));
                listByIndustry.add(byIndustry);
            } else {
                String otherName = otherIndustry.getName();
                if ("其他".equals(otherName)) {
                    Long otherNum = otherIndustry.getValue();
                    otherIndustry.setValue(otherNum + industryNum);
                } else {
                    otherIndustry.setName("其他");
                    otherIndustry.setValue(industryNum);
                }
            }
        });

        long otherIndustryNum = otherIndustry.getValue() == null ? 0 : otherIndustry.getValue();
        if (otherIndustryNum > 0) {
            otherIndustry.setPercent(getPercent(otherIndustryNum, total));
            listByIndustry.add(otherIndustry);
        }

        listByProvince.sort(Comparator.comparing(GatewayCountDtoResult::getValue).reversed());
        listByVendor.sort(Comparator.comparing(GatewayCountDtoResult::getValue).reversed());
        listByIndustry.sort(Comparator.comparing(GatewayCountDtoResult::getValue).reversed());


        result.setListByProvince(listByProvince);
        result.setListByVendor(listByVendor);
        result.setListByIndustry(listByIndustry);
        result.setAddGatewayNum(String.valueOf(total));
        return result;
    }


    private String convertDataType(Object obj) {

        String result = "0";

        try {
            if (obj instanceof Long || obj instanceof Double || obj instanceof Float) {
                result = String.valueOf(obj);
            }
        } catch (Exception ex) {
            result = "0";
        }

        return result;
    }


    public Map<String, String> getDicAreaMap() {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andLevelEqualTo(1);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        return dicAreaInfoList.stream().distinct().collect(Collectors.toMap(DicAreaInfo::getGcode, DicAreaInfo::getGname))
                .entrySet().stream().collect(Collectors.toMap(m -> String.valueOf(m.getKey()), Map.Entry::getValue));
    }


    public Map<String, String> getDicAreaMap(List<String> provCodeList) {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andLevelEqualTo(1);
        criteria.andPcodeIn(provCodeList);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        return dicAreaInfoList.stream().distinct().collect(Collectors.toMap(DicAreaInfo::getGcode, DicAreaInfo::getGname))
                .entrySet().stream().collect(Collectors.toMap(m -> String.valueOf(m.getKey()), Map.Entry::getValue));
    }

    public Map<String, String> getDicCityMap(List<String> cityCodeList) {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andGcodeIn(cityCodeList);
        criteria.andLevelEqualTo(2);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        return dicAreaInfoList.stream().collect(Collectors.toMap(DicAreaInfo::getGcode, DicAreaInfo::getGname))
                .entrySet().stream().collect(Collectors.toMap(m -> String.valueOf(m.getKey()), Map.Entry::getValue));
    }


    public Map<String, String> getDictCityMap(List<String> provCodeList) {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        List<Integer> list = provCodeList.stream().map(Integer::valueOf).collect(Collectors.toList());
        criteria.andPidIn(list);
        criteria.andLevelEqualTo(2);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        return dicAreaInfoList.stream().collect(Collectors.toMap(DicAreaInfo::getGcode, DicAreaInfo::getGname))
                .entrySet().stream().collect(Collectors.toMap(m -> String.valueOf(m.getKey()), Map.Entry::getValue));
    }

    public Map<String, String> getDicCityMap() {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andLevelEqualTo(2);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        return dicAreaInfoList.stream().collect(Collectors.toMap(DicAreaInfo::getGcode, DicAreaInfo::getGname))
                .entrySet().stream().collect(Collectors.toMap(m -> String.valueOf(m.getKey()), Map.Entry::getValue));
    }

    public Map<String, String> getDicFactoryMap() {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);
        return dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryCode, DicFactory::getFactoryName));
    }


    public Map<Long, String> getDicFactoryModelMap() {
        DictDeviceModelExample example = new DictDeviceModelExample();
        List<DictDeviceModel> list = dictDeviceModelMapper.selectFactModelByExample(example);
        return list.stream().collect(Collectors.toMap(DictDeviceModel::getDeviceModelId, DictDeviceModel::getDeviceModel));
    }

    public Map<Long, String> getDictFactoryModelMap(List<Long> factidsList) {
        DictDeviceModelExample example = new DictDeviceModelExample();
        DictDeviceModelExample.Criteria criteria = example.createCriteria();
        criteria.andFactoryIdIn(factidsList);
        List<DictDeviceModel> result = dictDeviceModelMapper.selectFactModelByExample(example);
        return result.stream().collect(Collectors.toMap(DictDeviceModel::getDeviceModelId, DictDeviceModel::getDeviceModel));
    }

    public Map<Long, String> getDicFactoryModelMap(List<Long> list) {
        DictDeviceModelExample example = new DictDeviceModelExample();
        DictDeviceModelExample.Criteria criteria = example.createCriteria();
        criteria.andDeviceModelIdIn(list);
        List<DictDeviceModel> result = dictDeviceModelMapper.selectFactModelByExample(example);
        return result.stream().collect(Collectors.toMap(DictDeviceModel::getDeviceModelId, DictDeviceModel::getDeviceModel));
    }

    public Map<String, String> getDictFactoryNameMap() {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);
        return dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryName, DicFactory::getFactoryCode));
    }


    public Map<String, Long> getDictFactoryCodeIdsMap() {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);
        return dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryCode, DicFactory::getFactoryId));
    }


    public Map<String, String> getDictFactoryCodeByIds(List<Long> list) {
        DicFactoryExample example = new DicFactoryExample();
        DicFactoryExample.Criteria criteria = example.createCriteria();
        criteria.andFactoryIdIn(list);
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByExample(example);
        return dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryCode, DicFactory::getFactoryName));
    }


    /*public Map<String, String> getDicFactoryMap(List<String> vendorCodesList) {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);

        Map<String, String> codeNameMap = new HashMap<>();
        if (CollectionUtils.isEmpty(vendorCodesList)) {
            codeNameMap = dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryCode, DicFactory::getFactoryName));
        } else {
            for (DicFactory dicFactory : dicFactoryList) {
                String factoryCode = dicFactory.getFactoryCode();
                String factoryName = dicFactory.getFactoryName();
                if (vendorCodesList.contains(factoryCode)) {
                    codeNameMap.put(factoryCode, factoryName);
                }
            }
        }
        //return dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryId, DicFactory::getFactoryName));
        return codeNameMap;
    }*/

    public Map<String, String> getDictIndustryMap() {
        DictIndustryExample example = new DictIndustryExample();
        List<DictIndustry> industryList = dictIndustryMapper.selectByExample(example);
        return industryList.stream().collect(Collectors.toMap(DictIndustry::getCode, DictIndustry::getIndustryName))
                .entrySet().stream().collect(Collectors.toMap(m -> String.valueOf(m.getKey()), Map.Entry::getValue));
    }

    public List<String> getDicFactoryCodeListById(List<String> vendorIdList) {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);
        Map<Long, String> dicFactoryIdCodeMap = dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryId, DicFactory::getFactoryCode));
        List<String> finalVendorList = new ArrayList<>();
        for (String vendor : vendorIdList) {
            if (StringUtils.isNotBlank(vendor)) {
                String vendorCode = dicFactoryIdCodeMap.get(Long.parseLong(vendor));
                finalVendorList.add(vendorCode);
            }
        }
        return finalVendorList;
    }


    public List<String> getDicFactoryIdCodeMap(List<String> vendorIdList) {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);
        Map<Long, String> dicFactoryIdCodeMap = dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryId, DicFactory::getFactoryCode));

        List<String> vendorCodeList = new ArrayList<>();
        vendorIdList.forEach(venderId -> vendorCodeList.add(dicFactoryIdCodeMap.getOrDefault(Long.parseLong(venderId), "")));

        return vendorCodeList;
    }


    private GatewayAlarmCountResult getGatewayAlarmCount(List<GatewayAlarmQueryCount> list, List<GatewayAlarmTypeQueryCount> typeList, List<GatewayOnOfflineAllCount> allCounts, int flag) {
        GatewayAlarmCountResult result = new GatewayAlarmCountResult();

        List<GatewayAlarmTypeQueryCount> alarmTypeUniqList = typeList.stream().distinct().collect(Collectors.toList());
        List<GatewayOnOfflineAllCount> allUniqList = allCounts.stream().distinct().collect(Collectors.toList());

        // prov,city,vendor
        Map<String, Long> codeAllCountMap = allUniqList.stream().collect(Collectors.groupingBy(GatewayOnOfflineAllCount::getName, Collectors.reducing(0L, GatewayOnOfflineAllCount::getCount, Long::sum)));
        Map<String, Long> countMap = alarmTypeUniqList.stream().collect(Collectors.groupingBy(GatewayAlarmTypeQueryCount::getName, Collectors.reducing(0L, GatewayAlarmTypeQueryCount::getCount, Long::sum)));

        List<GatewayAlarmCountAndPie> gtPieList = new LinkedList<>();

        // (code,name)
        Map<String, String> provMap = getDicAreaMap();
        Map<String, String> cityMap = getDicCityMap();
        Map<String, String> factoryMap = getDicFactoryMap();
        // 根据数量倒序
        list.sort(Comparator.comparing(GatewayAlarmQueryCount::getCount).reversed());
        list.forEach(queryCount -> {
            String name = queryCount.getName();
            if (StringUtils.isNotBlank(name)) {
                GatewayAlarmCountAndPie countAndPie = new GatewayAlarmCountAndPie();
                long count = queryCount.getCount();
                // 本省、地市、厂商类型的总网关数
                long allTypeCount = countMap.get(name);

                String gName = null;
                switch (flag) {
                    case 1:
                        gName = provMap.getOrDefault(name, "其他");
                        break;
                    case 2:
                        gName = cityMap.getOrDefault(name, "其他");
                        break;
                    case 3:
                        gName = factoryMap.getOrDefault(name, "其他");
                        break;
                }

                countAndPie.setName(gName);
                countAndPie.setValue(String.valueOf(count));
                long total = codeAllCountMap.get(name);
                String percent = getPercent(count, total);
                countAndPie.setRatio(percent);

                List<PieData> pieDataList = new ArrayList<>();

                alarmTypeUniqList.forEach(atypeCount -> {
                    PieData pieData = new PieData();
                    String typeCountName = atypeCount.getName();
                    if (name.equalsIgnoreCase(typeCountName)) {
                        String typeName = atypeCount.getTypeName();
                        long typeCount = atypeCount.getCount();
                        pieData.setName(typeName);
                        pieData.setValue(String.valueOf(typeCount));
                        String piePercent = getPercent(typeCount, allTypeCount);
                        pieData.setRatio(piePercent);
                        pieDataList.add(pieData);
                    }
                });

                countAndPie.setPieData(pieDataList);
                gtPieList.add(countAndPie);
          /*  if (name.equals("CIOT")) {
                System.err.println("name:" + name);
                System.err.println("count:" + count);
                System.err.println("allTypeCount:" + allTypeCount);
                System.err.println("total:" + total);
                System.err.println("percent:" + percent);
                System.err.println("countAndPie:" + JSON.toJSONString(countAndPie));
                System.err.println("pieDataList:" + JSON.toJSONString(pieDataList));
                System.err.println("gtPieList111:" + JSON.toJSONString(gtPieList));
            }*/
            }
        });


        result.setList(gtPieList);

        return result;
    }


    private String getListString(List<String> list) {
        StringBuilder sb = new StringBuilder();
        list.forEach(item -> sb.append("'").append(item).append("',"));
        return StringUtils.substring(sb.toString(), 0, sb.length() - 1);
    }


    private String decimalFormat(String num) {
        String result;
        if (StringUtils.isNotBlank(num) && num.contains(".")) {
            DecimalFormat df = new DecimalFormat("0.00");
            result = df.format(Double.parseDouble(num));
        } else {
            result = num;
        }

        return result;
    }

    private String dateFormat(long dt) {
        String date = String.valueOf(dt);
        return new StringBuilder(date.substring(0, 4)).append("-").append(date, 4, 6).append("-").append(date, 6, 8).toString();
    }

    private String decimalFormatSec(String num) {
        String result;
        if (StringUtils.isNotBlank(num) && num.contains(".")) {
            DecimalFormat df = new DecimalFormat("0");
            result = df.format(Double.parseDouble(num));
        } else {
            result = num;
        }

        return result;
    }

    private Long dateFormat(String dt) {
        return Long.parseLong(dt.replace("-", ""));
    }

    private String getPercent(Long num, Long total) {
        String perp;
        try {
            if (Objects.equals(num, total)) {
                perp = "100";
            } else {
                perp = decimalFormat(String.valueOf(new BigDecimal((float) num / total).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() * 100));
            }
        } catch (Exception e) {
            perp = "0";
        }

        return perp;
    }

    private String getPercentSec(Long num, Long total) {
        String perp;
        try {
            perp = decimalFormatSec(String.valueOf(new BigDecimal((float) num / total).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() * 100));
        } catch (Exception e) {
            perp = "0";
        }

        return perp;
    }


    private List<String> getOtherNameList(List<String> oldXAxisList) {
        Set<String> xAxisSet = new HashSet<>();
        oldXAxisList.forEach(oldXAxis -> xAxisSet.add(oldXAxis.split("_")[0] + "_其他"));
        return new ArrayList<>(xAxisSet);
    }


}
