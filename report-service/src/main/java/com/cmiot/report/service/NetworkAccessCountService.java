package com.cmiot.report.service;

import com.cmiot.report.dto.customer.*;

/**
 * @Classname NetworkAccessCountService
 * @Description
 * @Date 2022/8/9 16:13
 * @Created by lei
 */
public interface NetworkAccessCountService {
    NetworkUsageChartData getNetworkUsage(NetworkUsageChartRequest request);

    NetworkUsagePieData getNetworkUsageDeviceStatus(NetworkUsageChartRequest request);

    NetworkPerferChartData getProgramPreference(NetworkPerferRequest request);

    NetworkPerferChartData getSitePreference(NetworkPerferRequest request);
}
