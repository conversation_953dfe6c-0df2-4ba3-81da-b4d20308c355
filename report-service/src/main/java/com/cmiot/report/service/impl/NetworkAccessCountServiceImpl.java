package com.cmiot.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.cmiot.report.bean.GatewayNetworkFlowExample;
import com.cmiot.report.bean.NetworkPerferCount;
import com.cmiot.report.bean.NetworkUsageDeviceCountData;
import com.cmiot.report.bean.NetworkUsageStaData;
import com.cmiot.report.dto.customer.*;
import com.cmiot.report.mapper.GatewayNetworkFlowMapper;
import com.cmiot.report.mapper.HguNetworkVisitDetailMapper;
import com.cmiot.report.mapper.MediaNetworkVisitCountMapper;
import com.cmiot.report.service.DicDataService;
import com.cmiot.report.service.NetworkAccessCountService;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.MyNumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigInteger;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.ToLongFunction;

/**
 * @Classname NetworkAccessCountServiceImpl
 * @Description
 * @Date 2022/8/9 16:13
 * @Created by lei
 */
@Service
public class NetworkAccessCountServiceImpl implements NetworkAccessCountService {
    private final static Logger logger = LoggerFactory.getLogger(NetworkAccessCountServiceImpl.class);

    @Autowired
    private HguNetworkVisitDetailMapper hguNetworkVisitDetailMapper;

    @Autowired
    private GatewayNetworkFlowMapper gatewayNetworkFlowMapper;

    @Autowired
    private MediaNetworkVisitCountMapper mediaNetworkVisitCountMapper;

    @Autowired
    private DicDataService dicDataService;

    private final int STATUS_FREE = 0;
    private final int STATUS_NORMAL = 1;
    private final int STATUS_BUSY = 2;

    @Value("${network.threshold.floor:10}")
    private Integer networkThresholdFloor;

    @Value("${network.threshold.upper:80}")
    private Integer networkThresholdUpper;


    @Override
    public NetworkUsageChartData getNetworkUsage(NetworkUsageChartRequest request) {
        logger.info("用户行为分析—网络使用情况，接口方法入参=：{}", request);

        NetworkUsageChartData result = new NetworkUsageChartData();
        try {
            String provCodes = request.getProvince();
            String cityCodes = request.getCity();

            if (StringUtils.isBlank(request.getStartTime())) {
                return null;
            }
            if (StringUtils.isBlank(request.getEndTime())) {
                return null;
            }
            // yyyyMMdd
            Long startTime = Long.parseLong(DateUtil.formatInputs(request.getStartTime()));

            Long endTime = Long.parseLong(DateUtil.formatInputs(request.getEndTime()));

            // 类型：区域1，行业2，时间3
            String type = request.getType();

            // 业务类型
            String businessType = request.getBusinessType();

            //行业
            String industry = request.getIndustry();

            if (StringUtils.isBlank(type)) {
                return null;
            }

            GatewayNetworkFlowExample example = new GatewayNetworkFlowExample();
            GatewayNetworkFlowExample.Criteria criteria = example.createCriteria();


            List<String> provList = new ArrayList<>();
            if (StringUtils.isNotBlank(provCodes)) {
                provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provCodes, "\\,"));
                criteria.andProvinceCodeIn(provList);
            }

            List<String> cityCodeList = null;
            if (StringUtils.isNotBlank(cityCodes)) {
                cityCodeList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCodes, "\\,"));
                criteria.andCityCodeIn(cityCodeList);
            }

            List<String> industryCodes = null;
            if (StringUtils.isNotBlank(industry)) {
                industryCodes = (List<String>) CollectionUtils.arrayToList(StringUtils.split(industry, "\\,"));

                List<String> industryNumberCodes = new ArrayList<>();
                for (String industryCode : industryCodes) {
                    try {
                        industryNumberCodes.add(Integer.valueOf(industryCode).toString());
                    } catch (Exception e) {
                        logger.info("industryCode error: {}", industryCode, e);
                    }
                }
                criteria.andIndustryIn(industryNumberCodes);
            }

            List<String> businessTypeCodes = null;
            if (StringUtils.isNotBlank(businessType)) {
                businessTypeCodes = (List<String>) CollectionUtils.arrayToList(StringUtils.split(businessType, "\\,"));
                criteria.andBussinessTypeIn(businessTypeCodes);
            }

            if (startTime != null) {
                criteria.andGdateGreaterThanOrEqualTo(startTime);
            }

            if (endTime != null) {
                criteria.andGdateLessThanOrEqualTo(endTime);
            }

            // 1-省份,11-地市,2-行业,3-时间
            int flag = 11;

            // 横坐标
            List<String> xAxisList = new ArrayList<>();
            List<NetworkUsageStaData> networkUsageStaDataList = new ArrayList<>();
            if ("1".equalsIgnoreCase(type)) {
                //区域
                if (!CollectionUtils.isEmpty(provList) && provList.size() == 1) {
                    // 仅一个省份会显示该省的多个地市
                    networkUsageStaDataList = this.gatewayNetworkFlowMapper.selectByCityExample(example);

                    if (CollectionUtils.isEmpty(cityCodeList)) {
                        // (cityCode,name)
                        Map<String, String> dicCityMap = this.dicDataService.getDictCityMap(provList);
                        xAxisList.addAll(dicCityMap.keySet());
                    } else {
                        xAxisList.addAll(cityCodeList);
                    }
                } else if ((!CollectionUtils.isEmpty(provList) && provList.size() >= 2)) {
                    // 两个以上省份则显示省份维度
                    networkUsageStaDataList = this.gatewayNetworkFlowMapper.selectByProvExample(example);

                    xAxisList.addAll(provList);
                    flag = 1;
                } else if (CollectionUtils.isEmpty(provList)) {
                    // 全部省份维度
                    networkUsageStaDataList = this.gatewayNetworkFlowMapper.selectByProvExample(example);

                    // (provCode,name)
                    Map<String, String> dicProvMap = this.dicDataService.getDicAreaMap();
                    xAxisList.addAll(dicProvMap.keySet());
                    flag = 1;
                }
            } else if ("2".equalsIgnoreCase(type)) {
                //行业
                networkUsageStaDataList = gatewayNetworkFlowMapper.selectByIndustryExample(example);

                if (CollectionUtils.isEmpty(industryCodes)) {
                    Map<String, String> dicFactoryMap = this.dicDataService.getDictIndustryMap();
                    xAxisList.addAll(dicFactoryMap.values());
                } else {
                    Map<String, String> dicFactoryMap = this.dicDataService.getDictIndustryMap(industryCodes);
                    xAxisList.addAll(dicFactoryMap.values());
                }
                flag = 2;
            } else if ("3".equalsIgnoreCase(type)) {
                //时间
                //时间类型 1 天， 2 近7天，3 近30天，4 近1年
                //按小时， 按天， 间隔5天， 按月
                String timeType = request.getTimeType();

                if ("1".equals(timeType)) {
                    //按时间
                    List<NetworkUsageStaData> temps = gatewayNetworkFlowMapper.selectByTimeHourExample(example);
                    for (int i = 0; i < 24; i++) {
                        boolean have = false;
                        for (NetworkUsageStaData temp : temps) {
                            String tempName = temp.getName();
                            if (String.valueOf(i).equals(tempName)) {
                                networkUsageStaDataList.add(temp);
                                have = true;
                                break;
                            }
                        }
                        if (!have) {
                            NetworkUsageStaData networkUsageStaData = new NetworkUsageStaData();
                            networkUsageStaData.setName(String.valueOf(i));
                            networkUsageStaData.setTotalUsage(0d);
                            networkUsageStaData.setTotalBandwidth(0d);
                            networkUsageStaDataList.add(networkUsageStaData);
                        }
                    }
                } else {
                    //按天， 7天
                    if ("2".equals(timeType)) {
                        List<NetworkUsageStaData> temps = gatewayNetworkFlowMapper.selectByTimeExample(example);

                        LocalDateTime startTimeLocal = dateToLocalDateTime(request.getStartTime());

                        DateTimeFormatter dfyyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
                        for (int i = 0; i < 7; i++) {
                            boolean have = false;
                            for (NetworkUsageStaData temp : temps) {
                                String nameTemp = temp.getName();
                                if (nameTemp.equals(startTimeLocal.plusDays(i).format(dfyyyyMMdd))) {
                                    NetworkUsageStaData networkUsageStaData = new NetworkUsageStaData();
                                    networkUsageStaData.setName(startTimeLocal.plusDays(i).format(dfyyyyMMdd));
                                    networkUsageStaData.setTotalUsage(temp.getTotalUsage());
                                    networkUsageStaData.setTotalBandwidth(temp.getTotalBandwidth());
                                    networkUsageStaDataList.add(networkUsageStaData);
                                    have = true;
                                }

                            }
                            if (!have) {
                                NetworkUsageStaData networkUsageStaData = new NetworkUsageStaData();
                                networkUsageStaData.setName(startTimeLocal.plusDays(i).format(dfyyyyMMdd));
                                networkUsageStaData.setTotalUsage(0d);
                                networkUsageStaData.setTotalBandwidth(0d);
                                networkUsageStaDataList.add(networkUsageStaData);
                            }
                        }

                    } else if ("3".equals(timeType)) {
                        //一个月30天， 间隔5天
                        List<NetworkUsageStaData> temps = gatewayNetworkFlowMapper.selectByTimeExample(example);

                        List<NetworkUsageStaData> tempEveryDay = new ArrayList<>();

                        LocalDateTime startTimeLocal = dateToLocalDateTime(request.getStartTime());
                        LocalDateTime endTimeLocal = dateToLocalDateTime(request.getEndTime());
                        DateTimeFormatter dfyyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
                        DateTimeFormatter dfyyyy_MM_dd = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                        for (int i = 0; i < 30; i++) {
                            boolean have = false;
                            for (NetworkUsageStaData temp : temps) {
                                String nameTemp = temp.getName();
                                if (nameTemp.equals(startTimeLocal.plusDays(i).format(dfyyyyMMdd))) {
                                    NetworkUsageStaData networkUsageStaData = new NetworkUsageStaData();
                                    networkUsageStaData.setName(startTimeLocal.plusDays(i).format(dfyyyyMMdd));
                                    networkUsageStaData.setTotalUsage(temp.getTotalUsage());
                                    networkUsageStaData.setTotalBandwidth(temp.getTotalBandwidth());
                                    tempEveryDay.add(networkUsageStaData);
                                    have = true;
                                }

                            }
                            if (!have) {
                                NetworkUsageStaData networkUsageStaData = new NetworkUsageStaData();
                                networkUsageStaData.setName(startTimeLocal.plusDays(i).format(dfyyyyMMdd));
                                networkUsageStaData.setTotalUsage(0d);
                                networkUsageStaData.setTotalBandwidth(0d);
                                tempEveryDay.add(networkUsageStaData);
                            }
                        }
                        int interval = 5;
                        //按5天聚合
                        LocalDateTime dateAix = dateToLocalDateTime(request.getStartTime());
                        List<NetworkUsageStaData> tempFive = new ArrayList<>();
                        int step = 0;
                        for (NetworkUsageStaData networkUsageStaData : tempEveryDay) {
                            step++;
                            tempFive.add(networkUsageStaData);
                            if (step >= 5) {
                                if (!tempFive.isEmpty()) {
                                    NetworkUsageStaData data = new NetworkUsageStaData();
                                    data.setName(dateAix.format(dfyyyy_MM_dd));
                                    double totalBandwidth = 0d;
                                    double totalUsage = 0d;
                                    for (NetworkUsageStaData networkUsageStaDataTemp : tempFive) {
                                        totalBandwidth += networkUsageStaDataTemp.getTotalBandwidth();
                                        totalUsage += networkUsageStaDataTemp.getTotalUsage();
                                    }
                                    data.setTotalUsage(totalUsage);
                                    data.setTotalBandwidth(totalBandwidth);
                                    networkUsageStaDataList.add(data);
                                }

                                dateAix = dateAix.plusDays(interval);
                                tempFive = new ArrayList<>();
                                step = 0;
                            }
                        }

                    } else {
                        //按月, 12个月
                        DateTimeFormatter dfyyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
                        LocalDateTime startTimeLocal = dateToLocalDateTime(request.getStartTime());
                        LocalDateTime endTimeLocal = dateToLocalDateTime(request.getEndTime());
                        startTime = Long.valueOf(startTimeLocal.withDayOfMonth(1).format(dfyyyyMMdd));

                        GatewayNetworkFlowExample exampleYear = new GatewayNetworkFlowExample();
                        GatewayNetworkFlowExample.Criteria criteriaYear = exampleYear.createCriteria();

                        if (provList != null && !provList.isEmpty()) {
                            criteriaYear.andProvinceCodeIn(provList);
                        }

                        if (cityCodeList != null && !cityCodeList.isEmpty()) {
                            criteriaYear.andCityCodeIn(cityCodeList);
                        }

                        if (industryCodes != null && !industryCodes.isEmpty()) {
                            criteriaYear.andIndustryIn(industryCodes);
                        }

                        if (businessTypeCodes != null && !businessTypeCodes.isEmpty()) {
                            criteriaYear.andBussinessTypeIn(businessTypeCodes);
                        }

                        criteriaYear.andGdateGreaterThanOrEqualTo(startTime);
                        criteriaYear.andGdateLessThanOrEqualTo(endTime);

                        List<NetworkUsageStaData> temps = gatewayNetworkFlowMapper.selectByTimeMonthExample(exampleYear);

                        DateTimeFormatter dfyyyy_MM = DateTimeFormatter.ofPattern("yyyy-MM");
                        DateTimeFormatter dfyyyyMM = DateTimeFormatter.ofPattern("yyyyMM");
                        int i = 0;
                        while (!startTimeLocal.plusMonths(i).withDayOfMonth(1).isAfter(endTimeLocal.withDayOfMonth(1))) {
                            boolean have = false;
                            for (NetworkUsageStaData temp : temps) {
                                String nameTemp = temp.getName();
                                if (nameTemp.equals(startTimeLocal.plusMonths(i).format(dfyyyyMM))) {
                                    NetworkUsageStaData networkUsageStaData = new NetworkUsageStaData();
                                    networkUsageStaData.setName(startTimeLocal.plusMonths(i).format(dfyyyy_MM));
                                    networkUsageStaData.setTotalUsage(temp.getTotalUsage());
                                    networkUsageStaData.setTotalBandwidth(temp.getTotalBandwidth());
                                    networkUsageStaDataList.add(networkUsageStaData);
                                    have = true;
                                }
                            }
                            if (!have) {
                                NetworkUsageStaData networkUsageStaData = new NetworkUsageStaData();
                                networkUsageStaData.setName(startTimeLocal.plusMonths(i).format(dfyyyy_MM));
                                networkUsageStaData.setTotalUsage(0d);
                                networkUsageStaData.setTotalBandwidth(0d);
                                networkUsageStaDataList.add(networkUsageStaData);
                            }
                            i++;
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(networkUsageStaDataList)) {
                    for (NetworkUsageStaData networkUsageStaData : networkUsageStaDataList) {
                        xAxisList.add(networkUsageStaData.getName());
                    }
                }
                flag = 3;
            } else {
                return null;
            }

            logger.info("用户行为分析—网络使用情况，查询结果List:{}", JSON.toJSONString(networkUsageStaDataList));
            logger.info("用户行为分析—网络使用情况，维度xAxisList:{}", JSON.toJSONString(xAxisList));
            logger.info("用户行为分析—网络使用情况，类型flag:{}", flag);
            if (!CollectionUtils.isEmpty(networkUsageStaDataList)) {
                result = networkUsageSta(networkUsageStaDataList, xAxisList, flag);
            } else {
                List<String> xAxisLists = new ArrayList<>();

                List<NetworkUsageRatioData> useBroadbandRatioData = new ArrayList<>();
                List<Double> actualNetworkTrafficData = new ArrayList<>();

                // (provCode,名称)
                Map<String, String> provMap = this.dicDataService.getDicAreaMap();
                // (cityCode,名称)
                Map<String, String> cityMap = this.dicDataService.getDicCityMap();

                for (String xAxis : xAxisList) {
                    String finalName = null;
                    switch (flag) {
                        // 省份(code->名称)
                        case 1:
                            finalName = provMap.getOrDefault(xAxis, "其他");
                            break;
                        // 地市(code->名称)
                        case 11:
                            finalName = cityMap.getOrDefault(xAxis, "其他");
                            break;
                        // 行业
                        case 2:
                            finalName = xAxis;
                            break;
                        // 时间
                        case 3:
                            finalName = xAxis;
                            break;
                    }
                    useBroadbandRatioData.add(new NetworkUsageRatioData(0.0D, 0));
                    actualNetworkTrafficData.add(0D);
                    xAxisLists.add(finalName);
                }

                result.setxAxis(xAxisLists);
                result.setUseBroadbandRatioData(useBroadbandRatioData);
                result.setActualNetworkTrafficData(actualNetworkTrafficData);
                logger.info("用户行为分析—网络使用情况，查询结果为空设置默认值.");
            }
        } catch (Exception ex) {
            logger.error("用户行为分析—网络使用情况，异常={}", ex.getMessage(), ex);
        }

        logger.info("用户行为分析—网络使用情况，接口方法出参=：{}", JSON.toJSONString(result));
        return result;
    }

    private LocalDateTime dateToLocalDateTime(String dateTime) {
        return LocalDate.parse(dateTime, DateTimeFormatter.ISO_DATE).atStartOfDay();
    }

    @Override
    public NetworkUsagePieData getNetworkUsageDeviceStatus(NetworkUsageChartRequest request) {
        logger.info("用户行为分析—网络使用情况，接口方法入参=：{}", request);

        NetworkUsagePieData result = new NetworkUsagePieData();
        result.setBusyness(0);
        result.setNormal(0);
        result.setLeisure(0);
        try {
            String provCodes = request.getProvince();
            String cityCodes = request.getCity();

            // yyyyMMdd
            Long startTime = Long.parseLong(DateUtil.formatInputs(request.getStartTime()));

            Long endTime = Long.parseLong(DateUtil.formatInputs(request.getEndTime()));

            // 类型：区域1，行业2，时间3
            String type = request.getType();

            // 业务类型
            String businessType = request.getBusinessType();

            //行业
            String industry = request.getIndustry();

            if (StringUtils.isBlank(type)) {
                return null;
            }

            String selectValue = request.getSelectValue();
/*            if (StringUtils.isBlank(selectValue)) {
                return null;
            }*/

            GatewayNetworkFlowExample example = new GatewayNetworkFlowExample();
            GatewayNetworkFlowExample.Criteria criteria = example.createCriteria();

            List<String> provList = new ArrayList<>();
            if (StringUtils.isNotBlank(provCodes)) {
                provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provCodes, "\\,"));
                criteria.andProvinceCodeIn(provList);
            }

            List<String> cityCodeList = null;
            if (StringUtils.isNotBlank(cityCodes)) {
                cityCodeList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCodes, "\\,"));
                criteria.andCityCodeIn(cityCodeList);
            }

            List<String> industryCodes = null;
            if (StringUtils.isNotBlank(industry)) {
                industryCodes = (List<String>) CollectionUtils.arrayToList(StringUtils.split(industry, "\\,"));

                List<String> industryNumberCodes = new ArrayList<>();
                for (String industryCode : industryCodes) {
                    try {
                        industryNumberCodes.add(Integer.valueOf(industryCode).toString());
                    } catch (Exception e) {
                        logger.info("industryCode error: {}", industryCode, e);
                    }
                }
                criteria.andIndustryIn(industryNumberCodes);
            }

            List<String> businessTypeCodes = null;
            if (StringUtils.isNotBlank(businessType)) {
                businessTypeCodes = (List<String>) CollectionUtils.arrayToList(StringUtils.split(businessType, "\\,"));
                criteria.andBussinessTypeIn(businessTypeCodes);
            }

            String timeType = request.getTimeType();

            // 1-省份,11-地市,2-行业,3-时间
            int flag = 11;

            // 横坐标
            List<String> xAxisList = new ArrayList<>();
            String areaCode = "";
            String industryCode = "";
            String selectTime = "";


            if (!"3".equals(type)) {
                if (startTime != null) {
                    criteria.andGdateGreaterThanOrEqualTo(startTime);
                }

                if (endTime != null) {
                    criteria.andGdateLessThanOrEqualTo(endTime);
                }
            }
            if (selectValue == null) {
                selectValue = "";
            }

            if ("1".equalsIgnoreCase(type)) {
                //区域
                if (!CollectionUtils.isEmpty(provList) && provList.size() == 1) {
                    // 仅一个省份会显示该省的多个地市
                    Map<String, String> dicCityMap = this.dicDataService.getDictCityMap(provList);
                    for (Map.Entry<String, String> entry : dicCityMap.entrySet()) {
                        if (selectValue.equals(entry.getValue())) {
                            areaCode = entry.getKey();
                        }
                    }
                } else {
                    // 省份维度
                    // (provCode,name)
                    Map<String, String> dicAreaMap = this.dicDataService.getDicAreaMap();
                    for (Map.Entry<String, String> entry : dicAreaMap.entrySet()) {
                        if (selectValue.equals(entry.getValue())) {
                            areaCode = entry.getKey();
                        }
                    }
                    flag = 1;
                }
            } else if ("2".equalsIgnoreCase(type)) {
                //行业
                Map<String, String> dicFactoryMap;

                if (CollectionUtils.isEmpty(industryCodes)) {
                    dicFactoryMap = this.dicDataService.getDictIndustryMap();
                } else {
                    dicFactoryMap = this.dicDataService.getDictIndustryMap(industryCodes);
                }

                if (dicFactoryMap != null && !dicFactoryMap.isEmpty()) {
                    for (Map.Entry<String, String> entry : dicFactoryMap.entrySet()) {
                        if (selectValue.equals(entry.getValue())) {
                            industryCode = entry.getKey();
                        }
                    }
                }
                flag = 2;
            } else if ("3".equalsIgnoreCase(type)) {
                //时间
                flag = 3;
                selectTime = selectValue;
            } else {
                return null;
            }
            List<NetworkUsageDeviceCountData> networkUsageDeviceCountData = null;
            switch (flag) {
                // 省份(code->名称)
                case 1:
                    if (StringUtils.isNotBlank(areaCode)) {
                        criteria.andProvinceCodeEqualTo(areaCode);
                    }
                    networkUsageDeviceCountData = this.gatewayNetworkFlowMapper.selectDeviceCountByExample(example);
                    break;
                // 地市(code->名称)
                case 11:
                    if (StringUtils.isNotBlank(areaCode)) {
                        criteria.andCityCodeEqualTo(areaCode);
                    }
                    networkUsageDeviceCountData = this.gatewayNetworkFlowMapper.selectDeviceCountByExample(example);
                    break;
                // 行业
                case 2:
                    if (StringUtils.isNotBlank(industryCode)) {
                        try {
                            String codeTemp = Integer.valueOf(industryCode).toString();
                            criteria.andIndustryEqualTo(codeTemp);
                        } catch (Exception e) {
                            logger.info("selectValue 错误: {}", selectValue, e);
                        }
                    }
                    networkUsageDeviceCountData = this.gatewayNetworkFlowMapper.selectDeviceCountByExample(example);
                    break;
                // 时间
                case 3:
                    Long st = null;
                    if (StringUtils.isNotBlank(selectTime)) {
                        try {
                            st = Long.valueOf(selectTime.replace("-", ""));
                        } catch (Exception ignore) {
                        }
                    }

                    if ("1".equals(timeType)) {
                        //小时数据
                        criteria.andGdateGreaterThanOrEqualTo(startTime);
                        criteria.andGdateLessThanOrEqualTo(endTime);
                        if (st != null && st >= 0L && st < 24L) {
                            criteria.andTimeHourEqualTo(st);
                            networkUsageDeviceCountData = this.gatewayNetworkFlowMapper.selectDeviceHourCountByExample(example);
                        } else {
                            networkUsageDeviceCountData = this.gatewayNetworkFlowMapper.selectDeviceCountByExample(example);
                        }
                    } else if ("2".equals(timeType)) {
                        //天数据
                        if (st != null) {
                            criteria.andGdateEqualTo(st);
                        } else {
                            criteria.andGdateGreaterThanOrEqualTo(startTime);
                            criteria.andGdateLessThanOrEqualTo(endTime);
                        }
                        networkUsageDeviceCountData = this.gatewayNetworkFlowMapper.selectDeviceCountByExample(example);
                    } else if ("3".equals(timeType)) {
                        //5天数据
                        if (st != null) {
                            DateTimeFormatter dfYYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");
                            LocalDate startDateLocal = LocalDate.parse(String.valueOf(st), dfYYYYMMDD);
                            LocalDate endDateLocal = startDateLocal.plusDays(4);
                            criteria.andGdateGreaterThanOrEqualTo(Long.valueOf(startDateLocal.format(dfYYYYMMDD)));
                            criteria.andGdateLessThanOrEqualTo(Long.valueOf(endDateLocal.format(dfYYYYMMDD)));
                        } else {
                            criteria.andGdateGreaterThanOrEqualTo(startTime);
                            criteria.andGdateLessThanOrEqualTo(endTime);
                        }
                        networkUsageDeviceCountData = this.gatewayNetworkFlowMapper.selectDeviceCountByExample(example);

                    } else if ("4".equals(timeType)) {
                        // 1个月数据
                        if (st != null) {
                            DateTimeFormatter dfYYYYMM = DateTimeFormatter.ofPattern("yyyyMM");
                            DateTimeFormatter dfYYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");
                            LocalDate startDateLocal = LocalDate.parse(String.valueOf(st), dfYYYYMM);
                            LocalDate endDateLocal = startDateLocal.plusMonths(1).minusDays(1);
                            criteria.andGdateGreaterThanOrEqualTo(Long.valueOf(startDateLocal.format(dfYYYYMMDD)));
                            criteria.andGdateLessThanOrEqualTo(Long.valueOf(endDateLocal.format(dfYYYYMMDD)));
                        } else {
                            criteria.andGdateGreaterThanOrEqualTo(startTime);
                            criteria.andGdateLessThanOrEqualTo(endTime);
                        }
                        networkUsageDeviceCountData = this.gatewayNetworkFlowMapper.selectDeviceCountByExample(example);
                    }
                    break;
            }
            Integer normal = 0;
            Integer busyness = 0;
            Integer leisure = 0;

            if (networkUsageDeviceCountData != null) {
                for (NetworkUsageDeviceCountData networkUsageDeviceCountDatum : networkUsageDeviceCountData) {
                    Integer deviceUsage = networkUsageDeviceCountDatum.getDeviceUsage();
                    Integer cnum = networkUsageDeviceCountDatum.getCnum();
                    if (deviceUsage < networkThresholdFloor) {
                        leisure += cnum;
                    } else if (deviceUsage < networkThresholdUpper) {
                        normal += cnum;
                    } else {
                        busyness += cnum;
                    }
                }
            }

            result.setNormal(normal);
            result.setLeisure(leisure);
            result.setBusyness(busyness);
        } catch (Exception ex) {
            logger.error("用户行为分析—网络使用情况，异常={}", ex.getMessage(), ex);
        }
        logger.info("用户行为分析—网络使用情况，接口方法出参=：{}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public NetworkPerferChartData getProgramPreference(NetworkPerferRequest request) {
        // 省份
        String province = request.getProvince();
        List<String> provinceCodes = null;
        if (StringUtils.isNotBlank(province)) {
            provinceCodes = (List<String>) CollectionUtils.arrayToList(StringUtils.split(province, "\\,"));
        }

        // 地市
        String city = request.getCity();
        List<String> cityCodes = null;
        if (StringUtils.isNotBlank(city)) {
            cityCodes = (List<String>) CollectionUtils.arrayToList(StringUtils.split(city, "\\,"));
        }

        // yyyyMMdd
        Long startTime = Long.parseLong(DateUtil.formatInputs(request.getStartTime()));

        Long endTime = Long.parseLong(DateUtil.formatInputs(request.getEndTime()));

        String type = request.getViewingMode();

        List<NetworkPerferCount> networkPerferCounts =
                this.mediaNetworkVisitCountMapper.selectCount(provinceCodes, cityCodes, startTime, endTime, type);
        if (networkPerferCounts == null) {
            return null;
        }
        NetworkPerferChartData result = new NetworkPerferChartData();
        List<NetworkRankData> listRank = new ArrayList<>();
        List<String> xAxis = new ArrayList<>();
        List<String> data = new ArrayList<>();
        long total = networkPerferCounts.stream()
                .map(NetworkPerferCount::getVisitDuration)
                .mapToLong(value -> value)
                .sum();
        double totalPercent = 0d;
        for (NetworkPerferCount networkPerferCount : networkPerferCounts) {
            xAxis.add(networkPerferCount.getName());
            Long timeMi = networkPerferCount.getVisitDuration();
            double timeHour = MyNumUtils.decimalTwo((double) networkPerferCount.getVisitDuration() / 60D);
            data.add(String.valueOf(timeHour));

            NetworkRankData networkRankData = new NetworkRankData();
            networkRankData.setName(networkPerferCount.getName());
            networkRankData.setValue(String.valueOf(timeHour));
            if (total != 0) {
                double percent = (double) timeMi * 100D / (double) total;
                if (totalPercent + percent > 100L) {
                    percent = 100L - totalPercent;
                }

                networkRankData.setPercent(String.valueOf(MyNumUtils.decimalTwo(percent)));
                totalPercent += percent;
            } else {
                networkRankData.setPercent("0");
            }
            listRank.add(networkRankData);
        }

        result.setData(data);
        result.setListRank(listRank);
        result.setXAxis(xAxis);
        return result;
    }

    @Override
    public NetworkPerferChartData getSitePreference(NetworkPerferRequest request) {
        // 省份
        String province = request.getProvince();
        List<String> provinceCodes = null;
        if (StringUtils.isNotBlank(province)) {
            provinceCodes = (List<String>) CollectionUtils.arrayToList(StringUtils.split(province, "\\,"));
        }

        // 地市
        String city = request.getCity();
        List<String> cityCodes = null;
        if (StringUtils.isNotBlank(city)) {
            cityCodes = (List<String>) CollectionUtils.arrayToList(StringUtils.split(city, "\\,"));
        }

        // yyyyMMdd
        Long startTime = Long.parseLong(DateUtil.formatInputs(request.getStartTime()));

        Long endTime = Long.parseLong(DateUtil.formatInputs(request.getEndTime()));

        List<NetworkPerferCount> networkPerferCounts =
                this.hguNetworkVisitDetailMapper.selectCount(provinceCodes, cityCodes, startTime, endTime);
        if (networkPerferCounts == null) {
            return null;
        }
        NetworkPerferChartData result = new NetworkPerferChartData();
        List<NetworkRankData> listRank = new ArrayList<>();
        List<String> xAxis = new ArrayList<>();
        List<String> data = new ArrayList<>();
        long total = networkPerferCounts.stream()
                .map(NetworkPerferCount::getVisitDuration)
                .mapToLong(value -> value)
                .sum();
        double totalPercent = 0d;
        for (NetworkPerferCount networkPerferCount : networkPerferCounts) {
            xAxis.add(networkPerferCount.getName());
            Long timeMi = networkPerferCount.getVisitDuration();
            double timeHour = MyNumUtils.decimalTwo((double) networkPerferCount.getVisitDuration() / 60D);
            data.add(String.valueOf(timeHour));

            NetworkRankData networkRankData = new NetworkRankData();
            networkRankData.setName(networkPerferCount.getName());
            networkRankData.setValue(String.valueOf(timeHour));
            if (total != 0) {
                double percent = (double) timeMi * 100D / (double) total;
                if (totalPercent + percent > 100L) {
                    percent = 100L - totalPercent;
                }
                networkRankData.setPercent(String.valueOf(MyNumUtils.decimalTwo(percent)));
                totalPercent += percent;
            } else {
                networkRankData.setPercent("0");
            }
            listRank.add(networkRankData);
        }

        result.setData(data);
        result.setListRank(listRank);
        result.setXAxis(xAxis);
        return result;
    }

    private NetworkUsageChartData networkUsageSta(List<NetworkUsageStaData> networkUsageStaDataList,
                                                  List<String> xAxisList,
                                                  int flag) {
        // (provCode,名称)
        Map<String, String> provMap = this.dicDataService.getDicAreaMap();
        // (cityCode,名称)
        Map<String, String> cityMap = this.dicDataService.getDicCityMap();

        NetworkUsageChartData result = new NetworkUsageChartData();
        List<String> xAxisListFinal = new ArrayList<>();
        List<NetworkUsageRatioData> useBroadbandRatioData = new ArrayList<>();
        List<Double> actualNetworkTrafficData = new ArrayList<>();

        Map<String, NetworkUsageStaData> networkUsageStaDataMap = new HashMap<>();
        for (NetworkUsageStaData networkUsageStaData : networkUsageStaDataList) {
            networkUsageStaDataMap.put(networkUsageStaData.getName(), networkUsageStaData);
        }

        for (String xName : xAxisList) {
            String finalName = null;
            switch (flag) {
                // 省份(code->名称)
                case 1:
                    finalName = provMap.getOrDefault(xName, "其他");
                    break;
                // 地市(code->名称)
                case 11:
                    finalName = cityMap.getOrDefault(xName, "其他");
                    break;
                // 行业
                case 2:
                    finalName = xName;
                    break;
                // 时间
                case 3:
                    finalName = xName;
                    break;
            }
            xAxisListFinal.add(finalName);

            if (networkUsageStaDataMap.containsKey(xName)) {
                NetworkUsageStaData networkUsageStaData = networkUsageStaDataMap.get(xName);
                Double totalUsage = networkUsageStaData.getTotalUsage();
                Double totalBandwidth = networkUsageStaData.getTotalBandwidth();

                int status;
                if (totalBandwidth != 0D) {
                    double percent = totalUsage * 100 / totalBandwidth;
                    if (percent > 100d) {
                        percent = 100d;
                    }
                    if (percent < networkThresholdFloor) {
                        status = STATUS_FREE;
                    } else if (percent < networkThresholdUpper) {
                        status = STATUS_NORMAL;
                    } else {
                        status = STATUS_BUSY;
                    }
                    useBroadbandRatioData.add(new NetworkUsageRatioData(MyNumUtils.decimalTwo(percent), status));
                } else {
                    useBroadbandRatioData.add(new NetworkUsageRatioData(0.0D, STATUS_FREE));
                }

                actualNetworkTrafficData.add(MyNumUtils.decimalTwo(totalUsage));
                networkUsageStaDataMap.remove(xName);
            } else {
                useBroadbandRatioData.add(new NetworkUsageRatioData(0.0D, 0));
                actualNetworkTrafficData.add(0D);
            }
        }

        result.setActualNetworkTrafficData(actualNetworkTrafficData);
        result.setUseBroadbandRatioData(useBroadbandRatioData);
        result.setxAxis(xAxisListFinal);
        return result;
    }



}
