package com.cmiot.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cmiot.report.common.ComUtils;
import com.cmiot.report.dto.enterprise.*;
import com.cmiot.report.facade.enterprise.*;
import com.cmiot.report.mapper.EnterpriseGatewayCountMapper;
import com.cmiot.report.service.EnterpriseDeviceStatisticService;
import com.cmiot.report.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;

/**
 * @Classname EnterpriseDeviceStatisticServiceImpl
 * @Description
 * @Date 2023/8/1 9:22
 * @Created by lei
 */
@Service
public class EnterpriseDeviceStatisticServiceImpl implements EnterpriseDeviceStatisticService {

    private final static Logger logger = LoggerFactory.getLogger(EnterpriseDeviceStatisticServiceImpl.class);

    @Autowired
    private EnterpriseGatewayCountMapper enterpriseGatewayCountMapper;

    @Override
    public DeviceOverviewResult getDeviceOverview(Long eid) {
        String day = DateUtil.getYesterday();
        String weekDay = DateUtil.getLastWeekDay();
        String month = DateUtil.getLastMonth();
        DeviceOverviewResult deviceOverviewResult = new DeviceOverviewResult();
        if (eid == null) {
            return null;
        }
        //查询占比
        DeviceOverviewRateInfo deviceOverviewRateInfo = new DeviceOverviewRateInfo();
        String deviceName = "网关";
        EnterpriseGatewayCountInfo enterpriseGatewayCountInfo =
                this.enterpriseGatewayCountMapper.getDeviceOverviewDay(eid, day);
        if (enterpriseGatewayCountInfo != null) {
            deviceOverviewResult.setAllNum(enterpriseGatewayCountInfo.getAllNum());
            deviceOverviewResult.setDayNum(enterpriseGatewayCountInfo.getDayNum());

            deviceOverviewRateInfo.setName(deviceName);
            deviceOverviewRateInfo.setDeviceNum(enterpriseGatewayCountInfo.getAllNum());
        } else {
            deviceOverviewResult.setAllNum(0);
            deviceOverviewResult.setDayNum(0);
            deviceOverviewRateInfo.setName(deviceName);
            deviceOverviewRateInfo.setDeviceNum(0);
        }

        EnterpriseGatewayCountInfo enterpriseGatewayCountInfoWeek =
                this.enterpriseGatewayCountMapper.getDeviceOverviewWeek(eid, weekDay);
        if (enterpriseGatewayCountInfoWeek != null) {
            deviceOverviewResult.setWeekNum(enterpriseGatewayCountInfoWeek.getWeekNum());
        } else {
            deviceOverviewResult.setWeekNum(0);
        }

        EnterpriseGatewayCountInfo enterpriseGatewayCountInfoMonth =
                this.enterpriseGatewayCountMapper.getDeviceOverviewMonth(eid, month);

        if (enterpriseGatewayCountInfoMonth != null) {
            deviceOverviewResult.setMonthNum(enterpriseGatewayCountInfoMonth.getMonthNum());
        } else {
            deviceOverviewResult.setMonthNum(0);
        }

        List<DeviceOverviewRateInfo> deviceRate = new ArrayList<>();
        deviceRate.add(deviceOverviewRateInfo);
        deviceOverviewResult.setDeviceRate(deviceRate);

        return deviceOverviewResult;
    }

/*    public static void main(String[] args) throws ParseException {
        String monthEnd = DateUtil.getLastMonth();
        String monthStart = null;
        try {
            monthStart = DateUtil.getOffsetMonth(monthEnd, -11);
        } catch (ParseException e) {
        }
        System.out.println(monthStart);
        System.out.println(monthEnd);

        System.out.println("###########");
        for (int i = 0; i < 12; i++) {
            String month = "";
            month = DateUtil.getOffsetMonth(monthStart, i);
            System.out.println(month);
        }
    }*/

    @Override
    public List<DeviceGraphDataResult> getDeviceGraphData(Long eid) {
        if (eid == null) {
            return new ArrayList<>();
        }
        String monthEnd = DateUtil.getLastMonth();
        String monthStart = null;
        try {
            monthStart = DateUtil.getOffsetMonth(monthEnd, -11);
        } catch (ParseException e) {
            logger.error("获取时间错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
        List<DeviceMonthActiveInfo> deviceMonthActiveInfos =
                this.enterpriseGatewayCountMapper.getDeviceMonthActiveRecord(eid, monthStart, monthEnd);
        List<DeviceGraphDataResult> results = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            String month = "";
            try {
                month = DateUtil.getOffsetMonth(monthStart, i);

                DeviceGraphDataResult deviceGraphDataResult = new DeviceGraphDataResult();

                String timeDisplay = DateUtil.formatInputMonth(month);

                if (timeDisplay != null) {
                    deviceGraphDataResult.setTime(timeDisplay);
                } else {
                    deviceGraphDataResult.setTime(month);
                }
                deviceGraphDataResult.setAllDeviceNum(0);
                deviceGraphDataResult.setOnlineDeviceNum(0);
                for (DeviceMonthActiveInfo deviceMonthActiveInfo : deviceMonthActiveInfos) {
                    if (Objects.equals(month, deviceMonthActiveInfo.getTime())) {
                        deviceGraphDataResult.setAllDeviceNum(deviceMonthActiveInfo.getAllDeviceNum());
                        deviceGraphDataResult.setOnlineDeviceNum(deviceMonthActiveInfo.getOnlineDeviceNum());
                    }
                }
                results.add(deviceGraphDataResult);
            } catch (ParseException e) {
                logger.error("获取时间错误: {}", e.getMessage(), e);
            }
        }
        return results;
    }

    public static void main(String[] args) throws ParseException {
/*        String endDate = DateUtil.getYesterday();
        String startDate = DateUtil.format(DateUtil.getBeforeDayDate(30), DateUtil.MONTH_DATE_PATTERN);
        System.out.println(endDate);
        System.out.println(startDate);
        System.out.println("####");
        for (int i = 30; i > 0; i--) {
            String day1 = DateUtil.getBeforeDay(i, DateUtil.MONTH_DATE_PATTERN);
            System.out.println(day1);
        }*/

/*        String endDate = DateUtil.getLastWeekDay();
        String startDate = DateUtil.getBeforeWeekSunday(12);
        System.out.println(endDate);
        System.out.println(startDate);
        System.out.println("####");
        for (int i = 12; i > 0; i--) {
            String day1 = DateUtil.getBeforeWeekSunday(i);
            System.out.println(day1);
        }*/

        String endDate = DateUtil.getLastMonth();
        String startDate = "";
        try {
            startDate = DateUtil.getOffsetMonth(endDate, -11);
        } catch (Exception e) {
            startDate = endDate;
        }
        System.out.println(endDate);
        System.out.println(startDate);
        System.out.println("####");
        for (int i = 11; i >= 0; i--) {
            String day1 = DateUtil.getOffsetMonth(endDate, -i);
            System.out.println(day1);
        }
    }

    @Override
    public SubDeivceGraphDataResult getStaDeivceGraphData(Long eid, Integer dateType) {
        if (eid == null) {
            return null;
        }
        SubDeivceGraphDataResult subDeivceGraphDataResult = new SubDeivceGraphDataResult();
        if (dateType == 0) {
            //t_device_enterprise_count_day_all查询最近30天
            String endDate = DateUtil.getYesterday();
            String startDate = DateUtil.format(DateUtil.getBeforeDayDate(30), DateUtil.MONTH_DATE_PATTERN);
            //填充空数据
            List<SubDeivceGraphDataResultData> data = new ArrayList<>();
            for (int i = 30; i > 0; i--) {
                String day = DateUtil.getBeforeDay(i, DateUtil.DATE_PATTERN);
                SubDeivceGraphDataResultData subDeivceGraphDataResultData = new SubDeivceGraphDataResultData();
                subDeivceGraphDataResultData.setDateTime(day);
                subDeivceGraphDataResultData.setAllStaDevice(0L);
                subDeivceGraphDataResultData.setWireStaDevice(0L);
                subDeivceGraphDataResultData.setWirelessStaDevice(0L);
                data.add(subDeivceGraphDataResultData);
            }
            List<DeviceEnterpriseCountInfo> deviceEnterpriseCountInfos =
                    this.enterpriseGatewayCountMapper.getSubDeviceDayCount(eid, startDate, endDate);

            if (deviceEnterpriseCountInfos == null || deviceEnterpriseCountInfos.isEmpty()) {
                subDeivceGraphDataResult.setData(data);
                subDeivceGraphDataResult.setDataRate(new ArrayList<>());
            } else {
                for (DeviceEnterpriseCountInfo deviceEnterpriseCountInfo : deviceEnterpriseCountInfos) {
                    SubDeivceGraphDataResultData subInfo = null;

                    String timeDisplay = DateUtil.formatInput(deviceEnterpriseCountInfo.getPdate());
                    for (SubDeivceGraphDataResultData datum : data) {
                        String dateTimeStr = datum.getDateTime();
                        if (dateTimeStr.equalsIgnoreCase(timeDisplay)) {
                            subInfo = datum;
                            break;
                        }
                    }
                    if (subInfo != null) {
                        subInfo.setAllStaDevice(deviceEnterpriseCountInfo.getCountNum());
                        subInfo.setWireStaDevice(deviceEnterpriseCountInfo.getWireNum());
                        subInfo.setWirelessStaDevice(deviceEnterpriseCountInfo.getWirelessNum());
                    }
                }


                List<SubDeivceGraphDataResultDataRate> dataRate = new ArrayList<>();
                DeviceEnterpriseCountInfo lastDayInfo = CollectionUtil.getLast(deviceEnterpriseCountInfos);
                SubDeivceGraphDataResultDataRate dataRateInfoWire = new SubDeivceGraphDataResultDataRate();
                dataRateInfoWire.setNum(lastDayInfo.getWireNum());
                String dataName = "有线";
                dataRateInfoWire.setName(dataName);
                dataRate.add(dataRateInfoWire);

                SubDeivceGraphDataResultDataRate dataRateInfoWireless = new SubDeivceGraphDataResultDataRate();
                dataRateInfoWireless.setNum(lastDayInfo.getWirelessNum());
                String dataNameWireless = "无线";
                dataRateInfoWireless.setName(dataNameWireless);
                dataRate.add(dataRateInfoWireless);

                subDeivceGraphDataResult.setDataRate(dataRate);
                subDeivceGraphDataResult.setData(data);
            }
        } else if (dateType == 1) {
            //t_device_enterprise_count_week_all查询最近12周
            String endDate = DateUtil.getLastWeekDay();
            String startDate = DateUtil.getBeforeWeekSunday(12);

            List<SubDeivceGraphDataResultData> data = new ArrayList<>();
            for (int i = 12; i > 0; i--) {
                String day = DateUtil.getBeforeWeekSunday(i);
                SubDeivceGraphDataResultData subDeivceGraphDataResultData = new SubDeivceGraphDataResultData();
                subDeivceGraphDataResultData.setDateTime(day);
                subDeivceGraphDataResultData.setAllStaDevice(0L);
                subDeivceGraphDataResultData.setWireStaDevice(0L);
                subDeivceGraphDataResultData.setWirelessStaDevice(0L);
                data.add(subDeivceGraphDataResultData);
            }


            List<DeviceEnterpriseCountInfo> deviceEnterpriseCountInfos =
                    this.enterpriseGatewayCountMapper.getSubDeviceWeekCount(eid, startDate, endDate);
            if (deviceEnterpriseCountInfos == null || deviceEnterpriseCountInfos.isEmpty()) {
                subDeivceGraphDataResult.setData(data);
                subDeivceGraphDataResult.setDataRate(new ArrayList<>());
            } else {
                List<SubDeivceGraphDataResultDataRate> dataRate = new ArrayList<>();
                DeviceEnterpriseCountInfo lastDayInfo = CollectionUtil.getLast(deviceEnterpriseCountInfos);
                SubDeivceGraphDataResultDataRate dataRateInfoWire = new SubDeivceGraphDataResultDataRate();
                dataRateInfoWire.setNum(lastDayInfo.getWireNum());
                String dataName = "有线";
                dataRateInfoWire.setName(dataName);
                dataRate.add(dataRateInfoWire);

                SubDeivceGraphDataResultDataRate dataRateInfoWireless = new SubDeivceGraphDataResultDataRate();
                dataRateInfoWireless.setNum(lastDayInfo.getWirelessNum());
                String dataNameWireless = "无线";
                dataRateInfoWireless.setName(dataNameWireless);
                dataRate.add(dataRateInfoWireless);

                for (DeviceEnterpriseCountInfo deviceEnterpriseCountInfo : deviceEnterpriseCountInfos) {
                    SubDeivceGraphDataResultData subInfo = null;
                    String pdata = deviceEnterpriseCountInfo.getPdate();
                    for (SubDeivceGraphDataResultData datum : data) {
                        String dateTimeStr = datum.getDateTime();
                        if (dateTimeStr.equalsIgnoreCase(pdata)) {
                            subInfo = datum;
                            break;
                        }
                    }
                    if (subInfo != null) {
                        subInfo.setAllStaDevice(deviceEnterpriseCountInfo.getCountNum());
                        subInfo.setWireStaDevice(deviceEnterpriseCountInfo.getWireNum());
                        subInfo.setWirelessStaDevice(deviceEnterpriseCountInfo.getWirelessNum());
                    }
                }
                for (SubDeivceGraphDataResultData datum : data) {
                    String timeDisplay = DateUtil.formatInput(datum.getDateTime());
                    if (timeDisplay != null) {
                        datum.setDateTime(timeDisplay);
                    }
                }

                subDeivceGraphDataResult.setDataRate(dataRate);
                subDeivceGraphDataResult.setData(data);
            }
        } else if (dateType == 2) {
            //t_device_enterprise_count_month_all查询最近12月
            String endDate = DateUtil.getLastMonth();
            String startDate = "";
            try {
                startDate = DateUtil.getOffsetMonth(endDate, -11);
            } catch (Exception e) {
                startDate = endDate;
            }
            List<SubDeivceGraphDataResultData> data = new ArrayList<>();
            for (int i = 11; i >= 0; i--) {
                try {
                    String day = DateUtil.getOffsetMonth(endDate, -i);
                    SubDeivceGraphDataResultData subDeivceGraphDataResultData = new SubDeivceGraphDataResultData();
                    subDeivceGraphDataResultData.setDateTime(day);
                    subDeivceGraphDataResultData.setAllStaDevice(0L);
                    subDeivceGraphDataResultData.setWireStaDevice(0L);
                    subDeivceGraphDataResultData.setWirelessStaDevice(0L);
                    data.add(subDeivceGraphDataResultData);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
            List<DeviceEnterpriseCountInfo> deviceEnterpriseCountInfos =
                    this.enterpriseGatewayCountMapper.getSubDeviceMonthCount(eid, startDate, endDate);
            if (deviceEnterpriseCountInfos == null || deviceEnterpriseCountInfos.isEmpty()) {
                subDeivceGraphDataResult.setData(new ArrayList<>());
                subDeivceGraphDataResult.setDataRate(new ArrayList<>());
            } else {
                List<SubDeivceGraphDataResultDataRate> dataRate = new ArrayList<>();
                DeviceEnterpriseCountInfo lastDayInfo = CollectionUtil.getLast(deviceEnterpriseCountInfos);
                SubDeivceGraphDataResultDataRate dataRateInfoWire = new SubDeivceGraphDataResultDataRate();
                dataRateInfoWire.setNum(lastDayInfo.getWireNum());
                String dataName = "有线";
                dataRateInfoWire.setName(dataName);
                dataRate.add(dataRateInfoWire);

                SubDeivceGraphDataResultDataRate dataRateInfoWireless = new SubDeivceGraphDataResultDataRate();
                dataRateInfoWireless.setNum(lastDayInfo.getWirelessNum());
                String dataNameWireless = "无线";
                dataRateInfoWireless.setName(dataNameWireless);
                dataRate.add(dataRateInfoWireless);

                for (DeviceEnterpriseCountInfo deviceEnterpriseCountInfo : deviceEnterpriseCountInfos) {
                    SubDeivceGraphDataResultData subInfo = null;
                    String pdata = deviceEnterpriseCountInfo.getPdate();
                    for (SubDeivceGraphDataResultData datum : data) {
                        String dateTimeStr = datum.getDateTime();
                        if (dateTimeStr.equalsIgnoreCase(pdata)) {
                            subInfo = datum;
                            break;
                        }
                    }
                    if (subInfo != null) {
                        subInfo.setAllStaDevice(deviceEnterpriseCountInfo.getCountNum());
                        subInfo.setWireStaDevice(deviceEnterpriseCountInfo.getWireNum());
                        subInfo.setWirelessStaDevice(deviceEnterpriseCountInfo.getWirelessNum());
                    }
                }
                for (SubDeivceGraphDataResultData datum : data) {
                    String timeDisplay = DateUtil.formatInputMonth(datum.getDateTime());
                    if (timeDisplay != null) {
                        datum.setDateTime(timeDisplay);
                    }
                }
                subDeivceGraphDataResult.setDataRate(dataRate);
                subDeivceGraphDataResult.setData(data);
            }
        } else {
            subDeivceGraphDataResult.setData(new ArrayList<>());
            subDeivceGraphDataResult.setDataRate(new ArrayList<>());

        }
        return subDeivceGraphDataResult;
    }

    @Override
    public DeviceFlowDataResult deviceFlow(Long eid, String startTime, String endTime) {
        if (eid == null) {
            return null;
        }
        DeviceFlowDataResult deviceFlowDataResult = new DeviceFlowDataResult();

        //补齐日期
        List<String> days = ComUtils.getDays(startTime, endTime);
        List<String> time = new ArrayList<>();
        List<String> maxTxrate = new ArrayList<>();
        List<String> maxRxrate = new ArrayList<>();
        List<String> averTxrate = new ArrayList<>();
        List<String> averRxrate = new ArrayList<>();

        String startDate = DateUtil.formatToyyyyMMdd(startTime);
        String endDate = DateUtil.formatToyyyyMMdd(endTime);
        //t_gateway_enterprise_net_rate_day_all
        List<GatewayEnterpriseUseCountInfo> gatewayEnterpriseUseCountInfos =
                this.enterpriseGatewayCountMapper.getEnterpriseDeviceUseCount(eid, startDate, endDate);
        Map<String, String> maxTxrateMap = new HashMap<>();
        Map<String, String> maxRxrateMap = new HashMap<>();
        Map<String, String> averTxrateMap = new HashMap<>();
        Map<String, String> averRxrateMap = new HashMap<>();
        if (gatewayEnterpriseUseCountInfos != null && !gatewayEnterpriseUseCountInfos.isEmpty()) {
            for (GatewayEnterpriseUseCountInfo data : gatewayEnterpriseUseCountInfos) {
                String timeDisplay = DateUtil.formatInput(data.getPdate());
                if (timeDisplay == null) {
                    timeDisplay = data.getPdate();
                }
                maxTxrateMap.put(timeDisplay, data.getMaxTxrate());
                maxRxrateMap.put(timeDisplay, data.getMaxRxrate());
                averTxrateMap.put(timeDisplay, data.getAverTxrate());
                averRxrateMap.put(timeDisplay, data.getAverRxrate());
            }
        }

        for (String day : days) {
            time.add(day);
            if (maxTxrateMap.containsKey(day)) {
                maxTxrate.add(maxTxrateMap.get(day));
            } else {
                maxTxrate.add("0");
            }
            if (maxRxrateMap.containsKey(day)) {
                maxRxrate.add(maxRxrateMap.get(day));
            } else {
                maxRxrate.add("0");
            }
            if (averTxrateMap.containsKey(day)) {
                averTxrate.add(averTxrateMap.get(day));
            } else {
                averTxrate.add("0");
            }
            if (averRxrateMap.containsKey(day)) {
                averRxrate.add(averRxrateMap.get(day));
            } else {
                averRxrate.add("0");
            }
        }


        deviceFlowDataResult.setTime(time);
        deviceFlowDataResult.setMaxRxrate(maxTxrate);
        deviceFlowDataResult.setMaxTxrate(maxRxrate);
        deviceFlowDataResult.setAverTxrate(averTxrate);
        deviceFlowDataResult.setAverRxrate(averRxrate);


        return deviceFlowDataResult;
    }

    @Override
    public DeviceCpuRamDataResult getCpuRam(Long eid, String startTime, String endTime) {
        if (eid == null) {
            return null;
        }
        DeviceCpuRamDataResult result = new DeviceCpuRamDataResult();
        //补齐日期
        List<String> days = ComUtils.getDays(startTime, endTime);
        List<String> time = new ArrayList<>();
        List<String> cpuAverage = new ArrayList<>();
        List<String> ramAverage = new ArrayList<>();

        String startDate = DateUtil.formatToyyyyMMdd(startTime);
        String endDate = DateUtil.formatToyyyyMMdd(endTime);
        //t_gateway_enterprise_net_rate_day_all
        List<GatewayEnterpriseUseCountInfo> gatewayEnterpriseUseCountInfos =
                this.enterpriseGatewayCountMapper.getEnterpriseDeviceUseCount(eid, startDate, endDate);
        Map<String, String> cpuAverageMap = new HashMap<>();
        Map<String, String> ramAverageMap = new HashMap<>();
        if (gatewayEnterpriseUseCountInfos != null && !gatewayEnterpriseUseCountInfos.isEmpty()) {
            for (GatewayEnterpriseUseCountInfo data : gatewayEnterpriseUseCountInfos) {
                String timeDisplay = DateUtil.formatInput(data.getPdate());
                if (timeDisplay == null) {
                    timeDisplay = data.getPdate();
                }
                cpuAverageMap.put(timeDisplay, data.getCpuAverRate());
                ramAverageMap.put(timeDisplay, data.getRamAverRate());
            }
        }

        for (String day : days) {
            time.add(day);
            if (cpuAverageMap.containsKey(day)) {
                cpuAverage.add(cpuAverageMap.get(day));
            } else {
                cpuAverage.add("0");
            }
            if (ramAverageMap.containsKey(day)) {
                ramAverage.add(ramAverageMap.get(day));
            } else {
                ramAverage.add("0");
            }
        }


        result.setTime(time);
        result.setCpuAverage(cpuAverage);
        result.setRamAverage(ramAverage);

        return result;
    }
}
