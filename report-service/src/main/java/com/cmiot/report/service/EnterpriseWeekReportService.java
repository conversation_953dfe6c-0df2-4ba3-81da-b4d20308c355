package com.cmiot.report.service;

import com.cmiot.report.facade.dto.enterprise.WeekReportOverviewResult;
import com.cmiot.report.facade.dto.enterprise.WeekReportSubDeviceResult;
import com.cmiot.report.facade.dto.enterprise.WeekReportVisitsResult;

import java.util.Map;

/**
 * @Classname EnterpriseWeekReportService
 * @Description
 * @Date 2023/12/20 8:52
 * @Created by lei
 */
public interface EnterpriseWeekReportService {
    WeekReportOverviewResult getWeekReportOverview(Long eid);

    WeekReportVisitsResult getWeekReportVisits(Long eid);

    WeekReportSubDeviceResult getWeekReportSubDevice(Long eid);

    Map<String, Object> getWeekReportGatewayRunTimes(Long eid);
}
