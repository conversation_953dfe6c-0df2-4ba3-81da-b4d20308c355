package com.cmiot.report.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.LongSummaryStatistics;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.report.InitSystemConfig;
import com.cmiot.report.bean.DicFactory;
import com.cmiot.report.bean.DicFactoryExample;
import com.cmiot.report.bean.GatewayPeriodAggrAll;
import com.cmiot.report.bean.GatewayPeriodAggrAllExample;
import com.cmiot.report.bean.GatewayPeriodAll;
import com.cmiot.report.bean.GatewayPeriodAllExample;
import com.cmiot.report.bean.GatewayPppoeCountAll;
import com.cmiot.report.bean.GatewayPppoeCountAllExample;
import com.cmiot.report.dto.pppoe.PppoeFailListRequest;
import com.cmiot.report.dto.pppoe.PppoeFailListResult;
import com.cmiot.report.dto.pppoe.PppoeFailListResult.Item;
import com.cmiot.report.dto.pppoe.PppoeListRequest;
import com.cmiot.report.dto.pppoe.PppoeListResult;
import com.cmiot.report.dto.pppoe.PppoeListResult.PppoeGateway;
import com.cmiot.report.dto.pppoe.PppoeStatisticResult;
import com.cmiot.report.dto.pppoe.PppoeStatisticResult.Data;
import com.cmiot.report.mapper.CustomerAllMapper;
import com.cmiot.report.mapper.DicFactoryMapper;
import com.cmiot.report.mapper.GatewayPeriodAggrAllMapper;
import com.cmiot.report.mapper.GatewayPeriodAllMapper;
import com.cmiot.report.mapper.GatewayPppoeCountAllMapper;
import com.cmiot.report.service.ExportService;
import com.cmiot.report.service.GatewayPppoeService;
import com.cmiot.report.thread.task.PppoeExportTask;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.MyNumUtils;
import com.cmiot.report.util.TimerUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GatewayPppoeServiceImpl implements GatewayPppoeService {
	/**
	 * 单线程池。用于文件导出。
	 */
	public static final ExecutorService SINGLE_THREAD_EXECUTOR = Executors.newSingleThreadExecutor();
	
	@Autowired
	private GatewayPppoeCountAllMapper gatewayPppoeCountAllMapper;
	
	/**
	 * 获取网关厂商字典信息。
	 */
	@Autowired
	private DicFactoryMapper dicFactoryMapper;
	
	/**
	 * 当天活跃网关明细
	 */
	@Autowired
	private GatewayPeriodAggrAllMapper gatewayPeriodAggrAllMapper;
	
	/**
	 * 周期上报明细数据
	 */
	@Autowired
	private GatewayPeriodAllMapper gatewayPeriodAllMapper;
	
	/**
	 * 导出工具。
	 */
	@Autowired
	private ExportService exportService;
	
	/**
	 * 企业信息查询。
	 */
	@Autowired
	private CustomerAllMapper customerAllMapper;
	
	/**
	 * 临时目录
	 */
	@Value("${export.tmp.local}")
	private String tmpPath;
	
	/**
	 * 1 使用内网地址，否则使用外网地址
	 */
	@Value("${export.use-pri-url}")
	private Integer usePriUrl;
	
	@Override
	public List<Data> getDataList(
			String type,
			String province, 
			String city, 
			String vendor, 
			String startTime,
			String endTime) {
		log.info("getDataListByVendor province : {}, city : {}, vendor : {}, startTime : {}, endTime : {}", 
				province, city, vendor, startTime, endTime);
		
		// 省份为空，或者省份根据逗号分隔为多个
		List<String> pList = toList(province);
		// 地市编码
		List<String> cityList = toList(city);
		// 厂商ID
		List<String> vendorIdList = toList(vendor);
		
		// 查询厂商基本信息
		List<Long> vendorIdLongList = vendorIdList.stream()
				.mapToLong(str -> NumberUtils.toLong(str, 0))
				.filter(num -> num != 0)
				.boxed()
				.collect(Collectors.toList());
		DicFactoryExample exampleFactory = new DicFactoryExample();
		DicFactoryExample.Criteria criteriaFactory = exampleFactory.createCriteria();
		// 如果输入厂商编码为空，查询所有厂商信息
		if(!vendorIdLongList.isEmpty()) criteriaFactory.andFactoryIdIn(vendorIdLongList);
		List<DicFactory> dicFactList = dicFactoryMapper.selectByExample(exampleFactory);
		// 厂商code列表
		List<String> vNameList = dicFactList.stream()
				.map(dicF -> dicF.getFactoryCode())
				.collect(Collectors.toList());
		
		// 根据条件查询数据
		GatewayPppoeCountAllExample example = new GatewayPppoeCountAllExample();
		GatewayPppoeCountAllExample.Criteria criteria = example.createCriteria();
		// 指定多个省份
		if(pList.size() > 1) {
			criteria.andProvinceCodeIn(pList);
		} 
		// 如果省份只有一个，同时启动地市信息；多个省份不使用地市信息。
		else if(pList.size() == 1) {
			criteria.andProvinceCodeIn(pList);
			if(cityList.size() > 0) criteria.andCityCodeIn(cityList);
		}
		// 起始结束时间
		if(startTime != null) criteria.andPdateGreaterThanOrEqualTo(NumberUtils.toLong(startTime.replaceAll("-", "")));
		if(endTime != null) criteria.andPdateLessThanOrEqualTo(NumberUtils.toLong(endTime.replaceAll("-", "")));
		// 指定的厂商编码不为空的时候，设置对应的厂商code；为空查询全部。
		if(!vendorIdLongList.isEmpty()) criteria.andGatewayVendorIn(vNameList);
		
		// 目标数据
		List<GatewayPppoeCountAll> dataList = gatewayPppoeCountAllMapper.selectByExample(example);
		
		// 类型：区域1，厂商2
		List<Data> countDataList = null;
		if("1".equalsIgnoreCase(type)) {
			// 如果只有一个省份，根据地市分组
			if(pList.size() == 1) {
				countDataList = countDataList(dataList, 1);
			}
			// 如果有多个省份或者没有指定省份
			else {
				countDataList = countDataList(dataList, 2);
			}
		} else if("2".equalsIgnoreCase(type)) {
			// 根据厂商分组
			countDataList = countDataList(dataList, 3);
		}
		// 不返回null对象
		return countDataList == null ? new ArrayList<>() : countDataList;
	}
	
	/**
	 * 数据整合
	 * ptype = 1 根据地市分组
	 * ptype = 2 根据省份分组
	 * ptype = 3 根据厂商分组
	 */
	private List<PppoeStatisticResult.Data> countDataList(List<GatewayPppoeCountAll> dataList, int ptype) {
		// 数据分组
		Map<String, List<GatewayPppoeCountAll>> dataMapBy = null;
		if(ptype == 1) {
			dataMapBy = dataList.stream()
					.collect(Collectors.groupingBy(GatewayPppoeCountAll::getCityCode));
		} else if(ptype == 2) {
			dataMapBy = dataList.stream()
					.collect(Collectors.groupingBy(GatewayPppoeCountAll::getProvinceCode));
		} else if(ptype == 3) {
			dataMapBy = dataList.stream()
					.collect(Collectors.groupingBy(GatewayPppoeCountAll::getGatewayVendor));
		}
		
		// 预先设置返回对象
		List<PppoeStatisticResult.Data> resultList = new ArrayList<>();
		if(dataMapBy == null) {
			return resultList;
		}
		
		// 遍历整体分组
		Iterator<Entry<String, List<GatewayPppoeCountAll>>> iterator = dataMapBy.entrySet().iterator();
		while(iterator.hasNext()) {
			Entry<String, List<GatewayPppoeCountAll>> next = iterator.next();
			String key = next.getKey();
			List<GatewayPppoeCountAll> value = next.getValue();
			// 当前分组总数据量
			LongSummaryStatistics citySum = value.stream()
					.mapToLong(GatewayPppoeCountAll::getPppoeerrorCount)
					.summaryStatistics();
			// 分组的总量
			long cityTotal = citySum.getSum();
			if(cityTotal != 0) {
				// 根据状态码分组统计
				Map<String, Long> collect = value.stream()
						.collect(Collectors.groupingBy(
								GatewayPppoeCountAll::getPppoeerrorStatus, 
								Collectors.summingLong(GatewayPppoeCountAll::getPppoeerrorCount)));
				
				Iterator<Entry<String, Long>> iteratorStatus = collect.entrySet().iterator();
				List<PppoeStatisticResult.Reason> rList = new ArrayList<>();
				// 分组异常量
				long error_count = 0;
				while(iteratorStatus.hasNext()) {
					Entry<String, Long> statusValue = iteratorStatus.next();
					String pppoeError = statusValue.getKey();
					if(!"ERROR_NONE".equalsIgnoreCase(pppoeError) 
							&& pppoeError != null && pppoeError.length() > 0) {
						PppoeStatisticResult.Reason reason = new PppoeStatisticResult.Reason();
						// 展示名称
						reason.setName(InitSystemConfig.PPPOE_ERROR_CODE_DESC.getOrDefault(pppoeError, pppoeError));
						reason.setValue(statusValue.getValue());
						// 比例计算，保留两位小数
						// double rate = (double)statusValue.getValue() * 100 / cityTotal; 
						reason.setRate(0d);
						rList.add(reason);
						// 总量统计
						error_count += statusValue.getValue();
					}
					// 累计异常量
					// if(!"ERROR_NONE".equalsIgnoreCase(statusValue.getKey())) error_count += statusValue.getValue();
				}
				
				PppoeStatisticResult.Data psrData = new PppoeStatisticResult.Data();
				// 获取数据名称
				if(ptype == 1 || ptype == 2) {
					psrData.setName(InitSystemConfig.AREA_CODE_NAME.getOrDefault(key, key));
				} else {
					psrData.setName(InitSystemConfig.FACTORY_CODE_NAME.getOrDefault(key, key));
				}
				// psrData.setValue(cityTotal);
				psrData.setValue(error_count);
				psrData.setRate(MyNumUtils.decimalTwo((double) error_count * 100 / cityTotal));
				psrData.setReasonList(rList);
				resultList.add(psrData);
			}
		}
		// 数组排序返回
		List<Data> collect = resultList.stream()
				.filter(x -> (x.getName() != null && x.getName().length() > 0))
				.sorted(Comparator.comparingDouble(PppoeStatisticResult.Data::getRate).reversed())
				.collect(Collectors.toList());
		return collect;
	}
	
	/**
	 * 将逗号分隔的字符串拆分为list。
	 */
	private static List<String> toList(String str) {
		Set<String> pSet = new HashSet<>();
		if(str != null) {
			String[] split = str.split(",");
			for(String p : split) {
				if(p != null && p.length() >= 1) {
					pSet.add(p);
				}
			}
		}
		return new ArrayList<>(pSet);
	}
	
	public static void main(String[] args) {
		String startTime = "2022-06-01";
		System.err.println(startTime.replaceAll("-", ""));
	}

	@Override
	public PppoeListResult pppoeList(PppoeListRequest request) {
		// 获取基础参数
		String province = request.getProvince();
		String city = request.getCity();
		String vendor = request.getVendor();
		String sn = request.getSn();
		Integer page = request.getPage();
		Integer pageSize = request.getPageSize();
		
		log.info("pppoeList province : {}, city : {}, vendor : {}, sn : {}, page : {}, pageSize : {}", 
				province, city, vendor, sn, page, pageSize);
		
		// 省份为空，或者省份根据逗号分隔为多个
		List<String> pList = toList(province);
		// 地市编码
		List<String> cityList = toList(city);
		// 厂商ID
		List<String> vendorIdList = toList(vendor);
		List<Long> vendorIDLongList = vendorIdList.stream()
				.mapToLong(str -> NumberUtils.toLong(str, 0))
				.filter(num -> num != 0)
				.boxed().collect(Collectors.toList());
		
		// 
		int pageNum = 1;
		int pageSizeNum = 10;
		if(page != null) pageNum = page.intValue();
		if(pageSize != null) pageSizeNum = pageSize.intValue();
		
		// 分页查询
		GatewayPeriodAggrAllExample example = new GatewayPeriodAggrAllExample();
		GatewayPeriodAggrAllExample.Criteria criteria = example.createCriteria();
		if(pList.size() > 0) criteria.andProvinceCodeIn(pList);
		if(cityList.size() > 0) criteria.andCityCodeIn(cityList);
		if(vendorIDLongList.size() > 0) criteria.andGatewayVendorIdIn(vendorIDLongList);
		if(sn != null && sn.length() > 1) criteria.andGatewaySnEqualTo(sn);
		// 日期查询昨天的
		Date startDate = new Date(TimerUtils.getBeginDayOfYesterday());
		Date endDate = new Date(TimerUtils.getEndDayOfYesterDay());
		criteria.andRecordTimeBetween(startDate, endDate);
        Page<GatewayPeriodAggrAll> doSelectPage = PageHelper.startPage(pageNum,pageSizeNum)
        		.doSelectPage(() -> gatewayPeriodAggrAllMapper.selectByExample(example));
		
        // 组合明细数据
        List<GatewayPeriodAggrAll> detailList = doSelectPage.getResult();
        List<PppoeGateway> pgList = detailList.stream()
        		.filter(x -> x != null)
        		.map(x -> from(x))
        		.collect(Collectors.toList());
        
        // 数据组合
        PppoeListResult result = new PppoeListResult();
        result.setPage(pageNum);
        result.setPageSize(pageSizeNum);
        result.setTotal(doSelectPage.getTotal());
        result.setList(pgList);
		return result;
	}
	
	/**
	 * 对象装换
	 */
	private PppoeListResult.PppoeGateway from(GatewayPeriodAggrAll detail) {
		String province = InitSystemConfig.AREA_CODE_NAME.getOrDefault(detail.getProvinceCode(), detail.getProvinceCode());
		String cityCode = InitSystemConfig.AREA_CODE_NAME.getOrDefault(detail.getCityCode(), detail.getCityCode());
		String gatewayVendor = InitSystemConfig.FACTORY_CODE_NAME.getOrDefault(detail.getGatewayVendor(), detail.getGatewayVendor());
		PppoeListResult.PppoeGateway ppp = new PppoeListResult.PppoeGateway();
		ppp.setId(detail.getEnterpriseId());
		ppp.setProvince(province);
		ppp.setCity(cityCode);
		ppp.setGroupName(detail.getCustomerName());
		ppp.setSn(detail.getGatewaySn());
		ppp.setMac(detail.getGatewayMac());
		ppp.setVendor(gatewayVendor);
		ppp.setModel(detail.getGatewayProductclass());
		ppp.setRunningTime(detail.getRuningTime().longValue());
		return ppp;
	}

	@Override
	public String pppoeListExport(Long uid, String province, String city, String vendor, String sn) {
		
		String exportType = "网关拨号错误码明细导出";
		String exportName = "网关拨号错误码明细导出";
		ExportInfoDto record = exportService.record(uid, exportType, exportName);
		
		// 省份为空，或者省份根据逗号分隔为多个
		List<String> pList = toList(province);
		// 地市编码
		List<String> cityList = toList(city);
		// 厂商ID
		List<String> vendorIdList = toList(vendor);
		List<Long> vendorIDLongList = vendorIdList.stream()
				.mapToLong(str -> NumberUtils.toLong(str, 0))
				.filter(num -> num != 0)
				.boxed().collect(Collectors.toList());
		
		// 导出条件查询
		GatewayPeriodAllExample example = new GatewayPeriodAllExample();
		GatewayPeriodAllExample.Criteria criteria = example.createCriteria();
		if(sn != null && sn.length() > 1) criteria.andGatewaySnEqualTo(sn);
		if(pList.size() > 0) criteria.andProvinceCodeIn(pList);
		if(cityList.size() > 0) criteria.andCityCodeIn(cityList);
		if(vendorIDLongList.size() > 0) criteria.andFactoryIdIn(vendorIDLongList);
		
		// 日期查询昨天的
		// Date startDate = new Date(TimerUtils.getBeginDayOfYesterday());
		// Date endDate = new Date(TimerUtils.getEndDayOfYesterDay());
		// criteria.andSampleTimeBetween(startDate, endDate);
		// 排序设置
		criteria.andPppoeErrorNotEqualTo("ERROR_NONE");
		example.setOrderByClause(" sample_time desc ");
		
		// 启动异步导出线程
		SINGLE_THREAD_EXECUTOR.submit(new PppoeExportTask(customerAllMapper, gatewayPeriodAllMapper, exportService, record, example, tmpPath, usePriUrl));
		
		return record.getExportName();
	}

	@Override
	public PppoeFailListResult pppoeFailList(PppoeFailListRequest request) {
		// 基本参数
		String sn = request.getSn();
		Integer page = request.getPage();
		Integer pageSize = request.getPageSize();
		
		// 分页初始化
		int pageNum = 1;
		int pageSizeNum = 10;
		if(page != null) pageNum = page.intValue();
		if(pageSize != null) pageSizeNum = pageSize.intValue();
		
		// 分页查询明细
		GatewayPeriodAllExample example = new GatewayPeriodAllExample();
		GatewayPeriodAllExample.Criteria criteria = example.createCriteria();
		if(sn != null && sn.length() > 0) criteria.andGatewaySnEqualTo(sn);
		criteria.andPppoeErrorNotEqualTo("ERROR_NONE");
		example.setOrderByClause(" sample_time desc ");
		
		// 日期查询昨天的
		// Date startDate = new Date(TimerUtils.getBeginDayOfYesterday());
		// Date endDate = new Date(TimerUtils.getEndDayOfYesterDay());
		// criteria.andSampleTimeBetween(startDate, endDate);
        Page<GatewayPeriodAll> doSelectPage = PageHelper.startPage(pageNum, pageSizeNum)
        		.doSelectPage(() -> gatewayPeriodAllMapper.selectByExample(example));
        
        // 解析明细数据
        List<GatewayPeriodAll> gpaInfoList = doSelectPage.getResult();
        List<Item> collect = gpaInfoList.stream()
        		.filter(x -> x != null)
        		.map(x -> from(x)).collect(Collectors.toList());
		
		// 返回组合
        PppoeFailListResult result = new PppoeFailListResult();
        result.setPage(pageNum);
        result.setPageSize(pageSizeNum);
        result.setTotal(doSelectPage.getTotal());
        result.setList(collect);
		return result;
	}
	
	/**
	 * 对象装换
	 */
	private PppoeFailListResult.Item from(GatewayPeriodAll gpaInfo) {
		PppoeFailListResult.Item item = new PppoeFailListResult.Item();
		String reason = InitSystemConfig.PPPOE_ERROR_CODE_DESC.getOrDefault(gpaInfo.getPppoeError(), gpaInfo.getPppoeError());
		item.setReason(reason);
		item.setTime(DateUtil.dateToString(gpaInfo.getSampleTime(), DateUtil.DATE_TIME_PATTERN));
		return item;
	}
	
}
