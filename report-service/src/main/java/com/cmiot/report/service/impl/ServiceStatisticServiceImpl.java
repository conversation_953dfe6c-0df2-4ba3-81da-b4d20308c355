package com.cmiot.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.cmiot.report.bean.ComplaintsOrderExample;
import com.cmiot.report.bean.ComplaintsSolvedOrderExample;
import com.cmiot.report.dto.*;
import com.cmiot.report.mapper.ComplaintsOrderMapper;
import com.cmiot.report.mapper.ComplaintsSolvedOrderMapper;
import com.cmiot.report.service.ServiceStatisticService;
import com.cmiot.report.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * author: ranwei
 * date: 2022/08/02 16:21
 * description:
 * 客服统计分析
 */


@Service
@Slf4j
public class ServiceStatisticServiceImpl implements ServiceStatisticService {

    @Autowired
    private ComplaintsOrderMapper complaintsOrderMapper;

    @Autowired
    private ComplaintsSolvedOrderMapper complaintsSolvedOrderMapper;


    /**
     * 客服投诉工单分析
     *
     * @param request
     * @return
     */
    @Override
    public ServiceStatisticResult getCustomerServiceStatistic(ServiceStatisticRequest request) {
        log.info("企业客户分析 - 客服统计分析接口方法入参: {}", JSON.toJSONString(request));

        ServiceStatisticResult result = new ServiceStatisticResult();

        try {
            List<String> provCodes = request.getProvince();
            List<String> cityCodes = request.getCity();

            // yyyy-MM-dd
            String startDate = request.getStartDate();
            String endDate = request.getEndDate();

            // yyyyMMdd
            int startTime = Integer.parseInt(DateUtil.formatInputs(startDate));
            int endTime = Integer.parseInt(DateUtil.formatInputs(endDate));

            ComplaintsSolvedOrderExample solvedExample = new ComplaintsSolvedOrderExample();
            ComplaintsSolvedOrderExample.Criteria solvedCriteria = solvedExample.createCriteria();

            ComplaintsOrderExample example = new ComplaintsOrderExample();
            ComplaintsOrderExample.Criteria criteria = example.createCriteria();

            if (!CollectionUtils.isEmpty(provCodes)) {
                criteria.andProvinceCodeIn(provCodes);
                solvedCriteria.andProvinceCodeIn(provCodes);
            }
            if (!CollectionUtils.isEmpty(cityCodes)) {
                criteria.andCityCodeIn(cityCodes);
                solvedCriteria.andCityCodeIn(cityCodes);
            }
            criteria.andGdateBetween(startTime, endTime);
            solvedCriteria.andGdateBetween(startTime, endTime);

            // 按照投诉类型统计
            List<ServicePieData> orderList = complaintsOrderMapper.selectCountByExample(example);

            // 工单处理效率统计
            //ComplaintsSolOrder solOrder = complaintsSolvedOrderMapper.countOrdersByExample(solvedExample);
            //result.setTimelyRateOfInstallation(solOrder.getTimelyRateOfInstallation());
            //result.setTimelyMaintenanceRate(solOrder.getTimelyMaintenanceRate());
            //result.setFailureResolutionRate(solOrder.getFailureResolutionRate());
            //result.setComplaintResolutionRate(solOrder.getComplaintResolutionRate());
            //result.setTimelyComplaintRate(solOrder.getTimelyComplaintRate());
            //result.setCustomerServiceAccuracy(solOrder.getCustomerServiceAccuracy());
            //result.setPieData(CollectionUtils.isEmpty(orderList) ? new ArrayList<ServicePieData>() : orderList);


            ComplaintsSolOrderExt solOrder = complaintsSolvedOrderMapper.selectOrdersByExample(solvedExample);

            long installOrders = solOrder.getInstallOrders();
            long maintenanceSolvedOrders = solOrder.getMaintenanceSolvedOrders();
            long timelyMaintenanceSolvedOrders = solOrder.getTimelyMaintenanceSolvedOrders();
            long complaintsSolvedOrders = solOrder.getComplaintsSolvedOrders();
            long timelyComplaintsSolvedOrders = solOrder.getTimelyComplaintsSolvedOrders();
            long serviceOrders = solOrder.getServiceOrders();
            long totalOrders = solOrder.getTotalOrders();

            log.info("solOrder:{}", solOrder);

            // 装移机及时率
            result.setTimelyRateOfInstallation(Float.parseFloat(getPercent(installOrders, totalOrders)));
            // 故障维修及时率
            result.setTimelyMaintenanceRate(Float.parseFloat(getPercent(timelyMaintenanceSolvedOrders, totalOrders)));
            // 故障解决率
            result.setFailureResolutionRate(Float.parseFloat(getPercent(maintenanceSolvedOrders, totalOrders)));
            // 投诉解决率
            result.setComplaintResolutionRate(Float.parseFloat(getPercent(complaintsSolvedOrders, totalOrders)));
            // 投诉处理及时率
            result.setTimelyComplaintRate(Float.parseFloat(getPercent(timelyComplaintsSolvedOrders, totalOrders)));
            // 客服准确率
            result.setCustomerServiceAccuracy(Float.parseFloat(getPercent(serviceOrders, totalOrders)));

            result.setPieData(CollectionUtils.isEmpty(orderList) ? new ArrayList<>() : orderList);
        } catch (Exception e) {
            log.info("企业客户分析 - 客服统计分析接口查询异常: {}", e.getMessage(), e);
        }


        log.info("企业客户分析 - 客服统计分析接口方法出参: {}", JSON.toJSONString(result));
        return result;
    }

    private String decimalFormat(String num) {
        String result;
        if (StringUtils.isNotBlank(num) && num.contains(".")) {
            DecimalFormat df = new DecimalFormat("0.00");
            result = df.format(Double.parseDouble(num));
        } else {
            result = num;
        }

        return result;
    }

    private String getPercent(Long num, Long total) {
        String perp;
        try {
            if (Objects.equals(num, 0L)) {
                perp = "0";
            } else if (Objects.equals(num, total)) {
                perp = "100";
            } else {
                perp = decimalFormat(String.valueOf(new BigDecimal((float) num / total).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() * 100));
            }
        } catch (Exception e) {
            perp = "0";
        }

        return perp;
    }
}
