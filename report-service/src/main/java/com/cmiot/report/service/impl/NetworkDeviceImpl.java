package com.cmiot.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.cmiot.report.dto.NetworkDeviceQuery;
import com.cmiot.report.dto.NetworkDeviceRequset;
import com.cmiot.report.dto.NetworkDeviceRunTimeDetail;
import com.cmiot.report.mapper.NetworkDeviceAllMapper;
import com.cmiot.report.service.NetworkDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * author: ranwei
 * date: 2022/09/01 16:41
 * description:
 * 组网设备运行超时列表
 */

@Service
@Slf4j
public class NetworkDeviceImpl implements NetworkDeviceService {

    @Autowired
    private NetworkDeviceAllMapper networkDeviceAllMapper;

    // 组网设备超时,单位小时
    @Value("${device.list.sub-overtime}")
    private Integer sub_overtime;


    @Override
    public List<NetworkDeviceRunTimeDetail> networkDeviceList(NetworkDeviceRequset request) {
        log.info("小程序运行报告表 - 组网设备运行超时列表查询接口方法入参: {}", JSON.toJSONString(request));

        List<NetworkDeviceRunTimeDetail> result = new ArrayList<>();

        try {
            String eid = request.getEid();
            // yyyy-MM-dd
            String startTime = request.getStartTime();
            String endTime = request.getEndTime();

            NetworkDeviceQuery query = new NetworkDeviceQuery();
            query.setEid(Long.parseLong(eid));
            query.setStartTime(startTime);
            query.setEndTime(endTime);
            query.setOverTime(sub_overtime);

            List<NetworkDeviceRunTimeDetail> deviceList = networkDeviceAllMapper.networkDeviceList(query);

            if (!CollectionUtils.isEmpty(deviceList)) {
                result = deviceList;
            } else {
                log.info("企业客户分析 - 组网设备运行超时列表查询结果为空.");
            }

        } catch (Exception e) {
            log.info("小程序运行报告表 - 组网设备运行超时列表查询接口查询异常: {}", e.getMessage(), e);
        }

        log.info("企业客户分析 - 组网设备运行超时列表查询接口方法出参: {}", JSON.toJSONString(result));
        return result;
    }
}
