package com.cmiot.report.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmiot.fgeo.ams.dto.enterprise.EntUserInfo;
import com.cmiot.fgeo.ams.dto.manage.MRoleDto;
import com.cmiot.fgeo.ams.facade.feign.EnterpriseFeignClient;
import com.cmiot.fgeo.ams.facade.feign.ManageApiFeignClient;
import com.cmiot.fgeo.ams.facade.feign.ManageFeignClient;
import com.cmiot.fgeo.common.error.FGEOException;
import com.cmiot.report.bean.DicOperationTreeInfo;
import com.cmiot.report.bean.FgeoOperation;
import com.cmiot.report.bean.FgeoOperationLog;
import com.cmiot.report.dto.syslog.*;
import com.cmiot.report.mapper.DicOperationTreeMapper;
import com.cmiot.report.mapper.FgeoOperationMapper;
import com.cmiot.report.service.SystemLogService;
import com.cmiot.report.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname SystemLogServiceImpl
 * @Description
 * @Date 2022/7/20 16:31
 * @Created by lei
 */
@Service
public class SystemLogServiceImpl implements SystemLogService {

    private final static Logger logger = LoggerFactory.getLogger(SystemLogServiceImpl.class);

    @Autowired
    private FgeoOperationMapper fgeoOperationMapper;

    @Autowired
    private ManageApiFeignClient manageApiFeignClient;

    @Autowired
    private EnterpriseFeignClient enterpriseFeignClient;

    @Autowired
    private DicOperationTreeMapper dicOperationTreeMapper;

    @Autowired
    private ManageFeignClient manageFeignClient;


    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Override
    public SysLogQueryResult querySystemLogPage(SysLogQueryRequest sysLogQueryRequest) {
        if (sysLogQueryRequest.getPage() == null) {
            sysLogQueryRequest.setPage(1L);
        }
        if (sysLogQueryRequest.getPageSize() == null) {
            sysLogQueryRequest.setPageSize(10L);
        }
        sysLogQueryRequest.setOffset((sysLogQueryRequest.getPage() - 1)
                * sysLogQueryRequest.getPageSize());
        SysLogQueryResult result = new SysLogQueryResult();
        result.setPage(sysLogQueryRequest.getPage());
        result.setPageSize(sysLogQueryRequest.getPageSize());


        if (StringUtils.isNotBlank(sysLogQueryRequest.getStartTime())) {
            LocalDateTime localDateTime =
                    LocalDateTime.of(LocalDate.parse(sysLogQueryRequest.getStartTime()), LocalTime.MIN);
            sysLogQueryRequest.setStartTime(localDateTime.format(df));
        }
        if (StringUtils.isNotBlank(sysLogQueryRequest.getEndTime())) {
            LocalDateTime localDateTime =
                    LocalDateTime.of(LocalDate.parse(sysLogQueryRequest.getEndTime()), LocalTime.MAX);
            sysLogQueryRequest.setEndTime(localDateTime.format(df));
        }
        long total = this.fgeoOperationMapper.countOperationByGroupAccount(sysLogQueryRequest);
        result.setTotal(total);
        if (total == 0) {
            result.setList(Collections.emptyList());
            return result;
        }

        List<FgeoOperation> fgeoOperationList = this.fgeoOperationMapper.selectOperationByGroupAccount(sysLogQueryRequest);
        if (fgeoOperationList == null || fgeoOperationList.isEmpty()) {
            result.setList(Collections.emptyList());
            return result;
        }
        Set<Long> roleIds = new HashSet<>();
        try {
            for (FgeoOperation fgeoOperation : fgeoOperationList) {
                if (fgeoOperation.getUserRole() != null) {
                    BigInteger[] userRoles = (BigInteger[]) fgeoOperation.getUserRole();
                    for (BigInteger userRole : userRoles) {
                        roleIds.add(userRole.longValue());
                    }
                }
            }
        } catch (Exception e) {
            logger.info("用户角色id异常: {}", e.getMessage(), e);
        }
        Map<Long, String> roleNameMap = getRoleNames(new ArrayList<>(roleIds));
        List<SysLogListDTO> list = fgeoOperationList.stream()
                .map(fgeoOperation -> {
                    SysLogListDTO sysLogListDTO = new SysLogListDTO();
                    sysLogListDTO.setTraceId(fgeoOperation.getTraceId());
                    sysLogListDTO.setUserAccount(fgeoOperation.getUserAccount());

                    try {
                        BigInteger[] userRoles = (BigInteger[]) fgeoOperation.getUserRole();
                        if (userRoles != null) {
                            List<String> roleNames = new ArrayList<>();
                            for (BigInteger userRole : userRoles) {
                                if (roleNameMap.containsKey(userRole.longValue())) {
                                    roleNames.add(roleNameMap.get(userRole.longValue()));
                                }
                            }
                            sysLogListDTO.setUserRole(StringUtils.join(roleNames, ","));
                        } else {
                            sysLogListDTO.setUserRole("");
                        }
                    } catch (Exception e) {
                        logger.info("用户角色id异常: {}", e.getMessage(), e);
                    }

                    sysLogListDTO.setOperation(fgeoOperation.getOperationName());
                    sysLogListDTO.setOperationTime(DateUtil
                                    .dateToString(fgeoOperation.getLogTime(), DateUtil.DATE_TIME_PATTERN));
                    return sysLogListDTO;
                })
                .collect(Collectors.toList());
        result.setList(list);
        return result;
    }

    @Override
    public SysLogQueryResult userLogPage(SysLogQueryRequest sysLogQueryRequest) {
        if (sysLogQueryRequest.getPage() == null) {
            sysLogQueryRequest.setPage(1L);
        }
        if (sysLogQueryRequest.getPageSize() == null) {
            sysLogQueryRequest.setPageSize(10L);
        }
        sysLogQueryRequest.setOffset((sysLogQueryRequest.getPage() - 1)
                * sysLogQueryRequest.getPageSize());
        SysLogQueryResult result = new SysLogQueryResult();
        result.setPage(sysLogQueryRequest.getPage());
        result.setPageSize(sysLogQueryRequest.getPageSize());


        if (StringUtils.isNotBlank(sysLogQueryRequest.getStartTime())) {
            LocalDateTime localDateTime =
                    LocalDateTime.of(LocalDate.parse(sysLogQueryRequest.getStartTime()), LocalTime.MIN);
            sysLogQueryRequest.setStartTime(localDateTime.format(df));
        }
        if (StringUtils.isNotBlank(sysLogQueryRequest.getEndTime())) {
            LocalDateTime localDateTime =
                    LocalDateTime.of(LocalDate.parse(sysLogQueryRequest.getEndTime()), LocalTime.MAX);
            sysLogQueryRequest.setEndTime(localDateTime.format(df));
        }
        long total = this.fgeoOperationMapper.countOperation(sysLogQueryRequest);
        result.setTotal(total);
        if (total == 0) {
            result.setList(Collections.emptyList());
            return result;
        }

        List<FgeoOperation> fgeoOperationList = this.fgeoOperationMapper.selectOperation(sysLogQueryRequest);
        if (fgeoOperationList == null || fgeoOperationList.isEmpty()) {
            result.setList(Collections.emptyList());
            return result;
        }
        Set<Long> roleIds = new HashSet<>();
        try {
            for (FgeoOperation fgeoOperation : fgeoOperationList) {
                if (fgeoOperation.getUserRole() != null) {
                    BigInteger[] userRoles = (BigInteger[]) fgeoOperation.getUserRole();
                    for (BigInteger userRole : userRoles) {
                        roleIds.add(userRole.longValue());
                    }
                }
            }
        } catch (Exception e) {
            logger.info("用户角色id异常: {}", e.getMessage(), e);
        }
        Map<Long, String> roleNameMap = getRoleNames(new ArrayList<>(roleIds));
        List<SysLogListDTO> list = fgeoOperationList.stream()
                .map(fgeoOperation -> {
                    SysLogListDTO sysLogListDTO = new SysLogListDTO();
                    sysLogListDTO.setTraceId(fgeoOperation.getTraceId());
                    sysLogListDTO.setUserAccount(fgeoOperation.getUserAccount());

                    try {
                        BigInteger[] userRoles = (BigInteger[]) fgeoOperation.getUserRole();
                        if (userRoles != null) {
                            List<String> roleNames = new ArrayList<>();
                            for (BigInteger userRole : userRoles) {
                                if (roleNameMap.containsKey(userRole.longValue())) {
                                    roleNames.add(roleNameMap.get(userRole.longValue()));
                                }
                            }
                            sysLogListDTO.setUserRole(StringUtils.join(roleNames, ","));
                        } else {
                            sysLogListDTO.setUserRole("");
                        }
                    } catch (Exception e) {
                        logger.info("用户角色id异常: {}", e.getMessage(), e);
                    }

                    sysLogListDTO.setOperation(fgeoOperation.getOperationName());
                    sysLogListDTO.setOperationTime(DateUtil
                            .dateToString(fgeoOperation.getLogTime(), DateUtil.DATE_TIME_PATTERN));
                    return sysLogListDTO;
                })
                .collect(Collectors.toList());
        result.setList(list);
        return result;
    }

    private Map<Long, String> getRoleNames(List<Long> roleIds) {
        Map<Long, String> roleNameMap = new HashMap<>();
        try {
            List<MRoleDto> roleDtos = this.manageApiFeignClient.getRoleBaseInfoByIds(roleIds);
            if (roleDtos == null || roleDtos.isEmpty()) {
                return Collections.emptyMap();
            }
            for (MRoleDto roleDto : roleDtos) {
                roleNameMap.put(roleDto.getId(), roleDto.getName());
            }
        } catch (Exception e) {
            logger.info("查询角色信息错误: {}", e.getMessage(), e);
        }
        return roleNameMap;
    }

    @Override
    public SysLogDetailQueryResult querySystemLogDetailPage(SysLogDetailQueryRequest sysLogDetailQueryRequest) {
        if (sysLogDetailQueryRequest.getPage() == null) {
            sysLogDetailQueryRequest.setPage(1L);
        }
        if (sysLogDetailQueryRequest.getPageSize() == null) {
            sysLogDetailQueryRequest.setPageSize(10L);
        }
        sysLogDetailQueryRequest.setOffset((sysLogDetailQueryRequest.getPage() - 1) * sysLogDetailQueryRequest.getPageSize());
        SysLogDetailQueryResult result = new SysLogDetailQueryResult();
        result.setPage(sysLogDetailQueryRequest.getPage());
        result.setPageSize(sysLogDetailQueryRequest.getPageSize());

        long total = this.fgeoOperationMapper.countOperationLog(sysLogDetailQueryRequest);
        result.setTotal(total);
        if (total == 0) {
            result.setList(Collections.emptyList());
            return result;
        }

        List<FgeoOperationLog> fgeoOperationList = this.fgeoOperationMapper.selectOperationLog(sysLogDetailQueryRequest);
        if (fgeoOperationList == null || fgeoOperationList.isEmpty()) {
            result.setList(Collections.emptyList());
            return result;
        }
        List<SysLogDetailListDTO> list = fgeoOperationList.stream()
                .map(fgeoOperationLog -> {
                    SysLogDetailListDTO reInfo = new SysLogDetailListDTO();
                    reInfo.setTraceId(fgeoOperationLog.getTraceId());
                    reInfo.setMsg(fgeoOperationLog.getMsg());
                    reInfo.setLogTime(DateUtil.format(fgeoOperationLog.getLogTime(), DateUtil.DATE_TIME_PATTERN));
                    return reInfo;
                })
                .collect(Collectors.toList());
        result.setList(list);
        return result;

    }

    @Override
    public SysLogQueryResult allUsersLogPage(SysLogQueryRequest sysLogQueryRequest) {
        if (sysLogQueryRequest.getPage() == null) {
            sysLogQueryRequest.setPage(1L);
        }
        if (sysLogQueryRequest.getPageSize() == null) {
            sysLogQueryRequest.setPageSize(10L);
        }
        sysLogQueryRequest.setOffset((sysLogQueryRequest.getPage() - 1)
                * sysLogQueryRequest.getPageSize());
        SysLogQueryResult result = new SysLogQueryResult();
        result.setPage(sysLogQueryRequest.getPage());
        result.setPageSize(sysLogQueryRequest.getPageSize());


        if (StringUtils.isNotBlank(sysLogQueryRequest.getStartTime())) {
            LocalDateTime localDateTime =
                    LocalDateTime.of(LocalDate.parse(sysLogQueryRequest.getStartTime()), LocalTime.MIN);
            sysLogQueryRequest.setStartTime(localDateTime.format(df));
        }
        if (StringUtils.isNotBlank(sysLogQueryRequest.getEndTime())) {
            LocalDateTime localDateTime =
                    LocalDateTime.of(LocalDate.parse(sysLogQueryRequest.getEndTime()), LocalTime.MAX);
            sysLogQueryRequest.setEndTime(localDateTime.format(df));
        }
        long total = this.fgeoOperationMapper.selectCountByGroupAccount4e(sysLogQueryRequest);
        result.setTotal(total);
        if (total == 0) {
            result.setList(Collections.emptyList());
            return result;
        }

        List<FgeoOperation> fgeoOperationList = this.fgeoOperationMapper.selectPageByGroupAccount4e(sysLogQueryRequest);
        if (fgeoOperationList == null || fgeoOperationList.isEmpty()) {
            result.setList(Collections.emptyList());
            return result;
        }
        Set<Long> roleIds = new HashSet<>();
        try {
            for (FgeoOperation fgeoOperation : fgeoOperationList) {
                if (fgeoOperation.getUserRole() != null) {
                    BigInteger[] userRoles = (BigInteger[]) fgeoOperation.getUserRole();
                    for (BigInteger userRole : userRoles) {
                        roleIds.add(userRole.longValue());
                    }
                }
            }
        } catch (Exception e) {
            logger.info("用户角色id异常: {}", e.getMessage(), e);
        }
        List<EntUserInfo> enterpriseAllUser = enterpriseFeignClient.findEnterpriseAllUser(sysLogQueryRequest.getEid());
        Map<Long, String> roleNameMap = getEUserRoleNames(new ArrayList<>(roleIds));
        List<SysLogListDTO> list = fgeoOperationList.stream()
                .map(fgeoOperation -> {
                    SysLogListDTO sysLogListDTO = new SysLogListDTO();
                    sysLogListDTO.setTraceId(fgeoOperation.getTraceId());
                    sysLogListDTO.setUserAccount(fgeoOperation.getUserAccount());
                    sysLogListDTO.setUserId(fgeoOperation.getUid());
                    EntUserInfo entUserInfo1 = enterpriseAllUser.stream().filter(entUserInfo -> entUserInfo.getUserId().equals(fgeoOperation.getUid())).findFirst().orElse(null);
                    sysLogListDTO.setUserName(entUserInfo1 != null ? entUserInfo1.getUserName() : "");

                    try {
                        BigInteger[] userRoles = (BigInteger[]) fgeoOperation.getUserRole();
                        if (userRoles != null) {
                            List<String> roleNames = new ArrayList<>();
                            for (BigInteger userRole : userRoles) {
                                if (roleNameMap.containsKey(userRole.longValue())) {
                                    roleNames.add(roleNameMap.get(userRole.longValue()));
                                }
                            }
                            sysLogListDTO.setUserRole(StringUtils.join(roleNames, ","));
                        } else {
                            sysLogListDTO.setUserRole("");
                        }
                    } catch (Exception e) {
                        logger.info("用户角色id异常: {}", e.getMessage(), e);
                    }

                    sysLogListDTO.setOperation(fgeoOperation.getOperationName());
                    sysLogListDTO.setOperationTime(DateUtil
                            .dateToString(fgeoOperation.getLogTime(), DateUtil.DATE_TIME_PATTERN));
                    return sysLogListDTO;
                })
                .collect(Collectors.toList());
        result.setList(list);
        return result;
    }

    @Override
    public SysLogQueryResult userLogsPage(SysLogQueryRequest sysLogQueryRequest) {
        sysLogQueryRequest.setUserIds(Collections.singletonList(sysLogQueryRequest.getUid()));
        if (sysLogQueryRequest.getPage() == null) {
            sysLogQueryRequest.setPage(1L);
        }
        if (sysLogQueryRequest.getPageSize() == null) {
            sysLogQueryRequest.setPageSize(10L);
        }
        sysLogQueryRequest.setOffset((sysLogQueryRequest.getPage() - 1)
                * sysLogQueryRequest.getPageSize());
        SysLogQueryResult result = new SysLogQueryResult();
        result.setPage(sysLogQueryRequest.getPage());
        result.setPageSize(sysLogQueryRequest.getPageSize());


        if (StringUtils.isNotBlank(sysLogQueryRequest.getStartTime())) {
            LocalDateTime localDateTime =
                    LocalDateTime.of(LocalDate.parse(sysLogQueryRequest.getStartTime()), LocalTime.MIN);
            sysLogQueryRequest.setStartTime(localDateTime.format(df));
        }
        if (StringUtils.isNotBlank(sysLogQueryRequest.getEndTime())) {
            LocalDateTime localDateTime =
                    LocalDateTime.of(LocalDate.parse(sysLogQueryRequest.getEndTime()), LocalTime.MAX);
            sysLogQueryRequest.setEndTime(localDateTime.format(df));
        }
        Page<FgeoOperation> pageData = new Page<>(sysLogQueryRequest.getPage(), sysLogQueryRequest.getPageSize());
        IPage<FgeoOperation> fgeoOperationPage = this.fgeoOperationMapper.selectPageUserLogs(pageData, sysLogQueryRequest);
        if (fgeoOperationPage == null || fgeoOperationPage.getRecords().isEmpty()) {
            result.setTotal(0);
            result.setList(Collections.emptyList());
            return result;
        }
        result.setTotal(fgeoOperationPage.getTotal());
        List<FgeoOperation> fgeoOperationList = fgeoOperationPage.getRecords();
        Set<Long> roleIds = new HashSet<>();
        try {
            for (FgeoOperation fgeoOperation : fgeoOperationList) {
                if (fgeoOperation.getUserRole() != null) {
                    BigInteger[] userRoles = (BigInteger[]) fgeoOperation.getUserRole();
                    for (BigInteger userRole : userRoles) {
                        roleIds.add(userRole.longValue());
                    }
                }
            }
        } catch (Exception e) {
            logger.info("用户角色id异常: {}", e.getMessage(), e);
        }
        Map<Long, String> roleNameMap = getEUserRoleNames(new ArrayList<>(roleIds));
        List<SysLogListDTO> list = fgeoOperationList.stream()
                .map(fgeoOperation -> {
                    SysLogListDTO sysLogListDTO = new SysLogListDTO();
                    sysLogListDTO.setTraceId(fgeoOperation.getTraceId());
                    sysLogListDTO.setUserAccount(fgeoOperation.getUserAccount());
                    List<Long> ids = new ArrayList<>();
                    ids.add(fgeoOperation.getUid());
                    List<EntUserInfo> enterpriseAllUser = enterpriseFeignClient.findEnterpriseUserByIds(ids);
                    EntUserInfo entUserInfo1 = enterpriseAllUser.stream().filter(entUserInfo -> entUserInfo.getUserId().equals(fgeoOperation.getUid())).findFirst().orElse(null);
                    sysLogListDTO.setUserId(fgeoOperation.getUid());
                    sysLogListDTO.setUserName(entUserInfo1 != null ? entUserInfo1.getUserName() : "");
                    try {
                        BigInteger[] userRoles = (BigInteger[]) fgeoOperation.getUserRole();
                        if (userRoles != null) {
                            List<String> roleNames = new ArrayList<>();
                            for (BigInteger userRole : userRoles) {
                                if (roleNameMap.containsKey(userRole.longValue())) {
                                    roleNames.add(roleNameMap.get(userRole.longValue()));
                                }
                            }
                            sysLogListDTO.setUserRole(StringUtils.join(roleNames, ","));
                        } else {
                            sysLogListDTO.setUserRole("");
                        }
                    } catch (Exception e) {
                        logger.info("用户角色id异常: {}", e.getMessage(), e);
                    }

                    sysLogListDTO.setOperation(fgeoOperation.getOperationName());
                    sysLogListDTO.setOperationTime(DateUtil
                            .dateToString(fgeoOperation.getLogTime(), DateUtil.DATE_TIME_PATTERN));
                    return sysLogListDTO;
                })
                .collect(Collectors.toList());
        result.setList(list);
        return result;
    }


    @Override
    public Map<String, String> getLogDetail(String traceId) {
        List<DicOperationTreeInfo> dicOperationTreeInfos = dicOperationTreeMapper.selectAll();
        FgeoOperation userLogByTraceId = fgeoOperationMapper.getUserLogByTraceId(traceId);

        DicOperationTreeInfo dicOperationTreeInfo = dicOperationTreeInfos.stream()
                .filter(o -> o.getId().equals(userLogByTraceId.getOperationId()))
                .findFirst().orElse(null);
        if (dicOperationTreeInfo == null) {
            throw new FGEOException.InternalServerError("未找到该操作");
        }
        DicOperationTreeInfo parent = dicOperationTreeInfos.stream()
                .filter(o -> o.getId().equals(dicOperationTreeInfo.getPid()))
                .findFirst().orElse(null);
        String operationName;
        if (parent != null) {
            operationName = parent.getOperationName() + "-" + dicOperationTreeInfo.getOperationName();
        } else {
            operationName = dicOperationTreeInfo.getOperationName();
        }

        List<FgeoOperationLog> logDetail = fgeoOperationMapper.getLogDetail(traceId);
        FgeoOperationLog fgeoOperationLog = logDetail.get(1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String, String> result = new HashMap<>();
        result.put("input", operationName);
        result.put("output", fgeoOperationLog == null ? "" : (fgeoOperationLog.getMsg().substring(4, 7).equals("200") ? "成功" : "失败"));
        result.put("inputTime", sdf.format(userLogByTraceId.getLogTime()));
        result.put("outputTime", fgeoOperationLog == null ? "" : sdf.format(logDetail.get(1).getLogTime()));

        return result;
    }


    private Map<Long, String> getEUserRoleNames(List<Long> roleIds) {
        Map<Long, String> roleNameMap = new HashMap<>();
        try {
            List<MRoleDto> roleDtos = this.manageFeignClient.getEUserRoleBaseInfoByIds(roleIds);
            if (roleDtos == null || roleDtos.isEmpty()) {
                return Collections.emptyMap();
            }
            for (MRoleDto roleDto : roleDtos) {
                roleNameMap.put(roleDto.getId(), roleDto.getName());
            }
        } catch (Exception e) {
            logger.info("e查询角色信息错误: {}", e.getMessage(), e);
        }
        return roleNameMap;
    }

}
