package com.cmiot.report.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Map;

import com.cmiot.fileserver.facade.feign.UploadFeignClientV2;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.fgeo.taskmanager.enums.ExportStatusEnum;
import com.cmiot.fgeo.taskmanager.facade.feign.ExportServiceFeignClient;

import com.cmiot.report.service.ExportService;

/**
 * 导出过程的操作。
 * <AUTHOR>
 *
 */
@Service
public class ExportServiceImpl implements ExportService {
	/**
	 * 导入任务记录模块。
	 */
	@Autowired
	private ExportServiceFeignClient exportServiceFeignClient;
	
	/**
	 * 文件上传
	 */
	@Autowired
	private UploadFeignClientV2 uploadFeignClientV2;
	
	/**
	 * 导入任务开始，记录相关信息。
	 * @param userId Header中获取用户标识
	 * @param exportType 导出类型。例如： 网关列表导出
	 * @param exportName 导出名称。例如： gateway_data_0001
	 * @return 导出记录对象
	 */
	@Override
	public ExportInfoDto record(Long userId, String exportType, String exportName) {
		ExportInfoDto infoDto = new ExportInfoDto();
		infoDto.setCreatorId(userId);
		// 例如：网关列表导出
		infoDto.setExportType(exportType);
		// 导出名称
		infoDto.setExportName(exportName);
		// 状态:0 成功;1 导出中;2 失败
		infoDto.setStatus(ExportStatusEnum.EXECUTING);
		infoDto.setFailedDesc("");
		infoDto.setDownloadUrl("");
		return exportServiceFeignClient.exportInsert(infoDto);
	}
	
	/**
	 * 根据记录对象。
	 * @return 
	 */
	@Override
	public Integer updateRecord(ExportInfoDto infoDto) {
		return exportServiceFeignClient.exportUpdate(infoDto);
	}
	
	/**
	 * 返回文件地址。
	 * @param userId 用户ID
	 * @param inputFile 本地文件

	 * @return 文件服务器地址
	 * @throws IOException
	 */
	@Override
	public Map<?, ?> uploadFile(Long userId, File inputFile) throws IOException {
		FileInputStream fileInputStream = new FileInputStream(inputFile);
		MultipartFile multipartFile = new MockMultipartFile(
				inputFile.getName(), 
				inputFile.getName(),
				ContentType.APPLICATION_OCTET_STREAM.toString(), 
				fileInputStream);
		Map<?, ?> uploadResult = uploadFeignClientV2.uploadAuth(multipartFile, userId, true);
		/**
		 * Map resultMap = new HashMap();
            resultMap.put("privateUrl", innerUrl);
            resultMap.put("internetUrl", internetUrl);
            resultMap.put("resultCode", 0);
            resultMap.put("size", f2.length());
            resultMap.put("md5", MD5Util.getFileMD5String(f2));
		 */
		return uploadResult;
	}
	
}
