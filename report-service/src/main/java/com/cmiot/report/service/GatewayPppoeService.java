package com.cmiot.report.service;

import java.util.List;

import com.cmiot.report.dto.pppoe.PppoeFailListRequest;
import com.cmiot.report.dto.pppoe.PppoeFailListResult;
import com.cmiot.report.dto.pppoe.PppoeListRequest;
import com.cmiot.report.dto.pppoe.PppoeListResult;
import com.cmiot.report.dto.pppoe.PppoeStatisticResult.Data;

/**
 * 网关pppoe相关业务处理
 * @version v103
 * <AUTHOR>
 *
 */
public interface GatewayPppoeService {
	/**
	 * PPPOE厂商和地市维度统计。
	 */
	public List<Data> getDataList(String type, 
			String province, String city, 
			String vendor, 
			String startTime, String endTime);
	
	/**
	 * PPPOE页面网关明细查询。
	 */
	public PppoeListResult pppoeList(PppoeListRequest request);
	
	/**
	 * 导出pppoe明细数据。
	 */
	public String pppoeListExport(Long uid, String province, String city, String vendor, String sn);
	
	/**
	 * 状态明细查询
	 */
	public PppoeFailListResult pppoeFailList(PppoeFailListRequest request);
	
}
