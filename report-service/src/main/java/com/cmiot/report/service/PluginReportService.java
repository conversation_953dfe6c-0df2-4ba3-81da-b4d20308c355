package com.cmiot.report.service;

import com.cmiot.report.bean.plugin.EkitRestartAlertAnalysisParam;
import com.cmiot.report.dto.plugin.PluginBaseInfo;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.facade.dto.plugin.*;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface PluginReportService {
    PluginInstallAnalysisRes getInstallationAnalysis(PluginInstallAnalysisParam param, LocalDate localDate);

    PageRes<EkitRestartAlertAnalysisRes> ekitRestartAlertAnalysis(EkitRestartAlertAnalysisParam param);

    List<String> ekitRestartNumberList(EkitRestartNumberListParam param);

    PageRes<PluginCompositeOverview> getPluginInfoOverview(Integer column, Integer order, Integer page, Integer pageSize);

    PluginDetailOverview getPluginDetailOverview(GetPluginDetailOverviewPar getPluginDetailOverviewPar);

    PageRes<PluginCompositeOverview> getPluginInfoStatistic(Integer column, Integer order, Integer page, Integer pageSize);

    PluginDetailOverview getPluginInfoStatisticDetail(GetPluginDetailOverviewPar pluginInfoStatisticDetailPar);

    PluginBaseInfo getPluginInfo(Integer pluginId);

    List<String> getAllPluginVersion(Integer pluginId);

}
