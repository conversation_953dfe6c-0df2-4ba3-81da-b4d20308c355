package com.cmiot.report.service;


import com.cmiot.report.dto.*;

public interface SubDeviceService {

    SubOverviewResult getSubOverview(SubOverviewRequest request, String province);

    SubDeviceStatisticResult getStatisticIndex(SubDeviceStatisticRequest request, String province);

    SubDeviceTrendResult getTrendStatistic(SubDeviceTrendRequest request, String province);

    SubDeviceListResult getSubDeviceList(SubDeviceListRequest request, String province);

    String subDeviceExport(SubDeviceListRequest request, String province);
}
