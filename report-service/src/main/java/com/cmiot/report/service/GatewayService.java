package com.cmiot.report.service;

import com.cmiot.report.bean.GatewayAnomalyRecord;
import com.cmiot.report.bean.GatewayNotOnline;
import com.cmiot.report.dto.*;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.facade.dto.gateway.GatewaySubDeviceInfoDto;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface GatewayService {

    GatewayDetailResult getDetail(GatewayQuery gatewayQuery);

    GatewayDetailResult getGatewayRunDataDetail(GatewayQuery gatewayQuery);

    List<GatewayVersionResult> getGatewayVersionStatistics(GatewayQuery gatewayQuery);

    Map getGatewayFlowListByPage(GatewayQuery gatewayQuery);

    List<GatewayFlowListResult> getGatewayFlowList(GatewayQuery gatewayQuery);

    GatewayFlowTrendResult getGatewayFlowOverview(GatewayQuery gatewayQuery);

    List<GatewayFlowTrendResult> getGatewayFlowTrend(GatewayQuery gatewayQuery);

    GatewayCpuAndRamResult getGatewayCpuAndRamOverview(GatewayQuery gatewayQuery);

    Map getGatewayCpuAndRamStatistics(GatewayQuery gatewayQuery);

    Map rangeCpuAndRamOverLevelRatio(GatewayQuery gatewayQuery);

    List<GatewayCpuAndRamPieDataDto> getGatewayCpuGradStatistics(GatewayQuery gatewayQuery);

    List<GatewayCpuAndRamPieDataDto> getGatewayRamGradStatistics(GatewayQuery gatewayQuery);

    //List<GatewayCpuAndRamMaxTimeResult> getGatewayMaxCpuRamTime(GatewayQuery gatewayQuery);

    List<GatewayCurrentResult> getGatewayLatestFlow(GatewayQuery gatewayQuery);

    Map getGatewayCpuRamListByPage(GatewayQuery gatewayQuery);

    List<GatewayCpuRamListResult> getGatewayCpuRamList(GatewayQuery gatewayQuery);

    GatewayRunTimeResult getGatewayRunTimeData(GatewayQuery gatewayQuery);

    List<GatewayRunTimeDetail> getGatewayRunTimeList(GatewayQuery gatewayQuery);

    List<GatewayWanBandwidthDetail> getGatewayWanBandwidthList(GatewayQuery gatewayQuery);

    List<Object> getGatewayFlowVelocityListByEnterprise(GatewayQuery gatewayQuery);

    PageRes<Map<String, Object>> getPageGatewayFlowVelocity(GatewayQuery gatewayQuery);

    RealtimeFlowVelocityMessageVO getPollingRealtimeFlowVelocityMonitorReport(GatewayQuery gatewayQuery);

    List<Map<String, Object>> getGatewayWanConnNumByEnterprise(Long eid);

    PageRes<Map<String, Object>> getPageGatewayWanConnNum(GatewayQuery gatewayQuery);

    RealtimeWanConnMessageVO getPollingRealtimeWanConnReport(GatewayQuery gatewayQuery);


    Map<String, List<ConnectNumberDataDto>> getGatewayConnectNumberStatistics(GatewayQuery gatewayQuery);

    Map<String, List<ConnectNumberDataDto>> getGatewayConnectNumberStatistics95(GatewayQuery gatewayQuery);

    Map<String, Object> getPageConnectNumberDetail(GatewayQuery gatewayQuery);


    Map<String, Object> getGatewayConnectNumberBySn(GatewayQuery gatewayQuery);

    List<Map<String, String>> getGatewayWanConnServiceList(GatewayQuery gatewayQuery);

    List<GateWayConnectNumberResult> getGatewayConnectNumberList(GatewayQuery gatewayQuery);

    PageRes<AccessNetworkAnalysisResult> getPageAccessNetworkAnalysis(GatewayQuery gatewayQuery);

    List<GatewaySubDeviceInfoDto> getGatewaySubDeviceOnlineNum(List<String> gatewaySns);
}
