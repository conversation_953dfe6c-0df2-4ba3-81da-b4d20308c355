package com.cmiot.report.service.impl;

import com.cmiot.report.bean.*;
import com.cmiot.report.mapper.DicAreaInfoMapper;
import com.cmiot.report.mapper.DicFactoryMapper;
import com.cmiot.report.mapper.DictDeviceModelMapper;
import com.cmiot.report.mapper.DictIndustryMapper;
import com.cmiot.report.service.DicDataService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Classname DicDataServiceImpl
 * @Description
 * @Date 2022/8/9 17:19
 * @Created by lei
 */
@Service
public class DicDataServiceImpl implements DicDataService {

    @Autowired
    private DicAreaInfoMapper dicAreaInfoMapper;

    @Autowired
    private DicFactoryMapper dicFactoryMapper;

    @Autowired
    private DictDeviceModelMapper dictDeviceModelMapper;

    @Autowired
    private DictIndustryMapper dictIndustryMapper;


    @Override
    public Map<String, String> getDicAreaMap() {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andLevelEqualTo(1);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        return dicAreaInfoList.stream().distinct().collect(Collectors.toMap(DicAreaInfo::getGcode, DicAreaInfo::getGname))
                .entrySet().stream().collect(Collectors.toMap(m -> String.valueOf(m.getKey()), Map.Entry::getValue));
    }

    @Override
    public Map<String, String> getDictCityMap() {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andLevelEqualTo(2);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        Map<String, String> map = new LinkedHashMap<>();
        for (DicAreaInfo dicAreaInfo : dicAreaInfoList) {
            map.put(dicAreaInfo.getGcode(), dicAreaInfo.getGname());
        }
        return map;
    }

    @Override
    public Map<String, String> getDicAreaMap(List<String> provCodeList) {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andLevelEqualTo(1);
        criteria.andPcodeIn(provCodeList);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        Map<String, String> map = new LinkedHashMap<>();
        for (DicAreaInfo dicAreaInfo : dicAreaInfoList) {
            map.put(dicAreaInfo.getGcode(), dicAreaInfo.getGname());
        }
        return map;
    }

    @Override
    public Map<String, String> getDicCityMap(List<String> cityCodeList) {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andGcodeIn(cityCodeList);
        criteria.andLevelEqualTo(2);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        Map<String, String> map = new LinkedHashMap<>();
        for (DicAreaInfo dicAreaInfo : dicAreaInfoList) {
            map.put(dicAreaInfo.getGcode(), dicAreaInfo.getGname());
        }
        return map;
    }

    @Override
    public Map<String, String> getDictCityMap(List<String> provCodeList) {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        List<Integer> list = provCodeList.stream().map(Integer::valueOf).collect(Collectors.toList());
        criteria.andPidIn(list);
        criteria.andLevelEqualTo(2);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        Map<String, String> map = new LinkedHashMap<>();
        for (DicAreaInfo dicAreaInfo : dicAreaInfoList) {
            map.put(dicAreaInfo.getGcode(), dicAreaInfo.getGname());
        }
        return map;
    }

    @Override
    public Map<String, String> getDicCityMap() {
        DicAreaInfoExample example = new DicAreaInfoExample();
        DicAreaInfoExample.Criteria criteria = example.createCriteria();
        criteria.andLevelEqualTo(2);
        List<DicAreaInfo> dicAreaInfoList = dicAreaInfoMapper.selectByExample(example);
        Map<String, String> map = new LinkedHashMap<>();
        for (DicAreaInfo dicAreaInfo : dicAreaInfoList) {
            map.put(dicAreaInfo.getGcode(), dicAreaInfo.getGname());
        }
        return map;
    }

    @Override
    public Map<String, String> getDicFactoryMap() {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);

        Map<String, String> map = new LinkedHashMap<>();
        for (DicFactory dicFactory : dicFactoryList) {
            map.put(dicFactory.getFactoryCode(), dicFactory.getFactoryName());
        }
        return map;
    }

    @Override
    public Map<Long, String> getDicFactoryModelMap() {
        DictDeviceModelExample example = new DictDeviceModelExample();
        List<DictDeviceModel> list = dictDeviceModelMapper.selectFactModelByExample(example);
        Map<Long, String> map = new LinkedHashMap<>();
        for (DictDeviceModel deviceModel : list) {
            map.put(deviceModel.getDeviceModelId(), deviceModel.getDeviceModel());
        }
        return map;
    }

    @Override
    public Map<Long, String> getDictFactoryModelMap(List<Long> factidsList) {
        DictDeviceModelExample example = new DictDeviceModelExample();
        DictDeviceModelExample.Criteria criteria = example.createCriteria();
        criteria.andFactoryIdIn(factidsList);
        List<DictDeviceModel> result = dictDeviceModelMapper.selectFactModelByExample(example);
        Map<Long, String> map = new LinkedHashMap<>();
        for (DictDeviceModel deviceModel : result) {
            map.put(deviceModel.getDeviceModelId(), deviceModel.getDeviceModel());
        }
        return map;
    }

    @Override
    public Map<Long, String> getDicFactoryModelMap(List<Long> list) {
        DictDeviceModelExample example = new DictDeviceModelExample();
        DictDeviceModelExample.Criteria criteria = example.createCriteria();
        criteria.andDeviceModelIdIn(list);
        List<DictDeviceModel> result = dictDeviceModelMapper.selectFactModelByExample(example);
        return result.stream().collect(Collectors.toMap(DictDeviceModel::getDeviceModelId, DictDeviceModel::getDeviceModel));
    }

    @Override
    public Map<String, String> getDictFactoryNameMap() {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);
        return dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryName, DicFactory::getFactoryCode));
    }

    @Override
    public Map<String, Long> getDictFactoryCodeIdsMap() {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);
        return dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryCode, DicFactory::getFactoryId));
    }

    @Override
    public Map<String, String> getDictFactoryCodeByIds(List<Long> list) {
        DicFactoryExample example = new DicFactoryExample();
        DicFactoryExample.Criteria criteria = example.createCriteria();
        criteria.andFactoryIdIn(list);
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByExample(example);
        return dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryCode, DicFactory::getFactoryName));
    }


    /*public Map<String, String> getDicFactoryMap(List<String> vendorCodesList) {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);

        Map<String, String> codeNameMap = new HashMap<>();
        if (CollectionUtils.isEmpty(vendorCodesList)) {
            codeNameMap = dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryCode, DicFactory::getFactoryName));
        } else {
            for (DicFactory dicFactory : dicFactoryList) {
                String factoryCode = dicFactory.getFactoryCode();
                String factoryName = dicFactory.getFactoryName();
                if (vendorCodesList.contains(factoryCode)) {
                    codeNameMap.put(factoryCode, factoryName);
                }
            }
        }
        //return dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryId, DicFactory::getFactoryName));
        return codeNameMap;
    }*/

    @Override
    public Map<String, String> getDictIndustryMap() {
        DictIndustryExample example = new DictIndustryExample();
        example.setOrderByClause(" code asc ");
        List<DictIndustry> industryList = dictIndustryMapper.selectByExample(example);
        Map<String, String> map = new LinkedHashMap<>();
        for (DictIndustry dictIndustry : industryList) {
            map.put(dictIndustry.getCode(), dictIndustry.getIndustryName());
        }
        return map;
    }

    @Override
    public Map<String, String> getDictIndustryMap(List<String> list) {
        DictIndustryExample example = new DictIndustryExample();
        example.setOrderByClause(" code asc ");
        example.createCriteria()
                .andCodeIn(list);
        List<DictIndustry> industryList = dictIndustryMapper.selectByExample(example);
        Map<String, String> map = new LinkedHashMap<>();
        for (DictIndustry dictIndustry : industryList) {
            map.put(dictIndustry.getCode(), dictIndustry.getIndustryName());
        }
        return map;
    }

    @Override
    public List<String> getDicFactoryCodeListById(List<String> vendorIdList) {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);
        Map<Long, String> dicFactoryIdCodeMap = dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryId, DicFactory::getFactoryCode));
        List<String> finalVendorList = new ArrayList<>();
        for (String vendor : vendorIdList) {
            if (StringUtils.isNotBlank(vendor)) {
                String vendorCode = dicFactoryIdCodeMap.get(Long.parseLong(vendor));
                finalVendorList.add(vendorCode);
            }
        }
        return finalVendorList;
    }


    @Override
    public List<String> getDicFactoryIdCodeMap(List<String> vendorIdList) {
        DicFactoryExample example = new DicFactoryExample();
        List<DicFactory> dicFactoryList = dicFactoryMapper.selectByWindowExample(example);
        Map<Long, String> dicFactoryIdCodeMap = dicFactoryList.stream().collect(Collectors.toMap(DicFactory::getFactoryId, DicFactory::getFactoryCode));

        List<String> vendorCodeList = new ArrayList<>();
        vendorIdList.forEach(venderId -> vendorCodeList.add(dicFactoryIdCodeMap.getOrDefault(Long.parseLong(venderId), "")));

        return vendorCodeList;
    }
}
