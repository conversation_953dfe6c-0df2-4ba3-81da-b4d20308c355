package com.cmiot.report.service.impl;

import com.cmiot.e.apiservice.facade.ApiServiceFeignClient;
import com.cmiot.e.apiservice.facade.vo.DeviceClassifyResponse;
import com.cmiot.e.apiservice.facade.vo.DeviceInfoVo;
import com.cmiot.e.apiservice.facade.vo.DeviceListRequest;
import com.cmiot.e.apiservice.facade.vo.DeviceSearchRequest;
import com.cmiot.fgeo.common.error.FGEOException;
import com.cmiot.fgeo.devicemanager.dto.GatewayDetailDto;
import com.cmiot.fgeo.devicemanager.gatewayapi.GatewayApiServiceFeignClient;
import com.cmiot.report.bean.GatewayPeriodAll;
import com.cmiot.report.dto.device.DeviceFlowInfo;
import com.cmiot.report.dto.device.DeviceTimesInfo;
import com.cmiot.report.dto.enterprise.*;
import com.cmiot.report.facade.dto.enterprise.*;
import com.cmiot.report.mapper.DeviceMapper;
import com.cmiot.report.mapper.EnterpriseGatewayCountMapper;
import com.cmiot.report.mapper.EnterpriseGatewayOnlineTimeMapper;
import com.cmiot.report.mapper.GatewayPeriodAllMapper;
import com.cmiot.report.service.EnterpriseWeekReportService;
import com.cmiot.report.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Classname EnterpriseWeekReportServiceImpl
 * @Description
 * @Date 2023/12/20 8:52
 * @Created by lei
 */
@Service
public class EnterpriseWeekReportServiceImpl implements EnterpriseWeekReportService {

    private final static Logger logger = LoggerFactory.getLogger(EnterpriseWeekReportServiceImpl.class);

    @Autowired
    private EnterpriseGatewayCountMapper enterpriseGatewayCountMapper;

    @Autowired
    private GatewayPeriodAllMapper gatewayPeriodAllMapper;

    @Autowired
    private GatewayApiServiceFeignClient gatewayApiServiceFeignClient;

    @Autowired
    private ApiServiceFeignClient apiServiceFeignClient;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private EnterpriseGatewayOnlineTimeMapper enterpriseGatewayOnlineTimeMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    @Value("${continuous.running.collect.hour:1}")
    private Integer continuousRunningCollectHour;

    @Value("${continuous.running.time.limit:168}")
    //单位小时
    private Integer continuousRunningTimeLimit;

    @Value("${weekReportRankLimit:5}")
    private Integer weekReportRankLimit;

    @Override
    public WeekReportOverviewResult getWeekReportOverview(Long eid) {
        WeekReportOverviewResult weekReportOverviewResult = new WeekReportOverviewResult();
        String weekDay = DateUtil.getLastWeekDay();
        weekReportOverviewResult.setGatewayCount(0);
        weekReportOverviewResult.setGatewayOnlineCount(0);

        weekReportOverviewResult.setRestartDevice(new ArrayList<>());

        //获取网关总数、活跃数量
        EnterpriseGatewayCountInfo enterpriseGatewayCountInfoWeek =
                this.enterpriseGatewayCountMapper.getDeviceOverviewWeek(eid, weekDay);
        if (enterpriseGatewayCountInfoWeek != null) {
            weekReportOverviewResult.setGatewayCount(enterpriseGatewayCountInfoWeek.getAllNum());
            weekReportOverviewResult.setGatewayOnlineCount(enterpriseGatewayCountInfoWeek.getWeekNum());
        } else {
            weekReportOverviewResult.setGatewayCount(0);
            weekReportOverviewResult.setGatewayOnlineCount(0);
        }

        //企业流量
        try {
            Integer lastWeekDay = DateUtil.getLastWeekDate();
            String startDate = Objects.toString(DateUtil.getDayAtWeekStart(lastWeekDay));
            String endDate = Objects.toString(DateUtil.getDayAtWeekEnd(lastWeekDay));
            //初始化数据结构
            Double weekAllFlow = 0D;
            //初始化默认值
            List<String> days = DateUtil.getDaysByDuring(startDate, endDate, DateUtil.MONTH_DATE_PATTERN);
            List<String> flows = new ArrayList<>();
            List<String> averages = new ArrayList<>();
            String avgFlow = "0";
            for (String ignored : days) {
                flows.add("0");
                averages.add("0");
            }

            DecimalFormat decimalFormat = new DecimalFormat("#.##");
            try {
                List<EnterpriseNetworkTotalInfo> enterpriseNetworkTotalInfos =
                        this.enterpriseGatewayCountMapper.getEnterpriseNetworkTotalCount(eid, startDate, endDate);
                if (enterpriseNetworkTotalInfos != null && !enterpriseNetworkTotalInfos.isEmpty()) {
                    for (int i = 0; i < days.size(); i++) {
                        String day = days.get(i);
                        EnterpriseNetworkTotalInfo dayNetwork = null;
                        for (EnterpriseNetworkTotalInfo enterpriseNetworkTotalInfo : enterpriseNetworkTotalInfos) {
                            if (day.equalsIgnoreCase(enterpriseNetworkTotalInfo.getGdate())) {
                                dayNetwork = enterpriseNetworkTotalInfo;
                                break;
                            }
                        }
                        //存在流量值
                        if (dayNetwork != null) {
                            weekAllFlow += dayNetwork.getTotalFlow();
                            //转换成GB,取两位小数
                            String dayFlow = decimalFormat.format(dayNetwork.getTotalFlow() / 1024D);
                            flows.set(i, dayFlow);
                        }

                    }

                    avgFlow = decimalFormat.format(weekAllFlow / 7D / 1024D);
                    for (int i = 0; i < averages.size(); i++) {
                        averages.set(i, avgFlow);
                    }
                }
            } catch (Exception e) {
                logger.error("从数据库中获取企业流量信息失败: {}", e.getMessage(), e);
            }

            WeekReportOverviewFlowInfoDto weekReportOverviewFlowInfoDto = new WeekReportOverviewFlowInfoDto();

            //处理时间格式
            List<String> daysFormat = new ArrayList<>();
            for (String day : days) {
                String fday = DateUtil.formatFromTo(day, DateUtil.MONTH_DATE_PATTERN, DateUtil.MONTH_AND_DAY);
                if (fday != null) {
                    daysFormat.add(fday);
                } else {
                    daysFormat.add(day);
                }
            }

            weekReportOverviewFlowInfoDto.setTimes(daysFormat);
            weekReportOverviewFlowInfoDto.setFlows(flows);
            weekReportOverviewFlowInfoDto.setAverages(averages);
            weekReportOverviewResult.setFlowStatistics(weekReportOverviewFlowInfoDto);
        } catch (Exception e) {
            logger.error("获取企业流量信息失败: {}", e.getMessage(), e);
        }

        try {
            //企业长时间在线设备
            Date now = new Date();
            String nowTimeStr = DateUtil.format(now, DateUtil.UNSIGNED_DATE_TIME_PATTERN);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.HOUR, -continuousRunningCollectHour);
            Date timePre = calendar.getTime();
            String timePreStr = DateUtil.format(timePre, DateUtil.UNSIGNED_DATE_TIME_PATTERN);

            List<GatewayPeriodAll> gatewayPeriodAlls =
                    this.gatewayPeriodAllMapper.selectContinuousRunningOverTimeDevice(eid,
                            timePreStr, continuousRunningTimeLimit);
            if (gatewayPeriodAlls != null && !gatewayPeriodAlls.isEmpty()) {
                List<String> macList = new ArrayList<>();
                for (GatewayPeriodAll gatewayPeriodAll : gatewayPeriodAlls) {
                    macList.add(gatewayPeriodAll.getGatewayMac());
                }

                List<GatewayDetailDto> gatewayList =
                        this.gatewayApiServiceFeignClient.getGatewayDetailInfoByMacList(macList);

                if (gatewayList != null && !gatewayList.isEmpty()) {
                    List<String> restartDevice = new ArrayList<>();
                    for (GatewayDetailDto gatewayDetailDto : gatewayList) {
                        restartDevice.add(Objects.toString(gatewayDetailDto.getId()));
                    }
                    //查询当前企业绑定网关

                    DeviceListRequest deviceListRequest = new DeviceListRequest();
                    deviceListRequest.setIds(restartDevice);
                    DeviceClassifyResponse deviceClassifyResponse = null;
                    try {
                        deviceClassifyResponse = apiServiceFeignClient.deviceClassify(deviceListRequest);
                    } catch (Exception e){
                        logger.info("查询apiServiceFeignClient.deviceClassify异常: {}", e.getMessage(), e);
                    }
                    if (deviceClassifyResponse != null) {
                        List<DeviceInfoVo> deviceInfoVos = deviceClassifyResponse.getDevices();
                        if (deviceInfoVos != null && !deviceInfoVos.isEmpty()) {
                            List<String> currentRestartDevices = new ArrayList<>();
                            for (DeviceInfoVo deviceInfoVo : deviceInfoVos) {
                               try {
                                   if (eid.equals(deviceInfoVo.getEnterpriseId())) {
                                       currentRestartDevices.add(deviceInfoVo.getId());
                                   }
                               } catch (Exception e) {
                                   logger.info("查询DeviceInfoVo异常: {}", e.getMessage(), e);
                               }
                            }

                            weekReportOverviewResult.setRestartDevice(currentRestartDevices);
                        }
                    }


                }
            }
        } catch (Exception e) {
            logger.error("获取长时间运行网关失败: {}", e.getMessage(), e);
        }

        return weekReportOverviewResult;
    }

    @Override
    public WeekReportVisitsResult getWeekReportVisits(Long eid) {
        Integer lastWeekDay = DateUtil.getLastWeekDate();
        String startDate = Objects.toString(DateUtil.getDayAtWeekStart(lastWeekDay));
        String endDate = Objects.toString(DateUtil.getDayAtWeekEnd(lastWeekDay));
        WeekReportVisitsResult weekReportVisitsResult = new WeekReportVisitsResult();
        List<WeekReportVisitsWebsitesInfoDto> visitWebsites = new ArrayList<>();
        weekReportVisitsResult.setVisitWebsites(visitWebsites);
        List<EnterpriseVisitsCountInfo> enterpriseVisitsCountInfos =
                this.enterpriseGatewayCountMapper.getWeekReportVisitsCountRank(eid,
                        startDate, endDate, weekReportRankLimit);

        if (enterpriseVisitsCountInfos != null && !enterpriseVisitsCountInfos.isEmpty()) {
            for (EnterpriseVisitsCountInfo enterpriseVisitsCountInfo : enterpriseVisitsCountInfos) {
                WeekReportVisitsWebsitesInfoDto weekReportVisitsWebsitesInfoDto = new WeekReportVisitsWebsitesInfoDto();
                weekReportVisitsWebsitesInfoDto.setWebsiteName(enterpriseVisitsCountInfo.getDomainName());
                visitWebsites.add(weekReportVisitsWebsitesInfoDto);
            }
        }
        weekReportVisitsResult.setVisitWebsites(visitWebsites);
        return weekReportVisitsResult;
    }


    @Override
    public WeekReportSubDeviceResult getWeekReportSubDevice(Long eid) {
        WeekReportSubDeviceResult weekReportSubDeviceResult = new WeekReportSubDeviceResult();
        weekReportSubDeviceResult.setSubDeviceCount("0");
        weekReportSubDeviceResult.setFlowAnalysis(new ArrayList<>());
        weekReportSubDeviceResult.setSubDeviceConnectduration(new ArrayList<>());

        Integer lastWeekDay = DateUtil.getLastWeekDate();
        String startDate = Objects.toString(DateUtil.getDayAtWeekStart(lastWeekDay));
        String endDate = Objects.toString(DateUtil.getDayAtWeekEnd(lastWeekDay));

        List<DeviceEnterpriseCountInfo> deviceEnterpriseCountInfos =
                this.enterpriseGatewayCountMapper.getSubDeviceWeekCount(eid, startDate, endDate);
        if (deviceEnterpriseCountInfos != null && !deviceEnterpriseCountInfos.isEmpty()) {
            DeviceEnterpriseCountInfo deviceEnterpriseCountInfo = deviceEnterpriseCountInfos.get(0);
            weekReportSubDeviceResult.setSubDeviceCount(Objects.toString(deviceEnterpriseCountInfo.getCountNum(), "0"));
        }
        //流量
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        List<DeviceFlowInfo> deviceFlowInfos =
                this.deviceMapper.queryDeviceFlowRank(eid, startDate, endDate, weekReportRankLimit);
        if (deviceFlowInfos != null && !deviceFlowInfos.isEmpty()) {
            List<SubDeviceConnectFlowDto> flowAnalysis = new ArrayList<>();
            for (DeviceFlowInfo deviceFlowInfo : deviceFlowInfos) {
                SubDeviceConnectFlowDto subDeviceConnectFlowDto = new SubDeviceConnectFlowDto();
                subDeviceConnectFlowDto.setDeviceMac(deviceFlowInfo.getMac());
                subDeviceConnectFlowDto.setDeviceName(deviceFlowInfo.getDeviceName());
                subDeviceConnectFlowDto.setDeviceType(deviceFlowInfo.getDeviceType());
                subDeviceConnectFlowDto.setFlow(decimalFormat.format(deviceFlowInfo.getFlow() / 1024D));
                flowAnalysis.add(subDeviceConnectFlowDto);
            }
            weekReportSubDeviceResult.setFlowAnalysis(flowAnalysis);
        }

        //访问时长
        List<DeviceTimesInfo> deviceTimesInfos =
                this.deviceMapper.queryDeviceTimesRank(eid, startDate, endDate, weekReportRankLimit);
        if (deviceTimesInfos != null && !deviceTimesInfos.isEmpty()) {
            List<SubDeviceConnectDurationDto> subDeviceConnectduration = new ArrayList<>();
            for (DeviceTimesInfo deviceTimesInfo : deviceTimesInfos) {
                SubDeviceConnectDurationDto subDeviceConnectDurationDto = new SubDeviceConnectDurationDto();
                subDeviceConnectDurationDto.setDeviceMac(deviceTimesInfo.getMac());
                subDeviceConnectDurationDto.setDeviceName(deviceTimesInfo.getDeviceName());
                subDeviceConnectDurationDto.setDeviceType(deviceTimesInfo.getDeviceType());
                Long minute = deviceTimesInfo.getTimes() * 15L;
                Double hour = minute.doubleValue() / 60d;
                subDeviceConnectDurationDto.setDuration(decimalFormat.format(hour));
                subDeviceConnectduration.add(subDeviceConnectDurationDto);
            }
            weekReportSubDeviceResult.setSubDeviceConnectduration(subDeviceConnectduration);
        }

        return weekReportSubDeviceResult;
    }

/*    @Override
    public Map<String, Object> getWeekReportGatewayRunTimes(Long eid) {
        DeviceSearchRequest deviceListRequest = new DeviceSearchRequest();
        deviceListRequest.setEnterpriseId(eid);
        deviceListRequest.setDeviceType(1);//设备类型 1-网关，2-组网，3-aec
        List<DeviceInfoVo> deviceInfoVos = apiServiceFeignClient.deviceESearch(deviceListRequest);
        if (deviceInfoVos == null || deviceInfoVos.isEmpty()) {
            throw new FGEOException.InternalServerError("网关不存在，请重试");
        }

        this.redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        this.redisTemplate.setHashValueSerializer(new StringRedisSerializer());
        this.redisTemplate.afterPropertiesSet();
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        // 获取本周一的日期
        LocalDate thisMonday = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 计算上周一的日期
        LocalDate lastMonday = thisMonday.minusWeeks(1);

        //并行计算每天的平均值
        List<CompletableFuture<Map<LocalDate, String>>> futures = new ArrayList<>(deviceInfoVos.size());
        for (int i = 1; i <= 7; i++) {
            String yyyyMMdd = formatter.format(lastMonday);
            LocalDate finalLastMonday = lastMonday;
            CompletableFuture<Map<LocalDate, String>> future = CompletableFuture.supplyAsync(() -> {
                List<Object> objects = redisTemplate.executePipelined((RedisCallback<List<byte[]>>) connection -> {
                    String hkey = "ekit-portal:gatewayOnlineTimeStatistics:" + yyyyMMdd;
                    List<String> macs = deviceInfoVos.stream().map(DeviceInfoVo::getMac).collect(Collectors.toList());
                    byte[][] macBytesArray = new byte[macs.size()][];
                    for (int n = 0; n < macs.size(); n++) {
                        macBytesArray[n] = macs.get(n).getBytes(StandardCharsets.UTF_8);
                    }
                    return connection.hMGet(hkey.getBytes(StandardCharsets.UTF_8), macBytesArray);
                });
                Map<LocalDate, String> resultMap = new HashMap<>(1);
                if (!objects.isEmpty()) {
                    for (Object obj : objects) {
                        if (obj instanceof ArrayList) {
                            logger.info("{}-executePipelined==>{}", yyyyMMdd, obj);
                            List<Integer> list = (ArrayList<Integer>) obj;
                            int sum = list.stream()
                                    .filter(num -> num != null)
                                    .mapToInt(Integer::intValue)
                                    .sum();
                            //去掉为null之后剩余的元素个数
                            int count = list.size() - (int) list.stream().filter(Objects::isNull).count();
                            //计算一天的所有网关的平均在线率
//                            long sum = objects.stream().mapToLong(o -> (Long) o).sum();
                            double avg = (double)sum / 60 / count;
                            resultMap.put(finalLastMonday, String.format("%.2f", avg));
                        }
                    }
                } else {
                    resultMap.put(finalLastMonday, "0");
                }
                return resultMap;
            });
            futures.add(future);
            lastMonday = lastMonday.plusDays(1);
        }

        // 使用allOf方法来表示所有的并行任务
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        // 获得所有子任务的处理结果
        CompletableFuture<List<Map<LocalDate, String>>> finalResults = allFutures.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
        List<Map<LocalDate, String>> joinList = finalResults.join();
        logger.info("并行查询计算结果: {}", joinList);
        //按日期升序排序，抽出keys和values组成两个字段返回
        joinList.sort(Comparator.comparing(map -> map.keySet().iterator().next()));
        logger.info("joinList排序: {}", joinList);
        List<String> keys = joinList.stream().map(map -> DateTimeFormatter.ofPattern("MM/dd").format(map.keySet().iterator().next())).collect(Collectors.toList());
        List<String> values = joinList.stream().map(map -> map.values().iterator().next()).collect(Collectors.toList());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("times", keys);
        resultMap.put("gateway", values);
        return resultMap;
    }*/

    @Override
    public Map<String, Object> getWeekReportGatewayRunTimes(Long eid) {
        DeviceSearchRequest deviceListRequest = new DeviceSearchRequest();
        deviceListRequest.setEnterpriseId(eid);
        deviceListRequest.setDeviceType(1);//设备类型 1-网关，2-组网，3-aec
        List<DeviceInfoVo> deviceInfoVos = apiServiceFeignClient.deviceESearch(deviceListRequest);
        if (deviceInfoVos == null || deviceInfoVos.isEmpty()) {
            throw new FGEOException.InternalServerError("未找到任何网关信息");
        }
        List<String> macs = deviceInfoVos.stream().map(DeviceInfoVo::getMac).collect(Collectors.toList());
        // 获取本周一的日期
        LocalDate thisMonday = LocalDate.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 计算上周一的日期
        LocalDate lastMonday = thisMonday.minusWeeks(1);
        // 计算上周天的日期
        LocalDate lastSunday = thisMonday.minusDays(1);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String lastMondayStr = formatter.format(lastMonday);
        String lastSundayStr = formatter.format(lastSunday);

        List<EnterpriseGatewayOnlineTimeInfo> onlineTimeList = enterpriseGatewayOnlineTimeMapper.getOnlineTimeList(macs, lastMondayStr, lastSundayStr);
        //取出recordDate并格式化成MM/dd生成新的list
        List<String> recordDates = onlineTimeList.stream().map(EnterpriseGatewayOnlineTimeInfo::getRecordDate).collect(Collectors.toList());
        List<String> keys = recordDates.stream().map(o -> DateTimeFormatter.ofPattern("MM/dd").format(LocalDate.parse(o, formatter))).collect(Collectors.toList());
        //取出所有mac对应的平均在线时间
        List<Double> values = onlineTimeList.stream().map(EnterpriseGatewayOnlineTimeInfo::getAvgOnlineTime).collect(Collectors.toList());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("times", keys);
        resultMap.put("gateway", values);
        return resultMap;
    }
}
