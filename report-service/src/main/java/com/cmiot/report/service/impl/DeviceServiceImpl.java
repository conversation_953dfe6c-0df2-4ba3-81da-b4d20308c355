package com.cmiot.report.service.impl;

import com.cmiot.report.common.ComUtils;
import com.cmiot.report.dto.DeviceAppStatRunVo;
import com.cmiot.report.dto.GatewayQuery;
import com.cmiot.report.dto.device.*;
import com.cmiot.report.mapper.DeviceMapper;
import com.cmiot.report.service.DeviceService;
import com.cmiot.report.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeviceServiceImpl implements DeviceService {

    @Autowired
    DeviceMapper deviceMapper;

    @Override
    public DeviceOverviewCount queryDeviceOverviewCount(String province) {
        List<String> provinceCodes = null;
        if (StringUtils.isNotBlank(province)) {
            provinceCodes = Arrays.asList(province.split(","));
        }
        DeviceOverviewCount deviceOverviewCount = deviceMapper.queryDeviceOverviewCountAll(provinceCodes);
        return deviceOverviewCount == null
                ? new DeviceOverviewCount()
                : deviceOverviewCount;
    }

    @Override
    public DeviceOverviewResult queryDeviceOverviewResult(DeviceQuery deviceQuery) {
        DeviceOverviewResult deviceOverviewResult = new DeviceOverviewResult();
        try {
            //1、查询筛选条件下：网关下挂设备总量
            int totalUnderDeviceNum = deviceMapper.queryOverviewSelectCount(deviceQuery);
            deviceOverviewResult.setTotalUnderDeviceNum(String.valueOf(totalUnderDeviceNum));
            if (totalUnderDeviceNum > 0) {
                //2、查询筛选条件下：省份排名
                deviceQuery.setType("province_code");
                List<DeviceOverviewObject> listByProvince = deviceMapper.queryDeviceOverviewResult(deviceQuery);
                //计算比率
                listByProvince = listByProvince.stream().peek(s -> s.setPercent(new BigDecimal(s.getValue() / (double) totalUnderDeviceNum * 100).setScale(2, RoundingMode.HALF_UP) + "")).sorted(Comparator.comparing(DeviceOverviewObject::getValue).reversed()).collect(Collectors.toList());
                deviceOverviewResult.setListByProvince(listByProvince);
                //3、查询筛选条件下：厂商排名
                deviceQuery.setType("gateway_vendor");
                List<DeviceOverviewObject> listByVendor = deviceMapper.queryDeviceOverviewResult(deviceQuery);
                //计算比率
                listByVendor = listByVendor.stream().peek(s -> s.setPercent(new BigDecimal(s.getValue() / (double) totalUnderDeviceNum * 100).setScale(2, RoundingMode.HALF_UP) + "")).sorted(Comparator.comparing(DeviceOverviewObject::getValue).reversed()).collect(Collectors.toList());
                deviceOverviewResult.setListByVendor(listByVendor);
                //4、查询筛选条件下：行业排名
                deviceQuery.setType("industry_code");
                List<DeviceOverviewObject> listByIndustry = deviceMapper.queryDeviceOverviewResult(deviceQuery);
                //计算比率
                listByIndustry = listByIndustry.stream().peek(s -> s.setPercent(new BigDecimal(s.getValue() / (double) totalUnderDeviceNum * 100).setScale(2, RoundingMode.HALF_UP) + "")).sorted(Comparator.comparing(DeviceOverviewObject::getValue).reversed()).collect(Collectors.toList());
                deviceOverviewResult.setListByIndustry(listByIndustry);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return deviceOverviewResult;
    }

    @Override
    public DeviceWLANResult queryDeviceWLANResult(DeviceQuery deviceQuery) {
        DeviceWLANResult deviceWLANResult = new DeviceWLANResult();
        try {
            //1、根据type查询结果
            List<DeviceWLANObject> deviceWLANObjectList = deviceMapper.queryDeviceWLANResult(deviceQuery);
            //2、拆分行列
            String[] xAxis = deviceWLANObjectList.stream().map(DeviceWLANObject::getName).collect(Collectors.toList()).toArray(new String[deviceWLANObjectList.size()]);
            int[] deviceNum = deviceWLANObjectList.stream().mapToInt(DeviceWLANObject::getDeviceNum).toArray();
            double[] signalIntensity = deviceWLANObjectList.stream().mapToDouble(DeviceWLANObject::getWlanPower).toArray();
            //3、assemble
            deviceWLANResult.setXAxis(xAxis);
            deviceWLANResult.setDeviceNum(deviceNum);
            deviceWLANResult.setSignalIntensity(signalIntensity);
        } catch (Exception e) {
            log.error("设备无线信号均值趋势查询错误,{}", deviceQuery.toString());
            e.printStackTrace();
        }
        return deviceWLANResult;
    }

    @Override
    public DeviceAppStatCount queryDeviceAppStatCount(Long enterpriseId) {
        //累计下挂设备
        DeviceAppStatCount deviceAppStatCount = deviceMapper.queryDeviceAppStatCountTotal(enterpriseId);
        try {
            if (deviceAppStatCount != null) {
                //周活跃、日活跃使用自然周、自然月
                Integer lastWeekDay = DateUtil.getLastWeekDate();
                int dayStart = DateUtil.getDayAtWeekStart(lastWeekDay);
                int dayEnd = DateUtil.getDayAtWeekEnd(lastWeekDay);
                DeviceAppStatCount deviceAppStatCountWeek = deviceMapper.queryDeviceAppStatCountTotalWeek(enterpriseId, dayStart, dayEnd);
                if (deviceAppStatCountWeek != null) {
                    deviceAppStatCount.setWeekActive(deviceAppStatCountWeek.getWeekActive());
                }
                Integer lastMonth = DateUtil.getLastMonthDate();
                DeviceAppStatCount deviceAppStatCountMonth =
                        deviceMapper.queryDeviceAppStatCountTotalMonth(enterpriseId, lastMonth);
                if (deviceAppStatCountMonth != null) {
                    deviceAppStatCount.setMonthActive(deviceAppStatCountMonth.getMonthActive());
                }
            }


        } catch (Exception e) {
            log.info("查询异常: {}", e.getMessage(), e);
        }
        return deviceAppStatCount;
    }

    @Override
    public Map<String, Object> queryDeviceAppStatTrend(Long enterpriseId, String startDate, String endDate) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<DeviceAppStatTrendObject> countList = deviceMapper.queryDeviceAppStatTrendCount(enterpriseId, startDate, endDate);
            List<DeviceAppStatTrendObject> wlanList = deviceMapper.queryDeviceAppStatTrendWlan(enterpriseId, startDate, endDate);
            Map<String, DeviceAppStatTrendObject> countMap = countList.stream().collect(Collectors.toMap(DeviceAppStatTrendObject::getDate, Function.identity()));
            Map<String, DeviceAppStatTrendObject> wlanMap = wlanList.stream().collect(Collectors.toMap(DeviceAppStatTrendObject::getDate, Function.identity()));
            //补齐日期
            List<String> days = ComUtils.getDays(startDate, endDate);
            List<Integer> countListResult = new ArrayList<>();
            List<Double> wlanListResult = new ArrayList<>();
            for (String day : days) {
                if (countMap.containsKey(day)) {
                    countListResult.add(Integer.parseInt(countMap.get(day).getValue()));
                } else {
                    countListResult.add(0);
                }
                if (wlanMap.containsKey(day)) {
                    wlanListResult.add(Double.parseDouble(wlanMap.get(day).getValue()));
                } else {
                    wlanListResult.add(0.0d);
                }
            }
            Map<String, Object> quantity = new HashMap<>();
            Map<String, Object> wlanSignal = new HashMap<>();

            String[] time = days.toArray(new String[0]);

            quantity.put("time", time);
            quantity.put("access", countListResult.toArray(new Integer[0]));

            wlanSignal.put("time", time);
            wlanSignal.put("averageSignal", wlanListResult.toArray(new Double[countListResult.size()]));

            result.put("quantity", quantity);
            result.put("wlanSignal", wlanSignal);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", 500);
            result.put("message", "内部错误");
        }
        log.info("end queryDeviceAppStatTrend , result:{}", result);
        return result;
    }

    @Override
    public Integer queryDeviceAppStatRunCount(GatewayQuery gatewayQuery) {
        return deviceMapper.queryDeviceAppStatRunCount(gatewayQuery);
    }

    @Override
    public DeviceAppStatRunVo queryDeviceAppStatRunSpeed(GatewayQuery gatewayQuery) {
        return deviceMapper.queryDeviceAppStatRunSpeed(gatewayQuery);
    }
}
