package com.cmiot.report.service;

import com.cmiot.report.dto.syslog.SysLogDetailQueryRequest;
import com.cmiot.report.dto.syslog.SysLogDetailQueryResult;
import com.cmiot.report.dto.syslog.SysLogQueryRequest;
import com.cmiot.report.dto.syslog.SysLogQueryResult;

import java.util.Map;

/**
 * @Classname SystemLogService
 * @Description
 * @Date 2022/7/20 16:31
 * @Created by lei
 */
public interface SystemLogService {
    SysLogQueryResult querySystemLogPage(SysLogQueryRequest sysLogQueryRequest);

    SysLogQueryResult userLogPage(SysLogQueryRequest sysLogQueryRequest);

    SysLogDetailQueryResult querySystemLogDetailPage(SysLogDetailQueryRequest sysLogDetailQueryRequest);

    SysLogQueryResult allUsersLogPage(SysLogQueryRequest sysLogQueryRequest);

    SysLogQueryResult userLogsPage(SysLogQueryRequest sysLogQueryRequest);

    Map<String, String> getLogDetail(String traceId);

}
