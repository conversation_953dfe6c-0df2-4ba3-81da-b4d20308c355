package com.cmiot.report.service.impl;

import com.cmiot.report.GatewayOldResult;
import com.cmiot.report.bean.gateway.GatewayDelayAll;
import com.cmiot.report.dto.GatewayDelayDeviceResult;
import com.cmiot.report.dto.GatewayOldListResult;
import com.cmiot.report.dto.GatewayQuery;
import com.cmiot.report.mapper.GatewayDelayAllMapper;
import com.cmiot.report.service.GatewayDelayAllService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class GatewayDelayAllServiceImpl implements GatewayDelayAllService {
    @Autowired
    private GatewayDelayAllMapper gatewayDelayAllMapper;

    @Override
    public List<GatewayDelayAll> queryGatewayDelayAll(GatewayQuery query) {
        return gatewayDelayAllMapper.queryGatewayDelayAll(query);
    }

    @Override
    public int countGatewayDelayAll(GatewayQuery query) {
        return gatewayDelayAllMapper.countGatewayDelayAll(query);
    }

    @Override
    public int countGatewayAll(GatewayQuery query) {
        return gatewayDelayAllMapper.countGatewayAll(query);
    }

    @Override
    public List<GatewayOldResult> queryOldGatewayStatistics(GatewayQuery query) {
        return gatewayDelayAllMapper.queryOldGatewayStatistics(query);
    }

    @Override
    public List<GatewayOldListResult> queryOldGatewayList(GatewayQuery query) {
        return gatewayDelayAllMapper.queryOldGatewayList(query);
    }

    @Override
    public Map<String, Object> queryOldGatewayListByPage(GatewayQuery query) {
        Page<GatewayOldListResult> gatewayListResultPage = new Page<>();
        Map<String, Object> result = new HashMap<>();
        try {
            // 设置默认分页参数，避免 null 值导致的异常
            Integer page = query.getPage() != null ? query.getPage() : 1;
            Integer pageSize = query.getPageSize() != null ? query.getPageSize() : 10;

            gatewayListResultPage = PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> gatewayDelayAllMapper.queryOldGatewayList(query));
            result.put("list", gatewayListResultPage.getResult());
            result.put("page", gatewayListResultPage.getPageNum());
            result.put("pageSize", gatewayListResultPage.getPageSize());
            result.put("total", gatewayListResultPage.getTotal());
        } catch (Exception e) {
            // 这里可以添加日志记录
            throw new RuntimeException("老旧网关列表分页查询异常", e);
        }
        return result;
    }

    @Override
    public List<GatewayDelayDeviceResult> queryDelayDevicesBySn(GatewayQuery query) {
        return gatewayDelayAllMapper.queryDelayDevicesBySn(query);
    }
}
