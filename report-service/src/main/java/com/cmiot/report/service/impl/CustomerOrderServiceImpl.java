package com.cmiot.report.service.impl;

import com.cmiot.report.bean.customerOrder.*;
import com.cmiot.report.dto.customerOrder.*;
import com.cmiot.report.mapper.CustomerOrderAllMapper;
import com.cmiot.report.mapper.CustomerOrderPackageMapper;
import com.cmiot.report.mapper.CustomerOrderTrendsMapper;
import com.cmiot.report.service.CustomerOrderService;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.DateSplitUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class CustomerOrderServiceImpl implements CustomerOrderService {

    private static final String ORDER_1171 = "1171";  /*互联网专线*/
    private static final String ORDER_1172 = "1172";  /*精品宽带*/
    private static final String ORDER_1173 = "1173";  /*商务快线*/

    private static final String ORDER_OTHER = "9999";  /*其他*/

    private final static Logger logger = LoggerFactory.getLogger(CustomerOrderServiceImpl.class);

    @Autowired
    private CustomerOrderAllMapper customerOrderAllMapper;

    @Autowired
    private CustomerOrderPackageMapper customerOrderPackageMapper;

    @Autowired
    private CustomerOrderTrendsMapper customerOrderTrendsMapper;

    public static Map<String, String> provinceCodeMap = new HashMap<>();
    static {
        provinceCodeMap.put("110000", "北京市");
        provinceCodeMap.put("120000", "天津市");
        provinceCodeMap.put("130000", "河北省");
        provinceCodeMap.put("140000", "山西省");
        provinceCodeMap.put("150000", "内蒙古自治区");
        provinceCodeMap.put("210000", "辽宁省");
        provinceCodeMap.put("220000", "吉林省");
        provinceCodeMap.put("230000", "黑龙江省");
        provinceCodeMap.put("310000", "上海市");
        provinceCodeMap.put("320000", "江苏省");
        provinceCodeMap.put("330000", "浙江省");
        provinceCodeMap.put("340000", "安徽省");
        provinceCodeMap.put("350000", "福建省");
        provinceCodeMap.put("360000", "江西省");
        provinceCodeMap.put("370000", "山东省");
        provinceCodeMap.put("410000", "河南省");
        provinceCodeMap.put("420000", "湖北省");
        provinceCodeMap.put("430000", "湖南省");
        provinceCodeMap.put("440000", "广东省");
        provinceCodeMap.put("450000", "广西壮族自治区");
        provinceCodeMap.put("460000", "海南省");
        provinceCodeMap.put("500000", "重庆市");
        provinceCodeMap.put("510000", "四川省");
        provinceCodeMap.put("520000", "贵州省");
        provinceCodeMap.put("530000", "云南省");
        provinceCodeMap.put("540000", "西藏自治区");
        provinceCodeMap.put("610000", "陕西省");
        provinceCodeMap.put("620000", "甘肃省");
        provinceCodeMap.put("630000", "青海省");
        provinceCodeMap.put("640000", "宁夏回族自治区");
        provinceCodeMap.put("650000", "新疆维吾尔自治区");
        provinceCodeMap.put("810000", "香港特别行政区");
        provinceCodeMap.put("820000", "澳门特别行政区");
        provinceCodeMap.put("710000", "台湾省");

    }

    @Override
    public OverviewPieChartData customerOrderOverviewPieChart(OverviewPieChartRequest request) {
        OverviewPieChartData chartData = new OverviewPieChartData();
        try {
            String time = DateUtil.getYesterday();
            List<String> provList = new ArrayList<>();
            if(StringUtils.isNotBlank(request.getProvince())){
                provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(request.getProvince(), "\\,"));
            }
            List<OverviewPieChartAll> pieChartAllList = customerOrderAllMapper.queryCustomerOrderOverviewPieChart(time, provList);
            int internetLine = 0;
            int premiumBroadband = 0;
            int businessExpress = 0;
            int other = 0;

            for(OverviewPieChartAll overviewPieChartAll : pieChartAllList){
                if(ORDER_1171.equals(overviewPieChartAll.getBusinessType())){
                    internetLine = overviewPieChartAll.getOrderCount();
                } else if (ORDER_1172.equals(overviewPieChartAll.getBusinessType())){
                    premiumBroadband = overviewPieChartAll.getOrderCount();
                } else if (ORDER_1173.equals(overviewPieChartAll.getBusinessType())){
                    businessExpress = overviewPieChartAll.getOrderCount();
                } else if (ORDER_OTHER.equals(overviewPieChartAll.getBusinessType())){
                    other += overviewPieChartAll.getOrderCount();
                }
            }
            chartData.setInternetLine(internetLine);
            chartData.setPremiumBroadband(premiumBroadband);
            chartData.setBusinessExpress(businessExpress);
            chartData.setOther(other);
        } catch (Exception e){
            logger.error("智慧运营分析-企业订购业务分析-业务量总览饼图查询异常:{}", e.getMessage(), e);
        }
        logger.info("智慧运营分析-企业订购业务分析-业务量总览饼图查询,响应:{}", chartData);
        return chartData;
    }

    @Override
    public List<OverviewHistogramData> customerOrderOverviewHistogram(OverviewHistogramRequest request) {
        List<OverviewHistogramData> resultList = new ArrayList<>();
        try {
            String time = DateUtil.getYesterday();
            List<String> provList = new ArrayList<>();
            if(StringUtils.isNotBlank(request.getProvince())){
                provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(request.getProvince(), "\\,"));
            }
            // 初始化权限范围内的所有省的数据
            Map<String, OverviewHistogramData> tempMap = new HashMap<>();
            OverviewHistogramData overviewHistogramData;
            for(String provCode : provList){
                overviewHistogramData = new OverviewHistogramData();
                overviewHistogramData.setProvince(provinceCodeMap.get(provCode));
                overviewHistogramData.setOther(0);
                overviewHistogramData.setInternetLine(0);
                overviewHistogramData.setBusinessExpress(0);
                overviewHistogramData.setPremiumBroadband(0);
                tempMap.put(provCode, overviewHistogramData);
            }
            List<OverviewHistogramAll> histogramAllList = customerOrderAllMapper.queryCustomerOrderOverviewHistogram(time, provList);
            for(OverviewHistogramAll overviewHistogramAll : histogramAllList){
                String provinceCode = overviewHistogramAll.getProvinceCode();
                OverviewHistogramData tempData = tempMap.get(provinceCode);
                if(ORDER_1171.equals(overviewHistogramAll.getBusinessType())){
                    tempData.setInternetLine(overviewHistogramAll.getOrderCount());
                } else if (ORDER_1172.equals(overviewHistogramAll.getBusinessType())){
                    tempData.setPremiumBroadband(overviewHistogramAll.getOrderCount());
                } else if (ORDER_1173.equals(overviewHistogramAll.getBusinessType())){
                    tempData.setBusinessExpress(overviewHistogramAll.getOrderCount());
                } else if (ORDER_OTHER.equals(overviewHistogramAll.getBusinessType())){
                    int other = tempData.getOther();
                    other += overviewHistogramAll.getOrderCount();
                    tempData.setOther(other);
                }
            }

            for(Map.Entry<String, OverviewHistogramData> entry : tempMap.entrySet()){
                resultList.add(entry.getValue());
            }
        } catch (Exception e) {
            logger.error("智慧运营分析-企业订购业务分析-业务量总览柱状图查询异常:{}", e.getMessage(), e);
        }
        logger.info("智慧运营分析-企业订购业务分析-业务量总览柱状图,响应:{}", resultList);
        return resultList;
    }

    @Override
    public List<OrderTrendLineChartData> customerOrderTrendLineChart(OrderTrendLineChartRequest request) {
        List<OrderTrendLineChartData> OrderTrendLineChartDataList = new ArrayList<>();
        try {
            List<String> provList = request.getProvince();
            // 获取需要查询的时间段
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String start = request.getStartTime();
            String end = request.getEndTime();
            List<DateRange> timeList = new ArrayList<>();
            DateRange dateRange;
            switch (request.getTimeType()){
                case 1:
                    // 日粒度
                    List<String> dayTimeList = DateUtil.splitDateList(start, end);
                    for(String s : dayTimeList){
                        dateRange = new DateRange();
                        dateRange.setStartTime(s.concat(" 00:00:00"));
                        dateRange.setStartTimeInt(Integer.parseInt(s.replaceAll("-", "")));
                        dateRange.setEndTime(s.concat(" 23:59:59"));
                        dateRange.setEndTimeInt(Integer.parseInt(s.replaceAll("-", "")));
                        timeList.add(dateRange);
                    }
                    break;
                case 2:
                    // 周粒度
                    List<DateSplitUtil.Range> weekRangeList = DateSplitUtil.splitToWeeks(start, end);
                    for(DateSplitUtil.Range range : weekRangeList){
                        dateRange = new DateRange();
                        dateRange.setStartTime(format.format(range.getStart()).concat(" 00:00:00"));
                        dateRange.setStartTimeInt(Integer.parseInt(format.format(range.getStart()).replaceAll("-", "")));
                        dateRange.setEndTime(format.format(range.getEnd()).concat(" 23:59:59"));
                        dateRange.setEndTimeInt(Integer.parseInt(format.format(range.getEnd()).replaceAll("-", "")));
                        timeList.add(dateRange);
                    }
                    break;
                case 3:
                    // 月粒度
                    List<DateSplitUtil.Range> monthRangeList = DateSplitUtil.splitToMonths(start, end);
                    for(DateSplitUtil.Range range : monthRangeList){
                        dateRange = new DateRange();
                        dateRange.setStartTime(format.format(range.getStart()).concat(" 00:00:00"));
                        dateRange.setStartTimeInt(Integer.parseInt(format.format(range.getStart()).replaceAll("-", "")));
                        dateRange.setEndTime(format.format(range.getEnd()).concat(" 23:59:59"));
                        dateRange.setEndTimeInt(Integer.parseInt(format.format(range.getEnd()).replaceAll("-", "")));
                        timeList.add(dateRange);
                    }
                    break;
                default:
                    // 不支持的查询类型
                    logger.warn("不支持的时间类型:{}", request.getTimeType());
            }

            logger.info("业务增长趋势查询时间范围:{}", timeList);

            switch (request.getFilterCriteria()){
                case 1:
                    // 增量
                    for(DateRange range : timeList){
                        OrderTrendLineChartData orderTrendLineChartData = new OrderTrendLineChartData();
                        orderTrendLineChartData.setInternetLine(0);
                        orderTrendLineChartData.setOther(0);
                        orderTrendLineChartData.setBusinessExpress(0);
                        orderTrendLineChartData.setPremiumBroadband(0);
                        // 根据日,周,月粒度返回不同的时间
                        if(1 == request.getTimeType()){
                            orderTrendLineChartData.setTime(DateUtil.stringToDateString(range.getStartTime(),
                                    DateUtil.DATE_TIME_PATTERN, DateUtil.MONTH_DAY));
                        } else if(2 == request.getTimeType()){
                            orderTrendLineChartData.setTime(DateUtil.getWeekOfYear(range.getStartTime(),
                                    DateUtil.DATE_TIME_PATTERN));
                        } else if(3 == request.getTimeType()){
                            orderTrendLineChartData.setTime(DateUtil.stringToDateString(range.getStartTime(),
                                    DateUtil.DATE_TIME_PATTERN, DateUtil.YEAR_MONTH));
                        }

                        List<OrderTrendLineChartAll> trendLineChartAllList = customerOrderTrendsMapper.queryCustomerOrderTrendLineChart(
                                provList, 1, range.getStartTimeInt(), range.getEndTimeInt());
                        if(null != trendLineChartAllList && !trendLineChartAllList.isEmpty()){
                            int other = 0;
                            for(OrderTrendLineChartAll trendLineChartAll : trendLineChartAllList){
                                int orderCount = trendLineChartAll.getOrderCount();
                                String businessType = trendLineChartAll.getBusinessType();
                                if(ORDER_1171.equals(businessType)){
                                    orderTrendLineChartData.setInternetLine(orderCount);
                                } else if(ORDER_1172.equals(businessType)){
                                    orderTrendLineChartData.setPremiumBroadband(orderCount);
                                } else if(ORDER_1173.equals(businessType)){
                                    orderTrendLineChartData.setBusinessExpress(orderCount);
                                }else {
                                    other += orderCount;
                                }
                            }
                            orderTrendLineChartData.setOther(other);
                        }

                        OrderTrendLineChartDataList.add(orderTrendLineChartData);
                    }
                    break;
                case 2:
                    // 减量
                    for(DateRange range : timeList){
                        OrderTrendLineChartData orderTrendLineChartData = new OrderTrendLineChartData();
                        orderTrendLineChartData.setInternetLine(0);
                        orderTrendLineChartData.setOther(0);
                        orderTrendLineChartData.setBusinessExpress(0);
                        orderTrendLineChartData.setPremiumBroadband(0);
                        // 根据日,周,月粒度返回不同的时间
                        if(1 == request.getTimeType()){
                            orderTrendLineChartData.setTime(DateUtil.stringToDateString(range.getStartTime(),
                                    DateUtil.DATE_TIME_PATTERN, DateUtil.MONTH_DAY));
                        } else if(2 == request.getTimeType()){
                            orderTrendLineChartData.setTime(DateUtil.getWeekOfYear(range.getStartTime(),
                                    DateUtil.DATE_TIME_PATTERN));
                        } else if(3 == request.getTimeType()){
                            orderTrendLineChartData.setTime(DateUtil.stringToDateString(range.getStartTime(),
                                    DateUtil.DATE_TIME_PATTERN, DateUtil.YEAR_MONTH));
                        }
                        List<OrderTrendLineChartAll> trendLineChartAllList = customerOrderTrendsMapper.queryCustomerOrderTrendLineChart(
                                provList, 2, range.getStartTimeInt(), range.getEndTimeInt());
                        if(null != trendLineChartAllList && !trendLineChartAllList.isEmpty()){
                            int other = 0;
                            for(OrderTrendLineChartAll trendLineChartAll : trendLineChartAllList){
                                int orderCount = trendLineChartAll.getOrderCount();
                                String businessType = trendLineChartAll.getBusinessType();
                                if(ORDER_1171.equals(businessType)){
                                    orderTrendLineChartData.setInternetLine(orderCount);
                                } else if(ORDER_1172.equals(businessType)){
                                    orderTrendLineChartData.setPremiumBroadband(orderCount);
                                } else if(ORDER_1173.equals(businessType)){
                                    orderTrendLineChartData.setBusinessExpress(orderCount);
                                }else {
                                    other += orderCount;
                                }
                            }
                            orderTrendLineChartData.setOther(other);
                        }
                        OrderTrendLineChartDataList.add(orderTrendLineChartData);
                    }
                    break;
                case 3:
                    // 净增量
                    for(DateRange range : timeList){
                        OrderTrendLineChartData orderTrendLineChartData = new OrderTrendLineChartData();
                        orderTrendLineChartData.setInternetLine(0);
                        orderTrendLineChartData.setOther(0);
                        orderTrendLineChartData.setBusinessExpress(0);
                        orderTrendLineChartData.setPremiumBroadband(0);
                        // 根据日,周,月粒度返回不同的时间
                        if(1 == request.getTimeType()){
                            orderTrendLineChartData.setTime(DateUtil.stringToDateString(range.getStartTime(),
                                    DateUtil.DATE_TIME_PATTERN, DateUtil.MONTH_DAY));
                        } else if(2 == request.getTimeType()){
                            orderTrendLineChartData.setTime(DateUtil.getWeekOfYear(range.getStartTime(),
                                    DateUtil.DATE_TIME_PATTERN));
                        } else if(3 == request.getTimeType()){
                            orderTrendLineChartData.setTime(DateUtil.stringToDateString(range.getStartTime(),
                                    DateUtil.DATE_TIME_PATTERN, DateUtil.YEAR_MONTH));
                        }
                        // 订购
                        List<OrderTrendLineChartAll> subscribeAllList = customerOrderTrendsMapper.queryCustomerOrderTrendLineChart(
                                provList, 1, range.getStartTimeInt(), range.getEndTimeInt());
                        // 退订
                        List<OrderTrendLineChartAll> unSubscribeAllList = customerOrderTrendsMapper.queryCustomerOrderTrendLineChart(
                                provList, 2, range.getStartTimeInt(), range.getEndTimeInt());
                        int subscribeInternetLine = 0;
                        int subscribePremiumBroadband = 0;
                        int subscribeBusinessExpress = 0;
                        int subscribeOther = 0;
                        if(null != subscribeAllList && !subscribeAllList.isEmpty()){
                            for(OrderTrendLineChartAll trendLineChartAll : subscribeAllList){
                                int orderCount = trendLineChartAll.getOrderCount();
                                String businessType = trendLineChartAll.getBusinessType();
                                if(ORDER_1171.equals(businessType)){
                                    subscribeInternetLine = orderCount;
                                } else if(ORDER_1172.equals(businessType)){
                                    subscribePremiumBroadband = orderCount;
                                } else if(ORDER_1173.equals(businessType)){
                                    subscribeBusinessExpress = orderCount;
                                }else {
                                    subscribeOther += orderCount;
                                }
                            }
                        }

                        int unSubscribeInternetLine = 0;
                        int unSubscribePremiumBroadband = 0;
                        int unSubscribeBusinessExpress = 0;
                        int unSubscribeOther = 0;
                        if(null != unSubscribeAllList && !unSubscribeAllList.isEmpty()){
                            for(OrderTrendLineChartAll trendLineChartAll : unSubscribeAllList){
                                int orderCount = trendLineChartAll.getOrderCount();
                                String businessType = trendLineChartAll.getBusinessType();
                                if(ORDER_1171.equals(businessType)){
                                    unSubscribeInternetLine = orderCount;
                                } else if(ORDER_1172.equals(businessType)){
                                    unSubscribePremiumBroadband = orderCount;
                                } else if(ORDER_1173.equals(businessType)){
                                    unSubscribeBusinessExpress = orderCount;
                                }else {
                                    unSubscribeOther += orderCount;
                                }
                            }
                        }
                        orderTrendLineChartData.setInternetLine(subscribeInternetLine - unSubscribeInternetLine);
                        orderTrendLineChartData.setPremiumBroadband(subscribePremiumBroadband - unSubscribePremiumBroadband);
                        orderTrendLineChartData.setBusinessExpress(subscribeBusinessExpress - unSubscribeBusinessExpress);
                        orderTrendLineChartData.setOther(subscribeOther - unSubscribeOther);
                        OrderTrendLineChartDataList.add(orderTrendLineChartData);
                    }
                    break;
                default:
                    // 不支持的查询类型
                    logger.warn("不支持的查询类型:{}", request.getFilterCriteria());
            }
            logger.info("智慧运营分析-企业订购业务分析-业务发展趋势,响应:{}", OrderTrendLineChartDataList);
        } catch (Exception e){
            logger.error("智慧运营分析-企业订购业务分析-业务发展趋势,异常:{}", e.getMessage(), e);
        }
        return OrderTrendLineChartDataList;
    }

    @Override
    public Map<String, Object> cumulativePackageSubscription(PackageSubscriptionRequest request) {
        Map<String, Object> resultMap = new HashMap<>();
        int totalNum = 0;
        List<PackageSubscriptionData> resultList = new ArrayList<>();
        try {
            List<String> provList =  request.getProvince();
            // 初始化区域数据
            Map<String, PackageSubscriptionData> tempMap = new HashMap<>();
            PackageSubscriptionData packageSubscriptionData;
            for(String provinceCode : provList){
                packageSubscriptionData = new PackageSubscriptionData();
                packageSubscriptionData.setSubscriptionNum(0);
                packageSubscriptionData.setSubscriptionRatio("0.00");
                packageSubscriptionData.setProvinceName(provinceCodeMap.get(provinceCode));
                tempMap.put(provinceCode, packageSubscriptionData);
            }
            List<OrderPackageCountAll> orderPackageCountAllList = customerOrderPackageMapper.queryCustomerOrderPackageCount(
                    provList, request.getOderType(), Integer.parseInt(request.getEndTime().replaceAll("-", "")),
                    Integer.parseInt(request.getEndTime().replaceAll("-", "")));
            for(OrderPackageCountAll orderPackageCountAll : orderPackageCountAllList){
                String provinceCode = orderPackageCountAll.getProvince();
                PackageSubscriptionData tempData = tempMap.get(provinceCode);
                if(null != tempData){
                    tempData.setSubscriptionNum(orderPackageCountAll.getSubscriptionNum());
                    tempData.setSubscriptionRatio(orderPackageCountAll.getSubscriptionRatio() + "");
                    totalNum += orderPackageCountAll.getSubscriptionNum();
                    resultList.add(tempData);
                    tempMap.remove(provinceCode);
                }
            }

            if(!tempMap.isEmpty()){
                for(Map.Entry<String, PackageSubscriptionData> entry : tempMap.entrySet()){
                    resultList.add(entry.getValue());
                }
            }
            resultMap.put("totalNum", totalNum);
            resultMap.put("data", resultList);
        } catch (Exception e){
            logger.info("智慧运营分析-企业订购业务分析-累计套餐订购情况查询异常:{}", e.getMessage(), e);
        }
        logger.info("智慧运营分析-企业订购业务分析-累计套餐订购情况, 响应:{}", resultMap);
        return resultMap;
    }

    @Override
    public Map<String, Object> newPackageSubscription(PackageSubscriptionRequest request) {
        Map<String, Object> resultMap = new HashMap<>();
        List<PackageSubscriptionData> resultList = new ArrayList<>();
        int totalNum = 0;
        try {
            String startTime = request.getStartTime();
            String endTime = request.getEndTime();
            List<String> provList =  request.getProvince();
            // 初始化区域数据
            Map<String, PackageSubscriptionData> tempMap = new HashMap<>();
            PackageSubscriptionData packageSubscriptionData;
            for(String provinceCode : provList){
                packageSubscriptionData = new PackageSubscriptionData();
                packageSubscriptionData.setSubscriptionNum(0);
                packageSubscriptionData.setSubscriptionRatio("0.00");
                packageSubscriptionData.setProvinceName(provinceCodeMap.get(provinceCode));
                tempMap.put(provinceCode, packageSubscriptionData);
            }
            // 先查询全量
            int addCount = customerOrderPackageMapper.queryCustomerOrderPackageAddSum(provList, request.getOderType(),
                    Integer.parseInt(startTime.replaceAll("-", "")),
                    Integer.parseInt(endTime.replaceAll("-", "")));
            List<OrderPackageAddAll> orderPackageAddAllList = customerOrderPackageMapper.queryCustomerOrderPackageAddNum(
                    provList, request.getOderType(), Integer.parseInt(startTime.replaceAll("-", "")),
                    Integer.parseInt(endTime.replaceAll("-", "")));
            for(OrderPackageAddAll orderPackageAddAll : orderPackageAddAllList){
                String provinceCode = orderPackageAddAll.getProvince();
                PackageSubscriptionData tempData = tempMap.get(provinceCode);
                if(null != tempData){
                    tempData.setSubscriptionNum(orderPackageAddAll.getSubscriptionNum());
                    tempData.setSubscriptionRatio(getSubscriptionRatio(orderPackageAddAll.getSubscriptionNum(), addCount));
                    totalNum += orderPackageAddAll.getSubscriptionNum();
                    resultList.add(tempData);
                    tempMap.remove(provinceCode);
                }
            }

            if(!tempMap.isEmpty()){
                for(Map.Entry<String, PackageSubscriptionData> entry : tempMap.entrySet()){
                    resultList.add(entry.getValue());
                }
            }
            resultMap.put("totalNum", totalNum);
            resultMap.put("data", resultList);
        } catch (Exception e){
            logger.info("智慧运营分析-企业订购业务分析-新增套餐订购情况查询异常:{}", e.getMessage(), e);
        }
        logger.info("智慧运营分析-企业订购业务分析-新增套餐订购情况, 响应:{}", resultMap);
        return resultMap;
    }

    @Override
    public Map<String, Object> SinglePackageSubscription(PackageSubscriptionRequestExtend request) {
        Map<String, Object> resultMap = new HashMap<>();
        List<PackageSubscriptionTopData> resultList = new ArrayList<>();
        List<String> currentProv = new ArrayList<>(request.getProvince());
        try {
            int start = Integer.parseInt(request.getStartTime().replaceAll("-", ""));
            int end = Integer.parseInt(request.getEndTime().replaceAll("-", ""));
            Integer addType = request.getAddType();
            List<OrderPackageTop3All> topAllList;
            if(1 == addType){
                topAllList = customerOrderPackageMapper.queryOrderCountPackageTopN(
                        currentProv, request.getOderType(), start, end);
            } else {
                topAllList = customerOrderPackageMapper.queryOrderAddPackageTopN(
                        currentProv, request.getOderType(), start, end);
            }
            int totalNum = 0;
            if(null != topAllList && !topAllList.isEmpty()){
                for(OrderPackageTop3All orderTop : topAllList){
                    Integer addNum = orderTop.getAddNum();
                    totalNum += addNum;
                }
                for(OrderPackageTop3All orderTop : topAllList){
                    PackageSubscriptionTopData data = new PackageSubscriptionTopData();
                    // 过滤为0的
                    String packageCode = orderTop.getPackageCode();
                    Integer addNum = orderTop.getAddNum();
                    if(0 != addNum){
                        String percent = getSubscriptionRatio(addNum, totalNum);
                        data.setPackageCode(packageCode);
                        data.setNum(addNum);
                        data.setPercent(percent);
                        resultList.add(data);
                    }
                }
            }
            resultMap.put("totalNum", totalNum);
            resultMap.put("data", resultList);
        } catch (Exception e) {
            logger.error("智慧运营分析-企业订购业务分析-单个省份累计/新增套餐查询, 异常:{}", e.getMessage(), e);
        }
        logger.info("智慧运营分析-企业订购业务分析-单个省份累计/新增套餐查询, 响应:{}", resultMap);
        return resultMap;
    }

    /**
     * 套餐增长趋势折线图
     * @param request
     * @return
     */
    @Override
    public List<PackageGrowthTrendLineChartData> packageGrowthTrendLineChart(PackageGrowthTrendRequest request) {
        List<PackageGrowthTrendLineChartData> resultList = new ArrayList<>();
        try {
            List<String> provList = request.getProvince();
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String start = request.getStartTime();
            String end = request.getEndTime();
            List<DateRange> timeList = new ArrayList<>();
            DateRange dateRange;
            List<DateSplitUtil.Range> monthRangeList = DateSplitUtil.splitToMonths(start, end);
            // 初始化月份列表
            for(DateSplitUtil.Range range : monthRangeList){
                dateRange = new DateRange();
                dateRange.setStartTime(format.format(range.getStart()).concat(" 00:00:00"));
                dateRange.setStartTimeInt(Integer.parseInt(format.format(range.getStart()).replaceAll("-", "")));
                dateRange.setEndTime(format.format(range.getEnd()).concat(" 23:59:59"));
                dateRange.setEndTimeInt(Integer.parseInt(format.format(range.getEnd()).replaceAll("-", "")));
                timeList.add(dateRange);
            }
            logger.info("业务增长趋势查询时间范围:{}", timeList);

            for(DateRange range : timeList){
                PackageGrowthTrendLineChartData data = new PackageGrowthTrendLineChartData();
                data.setTime(DateUtil.stringToDateString(range.getStartTime(),
                        DateUtil.DATE_TIME_PATTERN, DateUtil.YEAR_MONTH));
                data.setValue(0);
                data.setFrontByThird(Collections.emptyList());
                OrderPackageAddAll packageAddAll = customerOrderPackageMapper.queryCustomerOrderPackageAddTrend(
                        provList, 1, range.getStartTimeInt(), range.getEndTimeInt());
                if(null != packageAddAll){
                    data.setValue(packageAddAll.getSubscriptionNum());
                }
                // 查询套餐订购top3
                List<OrderPackageTop3All> top3AllList = customerOrderPackageMapper.queryOrderPackageTop3(
                        provList, 1, range.getStartTimeInt(), range.getEndTimeInt());
                List<PackageGrowthTop> growthTops = new ArrayList<>();
                if(null != top3AllList && !top3AllList.isEmpty()){
                    for(OrderPackageTop3All top3All : top3AllList){
                        PackageGrowthTop top = new PackageGrowthTop();
                        top.setProvince(provinceCodeMap.get(top3All.getProvince()));
                        top.setPackageNum(top3All.getAddNum());
                        top.setPackageName(top3All.getPackageCode());
                        growthTops.add(top);
                    }
                }
                data.setFrontByThird(growthTops);
                resultList.add(data);
            }
        } catch (Exception e){
            logger.info("智慧运营分析-企业订购业务分析-套餐增长趋势折线图查询异常:{}", e.getMessage(), e);
        }
        logger.info("智慧运营分析-企业订购业务分析-套餐增长趋势折线图, 响应:{}", resultList);
        return resultList;
    }

    /**
     * 套餐增长趋势柱状图
     * @param request
     * @return
     */
    @Override
    public List<PackageGrowthTrendHistogramData> packageGrowthTrendHistogram(PackageGrowthTrendRequest request) {
        List<PackageGrowthTrendHistogramData> resultList = new ArrayList<>();
        try {
            List<String> provList =  request.getProvince();
            // 初始化区域数据
            Map<String, PackageGrowthTrendHistogramData> tempMap = new HashMap<>();
            PackageGrowthTrendHistogramData data;
            for(String provinceCode : provList){
                data = new PackageGrowthTrendHistogramData();
                data.setAreaName(provinceCodeMap.get(provinceCode));
                data.setValue(0);
                data.setFrontByThird(Collections.emptyList());
                tempMap.put(provinceCode, data);
            }
            int start = Integer.parseInt(request.getStartTime().replaceAll("-", ""));
            int end = Integer.parseInt(request.getEndTime().replaceAll("-", ""));

            List<OrderPackageAddAll> orderPackageAddAllList = customerOrderPackageMapper.queryCustomerOrderPackageAddTrendByArea(
                    provList, 1, start, end);
            for(OrderPackageAddAll orderPackageAdd : orderPackageAddAllList){
                String provinceCode = orderPackageAdd.getProvince();
                PackageGrowthTrendHistogramData tempData = tempMap.get(provinceCode);
                if(null != tempData){
                    tempData.setAreaName(provinceCodeMap.get(provinceCode));
                    tempData.setValue(orderPackageAdd.getSubscriptionNum());
                    // 查询当前省份套餐订购top3
                    List<String> currentProv = new ArrayList<>();
                    currentProv.add(orderPackageAdd.getProvince());
                    List<OrderPackageTop3All> top3AllList = customerOrderPackageMapper.queryOrderPackageTop3(
                            currentProv, 1, start, end);
                    List<PackageGrowthTop> growthTops = new ArrayList<>();
                    if(null != top3AllList && !top3AllList.isEmpty()){
                        for(OrderPackageTop3All top3All : top3AllList){
                            PackageGrowthTop top = new PackageGrowthTop();
                            top.setPackageNum(top3All.getAddNum());
                            top.setPackageName(top3All.getPackageCode());
                            growthTops.add(top);
                        }
                    }
                    tempData.setFrontByThird(growthTops);
                    resultList.add(tempData);
                    tempMap.remove(provinceCode);
                }
            }

            if(!tempMap.isEmpty()){
                for(Map.Entry<String, PackageGrowthTrendHistogramData> entry : tempMap.entrySet()){
                    resultList.add(entry.getValue());
                }
            }
        } catch (Exception e){
            logger.info("智慧运营分析-企业订购业务分析-套餐增长趋势柱状图查询异常:{}", e.getMessage(), e);
        }
        logger.info("智慧运营分析-企业订购业务分析-套餐增长趋势柱状图, 响应:{}", resultList);
        return resultList;
    }

    @Override
    public List<BandwidthStatisticalData> bandwidthStatisticalAnalysis(BandwidthStatisticalRequest request) {
        List<BandwidthStatisticalData> resultList = new ArrayList<>();
        try {
            String time = DateUtil.getYesterday();
            List<String> provList = request.getProvince();
            String businessType = request.getBusinessType();
            List<OrderBandwidthAll> orderBandwidthAllList = customerOrderAllMapper.queryBandwidthStatistical(time, provList, businessType);
            if(null != orderBandwidthAllList && !orderBandwidthAllList.isEmpty()){
                for(OrderBandwidthAll bandwidthAll : orderBandwidthAllList){
                    BandwidthStatisticalData data = new BandwidthStatisticalData();
                    Integer bandwidth = bandwidthAll.getBandwidth();
                    // 除以1024,KB转换成MB
                    if(null == bandwidth){
                        data.setBandwidth("0M");
                    } else {
                        data.setBandwidth((bandwidth / 1024) + "M");
                    }
                    data.setBandwidthNum(bandwidthAll.getBandwidthNum());
                    resultList.add(data);
                }
            }
        } catch (Exception e){
            logger.info("智慧运营分析-企业订购业务分析-带宽统计分析,异常:{}", e.getMessage(), e);
        }
        logger.info("智慧运营分析-企业订购业务分析-带宽统计分析,响应:{}", resultList);
        return resultList;
    }


    private String getSubscriptionRatio(Integer add, Integer sum) {
        String subscriptionRatio = "0.00";
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(2);
        if (null == add || null == sum || 0 == add || 0 == sum) {
            subscriptionRatio = "0.00";
        } else {
            subscriptionRatio = numberFormat.format(
                    (float) add / (float) sum * 100);
        }
        return subscriptionRatio;
    }
}
