package com.cmiot.report.service;

import java.util.List;
import java.util.Map;

/**
 * @Classname DicDataService
 * @Description
 * @Date 2022/8/9 17:19
 * @Created by lei
 */
public interface DicDataService {
    Map<String, String> getDicAreaMap();
    Map<String, String> getDictCityMap();

    Map<String, String> getDicAreaMap(List<String> provCodeList);

    Map<String, String> getDicCityMap(List<String> cityCodeList);

    Map<String, String> getDictCityMap(List<String> provCodeList);

    Map<String, String> getDicCityMap();

    Map<String, String> getDicFactoryMap();

    Map<Long, String> getDicFactoryModelMap();

    Map<Long, String> getDictFactoryModelMap(List<Long> factidsList);

    Map<Long, String> getDicFactoryModelMap(List<Long> list);

    Map<String, String> getDictFactoryNameMap();

    Map<String, Long> getDictFactoryCodeIdsMap();

    Map<String, String> getDictFactoryCodeByIds(List<Long> list);

    Map<String, String> getDictIndustryMap();
    Map<String, String> getDictIndustryMap(List<String> list);

    List<String> getDicFactoryCodeListById(List<String> vendorIdList);

    List<String> getDicFactoryIdCodeMap(List<String> vendorIdList);
}
