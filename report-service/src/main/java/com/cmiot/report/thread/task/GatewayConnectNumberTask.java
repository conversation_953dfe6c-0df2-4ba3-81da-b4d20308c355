package com.cmiot.report.thread.task;

import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.fgeo.taskmanager.enums.ExportStatusEnum;
import com.cmiot.report.common.poi.ExcelUtils;
import com.cmiot.report.dto.GateWayConnectNumberResult;
import com.cmiot.report.dto.GatewayQuery;
import com.cmiot.report.service.ExportService;
import com.cmiot.report.service.GatewayService;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
public class GatewayConnectNumberTask implements Runnable {

    private GatewayQuery gatewayQuery;

    private GatewayService gatewayService;

    private String tmpPath;

    private Integer usePriUrl;

    private ExportService exportService;

    private ExportInfoDto record;


    public GatewayConnectNumberTask(GatewayQuery gatewayQuery, GatewayService gatewayService, String tmpPath, Integer usePriUrl, ExportService exportService, ExportInfoDto record) {
        this.gatewayQuery = gatewayQuery;
        this.gatewayService = gatewayService;
        this.tmpPath = tmpPath;
        this.usePriUrl = usePriUrl;
        this.exportService = exportService;
        this.record = record;
    }


    @Override
    public void run() {

        log.info("导出网关连接数列表明细 start ... ");

        String localFilePath = tmpPath + "/" + record.getExportName() + ".xlsx";
        log.info("导出网关连接数列表明细 localFilePath : {} ", localFilePath);

        // 生成Excel数据文件
        try {
            List<GateWayConnectNumberResult> gatewayConnectNumberList = gatewayService.getGatewayConnectNumberList(gatewayQuery);
            for (GateWayConnectNumberResult gateWayConnectNumberResult : gatewayConnectNumberList) {
                String statisticalPeriod = gatewayQuery.getStartTime() + "至" + gatewayQuery.getEndTime();
                gateWayConnectNumberResult.setStatisticalPeriod(statisticalPeriod);
            }
            ExcelUtils.writeExcel(gatewayConnectNumberList, GateWayConnectNumberResult.class, localFilePath);
        } catch (Exception e) {
            log.error("导出网关连接数列表明细 Exception : {}", e.getMessage(), e);
        }


        // 上传文件
        String downLoadUrl = "";
        String failedDesc = "SUCCESS";
        try {
            Map<?, ?> uploadFile = exportService.uploadFile(record.getCreatorId(), new File(localFilePath));
            // 获取内网地址
            if(usePriUrl != null && usePriUrl.intValue() == 1) {
                if(uploadFile.containsKey("privateUrl")) {
                    downLoadUrl = uploadFile.get("privateUrl").toString();
                } else {
                    failedDesc = "上传文件响应无内网地址";
                }
            } else {
                if(uploadFile.containsKey("internetUrl")) {
                    downLoadUrl = uploadFile.get("internetUrl").toString();
                } else {
                    failedDesc = "上传文件响应无公网地址";
                }
            }
            log.info("导出网关连接数列表明细 downLoadUrl : {}, failedDesc : {} ", downLoadUrl, failedDesc);
        } catch (IOException e) {
            failedDesc = "文件上传异常:" + e.getMessage();
            log.warn("导出网关连接数列表明细 file : {} IOException : {}", localFilePath, e.getMessage());
        }

        // 上传记录设置
        record.setFailedDesc(failedDesc);
        record.setDownloadUrl(downLoadUrl);
        record.setStatus("SUCCESS".equalsIgnoreCase(failedDesc) ? ExportStatusEnum.SUCCESS : ExportStatusEnum.FAILED);
        Integer updateRecord = exportService.updateRecord(record);

        log.info("导出网关连接数列表明细 updateRecord : {} ... ", updateRecord);

    }
}
