package com.cmiot.report.thread.task;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;

import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.fgeo.taskmanager.enums.ExportStatusEnum;
import com.cmiot.report.InitSystemConfig;
import com.cmiot.report.bean.CustomerAll;
import com.cmiot.report.bean.CustomerAllExample;
import com.cmiot.report.bean.GatewayPeriodAll;
import com.cmiot.report.bean.GatewayPeriodAllExample;
import com.cmiot.report.mapper.CustomerAllMapper;
import com.cmiot.report.mapper.GatewayPeriodAllMapper;
import com.cmiot.report.service.ExportService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PppoeExportTask implements Runnable {
	/**
	 * 导出工具
	 */
	private ExportService exportService;
	
	/**
	 * 数据查询工具
	 */
	private GatewayPeriodAllMapper gatewayPeriodAllMapper;
	
	/**
	 * 数据查询条件
	 */
	private GatewayPeriodAllExample example;
	
	/**
	 * 导出记录
	 */
	private ExportInfoDto record;
	
	/**
	 * 文件临时目录
	 */
	private String tmpPath;
	
	/**
	 * 1 使用内网地址，否则使用外网地址
	 */
	private Integer usePriUrl;
	
	/**
	 * 企业信息查询。
	 */
	private CustomerAllMapper customerAllMapper;
	
	/**
	 * 缓存企业名称
	 */
	public static final Map<Long, String> CUSTOMER_NAME_CACHE = new HashMap<>();
	
	public PppoeExportTask(
			CustomerAllMapper customerAllMapper,
			GatewayPeriodAllMapper gatewayPeriodAllMapper, 
			ExportService exportService, 
			ExportInfoDto record, 
			GatewayPeriodAllExample example, 
			String tmpPath,
			Integer usePriUrl) {
		this.customerAllMapper = customerAllMapper;
		this.exportService = exportService;
		this.gatewayPeriodAllMapper = gatewayPeriodAllMapper;
		this.example = example;
		this.record = record;
		this.tmpPath = tmpPath;
		this.usePriUrl = usePriUrl;
	}
	
	@Override
	public void run() {
		log.info("PppoeExportTask start ... ");
		CUSTOMER_NAME_CACHE.clear();
		
		// 数据文件
		File dataFile = new File(tmpPath + "/" + record.getExportName() + ".csv");
		// 写入标题行
		try {
			List<String> titleList = new ArrayList<>();
			titleList.add("省份,地市,企业名称,网关SN,网关Mac,厂商,型号,累计运行时长(秒),采样时间,PPPOE拨号失败原因,PPPOE拨号失败原因描述");
			FileUtils.writeLines(dataFile, titleList, true);
		} catch (IOException e1) {
			log.info("PppoeExportTask writeLines titleList IOException : {}", e1.getMessage(), e1);
		}
		
		// 一页一页导出
		int page = 1;
		int pageSize = 10000;
		// 获取总数据量
		long total = gatewayPeriodAllMapper.countByExample(example);
		log.info("PppoeExportTask total : {}, page : {}, pageSize : {}", total, page, pageSize);
		
		// 循环分页查询数据
		while(total > 0) {
			log.info("PppoeExportTask total : {}, page : {}, pageSize : {}", total, page, pageSize);
			// 分页查询
			Page<GatewayPeriodAll> doSelectPage = PageHelper.startPage(page, pageSize)
	        		.doSelectPage(() -> gatewayPeriodAllMapper.selectByExample(example));
			List<GatewayPeriodAll> gpaInfoList = doSelectPage.getResult();
			
			// 加载企业名称信息
			loadCustomerName(gpaInfoList.stream().mapToLong(x -> x.getEnterpriseId()).boxed().collect(Collectors.toSet()));
			
			// 组织数据
	        List<String> collect = gpaInfoList.stream()
	        		.filter(x -> x != null)
	        		.map(x -> from(x))
	        		.collect(Collectors.toList());
	        // 数据写入文件
	        try {
				FileUtils.writeLines(dataFile, collect, true);
				log.info("PppoeExportTask collect.size : {} ", collect.size());
			} catch (IOException e) {
				log.warn("writeLines to file : {} IOException : {}", dataFile.getName(), e.getMessage());
			}
	        // 查询下一页
	        total -= pageSize;
	        page++;
		}
		
        // 上传文件
		String downLoadUrl = "";
		String failedDesc = "SUCCESS";
		try {
			Map<?, ?> uploadFile = exportService.uploadFile(record.getCreatorId(), dataFile);
			// 获取内网地址
			if(usePriUrl != null && usePriUrl.intValue() == 1) {
				if(uploadFile.containsKey("privateUrl")) {
					downLoadUrl = uploadFile.get("privateUrl").toString();
				} else {
					failedDesc = "上传文件响应无内网地址";
				}
			} else {
				if(uploadFile.containsKey("internetUrl")) {
					downLoadUrl = uploadFile.get("internetUrl").toString();
				} else {
					failedDesc = "上传文件响应无公网地址";
				}
			}
			log.info("PppoeExportTask downLoadUrl : {}, failedDesc : {} ", downLoadUrl, failedDesc);
		} catch (IOException e) {
			failedDesc = "文件上传异常:" + e.getMessage();
			log.warn("uploadFile file : {} IOException : {}", dataFile.getName(), e.getMessage());
		}
		
		// 上传记录设置
		record.setFailedDesc(failedDesc);
		record.setDownloadUrl(downLoadUrl);
		record.setStatus("SUCCESS".equalsIgnoreCase(failedDesc) ? ExportStatusEnum.SUCCESS : ExportStatusEnum.FAILED);
		Integer updateRecord = exportService.updateRecord(record);
        
		log.info("PppoeExportTask updateRecord : {} ... ", updateRecord);
	}
	
	/**
	 * 加载企业信息到缓存中。
	 */
	private void loadCustomerName(Set<Long> collect) {
		if(collect != null && !collect.isEmpty()) {
			// 减去已经存在的
			collect.removeAll(CUSTOMER_NAME_CACHE.keySet());
			// 
			if(!collect.isEmpty()) {
				CustomerAllExample exampleCustom = new CustomerAllExample();
				CustomerAllExample.Criteria criteria = exampleCustom.createCriteria();
				criteria.andEnterpriseIdIn(new ArrayList<Long>(collect));
				List<CustomerAll> custNameList = customerAllMapper.selectByExample(exampleCustom);
				// 遍历添加到map中
				custNameList.stream().forEach(x -> CUSTOMER_NAME_CACHE.put(x.getEnterpriseId(), x.getCustomerName()));
			}
		}
	}

	/**
	 * 导出数据整理
	 * 标题行
	 * 省份,地市,企业名称,网关SN,网关Mac,厂商,型号,累计运行时长(秒),采样时间,PPPOE拨号失败原因,PPPOE拨号失败原因描述
	 */
	private String from(GatewayPeriodAll detail) {
		String provinceCode = InitSystemConfig.AREA_CODE_NAME.getOrDefault(detail.getProvinceCode(), detail.getProvinceCode());
		String cityCode = InitSystemConfig.AREA_CODE_NAME.getOrDefault(detail.getCityCode(), detail.getCityCode());
		String gatewayVendor = InitSystemConfig.FACTORY_CODE_NAME.getOrDefault(detail.getGatewayVendor(), detail.getGatewayVendor());
		String pppoeErrorDesc = InitSystemConfig.PPPOE_ERROR_CODE_DESC.getOrDefault(detail.getPppoeError(), detail.getPppoeError());
		String custName = CUSTOMER_NAME_CACHE.getOrDefault(detail.getEnterpriseId(), String.valueOf(detail.getEnterpriseId()));
		return new StringBuffer()
				.append(provinceCode).append(",")
				.append(cityCode).append(",")
				.append(custName).append(",")
				.append(detail.getGatewaySn()).append(",")
				.append(detail.getGatewayMac()).append(",")
				.append(gatewayVendor).append(",")
				.append(detail.getGatewayProductclass()).append(",")
				.append(detail.getRuningTime()).append(",")
				.append(detail.getSampleTime()).append(",")
				.append(detail.getPppoeError()).append(",")
				.append(pppoeErrorDesc)
				.toString();
	}

}
