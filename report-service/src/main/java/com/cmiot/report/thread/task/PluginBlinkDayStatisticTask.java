package com.cmiot.report.thread.task;

import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.qyzw.analyse.api.feignclient.BlinkPluginStatFeignClient;
import com.cmiot.qyzw.analyse.api.request.BlinkPluginDailyStatRequest;
import com.cmiot.qyzw.analyse.api.response.BlinkPluginDailyStatVo;
import com.cmiot.report.dto.plugin.PluginInfoDayCount;
import com.cmiot.report.dto.plugin.PluginInfoMonthCount;
import com.cmiot.report.mapper.PluginInfoStatisticMapper;
import com.cmiot.report.service.DicDataService;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.redis.RedisOperatorService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Classname PluginBlinkDayStatisticTask
 * @Description
 * @Date 2024/2/4 18:09
 * @Created by lei
 */

@Component
public class PluginBlinkDayStatisticTask {

    private final static Logger logger = LoggerFactory.getLogger(PluginBlinkDayStatisticTask.class);
    //List<BlinkPluginDailyStatVo> getBlinkPluginDailyStat(BlinkPluginDailyStatRequest var1);

    @Autowired
    private BlinkPluginStatFeignClient blinkPluginStatFeignClient;

    @Autowired
    private PluginInfoStatisticMapper pluginInfoStatisticMapper;

    @Value("${plugin.bundleId.blink:2001}")
    private String bundleBlink;

    @Autowired
    private RedisOperatorService redisOperatorService;

    @Autowired
    private DicDataService dicDataService;

    @Scheduled(cron = "${plugin.blink.day.statistic.cron:0/30 * * * * ?}")
    public void blinkDayCount() {
        String keyRedis = "\"plugin:blink:day:statistic\"";
        Object pValue = redisOperatorService.rPop(keyRedis);
        if (pValue == null) {
            return;
        }
        String sdate = Objects.toString(pValue);
        if (StringUtils.isBlank(sdate)) {
            return;
        }
        logger.info("开始处理blink插件信息日统计");
        BlinkPluginDailyStatRequest request = new BlinkPluginDailyStatRequest();
        String date = DateUtil.getBeforeDay(1, DateUtil.DATE_PATTERN);
        request.setDate(date);
        List<BlinkPluginDailyStatVo> blinkPluginDailyStatVos = getBlinkDailyStat(request);

        logger.info("从blink获取到数据: {}", JSONUtil.toJSONString(blinkPluginDailyStatVos));
        String lastDay = DateUtil.getBeforeDay(2, DateUtil.MONTH_DATE_PATTERN);
        List<PluginInfoDayCount> lastDayCounts = this.pluginInfoStatisticMapper.getPluginInfoDayCount(lastDay, bundleBlink);
        logger.info("从t_plugin_info_day_count_all获取到前一天数据: {}", JSONUtil.toJSONString(lastDayCounts));
        Map<String, Long> lastDayTotalMap = new HashMap<>();

        if (blinkPluginDailyStatVos != null && !blinkPluginDailyStatVos.isEmpty()) {
            if (lastDayCounts != null && !lastDayCounts.isEmpty()) {
                for (PluginInfoDayCount lastDayCount : lastDayCounts) {
                    String key = lastDayCount.getBundleId() + "-"
                            + lastDayCount.getArea() + "-"
                            + lastDayCount.getFactoryId() + "-"
                            + lastDayCount.getDeviceModelId();
                    lastDayTotalMap.put(key, lastDayCount.getTotalNum());
                }
            }

            List<PluginInfoDayCount> pluginInfoDayCountList = new ArrayList<>();
            for (BlinkPluginDailyStatVo blinkPluginDailyStatVo : blinkPluginDailyStatVos) {

                PluginInfoDayCount pluginInfoDayCount = new PluginInfoDayCount();
                pluginInfoDayCount.setCountDate(date.replace("-", ""));
                pluginInfoDayCount.setBundleId(bundleBlink);
                pluginInfoDayCount.setArea(blinkPluginDailyStatVo.getProvince());
                pluginInfoDayCount.setFactoryId(blinkPluginDailyStatVo.getFactoryId());
                pluginInfoDayCount.setDeviceModelId(blinkPluginDailyStatVo.getDeviceModelId());
                pluginInfoDayCount.setNoUseNum(Long.valueOf(blinkPluginDailyStatVo.getNoUseNum()));
                pluginInfoDayCount.setTotalNum(Long.valueOf(blinkPluginDailyStatVo.getInstallNum()));
                pluginInfoDayCount.setThirtyDayActiveNum(Long.valueOf(blinkPluginDailyStatVo.getThirtyDayActiveNum()));

                Long nowTotal = pluginInfoDayCount.getTotalNum();
                String key = pluginInfoDayCount.getBundleId() + "-"
                        + pluginInfoDayCount.getArea() + "-"
                        + pluginInfoDayCount.getFactoryId() + "-"
                        + pluginInfoDayCount.getDeviceModelId();

                Long lastDayTotal = lastDayTotalMap.getOrDefault(key, 0L);

                pluginInfoDayCount.setIncrNum(nowTotal - lastDayTotal);

                pluginInfoDayCountList.add(pluginInfoDayCount);
            }
            if (!pluginInfoDayCountList.isEmpty()) {
                for (PluginInfoDayCount pluginInfoDayCount : pluginInfoDayCountList) {
                    this.pluginInfoStatisticMapper.insertPluginInfoDayCount(pluginInfoDayCount);
                }
            }

        }
    }

    private List<BlinkPluginDailyStatVo> getBlinkDailyStat(BlinkPluginDailyStatRequest request) {
        List<BlinkPluginDailyStatVo> blinkPluginDailyStatVos =
                this.blinkPluginStatFeignClient.getBlinkPluginDailyStat(request);
        if (blinkPluginDailyStatVos == null || blinkPluginDailyStatVos.isEmpty()) {
            return blinkPluginDailyStatVos;
        }
        Map<String, BlinkPluginDailyStatVo> blinkPluginDailyStatVoMap = new HashMap<>();
        Map<String, String> dicProvMap = this.dicDataService.getDicAreaMap();
        for (BlinkPluginDailyStatVo blinkPluginDailyStatVo : blinkPluginDailyStatVos) {
            String province = blinkPluginDailyStatVo.getProvince();
            if (!dicProvMap.containsKey(province)) {
                province = "";
                blinkPluginDailyStatVo.setProvince(province);
            }

            String key = province + "-" + blinkPluginDailyStatVo.getFactoryId() + "-" + blinkPluginDailyStatVo.getDeviceModelId();
            if (blinkPluginDailyStatVoMap.containsKey(key)) {
                BlinkPluginDailyStatVo oldVo = blinkPluginDailyStatVoMap.get(key);
                oldVo.setInstallNum(oldVo.getInstallNum() + blinkPluginDailyStatVo.getInstallNum());
                oldVo.setNoUseNum(oldVo.getNoUseNum() + blinkPluginDailyStatVo.getNoUseNum());
                oldVo.setThirtyDayActiveNum(oldVo.getThirtyDayActiveNum() + blinkPluginDailyStatVo.getThirtyDayActiveNum());
            } else {
                blinkPluginDailyStatVoMap.put(key, blinkPluginDailyStatVo);
            }
        }

        return new ArrayList<>(blinkPluginDailyStatVoMap.values());
    }


    //每月1号，6点进行统计
    @Scheduled(cron = "${plugin.blink.month.statistic.cron:0 0/1 * * * ?}")
    public void pluginMonthCount() {
        String keyRedis = "\"plugin:blink:month:statistic\"";
        Object pValue = redisOperatorService.rPop(keyRedis);
        if (pValue == null) {
            return;
        }
        String sdate = Objects.toString(pValue);
        if (StringUtils.isBlank(sdate)) {
            return;
        }
        logger.info("开始处理blink插件信息月统计");
        //String lastDay = DateUtil.getLastMonthLastDay(DateUtil.MONTH_DATE_PATTERN);
        String lastDay = DateUtil.getOffsetMonthLastDay(-1, DateUtil.MONTH_DATE_PATTERN);
        List<PluginInfoDayCount> pluginInfoDayCounts = this.pluginInfoStatisticMapper.getPluginInfoDayCount(lastDay, bundleBlink);
        logger.info("从t_plugin_info_day_count_all获取到上月最后一天数据: {}", JSONUtil.toJSONString(pluginInfoDayCounts));
        String lastTowMonthDay = DateUtil.getLastMonth(2);
        List<PluginInfoMonthCount> lastMonthPluginInfoDayCounts =
                this.pluginInfoStatisticMapper.getPluginInfoMonthCount(lastTowMonthDay, bundleBlink);
        logger.info("从t_plugin_info_month_count_all获取到上上月数据: {}", JSONUtil.toJSONString(lastMonthPluginInfoDayCounts));
        Map<String, PluginInfoMonthCount> lastTwoMonthDataMap = new HashMap<>();
        for (PluginInfoMonthCount lastMonthPluginInfoDayCount : lastMonthPluginInfoDayCounts) {

            String key = lastMonthPluginInfoDayCount.getBundleId() + "-"
                    + lastMonthPluginInfoDayCount.getArea() + "-"
                    + lastMonthPluginInfoDayCount.getFactoryId() + "-"
                    + lastMonthPluginInfoDayCount.getDeviceModelId();

            lastTwoMonthDataMap.put(key, lastMonthPluginInfoDayCount);
        }

        String lastMonthDay = DateUtil.getLastMonth(1);
        List<PluginInfoMonthCount> pluginInfoMonthCountList = new ArrayList<>();
        if (pluginInfoDayCounts != null && !pluginInfoDayCounts.isEmpty()) {
            for (PluginInfoDayCount pluginInfoDayCount : pluginInfoDayCounts) {
                PluginInfoMonthCount pluginInfoMonthCount = new PluginInfoMonthCount();
                pluginInfoMonthCount.setCountDate(lastMonthDay);
                pluginInfoMonthCount.setBundleId(bundleBlink);
                pluginInfoMonthCount.setArea(pluginInfoDayCount.getArea());
                pluginInfoMonthCount.setFactoryId(pluginInfoDayCount.getFactoryId());
                pluginInfoMonthCount.setDeviceModelId(pluginInfoDayCount.getDeviceModelId());
                pluginInfoMonthCount.setTotalNum(pluginInfoDayCount.getTotalNum());

                String key = pluginInfoMonthCount.getBundleId() + "-"
                        + pluginInfoMonthCount.getArea() + "-"
                        + pluginInfoMonthCount.getFactoryId() + "-"
                        + pluginInfoMonthCount.getDeviceModelId();

                Long lastTwoMonthTotal = 0L;
                if (lastTwoMonthDataMap.containsKey(key)) {
                    lastTwoMonthTotal = lastTwoMonthDataMap.get(key).getTotalNum();
                }
                pluginInfoMonthCount.setIncrNum(pluginInfoMonthCount.getTotalNum() - lastTwoMonthTotal);
                pluginInfoMonthCountList.add(pluginInfoMonthCount);
                lastTwoMonthDataMap.remove(key);
            }
        }
        //处理剩余的
        if (!lastTwoMonthDataMap.isEmpty()) {
            for (PluginInfoMonthCount lastData : lastTwoMonthDataMap.values()) {

                PluginInfoMonthCount pluginInfoMonthCount = new PluginInfoMonthCount();
                pluginInfoMonthCount.setCountDate(lastMonthDay);
                pluginInfoMonthCount.setBundleId(bundleBlink);
                pluginInfoMonthCount.setArea(lastData.getArea());
                pluginInfoMonthCount.setFactoryId(lastData.getFactoryId());
                pluginInfoMonthCount.setDeviceModelId(lastData.getDeviceModelId());
                pluginInfoMonthCount.setTotalNum(0L);
                pluginInfoMonthCount.setIncrNum(-lastData.getTotalNum());
                pluginInfoMonthCountList.add(pluginInfoMonthCount);
            }
        }
        if (!pluginInfoMonthCountList.isEmpty()) {
            for (PluginInfoMonthCount pluginInfoMonthCount : pluginInfoMonthCountList) {
                this.pluginInfoStatisticMapper.insertPluginInfoMonthCount(pluginInfoMonthCount);
            }
        }
    }

}
