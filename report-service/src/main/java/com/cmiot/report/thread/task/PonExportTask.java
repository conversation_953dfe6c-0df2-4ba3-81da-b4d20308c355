package com.cmiot.report.thread.task;


import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.fgeo.taskmanager.enums.ExportStatusEnum;
import com.cmiot.report.bean.PonPowerListDetailExample;
import com.cmiot.report.common.poi.ExcelUtils;
import com.cmiot.report.dto.PonListDTO;
import com.cmiot.report.mapper.PonPowerListDetailMapper;
import com.cmiot.report.service.ExportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public class PonExportTask implements Runnable {

    private final static Logger logger = LoggerFactory.getLogger(PonExportTask.class);

    private ExportService exportService;

    @Autowired
    private PonPowerListDetailMapper ponPowerListDetailMapper;

    @Autowired
    private PonPowerListDetailExample example;

    private ExportInfoDto record;

    private String tmpPath;

    private String exportName;

    private Integer usePriUrl;

    public PonExportTask(ExportService exportService, PonPowerListDetailMapper ponPowerListDetailMapper, PonPowerListDetailExample example, ExportInfoDto record, String tmpPath, String exportName, Integer usePriUrl) {
        this.exportService = exportService;
        this.ponPowerListDetailMapper = ponPowerListDetailMapper;
        this.example = example;
        this.record = record;
        this.tmpPath = tmpPath;
        this.exportName = exportName;
        this.usePriUrl = usePriUrl;
        logger.info("record:{}", this.record);
    }

    @Override
    public void run() {
        logger.info("PON光功率明细异步线程数据导出任务开始~");
        List<PonListDTO> voList = ponPowerListDetailMapper.selectDetailByExample(example);
        String localFilePath = tmpPath + "/" + exportName.replace(" ", "_") + ".xlsx";
        File dataFile = ExcelUtils.writeExcel(voList, PonListDTO.class, localFilePath);

        String downLoadUrl = "";
        String failedDesc = "SUCCESS";
        try {
            Map<?, ?> uploadFile = exportService.uploadFile(record.getCreatorId(), dataFile);
            // 获取内网地址
            if (usePriUrl != null && usePriUrl == 1) {
                if (uploadFile.containsKey("privateUrl")) {
                    downLoadUrl = uploadFile.get("privateUrl").toString();
                } else {
                    failedDesc = "上传文件响应无内网地址";
                }
            } else {
                if (uploadFile.containsKey("internetUrl")) {
                    downLoadUrl = uploadFile.get("internetUrl").toString();
                } else {
                    failedDesc = "上传文件响应无公网地址";
                }
            }
        } catch (IOException ex) {
            failedDesc = "文件上传异常:" + ex.getMessage();
            logger.error("PON光功率明细异步线程数据导出任务异常={}", ex.getMessage(), ex);
        }

        // 上传记录设置
        record.setFailedDesc(failedDesc);
        record.setDownloadUrl(downLoadUrl);
        record.setStatus("SUCCESS".equalsIgnoreCase(failedDesc) ? ExportStatusEnum.SUCCESS : ExportStatusEnum.FAILED);
        exportService.updateRecord(record);
        logger.info("PON光功率明细异步线程数据导出任务结束~");
    }
}
