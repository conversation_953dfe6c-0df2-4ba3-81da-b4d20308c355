package com.cmiot.report.thread;

/**
 * 线程池类型
 */
public enum ThreadPoolTypeEnum {
	/**
	 * 查询和家亲宽带信息
	 */
	QUERY_AND_HOME_PRO_BAND_INFO(1, "查询和家亲宽带信息"),
	UPDATE_RMS_INFO(2, "更新RMS-INFO信息");

	private Integer type;
	private String message;

	ThreadPoolTypeEnum(Integer type, String message) {
		this.type = type;
		this.message = message;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}
}
