package com.cmiot.report.thread;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 线程池工厂
 */
public class ThreadPoolFactory {

	private ConcurrentHashMap<Integer, ThreadPool> threadMap;

	private ThreadPoolFactory() {
		threadMap = new ConcurrentHashMap<>();
	}

	public static ThreadPoolFactory getInstance() {
		return LazyThreadPoolFactoryHolder.instance;
	}

	private static class LazyThreadPoolFactoryHolder {
		public static ThreadPoolFactory instance = new ThreadPoolFactory();
	}

	public ThreadPool getThreadPool(ThreadPoolValueInit init) {
		if (null == threadMap.get(init.getType())) {
			synchronized (ThreadPoolFactory.class) {
				if (null == threadMap.get(init.getType())) {
					ThreadPool threadPool = new ThreadPool(init.getCorePoolSize(), init.getMaximumPoolSize(), init.getKeepAliveTime(), TimeUnit.SECONDS, init.getDequeMaxSize(), init.getHandler(), init.getMessage());
					threadMap.put(init.getType(), threadPool);
				}
			}
		}
		return threadMap.get(init.getType());
	}
}
