package com.cmiot.report.thread.task;

import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.fgeo.taskmanager.enums.ExportStatusEnum;
import com.cmiot.report.InitSystemConfig;
import com.cmiot.report.bean.*;
import com.cmiot.report.bean.customer.CustomerMarketingScoreAll;
import com.cmiot.report.bean.customer.CustomerMarketingScoreQueryParam;
import com.cmiot.report.common.poi.ExcelUtils;
import com.cmiot.report.dto.SubDeviceList;
import com.cmiot.report.dto.customer.MarketingCustomerListRequest;
import com.cmiot.report.mapper.*;
import com.cmiot.report.service.ExportService;
import com.cmiot.report.util.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CustomerMarketingExportTask implements Runnable {
	/**
	 * 导出工具
	 */
	private ExportService exportService;

	/**
	 * 导出记录
	 */
	private ExportInfoDto record;

	/**
	 * 文件临时目录
	 */
	private String tmpPath;

	/**
	 * 1 使用内网地址，否则使用外网地址
	 */
	private Integer usePriUrl;


	private CustomerMarketingDayCountAllMapper customerMarketingDayCountAllMapper;
	private CustomerMarketingScoreAllMapper customerMarketingScoreAllMapper;
	private DictBassStdMapper dictBassStdMapper;
	private MarketingCustomerListRequest request;

	public CustomerMarketingExportTask(CustomerMarketingDayCountAllMapper customerMarketingDayCountAllMapper,
									   CustomerMarketingScoreAllMapper customerMarketingScoreAllMapper,
									   DictBassStdMapper dictBassStdMapper,
									   MarketingCustomerListRequest request,
									   ExportService exportService,
									   ExportInfoDto record,
									   String tmpPath,
									   Integer usePriUrl) {
		this.customerMarketingDayCountAllMapper = customerMarketingDayCountAllMapper;
		this.customerMarketingScoreAllMapper = customerMarketingScoreAllMapper;
		this.dictBassStdMapper = dictBassStdMapper;
		this.request = request;
		this.exportService = exportService;
		this.record = record;
		this.tmpPath = tmpPath;
		this.usePriUrl = usePriUrl;
	}


	@Override
	public void run() {
		log.info("CustomerMarketingExportTask start ... ");
		// bass_type='BASS_STD1_0028' 客户状态 (不在网-10,离网-11,未入网-12,在网-20)
		Map<String, String> customerStatusDict = getBassStd("BASS_STD1_0028");
		//价值分类
		Map<String, String> valueCategoryDict = getBassStd("BASS_STD1_0018");

		CustomerMarketingScoreQueryParam queryParam = createQueryPara(request);
		// 获取总数据量
		long total = customerMarketingScoreAllMapper.countByParam(queryParam);
		log.info("CustomerMarketingExportTask total : {}, page : {}, pageSize : {}", total);
		if (total == 0) {
			// 上传记录设置
			record.setFailedDesc("数据为空无法导出");
			record.setStatus(ExportStatusEnum.FAILED);
			Integer updateRecord = exportService.updateRecord(record);
			return;
		}

		try {
			List<CustomerMarketingScoreAll> customerMarketingScoreAlls =
					this.customerMarketingScoreAllMapper.selectByParam(queryParam);
			for (CustomerMarketingScoreAll data : customerMarketingScoreAlls) {
				String customerStatus = customerStatusDict.getOrDefault(data.getCustomerStatus(), "");
				String valueCategory = valueCategoryDict.getOrDefault(data.getValueCategory(), "");
				data.setCustomerStatus(customerStatus);
				data.setValueCategory(valueCategory);

				String areaName = data.getAreaName();
				if ("其他-其他".equals(areaName)) {
					areaName = "";
				}
				data.setAreaName(areaName);
			}
			// 数据文件
			String localFilePath = tmpPath + "/" + record.getExportName() + ".xlsx";
			File dataFile = ExcelUtils.writeExcel(customerMarketingScoreAlls, CustomerMarketingScoreAll.class, localFilePath);

			// 上传文件
			String downLoadUrl = "";
			String failedDesc = "SUCCESS";
			try {
				Map<?, ?> uploadFile = exportService.uploadFile(record.getCreatorId(), dataFile);
				// 获取内网地址
				if(usePriUrl != null && usePriUrl.intValue() == 1) {
					if(uploadFile.containsKey("privateUrl")) {
						downLoadUrl = uploadFile.get("privateUrl").toString();
					} else {
						failedDesc = "上传文件响应无内网地址";
					}
				} else {
					if(uploadFile.containsKey("internetUrl")) {
						downLoadUrl = uploadFile.get("internetUrl").toString();
					} else {
						failedDesc = "上传文件响应无公网地址";
					}
				}
				log.info("CustomerMarketingExportTask downLoadUrl : {}, failedDesc : {} ", downLoadUrl, failedDesc);
			} catch (IOException e) {
				failedDesc = "文件上传异常:" + e.getMessage();
				log.warn("uploadFile file : {} IOException : {}", dataFile.getName(), e.getMessage());
			}
			FileUtils.deleteQuietly(dataFile);
			// 上传记录设置
			record.setFailedDesc(failedDesc);
			record.setDownloadUrl(downLoadUrl);
			record.setStatus("SUCCESS".equalsIgnoreCase(failedDesc) ? ExportStatusEnum.SUCCESS : ExportStatusEnum.FAILED);
			Integer updateRecord = exportService.updateRecord(record);
			log.info("CustomerMarketingExportTask updateRecord : {} ... ", updateRecord);
		} catch (Exception e) {
			record.setFailedDesc("导出任务执行错误");
			record.setStatus( ExportStatusEnum.FAILED);
			Integer updateRecord = exportService.updateRecord(record);
			log.info("CustomerMarketingExportTask updateRecord : {} ... ", updateRecord);
		}
        

	}

	private CustomerMarketingScoreQueryParam createQueryPara(MarketingCustomerListRequest request) {
		String time = DateUtil.getYesterday();

		String provinceCode = request.getProvince();
		List<String> provList = null;
		if (StringUtils.isNotBlank(provinceCode)) {
			provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provinceCode, "\\,"));
		}

		String cityCode = request.getCity();
		List<String> cityList = null;
		if (StringUtils.isNotBlank(cityCode)) {
			cityList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCode, "\\,"));
		}
		String customerName = request.getBusiness();
		String customerId = request.getGroupCustomer();
		List<String> valueCategoryList = null;
		if (StringUtils.isNotBlank(request.getEnterpriseValueCategory())) {
			valueCategoryList =
					(List<String>) CollectionUtils
							.arrayToList(StringUtils.split(request.getEnterpriseValueCategory(), "\\,"));
		}
		List<String> customerStatusList = null;
		if (StringUtils.isNotBlank(request.getCustomerStatus())) {
			customerStatusList =
					(List<String>) CollectionUtils
							.arrayToList(StringUtils.split(request.getCustomerStatus(), "\\,"));
		}
		String scoreStart = request.getMarketingExponentStart();
		String scoreEnd = request.getMarketingExponentEnd();

		CustomerMarketingScoreQueryParam queryParam = new CustomerMarketingScoreQueryParam();
		queryParam.setTime(time);
		queryParam.setProvList(provList);
		queryParam.setCityList(cityList);
		queryParam.setCustomerName(customerName);
		queryParam.setCustomerId(customerId);
		queryParam.setValueCategoryList(valueCategoryList);
		queryParam.setCustomerStatusList(customerStatusList);
		queryParam.setScoreStart(scoreStart);
		queryParam.setScoreEnd(scoreEnd);
		return queryParam;
	}

	private Map<String, String> getBassStd(String bassType) {
		DictBassStdExample example = new DictBassStdExample();
		DictBassStdExample.Criteria criteria = example.createCriteria();

		criteria.andBassTypeEqualTo(bassType);
		List<DictBassStd> stdList = dictBassStdMapper.selectByExample(example);
		Map<String, String> result = new HashMap<>();
		for (DictBassStd dictBassStd : stdList) {
			result.put(dictBassStd.getCode(), dictBassStd.getName());
		}
		return result;
	}

	/**
	 * 导出数据整理
	 * 标题行
	 * 企业名称,归属地区,集团客户标识,企业价值分类,客户状态,营销指数
	 * @param data
	 * @param customerStatusDict
	 * @param valueCategoryDict
	 */
	private String from(CustomerMarketingScoreAll data,
						Map<String, String> customerStatusDict,
						Map<String, String> valueCategoryDict) {
		String customerStatus = customerStatusDict.getOrDefault(data.getCustomerStatus(), "");
		String valueCategory = valueCategoryDict.getOrDefault(data.getValueCategory(), "");
		return new StringBuffer()
				.append(data.getCustomerName()).append(",")
				.append(data.getAreaName()).append(",")
				.append(data.getCustomerId()).append(",")
				.append(valueCategory).append(",")
				.append(customerStatus).append(",")
				.append(data.getScore()).append(",")
				.toString();
	}

}
