package com.cmiot.report.thread;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池实现
 */
public class ThreadPool {

	private static Logger logger = LoggerFactory.getLogger(ThreadPool.class);

	private String threadPoolMessage;

	private ThreadPoolExecutor executor = null;

	public ThreadPool(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, int dequeMaxSize, RejectedExecutionHandler handler, String threadPoolMessage) {
		this.threadPoolMessage = threadPoolMessage;
		executor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, unit, new LinkedBlockingDeque<>(dequeMaxSize), handler);
	}

	/**
	 * 执行任务
	 * @param command
	 */
	public void execute(Runnable command) {
		if (null != executor) {
			logger.info(getLogMsg(null));
			executor.execute(command);
		}
	}

	/**
	 * 执行任务
	 * @param command
	 */
	public void execute(Runnable command, String logId) {
		if (null != executor) {
			logger.info(getLogMsg(logId));
			executor.execute(command);
		}
	}

	/**
	 * 关闭线程池
	 */
	public void shutdown() {
		if (null != executor) {
			executor.shutdown();
		}
	}

	private String getLogMsg(String logId) {
		StringBuilder msg = new StringBuilder(StringUtils.isBlank(logId) ? "" : logId + ",");
		msg.append("threadPoolMessage:").append(this.threadPoolMessage).append(",");
		msg.append("corePoolSize:").append(executor.getCorePoolSize()).append(",");
		msg.append("maximumPoolSize:").append(executor.getMaximumPoolSize()).append(",");
		msg.append("queueSize:").append(executor.getQueue().size()).append(",");
		msg.append("activeCount:").append(executor.getActiveCount()).append(",");
		msg.append("KeepAliveTime:").append(executor.getKeepAliveTime(TimeUnit.SECONDS)).append("s");
		return msg.toString();
	}
}
