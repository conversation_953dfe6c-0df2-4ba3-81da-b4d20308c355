package com.cmiot.report.thread;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池值初始化
 */
public class ThreadPoolValueInit {
	/**
	 * 线程池类型
	 */
	private int type;
	/**
	 * 线程池的基本大小
	 */
	private int corePoolSize = 32;
	/**
	 * 线程池中允许的最大线程数
	 */
	private int maximumPoolSize = 50;
	/**
	 * 如果一个线程处在空闲状态的时间超过了该属性值，就会因为超时而退出（单位：秒）
	 */
	private long keepAliveTime = 100L;
	/**
	 * 线程池队列最大值
	 */
	private int dequeMaxSize = 5000;
	/**
	 * 线程池信息
	 */
	private String message;
	/**
	 * 线程池的拒绝策略: </br>
	 * (1)AbortPolicy:该策略是线程池的默认策略。使用该策略时，如果线程池队列满了丢掉这个任务并且抛出RejectedExecutionException异常。</br>
	 * (2)DiscardPolicy:这个策略是AbortPolicy的salient版本，如果线程池队列满了，会直接丢掉这个任务并且不会有任何异常。</br>
	 * (3)DiscardOldestPolicy:这个策略从字面上也很好理解，丢弃最老的。也就是说如果队列满了，会将最早进入队列的任务删掉腾出空间，再尝试加入队列。</br>
	 * 因为队列是队尾进，队头出，所以队头元素是最老的，因此每次都是移除对头元素后再尝试入队。 </br>
	 * (4)CallerRunsPolicy:使用此策略，如果添加到线程池失败，那么主线程会自己去执行该任务，不会等待线程池中的线程去执行。就像是个急脾气的人，我等不到别人来做这件事就干脆自己干。</br>
	 * (5)自定义:如果以上策略都不符合业务场景，那么可以自己定义一个拒绝策略，只要实现RejectedExecutionHandler接口，并且实现rejectedExecution方法就可以了。
	 */
	private RejectedExecutionHandler handler;

	public ThreadPoolValueInit() {
	}

	/**
	 * @param type
	 *        线程池类型
	 * @param corePoolSize
	 *        线程池的基本大小
	 * @param maximumPoolSize
	 *        线程池中允许的最大线程数
	 * @param dequeMaxSize
	 *        线程池队列最大值
	 * @param message
	 *        线程池信息
	 * @param handler
	 *        线程池的拒绝策略 1:AbortPolicy; 2:DiscardPolicy; 3:DiscardOldestPolicy; 4:CallerRunsPolicy
	 */
	public ThreadPoolValueInit(int type, int corePoolSize, int maximumPoolSize, int dequeMaxSize, String message, int handler) {
		this.type = type;
		this.corePoolSize = corePoolSize;
		this.maximumPoolSize = maximumPoolSize;
		this.dequeMaxSize = dequeMaxSize;
		this.message = message;
		switch (handler) {
		case 1:
			this.handler = new ThreadPoolExecutor.AbortPolicy();
			break;
		case 2:
			this.handler = new ThreadPoolExecutor.DiscardPolicy();
			break;
		case 3:
			this.handler = new ThreadPoolExecutor.DiscardOldestPolicy();
			break;
		case 4:
			this.handler = new ThreadPoolExecutor.CallerRunsPolicy();
			break;
		default:
			throw new RuntimeException("网关重启任务线程池初始化未知线程池的拒绝策略方案");
		}

	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getCorePoolSize() {
		return corePoolSize;
	}

	public void setCorePoolSize(int corePoolSize) {
		this.corePoolSize = corePoolSize;
	}

	public int getMaximumPoolSize() {
		return maximumPoolSize;
	}

	public void setMaximumPoolSize(int maximumPoolSize) {
		this.maximumPoolSize = maximumPoolSize;
	}

	public long getKeepAliveTime() {
		return keepAliveTime;
	}

	public void setKeepAliveTime(long keepAliveTime) {
		this.keepAliveTime = keepAliveTime;
	}

	public int getDequeMaxSize() {
		return dequeMaxSize;
	}

	public void setDequeMaxSize(int dequeMaxSize) {
		this.dequeMaxSize = dequeMaxSize;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public RejectedExecutionHandler getHandler() {
		return handler;
	}

	public void setHandler(RejectedExecutionHandler handler) {
		this.handler = handler;
	}
}
