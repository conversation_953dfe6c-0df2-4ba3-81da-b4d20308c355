package com.cmiot.report.controller;

import com.alibaba.fastjson.JSON;
import com.cmiot.report.dto.ExportDataResult;
import com.cmiot.report.dto.customer.*;
import com.cmiot.report.facade.customer.CustomerMarketingFacade;
import com.cmiot.report.service.CustomerMarketingService;
import com.cmiot.report.util.UserAreaUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

/**
 * @Classname CustomerMarketingController
 * @Description
 * @Date 2022/8/25 11:53
 * @Created by lei
 * TODO
 */
@RestController
public class CustomerMarketingController implements CustomerMarketingFacade {

    private final static Logger logger = LoggerFactory.getLogger(CustomerMarketingController.class);

    @Autowired
    private CustomerMarketingService customerMarketingService;

    @Override
    public CustomerMarketingDistributionData targetCustomerDistribution(String province, TargetCustomerDistributionRequest request) {
        logger.info("企业客户分析-精准客户营销-精准提速营销目标客户分布 查询接口方法入参=：{}, province: {}", JSON.toJSONString(request), province);
        try {
            if (province != null) {
                String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
                if(StringUtils.isBlank(area)){
                    CustomerMarketingDistributionData data = new CustomerMarketingDistributionData();
                    data.setTotalTargetCustomers("");
                    CustomerMarketingDistributionStatistics statistics = new CustomerMarketingDistributionStatistics();
                    statistics.setTotalTargetCustomers(0L);
                    statistics.setYesterdayNewCustomers(0L);
                    statistics.setYesterdayReduceCustomers(0L);
                    statistics.setLastWeekNewCustomers(0L);
                    statistics.setLastMonthNewCustomers(0L);
                    statistics.setLastWeekReduceCustomers(0L);
                    statistics.setLastMonthReduceCustomers(0L);
                    data.setStatistics(statistics);
                    data.setCustomersDistribution(Collections.emptyList());
                    data.setRecommendationRate(Collections.emptyList());
                    return data;
                }
                request.setProvince(area);
            }
            CustomerMarketingDistributionData result = this.customerMarketingService.targetCustomerDistribution(request);
            logger.info("企业客户分析-精准客户营销-精准提速营销目标客户分布，接口方法出参={}", JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            logger.info("查询错误 {}", e.getMessage(), e);
            return null;
        }

    }

    @Override
    public ChangesInTargetUsersData changesInTargetUsers(String province, ChangesInTargetUsersRequest request) {
        logger.info("企业客户分析-精准客户营销-目标客户数变化情况 查询接口方法入参=：{}, province: {}", JSON.toJSONString(request), province);
        try {
            if(province != null){
                String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
                if(StringUtils.isBlank(area)){
                    ChangesInTargetUsersData data = new ChangesInTargetUsersData();
                    data.setXAxis(Collections.emptyList());
                    data.setYesterdayNewCustomers(Collections.emptyList());
                    data.setYesterdayReduceCustomers(Collections.emptyList());
                    data.setYesterdayTargetCustomers(Collections.emptyList());
                    return data;
                }
                request.setProvince(area);
            }
            ChangesInTargetUsersData result = this.customerMarketingService.changesInTargetUsers(request);
            logger.info("企业客户分析-精准客户营销-目标客户数变化情况，接口方法出参={}", JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            logger.info("查询错误 {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public MarketingCustomerListData precisionMarketingCustomerList(String province, MarketingCustomerListRequest request) {
        logger.info("企业客户分析-精准客户营销-精准营销客户列表 查询接口方法入参=：{}, province: {}", JSON.toJSONString(request), province);
        try {
            if(province != null){
                String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
                if(StringUtils.isBlank(area)){
                    MarketingCustomerListData data = new MarketingCustomerListData();
                    data.setPage(request.getPage());
                    data.setPageSize(request.getPageSize());
                    data.setTotal(0L);
                    data.setList(Collections.emptyList());
                    return data;
                }
                request.setProvince(area);
            }
            MarketingCustomerListData result = this.customerMarketingService.precisionMarketingCustomerList(request);
            logger.info("企业客户分析-精准客户营销-精准营销客户列表，接口方法出参={}", JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            logger.info("查询错误 {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public ExportDataResult precisionMarketingCustomerListExport(Long uid, String province, MarketingCustomerListRequest request) {
        logger.info("企业客户分析-精准客户营销-精准营销客户列表 导出接口方法入参=：{}, province: {}", JSON.toJSONString(request), province);
        try {
            request.setUid(uid);
            if (province != null) {
                String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
                //如果为空，用户没有任何区域权限
                if (StringUtils.isBlank(area)) {
                    ExportDataResult result = new ExportDataResult();
                    result.setName("用户没有任何区域权限");
                    return result;
                }
                request.setProvince(area);
            }
            ExportDataResult result = this.customerMarketingService.precisionMarketingCustomerListExport(request);
            logger.info("企业客户分析-精准客户营销-精准营销客户列表，接口方法出参={}", JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            logger.info("导出错误 {}", e.getMessage(), e);
            return null;
        }
    }
}
