package com.cmiot.report.controller;

import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.report.bean.plugin.EkitRestartAlertAnalysisParam;
import com.cmiot.report.dto.plugin.PluginBaseInfo;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.facade.dto.plugin.*;
import com.cmiot.report.facade.gateway.PluginReportFacade;
import com.cmiot.report.service.PluginReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public class PluginReportController implements PluginReportFacade {

    @Autowired
    private PluginReportService pluginReportService;

    @Override
    public PluginInstallAnalysisRes getInstallationAnalysis(PluginInstallAnalysisParam param) {
        LocalDate localDate = LocalDate.now().minusDays(1);
        return pluginReportService.getInstallationAnalysis(param, localDate);
    }

    @Override
    public PageRes<EkitRestartAlertAnalysisRes> ekitRestartAlertAnalysis(EkitRestartAlertAnalysisParam param) {
        return pluginReportService.ekitRestartAlertAnalysis(param);
    }

    @Override
    public List<String> ekitRestartNumberList(EkitRestartNumberListParam param) {
        return pluginReportService.ekitRestartNumberList(param);
    }

    @Override
    public PageRes<PluginCompositeOverview> getPluginInfoOverview(Integer column,
                                                                  Integer order,
                                                                  Integer page,
                                                                  Integer pageSize) {
        log.info("查询插件统计概览, column: {}, order: {}, page: {}, pageSize: {}", column, order, page, pageSize);
        PageRes<PluginCompositeOverview> pageRes = pluginReportService.getPluginInfoOverview(column, order, page, pageSize);
        log.info("查询插件统计概览结果, pageRes: {}", JSONUtil.toJSONString(pageRes));

        return pageRes;
    }

    @Override
    public PluginDetailOverview getPluginDetailOverview(GetPluginDetailOverviewPar getPluginDetailOverviewPar) {
        log.info("查询插件详情概览, getPluginDetailOverviewPar: {}", JSONUtil.toJSONString(getPluginDetailOverviewPar));
        PluginDetailOverview pluginDetailOverview = pluginReportService.getPluginDetailOverview(getPluginDetailOverviewPar);
        log.info("查询插件详情概览结果, pageRes: {}", JSONUtil.toJSONString(pluginDetailOverview));
        return pluginDetailOverview;
    }

    @Override
    public PageRes<PluginCompositeOverview> getPluginInfoStatistic(Integer column, Integer order, Integer page, Integer pageSize) {
        log.info("OSGi查询插件统计概览, column: {}, order: {}, page: {}, pageSize: {}", column, order, page, pageSize);
        PageRes<PluginCompositeOverview> pluginInfoStatistic = pluginReportService.getPluginInfoStatistic(column, order, page, pageSize);
        log.info("OSGi查询插件统计概览结果, pageRes: {}", JSONUtil.toJSONString(pluginInfoStatistic));
        return pluginInfoStatistic;
    }

    @Override
    public PluginDetailOverview getPluginInfoStatisticDetail(GetPluginDetailOverviewPar pluginInfoStatisticDetailPar) {
        log.info("OSGi查询插件详情概览, getPluginDetailOverviewPar: {}", JSONUtil.toJSONString(pluginInfoStatisticDetailPar));
        PluginDetailOverview pluginInfoStatisticDetail = pluginReportService.getPluginInfoStatisticDetail(pluginInfoStatisticDetailPar);
        log.info("OSGi查询插件详情概览结果, pageRes: {}", JSONUtil.toJSONString(pluginInfoStatisticDetail));
        return pluginInfoStatisticDetail;
    }

    @Override
    public PluginBaseInfo getPluginInfo(Integer pluginId) {
        return pluginReportService.getPluginInfo(pluginId);
    }

    @Override
    public List<String> getListPluginVersion(Integer pluginId) {
        return pluginReportService.getAllPluginVersion(pluginId);
    }
}
