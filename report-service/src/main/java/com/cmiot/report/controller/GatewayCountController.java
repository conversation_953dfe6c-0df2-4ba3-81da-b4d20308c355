package com.cmiot.report.controller;

import com.cmiot.report.dto.*;
import com.cmiot.report.facade.gateway.GatewayCountFacade;
import com.cmiot.report.service.GatewayCountService;
import com.cmiot.report.util.UserAreaUtil;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


@RestController
public class GatewayCountController implements GatewayCountFacade {


    @Autowired
    private GatewayCountService gatewayCountService;


    @Override
    public GatewayOverviewResult gatewayOverview(String province) {
        List<String> provinceCode = null;
        if (province != null) {
            provinceCode = Arrays.asList(province.split(","));
            if (provinceCode.isEmpty()) {
                GatewayOverviewResult overviewResult = new GatewayOverviewResult();
                String numZero = "0";
                overviewResult.setTotalNum(numZero);
                overviewResult.setAddNum(numZero);
                overviewResult.setDayActiveNum(numZero);
                overviewResult.setRunOffNum(numZero);
                overviewResult.setTotalMonthOnMonth(numZero);
                overviewResult.setTotalYearOnYear(numZero);
                overviewResult.setConnectFamilyGatewayNum(numZero);
                overviewResult.setConnectFamilyGatewayRate(numZero);
                return overviewResult;
            }
        }
        return gatewayCountService.getOverviewCount(provinceCode);
    }


    @Override
    public GatewayCountResult getGatewayCount(String province, GatewayCountRequest request) {
        if (province != null) {
            String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (StringUtils.isBlank(area)) {
                GatewayCountResult gatewayCountResult = new GatewayCountResult();
                gatewayCountResult.setTotalGatewayNum("0");
                gatewayCountResult.setListByIndustry(Collections.emptyList());
                gatewayCountResult.setListByProvince(Collections.emptyList());
                gatewayCountResult.setListByVendor(Collections.emptyList());

                return gatewayCountResult;
            }
            request.setProvince(area);
        }

        return gatewayCountService.getGatewayCount(request);
    }


    @Override
    public GatewayIncrementCountResult getGatewayIncrementCount(String province, GatewayIncrementCountRequst request) {
        if (province != null) {
            String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (StringUtils.isBlank(area)) {
                GatewayIncrementCountResult result = new GatewayIncrementCountResult();
                result.setAddGatewayNum("0");
                result.setListByIndustry(Collections.emptyList());
                result.setListByVendor(Collections.emptyList());
                result.setListByProvince(Collections.emptyList());
                return result;
            }
            request.setProvince(area);
        }
        return gatewayCountService.getGatewayIncrementCount(request);
    }


    @Override
    public List<GatewayOnOffLineNumResult> gatewayOnOfflineNumStatistic(GatewayOnOfflineCountRequest request, String province) {

        return gatewayCountService.gatewayOnOfflineNumStatistic(request, province);
    }


//    @Override
//    public GatewayOnOffLineNumOverviewResult gatewayOnOfflineNumOverviewStatistic(GatewayOnOfflineCountRequest request) {
//
//        return null;
//    }


    @Override
    public MiniProgramOnOfflineCountResult gatewayOnOfflineTrendStatistic(String eid, MiniProgramOnOfflineCountRequest request) {

        request.setEid(eid);

        return gatewayCountService.gatewayOnOfflineTrendStatistic(request);
    }

    @Override
    public void setAlarmThreshold(AlarmThresholdRequest request) {

        gatewayCountService.setAlarmThreshold(request);
    }

    @Override
    public AlarmThresholdResult getAlarmThreshold() {
        return gatewayCountService.getAlarmThreshold();
    }


    @Override
    public GatewayAlarmCountResult getGatewayAlarmCount(GatewayAlarmRequst request, String province) {

        return gatewayCountService.getGatewayAlarmCount(request, province);
    }


    @Override
    public GatewayAlarmListResult getGatewayAlarmList(GatewayAlarmListRequst request, String province) {

        return gatewayCountService.getGatewayAlarmList(request, province);
    }

    @Override
    public PonStatisticResult getGatewayPonStatistic(String province, PonStatisticRequst request) {
        if (province != null) {
            String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (StringUtils.isBlank(area)) {
                PonStatisticResult result = new PonStatisticResult();
                result.setxAxis(Collections.emptyList());
                result.setReceivePowerStrongRate(Collections.emptyList());
                result.setReceivePowerStrongAverage(Collections.emptyList());
                result.setReceivePowerWeakRate(Collections.emptyList());
                result.setReceivePowerWeakAverage(Collections.emptyList());
                return result;
            }
            request.setProvince(area);
        }
        return gatewayCountService.getGatewayPonStatistic(request);
    }

    @Override
    public PonListResult getGatewayPonList(String province, PonListRequst request) {
        if (province != null) {
            String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (StringUtils.isBlank(area)) {
                PonListResult result = new PonListResult();
                result.setPage(request.getPage());
                result.setPageSize(request.getPageSize());
                result.setTotal(0L);
                result.setList(Collections.emptyList());
                return result;
            }
            request.setProvince(area);
        }
        return gatewayCountService.getGatewayPonList(request);
    }

    @Override
    public String getGatewayPonListExport(PonListExportRequest request, String province, String uid) {
        if (province != null) {
            String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (StringUtils.isBlank(area)) {
                return "用户没有任何区域权限";
            }
            request.setProvince(area);
        }
        request.setUid(uid);

        return gatewayCountService.getGatewayPonListExport(request);
    }


}
