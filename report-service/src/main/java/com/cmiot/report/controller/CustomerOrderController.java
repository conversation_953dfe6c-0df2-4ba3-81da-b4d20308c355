package com.cmiot.report.controller;

import com.cmiot.report.dto.customerOrder.*;
import com.cmiot.report.facade.customer.CustomerOrderFacade;
import com.cmiot.report.service.CustomerOrderService;
import com.cmiot.report.util.UserAreaUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
public class CustomerOrderController implements CustomerOrderFacade {

    private final static Logger logger = LoggerFactory.getLogger(CustomerOrderController.class);

    @Autowired
    private CustomerOrderService customerOrderService;


    @Override
    public OverviewPieChartData customerOrderOverviewPieChart(String province) {
        logger.info("智慧运营分析-企业订购业务分析-业务量总览饼图,省份:{}", province);
        // 获取区域
        OverviewPieChartRequest request = new OverviewPieChartRequest();
        if(StringUtils.isNotBlank(province)){
            String area = UserAreaUtil.userAndSelectIntersection(province, "");
            if(StringUtils.isBlank(area)){
                // 没有任何区域的权限
                return new OverviewPieChartData();
            }
            request.setProvince(area);
            return customerOrderService.customerOrderOverviewPieChart(request);
        } else {
            logger.info("智慧运营分析-企业订购业务分析-业务量总览饼图查询无任务区域权限");
            return new OverviewPieChartData();
        }
    }

    @Override
    public List<OverviewHistogramData> customerOrderOverviewHistogram(String province) {
        logger.info("智慧运营分析-企业订购业务分析-业务量总览柱状图,省份:{}", province);
        // 获取区域
        OverviewHistogramRequest request = new OverviewHistogramRequest();
        if(StringUtils.isNotBlank(province)){
            String area = UserAreaUtil.userAndSelectIntersection(province, "");
            if(StringUtils.isBlank(area)){
                // 没有任何区域的权限
                return Collections.emptyList();
            }
            request.setProvince(area);
            return customerOrderService.customerOrderOverviewHistogram(request);
        } else {
            logger.info("智慧运营分析-企业订购业务分析-业务量总览柱状图查询无任务区域权限");
            return Collections.emptyList();
        }
    }

    @Override
    public List<OrderTrendLineChartData> customerOrderTrendLineChart(String province, OrderTrendLineChartRequest request) {
        logger.info("智慧运营分析-企业订购业务分析-业务发展趋势,省份:{}, 请求参数:{}", province, request);
        // 获取区域
        if(StringUtils.isNotBlank(province)){
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            if(null == area || area.isEmpty()){
                // 没有任何区域的权限
                return Collections.emptyList();
            }
            request.setProvince(area);
            return customerOrderService.customerOrderTrendLineChart(request);
        } else {
            logger.info("智慧运营分析-企业订购业务分析-业务发展趋势查询无任务区域权限");
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, Object> getCumulativePackageSubscription(String province, PackageSubscriptionRequest request) {
        logger.info("智慧运营分析-企业订购业务分析-累计套餐订购情况,省份:{}, 请求参数:{}", province, request);
        // 获取区域
        if(StringUtils.isNotBlank(province)){
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            if(null == area || area.isEmpty()){
                // 没有任何区域的权限
                return Collections.emptyMap();
            }
            request.setProvince(area);
            return customerOrderService.cumulativePackageSubscription(request);
        } else {
            logger.info("智慧运营分析-企业订购业务分析-累计套餐订购情况无区域权限");
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, Object> getNewPackageSubscription(String province, PackageSubscriptionRequest request) {
        logger.info("智慧运营分析-企业订购业务分析-新增套餐订购情况,省份:{}, 请求参数:{}", province, request);
        // 获取区域
        if(StringUtils.isNotBlank(province)){
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            if(null == area || area.isEmpty()){
                // 没有任何区域的权限
                return Collections.emptyMap();
            }
            request.setProvince(area);
            return customerOrderService.newPackageSubscription(request);
        } else {
            logger.info("智慧运营分析-企业订购业务分析-新增套餐订购情况无区域权限");
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, Object> getSinglePackageSubscription(String province, PackageSubscriptionRequestExtend request) {
        logger.info("智慧运营分析-企业订购业务分析-新增套餐订购情况,省份:{}, 请求参数:{}", province, request);
        List<String> requestProvList = request.getProvince();
        if(null == requestProvList || 1 != requestProvList.size()){
            logger.info("智慧运营分析-企业订购业务分析-新增套餐订购情况统计请求错误");
            // 没有任何区域的权限
            return Collections.emptyMap();
        }
        // 获取区域
        if(StringUtils.isNotBlank(province)){
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            if(null == area || area.size() != 1){
                // 没有任何区域的权限
                logger.info("智慧运营分析-企业订购业务分析-新增套餐订购情况统计请求错误");
                return Collections.emptyMap();
            }
            request.setProvince(area);
            return customerOrderService.SinglePackageSubscription(request);
        } else {
            logger.info("智慧运营分析-企业订购业务分析-新增套餐订购情况无区域权限");
            return Collections.emptyMap();
        }
    }

    @Override
    public List<PackageGrowthTrendLineChartData> getPackageGrowthTrendLineChart(String province, PackageGrowthTrendRequest request) {
        logger.info("智慧运营分析-企业订购业务分析-套餐增长趋势折线图,省份:{}, 请求参数:{}", province, request);
        // 获取区域
        if(StringUtils.isNotBlank(province)){
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            if(null == area || area.isEmpty()){
                // 没有任何区域的权限
                return Collections.emptyList();
            }
            request.setProvince(area);
            return customerOrderService.packageGrowthTrendLineChart(request);
        } else {
            logger.info("智慧运营分析-企业订购业务分析-套餐增长趋势折线图无区域权限");
            return Collections.emptyList();
        }
    }

    @Override
    public List<PackageGrowthTrendHistogramData> getPackageGrowthTrendHistogram(String province, PackageGrowthTrendRequest request) {
        logger.info("智慧运营分析-企业订购业务分析-套餐增长趋势柱状图,省份:{}, 请求参数:{}", province, request);
        // 获取区域
        if(StringUtils.isNotBlank(province)){
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            if(null == area || area.isEmpty()){
                // 没有任何区域的权限
                return Collections.emptyList();
            }
            request.setProvince(area);
            return customerOrderService.packageGrowthTrendHistogram(request);
        } else {
            logger.info("智慧运营分析-企业订购业务分析-套餐增长趋势柱状图无区域权限");
            return Collections.emptyList();
        }
    }

    @Override
    public List<BandwidthStatisticalData> getBandwidthStatisticalAnalysis(String province, BandwidthStatisticalRequest request) {
        logger.info("智慧运营分析-企业订购业务分析-带宽统计分析,省份:{}, 请求参数:{}", province, request);
        // 获取区域
        if(StringUtils.isNotBlank(province)){
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            if(null == area || area.isEmpty()){
                // 没有任何区域的权限
                return Collections.emptyList();
            }
            request.setProvince(area);
            return customerOrderService.bandwidthStatisticalAnalysis(request);
        } else {
            logger.info("智慧运营分析-企业订购业务分析-带宽统计分析无区域权限");
            return Collections.emptyList();
        }
    }
}
