package com.cmiot.report.controller;

import com.cmiot.fgeo.common.error.FGEOException;
import com.cmiot.fgeo.taskmanager.dto.ExportInfoDto;
import com.cmiot.report.bean.GatewayAnomalyRecord;
import com.cmiot.report.dto.*;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.facade.gateway.GatewayFacade;
import com.cmiot.report.service.DicDataService;
import com.cmiot.report.service.ExportService;
import com.cmiot.report.service.GatewayService;
import com.cmiot.report.thread.task.GatewayConnectNumberTask;
import com.cmiot.report.thread.task.GatewayCpuRamDataTask;
import com.cmiot.report.thread.task.GatewayFlowTask;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.PermissionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;


/**
 * <p>
 * 网关统计分析
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@RestController
@Slf4j
public class GatewayController implements GatewayFacade {

    /**
     * 单线程池。用于文件导出。
     */
    public static final ExecutorService SINGLE_THREAD_EXECUTOR = Executors.newSingleThreadExecutor();

    /**
     * 导出工具。
     */
    @Autowired
    private ExportService exportService;

    /**
     * 临时目录
     */
    @Value("${export.tmp.local}")
    private String tmpPath;

    /**
     * 1 使用内网地址，否则使用外网地址
     */
    @Value("${export.use-pri-url}")
    private Integer usePriUrl;

    @Autowired
    GatewayService gatewayService;

    @Autowired
    private DicDataService dicDataService;

    @Override
    public GatewayDetailResult gatewayDetail(String gatewaySn) {
        log.info("gatewayDetail, query param: {}", gatewaySn);
        GatewayQuery gatewayQuery = new GatewayQuery();
        gatewayQuery.setSn(gatewaySn);
        GatewayDetailResult gatewayDetailResult = gatewayService.getGatewayRunDataDetail(gatewayQuery);
        log.info("gatewayDetail, result: {}", gatewayDetailResult);
        return gatewayDetailResult;
    }

    @Override
    public Map gatewayVersionStatistic(GatewayQuery gatewayQuery, String province) {
        log.info("gatewayVersionStatistic, query param: {}, province: {}", gatewayQuery, province);
        gatewayQuery.initParams();
        Map result = new HashMap();
        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("user provice is null");
            return result;
        }
        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvince(permission.get(0));
            gatewayQuery.setProvinceList(permission);
        }
        log.info("gatewayVersionStatistic permission: {}", permission);
        if (StringUtils.isEmpty(gatewayQuery.getModel())) {
            result.put("list", new ArrayList<>());
            log.info("gatewayVersionStatistic, result size: 0");
            return result;
        }
        List<GatewayVersionResult> gatewayVersionResultList = gatewayService.getGatewayVersionStatistics(gatewayQuery);
        result.put("list", gatewayVersionResultList);
        log.info("gatewayVersionStatistic, result size: {}", gatewayVersionResultList.size());
        return result;
    }

    @Override
    public GatewayFlowTrendResult flowOverview(GatewayQuery gatewayQuery, String province) {
        log.info("flowOverview, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();

        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("user provice is null");
            return new GatewayFlowTrendResult(null);
        }
        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvince(permission.get(0));
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }
        log.info("flowOverview ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());
        GatewayFlowTrendResult gatewayFlowTrendResult = gatewayService.getGatewayFlowOverview(gatewayQuery);
        log.info("flowOverview, result ：{}", gatewayFlowTrendResult);
        return gatewayFlowTrendResult;
    }

    @Override
    public List<GatewayFlowTrendResult> gatewayFlowStatistic(GatewayQuery gatewayQuery, String province) {
        log.info("gatewayFlowStatistic, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();

        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("user provice is null");
            return new ArrayList<>();
        }
        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvince(permission.get(0));
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }
        log.info("gatewayFlowStatistic ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());
        List<GatewayFlowTrendResult> gatewayFlowTrendResults = gatewayService.getGatewayFlowTrend(gatewayQuery);
        log.info("gatewayFlowStatistic, result size: {}", gatewayFlowTrendResults.size());
        return gatewayFlowTrendResults;
    }

    @Override
    public Map gatewayFlowList(GatewayQuery gatewayQuery, String province) {
        log.info("gatewayFlowList, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();

        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("user provice is null");
            return new HashMap();
        }
        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvince(permission.get(0));
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }
        log.info("gatewayFlowList ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());
        Map result = gatewayService.getGatewayFlowListByPage(gatewayQuery);
        log.info("gatewayFlowList, result: {}", result);
        return result;
    }

    @Override
    public ExportGatewayFlowList exportGatewayFlowList(Long uid, GatewayQuery gatewayQuery) {
        log.info("exportGatewayFlowList, query param: {}", gatewayQuery);

        String exportName = "网关流量数据列表";
        ExportInfoDto record = exportService.record(uid, exportName, exportName);

        SINGLE_THREAD_EXECUTOR.submit(new GatewayFlowTask(record, gatewayQuery, gatewayService, tmpPath, usePriUrl, exportService));

        ExportGatewayFlowList exportInfo = new ExportGatewayFlowList();
        exportInfo.setName(record.getExportName());
        return exportInfo;
    }

    @Override
    public List<GatewayFlowTrendResult> gatewayFlowTrend(GatewayQuery gatewayQuery) {
        log.info("gatewayFlowTrend, query param: {}", gatewayQuery);
        gatewayQuery.initParams();
        List<GatewayFlowTrendResult> gatewayFlowTrendResults = gatewayService.getGatewayFlowTrend(gatewayQuery);
        log.info("gatewayFlowTrend, result size: {}", gatewayFlowTrendResults.size());
        return gatewayFlowTrendResults;
    }

    @Override
    public GatewayCpuAndRamResult cpuAndRamOverview(GatewayQuery gatewayQuery, String province) {
        log.info("cpuAndRamOverview, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();
        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("cpuAndRamOverview user provice is null");
            return new GatewayCpuAndRamResult();
        }
        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvince(permission.get(0));
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }
        log.info("cpuAndRamOverview ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());
        GatewayCpuAndRamResult result = gatewayService.getGatewayCpuAndRamOverview(gatewayQuery);
        log.info("cpuAndRamOverview, result: {}", result);
        return result;
    }


    @Override
    public Map typeCpuAndRamAverage(GatewayQuery gatewayQuery, String province) {
        log.info("typeCpuAndRamAverage, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();
        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("typeCpuAndRamAverage user provice is null");
            return new HashMap();
        }
        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvince(permission.get(0));
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }
        log.info("typeCpuAndRamAverage ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());
        Map result = gatewayService.getGatewayCpuAndRamStatistics(gatewayQuery);
        log.info("typeCpuAndRamAverage, result: {}", result);
        return result;
    }

    @Override
    public Map<String, Object> rangeCpuAndRamNumRatio(GatewayQuery gatewayQuery, String province) {
        log.info("rangeCpuAndRamNumRatio, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();

        Map result = new HashMap();
        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("rangeCpuAndRamNumRatio user provice is null");
            return result;
        }
        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvince(permission.get(0));
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }
        log.info("rangeCpuAndRamNumRatio ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());
        List<GatewayCpuAndRamPieDataDto> gatewayCpuPieDataDtos = gatewayService.getGatewayCpuGradStatistics(gatewayQuery);
        List<GatewayCpuAndRamPieDataDto> gatewayRamPieDataDtos = gatewayService.getGatewayRamGradStatistics(gatewayQuery);
        result.put("cpuPieData", gatewayCpuPieDataDtos);
        result.put("ramPieData", gatewayRamPieDataDtos);
        log.info("rangeCpuAndRamNumRatio, result: {}", result);
        return result;
    }

    @Override
    public Map<String, Object> gatewayCpuRamDatalist(GatewayQuery gatewayQuery, String province) {
        log.info("gatewayCpuRamDatalist, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();
        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("gatewayCpuRamDatalist user provice is null");
            return new HashMap<>();
        }
        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvince(permission.get(0));
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }
        log.info("gatewayCpuRamDatalist ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());

        Map result = gatewayService.getGatewayCpuRamListByPage(gatewayQuery);
        log.info("gatewayCpuRamDatalist, result: {}", result);
        return result;

    }

    @Override
    public ExportGatewayFlowList exportGatewayCpuRamDatalist(Long uid, String province, GatewayQuery gatewayQuery) {
        log.info("exportGatewayCpuRamDatalist, query param: {}, province: {}", gatewayQuery, province);
        gatewayQuery.initParams();
        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("exportGatewayCpuRamDatalist user provice is null");
            throw new FGEOException.NotAcceptable("用户没有区域权限，无法导出数据");
        }

        try {
            List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
            if (!CollectionUtils.isEmpty(permission)) {
                gatewayQuery.setProvince(permission.get(0));
                gatewayQuery.setProvinceList(permission);
                if (1 == permission.size()) {
                    // 获取当前省市全部地市
                    Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                    Set<String> strings = data.keySet();
                    List<String> ls = new ArrayList(strings);
                    if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                        if (!ls.containsAll(gatewayQuery.getCityList())) {
                            gatewayQuery.setCityList(ls);
                        }
                    } else {
                        gatewayQuery.setCityList(ls);
                    }
                }
            }
            log.info("gatewayCpuRamDatalist ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());

            String exportName = "网关CPU和内存分析列表明细";

            ExportInfoDto record = exportService.record(uid, exportName, exportName);

            SINGLE_THREAD_EXECUTOR.submit(new GatewayCpuRamDataTask(record, gatewayQuery, gatewayService, tmpPath, usePriUrl, exportService));

            ExportGatewayFlowList exportInfo = new ExportGatewayFlowList();
            exportInfo.setName(record.getExportName());
            return exportInfo;
        } catch (Exception e) {
            log.error("导出网关CPU和内存分析列表明细异常: {}", e.getMessage(), e);
            throw new FGEOException.InternalServerError("导出失败，请稍后重试");
        }
    }

    @Override
    public Map rangeCpuAndRamOverLevelRatio(GatewayQuery gatewayQuery, String province) {
        log.info("rangeCpuAndRamOverLevelRatio, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();
        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("rangeCpuAndRamOverLevelRatio user provice is null");
            return new HashMap();
        }
        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvince(permission.get(0));
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                // 获取当前省市全部地市
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }
        log.info("rangeCpuAndRamOverLevelRatio ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());
        Map result = gatewayService.rangeCpuAndRamOverLevelRatio(gatewayQuery);
        log.info("rangeCpuAndRamOverLevelRatio, result: {}", result);
        return result;
    }

    @Override
    public Map<String, Object> gatewayDailyFlowMonitor(GatewayQuery gatewayQuery) {
        log.info("gatewayDailyFlowMonitor, query param: {}", gatewayQuery);
        List<GatewayWanBandwidthDetail> gatewayWanBandwidthList = gatewayService.getGatewayWanBandwidthList(gatewayQuery);
        Map<String, Object> resultMap = new HashMap<>();
        //组装成前端需要的格式
        if (!CollectionUtils.isEmpty(gatewayWanBandwidthList)) {
            List<String> xAxis = gatewayWanBandwidthList.stream().map(GatewayWanBandwidthDetail::getRecordTime).collect(Collectors.toList());
            resultMap.put("xAxis", xAxis);
            List<Double> topFlow = gatewayWanBandwidthList.stream().map(GatewayWanBandwidthDetail::getUpload).collect(Collectors.toList());
            resultMap.put("topFlow", topFlow);
            List<Double> downFlow = gatewayWanBandwidthList.stream().map(GatewayWanBandwidthDetail::getDownload).collect(Collectors.toList());
            resultMap.put("downFlow", downFlow);
            return resultMap;
        }
        return null;
    }

    @Override
    public Map<String, List<ConnectNumberDataDto>> quantityProportion(GatewayQuery gatewayQuery) {
        log.info("连接数在各个范围内的数量quantityProportion, query param: {}", gatewayQuery);
        gatewayQuery.initParams();
        return gatewayService.getGatewayConnectNumberStatistics(gatewayQuery);
    }


    @Override
    public Map<String, List<ConnectNumberDataDto>> quantityProportion95(GatewayQuery gatewayQuery) {
        log.info("连接数在各个范围内的数量quantityProportion95, query param: {}", gatewayQuery);
        gatewayQuery.initParams();
        return gatewayService.getGatewayConnectNumberStatistics95(gatewayQuery);
    }

    @Override
    public Map<String, Object> getPageConnectNumberDetail(GatewayQuery gatewayQuery) {
        log.info("分页查询连接数监控列表 getPageConnectNumberDetail, query param: {}", gatewayQuery);
        gatewayQuery.initParams();
        return gatewayService.getPageConnectNumberDetail(gatewayQuery);
    }

    @Override
    public Map<String, Object> getConnectNumberLineChart(GatewayQuery gatewayQuery) {
        log.info("连接数折线图 getConnectNumberLineChart, query param: {}", gatewayQuery);
        gatewayQuery.initParams();
        return gatewayService.getGatewayConnectNumberBySn(gatewayQuery);
    }

    @Override
    public List<Map<String, String>> getGatewayWanConnServiceList(GatewayQuery gatewayQuery) {
        log.info("连接数折线图-连接类型下拉选项 getGatewayWanConnServiceList, query param: {}", gatewayQuery);
        gatewayQuery.initParams();
        return gatewayService.getGatewayWanConnServiceList(gatewayQuery);
    }

    @Override
    public ExportGatewayFlowList export(Long uid, GatewayQuery gatewayQuery) {
        log.info("连接数列表导出 export, query param: {}", gatewayQuery);
        gatewayQuery.initParams();

        try {
            String exportName = "网关连接数列表明细";

            ExportInfoDto record = exportService.record(uid, exportName, exportName);

            SINGLE_THREAD_EXECUTOR.submit(new GatewayConnectNumberTask(gatewayQuery, gatewayService, tmpPath, usePriUrl, exportService, record));

            ExportGatewayFlowList exportInfo = new ExportGatewayFlowList();
            exportInfo.setName(record.getExportName());
            return exportInfo;
        } catch (Exception e) {
            log.error("导出网关连接数列表明细异常: {}", e.getMessage(), e);
            throw new FGEOException.InternalServerError("导出失败，请稍后重试");
        }
    }


    @Override
    public PageRes<AccessNetworkAnalysisResult> accessNetworkAnalysis(GatewayQuery gatewayQuery) {
        log.info("分页查询用户访问网络分析 accessNetworkAnalysis, query param: {}", gatewayQuery);
        gatewayQuery.initParams();
        return gatewayService.getPageAccessNetworkAnalysis(gatewayQuery);
    }
}
