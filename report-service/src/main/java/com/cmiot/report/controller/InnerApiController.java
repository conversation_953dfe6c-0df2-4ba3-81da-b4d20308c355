package com.cmiot.report.controller;

import com.cmiot.report.facade.dto.gateway.GatewaySubDeviceInfoDto;
import com.cmiot.report.service.GatewayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Classname InnerApiController
 * @Description
 * @Date 2024/12/5 10:44
 * @Created by lei
 */
@RestController
public class InnerApiController {

    @Autowired
    private GatewayService gatewayService;

    @PostMapping("/innerapi/ge-report/getGatewaySubDeviceOnlineNum")
    public List<GatewaySubDeviceInfoDto> getGatewaySubDeviceOnlineNum(@RequestBody List<String> gatewaySns) {
        return this.gatewayService.getGatewaySubDeviceOnlineNum(gatewaySns);
    }
}
