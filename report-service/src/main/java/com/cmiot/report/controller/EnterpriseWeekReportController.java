package com.cmiot.report.controller;

import com.cmiot.report.facade.dto.enterprise.WeekReportOverviewResult;
import com.cmiot.report.facade.dto.enterprise.WeekReportSubDeviceResult;
import com.cmiot.report.facade.dto.enterprise.WeekReportVisitsResult;
import com.cmiot.report.service.EnterpriseWeekReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Classname EnterpriseWeekReportController
 * @Description
 * @Date 2023/12/19 18:42
 * @Created by lei
 */
@RestController
public class EnterpriseWeekReportController {


    @Autowired
    private EnterpriseWeekReportService enterpriseWeekReportService;

    /**
     * https://yapi.iotxmm.com/project/955/interface/api/42074
     * 获取企业周报概览信息
     * 网关总数
     * 网关流量统计
     * 网关长时间运行设备
     * @param eid
     * @return
     */

    @GetMapping(value = "/v1/e/statistic/weekReport/overview")
    public WeekReportOverviewResult getWeekReportOverview(@RequestHeader("eid") Long eid) {
        if (eid == null) {
            return null;
        }
        return this.enterpriseWeekReportService.getWeekReportOverview(eid);
    }


    /**
     * https://yapi.iotxmm.com/project/955/interface/api/39899
     * 运行报告-周报-访问情况
     * 访问次数最多的5个网站
     * @param eid
     * @return
     */

    @GetMapping(value = "/v1/e/statistic/weekReport/visits")
    public WeekReportVisitsResult getWeekReportVisits(@RequestHeader("eid") Long eid) {
        if (eid == null) {
            return null;
        }
        return this.enterpriseWeekReportService.getWeekReportVisits(eid);
    }


    /**
     * https://yapi.iotxmm.com/project/955/interface/api/39908
     * 运行报告-周报-网关-下挂设备分析
     * @param eid
     * @return
     */

    @GetMapping(value = "/v1/e/statistic/weekReport/gateway/subDevice")
    public WeekReportSubDeviceResult getWeekReportSubDevice(@RequestHeader("eid") Long eid) {
        if (eid == null) {
            return null;
        }
        return this.enterpriseWeekReportService.getWeekReportSubDevice(eid);
    }


    @GetMapping(value = "/v1/e/statistic/weekReport/onlineTimes")
    public Map<String, Object> getWeekReportGatewayRunTimes(@RequestHeader("eid") Long eid) {
        if (eid == null) {
            return null;
        }
        return this.enterpriseWeekReportService.getWeekReportGatewayRunTimes(eid);
    }

}
