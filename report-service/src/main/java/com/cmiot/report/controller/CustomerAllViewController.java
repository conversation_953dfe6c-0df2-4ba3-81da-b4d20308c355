package com.cmiot.report.controller;


import com.cmiot.report.dto.*;
import com.cmiot.report.facade.customer.CustomerAllViewFacade;
import com.cmiot.report.service.CustomerAllViewService;
import com.cmiot.report.util.UserAreaUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RestController
public class CustomerAllViewController implements CustomerAllViewFacade {

    @Autowired
    private CustomerAllViewService customerAllViewService;

    @Override
    public EnterCustDistResult getEnterpriseCustomerDistribution(String province, EnterCustomerRequest request) {
        if (province != null) {
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (area == null || area.isEmpty()) {
                EnterCustDistResult result = new EnterCustDistResult();
                result.setOverView(new EnterCustDistOverView());
                result.setMapData(Collections.emptyList());
                return result;
            }
            request.setProvince(area);
        }
        return customerAllViewService.getEnterpriseCustomerDistribution(request);

    }

    @Override
    public NewCustomerStatisticResult getNewCustomerStatistic(String province, EnterCustomerRequest request) {
        if (province != null) {
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (area == null || area.isEmpty()) {
                NewCustomerStatisticResult result = new NewCustomerStatisticResult();
                result.setOverView(new NewCustomerOverView());
                result.setChartData(Collections.emptyList());
                return result;
            }
            request.setProvince(area);
        }
        return customerAllViewService.getNewCustomerStatistic(request);

    }

    @Override
    public CustomerStatisticResult getCustomerStatistic(String province, EnterCustomerRequest request) {
        if (province != null) {
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (area == null || area.isEmpty()) {
                CustomerStatisticResult result = new CustomerStatisticResult();
                result.setData(new CustomerStatisticData());
                result.setChartData(Collections.emptyList());
                return result;
            }
            request.setProvince(area);
        }
        return customerAllViewService.getCustomerStatistic(request);
    }

    @Override
    public NewIndustryCustomerResult getNewIndustryCustomerStatistic(String province, EnterCustomerRequest request) {
        if (province != null) {
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (area == null || area.isEmpty()) {
                NewIndustryCustomerResult result = new NewIndustryCustomerResult();
                result.setList(Collections.emptyList());
                return result;
            }
            request.setProvince(area);
        }
        return customerAllViewService.getNewIndustryCustomerStatistic(request);

    }
}
