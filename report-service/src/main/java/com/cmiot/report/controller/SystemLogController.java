package com.cmiot.report.controller;

import com.alibaba.fastjson.JSON;
import com.cmiot.fgeo.common.error.FGEOException;
import com.cmiot.report.dto.syslog.SysLogDetailQueryRequest;
import com.cmiot.report.dto.syslog.SysLogDetailQueryResult;
import com.cmiot.report.dto.syslog.SysLogQueryRequest;
import com.cmiot.report.dto.syslog.SysLogQueryResult;
import com.cmiot.report.facade.sys.SystemLogFacade;
import com.cmiot.report.service.SystemLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Classname SystemLogController
 * @Description
 * @Date 2022/7/20 16:17
 * @Created by lei
 */
@RestController
public class SystemLogController implements SystemLogFacade {

    private final static Logger logger = LoggerFactory.getLogger(SystemLogController.class);

    @Autowired
    private SystemLogService systemLogService;

    @Override
    public SysLogQueryResult querySystemLogPage(SysLogQueryRequest sysLogQueryRequest) {
        try {
            SysLogQueryResult result = this.systemLogService.querySystemLogPage(sysLogQueryRequest);
            return result;
        } catch (Exception e) {
            logger.error("查询错误, {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public SysLogQueryResult userLogPage(SysLogQueryRequest sysLogQueryRequest) {
        try {
            SysLogQueryResult result = this.systemLogService.userLogPage(sysLogQueryRequest);
            return result;
        } catch (Exception e) {
            logger.error("查询错误, {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public SysLogDetailQueryResult querySystemLogDetailPage(String traceId,
                                                            SysLogDetailQueryRequest sysLogDetailQueryRequest) {

        try {
            sysLogDetailQueryRequest.setTraceId(traceId);
            SysLogDetailQueryResult result =
                    this.systemLogService.querySystemLogDetailPage(sysLogDetailQueryRequest);
            return result;
        } catch (Exception e) {
            logger.error("查询错误, {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public SysLogQueryResult allUsersLogPage(Long eid, Long uid, SysLogQueryRequest sysLogQueryRequest) {
        if (eid == null || uid == null) {
            throw new FGEOException.BadRequest("eid and uid is not null");
        }
        try {
            sysLogQueryRequest.setEid(eid);//查指定企业下所有用户的操作日志
            return this.systemLogService.allUsersLogPage(sysLogQueryRequest);
        } catch (Exception e) {
            logger.error("查询错误, {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public SysLogQueryResult userLogsPage(Long eid, Long uid, SysLogQueryRequest sysLogQueryRequest) {
        if (eid == null || uid == null) {
            throw new FGEOException.BadRequest("eid and uid is not null");
        }
        try {
            sysLogQueryRequest.setEid(eid);
            sysLogQueryRequest.setUid(uid);//查指定企业下的指定用户的操作日志
            return this.systemLogService.userLogsPage(sysLogQueryRequest);
        } catch (Exception e) {
            logger.error("查询错误, {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, String> logDetail(String traceId) {
        try {
            return this.systemLogService.getLogDetail(traceId);
        } catch (Exception e) {
            logger.error("查询错误, {}", e.getMessage(), e);
            return null;
        }
    }
}
