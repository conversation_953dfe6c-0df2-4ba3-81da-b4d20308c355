package com.cmiot.report.controller;

import com.cmiot.report.GatewayOldResult;
import com.cmiot.report.dto.ExportGatewayFlowList;
import com.cmiot.report.dto.GatewayCpuAndRamResult;
import com.cmiot.report.dto.GatewayDelayDeviceResult;
import com.cmiot.report.dto.GatewayQuery;
import com.cmiot.report.enums.GroupTypeEnum;
import com.cmiot.report.facade.gateway.GatewayOldCountFacade;
import com.cmiot.report.service.DicDataService;
import com.cmiot.report.service.GatewayDelayAllService;
import com.cmiot.report.service.GatewayService;
import com.cmiot.report.util.PermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class GatewayOldCountController implements GatewayOldCountFacade {

    @Autowired
    GatewayService gatewayService;

    @Autowired
    private DicDataService dicDataService;

    @Autowired
    private GatewayDelayAllService gatewayDelayAllService;

    @Override
    public GatewayOldResult oldGatewayOverview(GatewayQuery gatewayQuery, String province) {
        log.info("oldGatewayOverview, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();
        if (StringUtils.isEmpty(province)) {
            log.error("oldGatewayOverview user provice is null");
            return new GatewayOldResult();
        }
        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList<>(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }
        log.info("oldGatewayOverview ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());
        int delayTotal = gatewayDelayAllService.countGatewayDelayAll(gatewayQuery);
        log.info("oldGatewayOverview, delayTotal: {}", delayTotal);
        int total = gatewayDelayAllService.countGatewayAll(gatewayQuery);
        log.info("gateway total: {}", total);
        // 这里可将delayTotal设置到result中
        GatewayOldResult result = new GatewayOldResult();
        result.setOldNum(delayTotal * 1l);
        if (total > 0) {
            result.setOldPercentage(new BigDecimal((double) delayTotal / total * 100)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue());
        } else {
            result.setOldPercentage(0.00);
        }
        return result;
    }

    public static double round(double value, int scale) {
        return new BigDecimal(value)
                .setScale(scale, RoundingMode.HALF_UP)
                .doubleValue();
    }

    @Override
    public Map<String, Object> rangeOldGatewayNumRatio(GatewayQuery gatewayQuery, String province) {
        log.info("rangeOldGatewayNumRatio, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();

        Map<String, Object> result = new HashMap<>();
        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("rangeOldGatewayNumRatio user province is null");
            return result;
        }

        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList<>(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }

        log.info("rangeOldGatewayNumRatio ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());

        try {
            List<GatewayOldResult> gatewayOldResults = gatewayDelayAllService.queryOldGatewayStatistics(gatewayQuery);
            List<String> xAxis = new ArrayList<>();
            List<Long> oldNums = new ArrayList<>();
            List<Double> oldPercentages = new ArrayList<>();

            if (GroupTypeEnum.province.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayOldResults.stream().map(GatewayOldResult::getProvince).collect(Collectors.toList());
            } else if (GroupTypeEnum.city.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayOldResults.stream().map(GatewayOldResult::getCity).collect(Collectors.toList());
            } else if (GroupTypeEnum.vendor.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayOldResults.stream().map(GatewayOldResult::getGatewayVendor).collect(Collectors.toList());
            } else if (GroupTypeEnum.model.getTypeValue().equals(gatewayQuery.getGroupType())) {
                xAxis = gatewayOldResults.stream().map(GatewayOldResult::getDeviceModel).collect(Collectors.toList());
            }

            oldNums = gatewayOldResults.stream().map(GatewayOldResult::getOldNum).collect(Collectors.toList());
            oldPercentages = gatewayOldResults.stream().map(GatewayOldResult::getOldPercentage).collect(Collectors.toList());

            result.put("xAxis", xAxis);
            result.put("oldNums", oldNums);
            result.put("oldPercentages", oldPercentages);

        } catch (Exception e) {
            log.error("老旧网关在各个范围内的数量、占比rangeOldGatewayNumRatio异常: ", e);
        }

        log.info("rangeOldGatewayNumRatio, result: {}", result);
        return result;
    }

    @Override
    public Map<String, Object> oldGatewayDatalist(GatewayQuery gatewayQuery, String province) {
        log.info("OldGatewayDatalist, query param: {}, province:{}", gatewayQuery, province);
        gatewayQuery.initParams();

        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("OldGatewayDatalist user province is null");
            return new HashMap<>();
        }

        List<String> permission = PermissionUtils.getResult(province, gatewayQuery.getProvinceList());
        if (!CollectionUtils.isEmpty(permission)) {
            gatewayQuery.setProvinceList(permission);
            if (1 == permission.size()) {
                Map<String, String> data = this.dicDataService.getDictCityMap(permission);
                Set<String> strings = data.keySet();
                List<String> ls = new ArrayList<>(strings);
                if (!CollectionUtils.isEmpty(gatewayQuery.getCityList())) {
                    if (!ls.containsAll(gatewayQuery.getCityList())) {
                        gatewayQuery.setCityList(ls);
                    }
                } else {
                    gatewayQuery.setCityList(ls);
                }
            }
        }

        log.info("OldGatewayDatalist ProvinceList:{},CityList:{}", gatewayQuery.getProvinceList(), gatewayQuery.getCityList());
        Map<String, Object> result = gatewayDelayAllService.queryOldGatewayListByPage(gatewayQuery);
        log.info("OldGatewayDatalist, result: {}", result);
        return result;
    }

    @Override
    public ExportGatewayFlowList exportOldGatewayDatalist(Long uid, String province, GatewayQuery gatewayQuery) {
        return null;
    }

    @Override
    public List<GatewayDelayDeviceResult> queryDelayDevicesBySn(GatewayQuery gatewayQuery) {
        log.info("queryDelayDevicesBySn, query param: {}", gatewayQuery);
        gatewayQuery.initParams();

        if (StringUtils.isEmpty(gatewayQuery.getSn())) {
            log.error("queryDelayDevicesBySn sn is null or empty");
            return new ArrayList<>();
        }

        try {
            List<GatewayDelayDeviceResult> results = gatewayDelayAllService.queryDelayDevicesBySn(gatewayQuery);
            log.info("queryDelayDevicesBySn, result size: {}", results.size());
            return results;
        } catch (Exception e) {
            log.error("queryDelayDevicesBySn异常: ", e);
            return new ArrayList<>();
        }
    }
}
