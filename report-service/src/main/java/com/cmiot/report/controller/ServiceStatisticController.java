package com.cmiot.report.controller;


import com.cmiot.report.dto.ServiceStatisticRequest;
import com.cmiot.report.dto.ServiceStatisticResult;
import com.cmiot.report.facade.customer.ServiceStatisticFacade;
import com.cmiot.report.service.ServiceStatisticService;
import com.cmiot.report.util.UserAreaUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RestController
public class ServiceStatisticController implements ServiceStatisticFacade {

    @Autowired
    private ServiceStatisticService serviceStatisticService;

    @Override
    public ServiceStatisticResult getCustomerServiceStatistic(String province, ServiceStatisticRequest request) {
        if (province != null) {
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (area == null || area.isEmpty()) {
                ServiceStatisticResult result = new ServiceStatisticResult();
                result.setTimelyRateOfInstallation(0);
                result.setTimelyMaintenanceRate(0);
                result.setFailureResolutionRate(0);
                result.setComplaintResolutionRate(0);
                result.setTimelyComplaintRate(0);
                result.setCustomerServiceAccuracy(0);
                result.setPieData(Collections.emptyList());
                return result;
            }
            request.setProvince(area);
        }
        return serviceStatisticService.getCustomerServiceStatistic(request);
    }
}
