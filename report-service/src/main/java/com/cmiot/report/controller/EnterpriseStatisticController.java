package com.cmiot.report.controller;

import com.cmiot.fgeo.common.dto.PageInfoDto;
import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.report.dto.enterprise.DeviceCpuRamDataResult;
import com.cmiot.report.dto.enterprise.DeviceFlowDataResult;
import com.cmiot.report.facade.dto.enterprise.*;
import com.cmiot.report.facade.enterprise.DeviceGraphDataResult;
import com.cmiot.report.facade.enterprise.DeviceOverviewResult;
import com.cmiot.report.facade.enterprise.SubDeivceGraphDataResult;
import com.cmiot.report.service.EnterpriseComprehensiveService;
import com.cmiot.report.service.EnterpriseDeviceStatisticService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Classname EnterpriseStatisticController
 * @Description
 * @Date 2023/8/1 9:13
 * @Created by lei
 */

@RestController
public class EnterpriseStatisticController {
    private final static Logger logger = LoggerFactory.getLogger(EnterpriseStatisticController.class);

    @Autowired
    private EnterpriseDeviceStatisticService enterpriseDeviceStatisticService;

    @Autowired
    private EnterpriseComprehensiveService enterpriseComprehensiveService;

    /**
     * https://yapi.iotxmm.com/project/960/interface/api/31808
     * 获取企业设备概览
     * 统计当前企业网关的 累计数量（设备总数）、日活、周活（自然周）、月活（自然月） （截止前一天、网关+路由）
     * 饼图：网关、路由器总数占比（截止前一天）
     *
     * @param eid
     * @return
     */
    //enterpriseStats
    @GetMapping("/v1/e/statistic/enterprise/getDeviceOverview")
    public DeviceOverviewResult getDeviceOverview(@RequestHeader(value = "eid") Long eid) {
        logger.info("getDeviceOverview, eid: {}", eid);
        DeviceOverviewResult result = this.enterpriseDeviceStatisticService.getDeviceOverview(eid);
        logger.info("getDeviceOverview, result: {}", JSONUtil.toJSONString(result));
        return result;
    }


    /**
     * https://yapi.iotxmm.com/project/960/interface/api/31820
     * 获取设备概览图表数据
     * 获取设备总数和在线设备总数（活跃数），纵轴是数量，横轴是时间，以月为单位，展示最近12个月的信息
     *
     * @param eid
     * @return
     */
    @GetMapping("/v1/e/statistic/enterprise/getDeviceGraphData")
    public List<DeviceGraphDataResult> getDeviceGraphData(@RequestHeader(value = "eid") Long eid) {
        logger.info("getDeviceGraphData, eid: {}", eid);
        List<DeviceGraphDataResult> result = this.enterpriseDeviceStatisticService.getDeviceGraphData(eid);
        logger.info("getDeviceGraphData, result: {}", JSONUtil.toJSONString(result));
        return result;
    }

    /**
     * 获取下挂设备图表信息
     *
     * @param eid
     * @param dateType
     * @return
     */
    @GetMapping("/v1/e/statistic/enterprise/getStaDeivceGraphData")
    public SubDeivceGraphDataResult getSubDeivceGraphData(@RequestHeader(value = "eid") Long eid,
                                                          @RequestParam(value = "dateType") Integer dateType) {
        logger.info("getSubDeivceGraphData, eid: {}, dateType: {}", eid, dateType);
        SubDeivceGraphDataResult result = this.enterpriseDeviceStatisticService.getStaDeivceGraphData(eid, dateType);
        logger.info("getSubDeivceGraphData, result: {}", JSONUtil.toJSONString(result));
        return result;
    }

    /**
     * 设备流量统计
     *
     * @param eid
     * @param startTime
     * @return
     */
    @GetMapping("/v1/e/statistic/enterprise/deviceFlow")
    public DeviceFlowDataResult deviceFlow(@RequestHeader(value = "eid") Long eid,
                                           @RequestParam(value = "startTime") String startTime,
                                           @RequestParam(value = "endTime") String endTime) {
        logger.info("deviceFlow, eid: {}, startTime: {}, endTime: {}", eid, startTime, endTime);
        DeviceFlowDataResult result = this.enterpriseDeviceStatisticService.deviceFlow(eid, startTime, endTime);
        logger.info("deviceFlow, result: {}", JSONUtil.toJSONString(result));
        return result;
    }

    /**
     * CPU内存
     *
     * @param eid
     * @param startTime
     * @return
     */
    @GetMapping("/v1/e/statistic/enterprise/getCpuRam")
    public DeviceCpuRamDataResult getCpuRam(@RequestHeader(value = "eid") Long eid,
                                            @RequestParam(value = "startTime") String startTime,
                                            @RequestParam(value = "endTime") String endTime) {
        logger.info("getCpuRam, eid: {}, startTime: {}, endTime: {}", eid, startTime, endTime);
        DeviceCpuRamDataResult result = this.enterpriseDeviceStatisticService.getCpuRam(eid, startTime, endTime);
        logger.info("getCpuRam, result: {}", JSONUtil.toJSONString(result));
        return result;
    }


    /**
     * https://yapi.iotxmm.com/project/920/interface/api/38906
     * 获取客户360°画像（新增1.2.16）
     * @param eid
     * @return
     */
    @GetMapping("/v1/m/statistic/enterprise/{eid}/comprehensive")
    public EnterpriseComprehensiveResult getComprehensive(@PathVariable(value = "eid") Long eid) {
        logger.info("getComprehensive, eid: {},", eid);
        EnterpriseComprehensiveResult result = this.enterpriseComprehensiveService.getComprehensive(eid);
        logger.info("getComprehensive, result: {}", JSONUtil.toJSONString(result));
        return result;
    }


    /**
     * https://yapi.iotxmm.com/project/920/interface/api/38906
     * 获取用户网关信息列表（新增1.2.16）
     * @param eid
     * @return
     */
    @PostMapping("/v1/m/statistic/enterprise/{eid}/comprehensive/gateway")
    public PageInfoDto<ComprehensiveGatewayResult> getComprehensiveGateway(@PathVariable(value = "eid") Long eid,
                                                                           @RequestBody GetComprehensiveGatewayParam getComprehensiveGatewayParam) {
        logger.info("getComprehensiveGateway, eid: {},", eid);
        if (getComprehensiveGatewayParam == null) {
            return new PageInfoDto<>();
        }
        getComprehensiveGatewayParam.setEid(eid);
        PageInfoDto<ComprehensiveGatewayResult> result =
                this.enterpriseComprehensiveService.getComprehensiveGateway(getComprehensiveGatewayParam);
        logger.info("getComprehensiveGateway, result: {}", JSONUtil.toJSONString(result));
        return result;
    }


    /**
     * https://yapi.iotxmm.com/project/920/interface/api/38918
     * 客户360°画像套餐信息(新增1.2.16)
     * @param eid
     * @return
     */
    @PostMapping("/v1/m/statistic/enterprise/{eid}/comprehensive/package")
    public PageInfoDto<ComprehensivePackageResult> getComprehensivePackage(@PathVariable(value = "eid") Long eid,
                                                                           @RequestBody GetComprehensivePackageParam getComprehensivePackageParam) {
        logger.info("getComprehensivePackage, eid: {},", eid);
        if (getComprehensivePackageParam == null) {
            return new PageInfoDto<>();
        }
        getComprehensivePackageParam.setEid(eid);
        PageInfoDto<ComprehensivePackageResult> result =
                this.enterpriseComprehensiveService.getComprehensivePackage(getComprehensivePackageParam);
        logger.info("getComprehensivePackage, result: {}", JSONUtil.toJSONString(result));
        return result;
    }

}
