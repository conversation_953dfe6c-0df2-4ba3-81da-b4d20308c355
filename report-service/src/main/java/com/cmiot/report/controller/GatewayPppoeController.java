package com.cmiot.report.controller;

import java.util.Collections;
import java.util.List;

import com.cmiot.report.dto.ServiceStatisticResult;
import com.cmiot.report.util.UserAreaUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import com.cmiot.report.dto.pppoe.PppoeFailListRequest;
import com.cmiot.report.dto.pppoe.PppoeFailListResult;
import com.cmiot.report.dto.pppoe.PppoeListExportRequest;
import com.cmiot.report.dto.pppoe.PppoeListExportResult;
import com.cmiot.report.dto.pppoe.PppoeListRequest;
import com.cmiot.report.dto.pppoe.PppoeListResult;
import com.cmiot.report.dto.pppoe.PppoeStatisticRequest;
import com.cmiot.report.dto.pppoe.PppoeStatisticResult;
import com.cmiot.report.dto.pppoe.PppoeStatisticResult.Data;
import com.cmiot.report.facade.gateway.GatewayPppoeFacade;
import com.cmiot.report.service.GatewayPppoeService;

import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
public class GatewayPppoeController implements GatewayPppoeFacade {
	
	/**
	 * 网关pppoe相关业务处理
	 */
	@Autowired
	private GatewayPppoeService gatewayPppoeService;

	@Override
	public PppoeStatisticResult pppoeStatistic(String province, PppoeStatisticRequest request) {
		log.info("pppoeStatistic request : {}", request);
		String provinceRequest = request.getProvince();
		if (province != null) {
			String area = UserAreaUtil.userAndSelectIntersection(province, provinceRequest);
			//如果为空，用户没有任何区域权限
			if (StringUtils.isBlank(area)) {
				PppoeStatisticResult result = new PppoeStatisticResult();
				result.setList(Collections.emptyList());
				return result;
			}
			request.setProvince(area);
		}
		String city = request.getCity();
		String vendor = request.getVendor();
		String startTime = request.getStartTime();
		String endTime = request.getEndTime();
		
		// 类型：区域1，厂商2
		String type = request.getType();
		
		// 按区域统计
		List<Data> list = gatewayPppoeService.getDataList(type, request.getProvince(), city, vendor, startTime, endTime);
		
		// 返回数据组装
		PppoeStatisticResult result = new PppoeStatisticResult();
		if(list != null) result.setList(list);
		log.info("pppoeStatistic result : {}", result);
		return result;
	}

	@Override
	public PppoeListResult pppoeList(String province,PppoeListRequest request) {
		log.info("pppoeList request : {}", request);
		if (province != null) {
			String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
			//如果为空，用户没有任何区域权限
			if (StringUtils.isBlank(area)) {
				PppoeListResult result = new PppoeListResult();
				result.setPage(request.getPage());
				result.setPageSize(request.getPageSize());
				result.setTotal(0L);
				result.setList(Collections.emptyList());
				return result;
			}
			request.setProvince(area);
		}
		PppoeListResult pppoeList = gatewayPppoeService.pppoeList(request);
		log.info("pppoeList result : {}", pppoeList);
		return pppoeList;
	}

	@Override
	public PppoeListExportResult pppoeListExport(Long uid, String province, PppoeListExportRequest request) {
		log.info("pppoeListExport request : {}", request);
		String provinceRequest = request.getProvince();
		if (province != null) {
			String area = UserAreaUtil.userAndSelectIntersection(province, provinceRequest);
			//如果为空，用户没有任何区域权限
			if (StringUtils.isBlank(area)) {
				PppoeListExportResult result = new PppoeListExportResult();
				result.setName("用户没有任何区域权限");
				return result;
			}
			request.setProvince(area);
		}
		String city = request.getCity();
		String vendor = request.getVendor();
		String sn = request.getSn();
		
		String exportName = gatewayPppoeService.pppoeListExport(uid, request.getProvince(), city, vendor, sn);
		
		PppoeListExportResult result = new PppoeListExportResult();
		result.setName(exportName);
		log.info("pppoeListExport result : {}", result);
		return result;
	}

	@Override
	public PppoeFailListResult pppoeFailList(PppoeFailListRequest request) {
		log.info("pppoeFailList request : {}", request);
		PppoeFailListResult pppoeFailList = gatewayPppoeService.pppoeFailList(request);
		log.info("pppoeFailList result : {}", pppoeFailList);
		return pppoeFailList;
	}

}
