package com.cmiot.report.controller;


import com.cmiot.report.dto.*;
import com.cmiot.report.facade.device.SubDeviceFacade;
import com.cmiot.report.service.SubDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class SubDeviceController implements SubDeviceFacade {

    @Autowired
    private SubDeviceService subDeviceService;

    @Override
    public SubOverviewResult getSubOverview(SubOverviewRequest request, String province) {
        return subDeviceService.getSubOverview(request, province);
    }

    @Override
    public SubDeviceStatisticResult getStatisticIndex(SubDeviceStatisticRequest request, String province) {
        return subDeviceService.getStatisticIndex(request, province);
    }

    @Override
    public SubDeviceTrendResult getTrendStatistic(SubDeviceTrendRequest request, String province) {
        return subDeviceService.getTrendStatistic(request, province);
    }

    @Override
    public SubDeviceListResult getSubDeviceList(SubDeviceListRequest request, String province) {
        return subDeviceService.getSubDeviceList(request, province);
    }

    @Override
    public String subDeviceExport(SubDeviceListRequest request, String uid, String province) {
        request.setUid(uid);
        return subDeviceService.subDeviceExport(request, province);
    }
}
