package com.cmiot.report.controller;

import com.cmiot.report.dto.device.*;
import com.cmiot.report.facade.device.DeviceFacade;
import com.cmiot.report.service.DeviceService;
import com.cmiot.report.service.DicDataService;
import com.cmiot.report.util.PermissionUtils;
import com.cmiot.report.util.UserAreaUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@RestController
@Slf4j
public class DeviceController implements DeviceFacade {

    @Autowired
    DeviceService deviceService;

    @Autowired
    private DicDataService dicDataService;

    @Override
    public DeviceOverviewCount queryDeviceOverviewCount(String province) {
        if ("".equals(province)) {
            return new DeviceOverviewCount();
        }
        return deviceService.queryDeviceOverviewCount(province);
    }

    @Override
    public DeviceOverviewResult queryDeviceOverviewResult(String province, DeviceQuery deviceQuery) {
        if (province != null) {
            String area = UserAreaUtil.userAndSelectIntersection(province, deviceQuery.getProvince());
            //如果为空，用户没有任何区域权限
            if (StringUtils.isBlank(area)) {
                DeviceOverviewResult result = new DeviceOverviewResult();
                result.setTotalUnderDeviceNum("0");
                result.setListByIndustry(Collections.emptyList());
                result.setListByVendor(Collections.emptyList());
                result.setListByProvince(Collections.emptyList());
                return result;
            }
            deviceQuery.setProvince(area);
        }

        //province多选
        if (!StringUtils.isEmpty(deviceQuery.getProvince())) {
            deviceQuery.setProvinceCode(deviceQuery.getProvince().split(","));
        }
        //gateway_vendor多选
        if (!StringUtils.isEmpty(deviceQuery.getVendor())) {
            deviceQuery.setVendorCode(deviceQuery.getVendor().split(","));
        }
        return deviceService.queryDeviceOverviewResult(deviceQuery);
    }

    @Override
    public DeviceWLANResult queryDeviceWLANResult(DeviceQuery deviceQuery, String province) {
        log.info("start queryDeviceWLANResult , deviceQuery:{}, province:{}", deviceQuery, province);
        //province多选
        // 用户区域权限与查询区域取交集
        if (StringUtils.isEmpty(province)) {
            log.error("user provice is null");
            return new DeviceWLANResult();
        }
        if (!StringUtils.isEmpty(deviceQuery.getProvince())) {
            String[] split = deviceQuery.getProvince().split(",");
            List<String> list = Arrays.asList(split);
            List<String> permission = PermissionUtils.getResult(province, list);
            String[] strings = permission.toArray(new String[permission.size()]);
            deviceQuery.setProvinceCode(strings);
        } else {
            if (!PermissionUtils.userAllPermission(province)) {
                List<String> ownPermission = PermissionUtils.getOwnPermission(province);
                String[] strings = ownPermission.toArray(new String[ownPermission.size()]);
                deviceQuery.setProvinceCode(strings);
            }

        }

        if (StringUtils.isNotBlank(deviceQuery.getProvince())) {
            String[] provinceCode = deviceQuery.getProvinceCode();
            List<String> list = Arrays.asList(provinceCode);

            //        //city多选
            if (1 == list.size()) {
                //city多选
                Map<String, String> dicCityMap = this.dicDataService.getDictCityMap(list);
                Set<String> strings = dicCityMap.keySet();
                List<String> ls = new ArrayList(strings);
                String[] all = ls.toArray(new String[ls.size()]);
                if (!StringUtils.isEmpty(deviceQuery.getCity())) {
                    String[] split = deviceQuery.getCity().split(",");
                    List<String> city = Arrays.asList(split);
                    if (!ls.containsAll(city)) {
                        String[] c = city.toArray(new String[city.size()]);
                        deviceQuery.setCityCode(c);
                    } else {
                        deviceQuery.setCityCode(all);
                    }
                } else {
                    deviceQuery.setCityCode(all);
                }
            }
        }

         log.info("queryDeviceWLANResult ProvinceCode：{}，CityCode：{}", deviceQuery.getProvinceCode(), deviceQuery.getCityCode());
        //vendor多选
        if (!StringUtils.isEmpty(deviceQuery.getVendor())) {
            deviceQuery.setVendorCode(deviceQuery.getVendor().split(","));
        }
        //model多选
        if (!StringUtils.isEmpty(deviceQuery.getModel())) {
            deviceQuery.setModelCode(deviceQuery.getModel().split(","));
        }
        //根据type切换
        if (!StringUtils.isEmpty(deviceQuery.getType())) {
            String type = deviceQuery.getType();
            if ("province".equals(type)) {
                deviceQuery.setType("province_code");
            } else if ("vendor".equals(type)) {
                deviceQuery.setType("gateway_vendor");
            } else if ("model".equals(type)) {
                deviceQuery.setType("gateway_model");
            }else if ("city".equals(type)){
                deviceQuery.setType("city_code");
            }
        }
        return deviceService.queryDeviceWLANResult(deviceQuery);
    }

    @Override
    public DeviceAppStatCount queryDeviceAppStatCount(Long enterpriseId) {
        log.info("start queryDeviceAppStatCount , enterpriseId:{}", enterpriseId);
        DeviceAppStatCount deviceAppStatCount = deviceService.queryDeviceAppStatCount(enterpriseId);
        log.info("end queryDeviceAppStatCount , deviceAppStatCount:{}", deviceAppStatCount);
        return deviceAppStatCount == null ? new DeviceAppStatCount() : deviceAppStatCount;
    }

    @Override
    public Map<String, Object> queryDeviceAppStatTrend(Long enterpriseId, String startDate, String endDate) {
        log.info("start queryDeviceAppStatTrend , enterpriseId:{},startDate:{},endDate:{}", enterpriseId, startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        //参数校验
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            result.put("code", 400);
            result.put("message", "param error");
            return result;
        }
        try {
            if (sf.parse(startDate).after(sf.parse(endDate))) {
                log.info("startDate could not after endDate");
                result.put("code", 400);
                result.put("message", "startDate could not after endDate");
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", 500);
            result.put("message", "内部错误");
            return result;
        }
        return deviceService.queryDeviceAppStatTrend(enterpriseId, startDate, endDate);
    }
}
