package com.cmiot.report.controller;


import com.cmiot.fgeo.common.error.FGEOException;
import com.cmiot.report.bean.CustomerUneffect;
import com.cmiot.report.bean.GatewayAnomalyRecord;
import com.cmiot.report.bean.GatewayNotOnline;
import com.cmiot.report.dto.*;
import com.cmiot.report.facade.customer.CustomerOffGridFacade;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.service.CustomerOffGridService;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.UserAreaUtil;
import com.github.pagehelper.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@RestController
public class CustomerOffGridController implements CustomerOffGridFacade {

    private final static Logger logger = LoggerFactory.getLogger(CustomerOffGridController.class);

    @Autowired
    private CustomerOffGridService customerOffGridService;

    @Override
    public List getOffGridFactorThreshold() {
        return customerOffGridService.getOffGridFactorThreshold();
    }


    @Override
    public OffGridFactorThresholdDetail getOffGridFactorThresholdDetail(String factorKey) {
        return customerOffGridService.getOffGridFactorThresholdDetail(factorKey);
    }

    @Override
    public void setOffGridFactorIndicaterThreshold(OffGridFactorIndicatorThresholdSet factorSet) {
        customerOffGridService.setOffGridFactorIndicaterThreshold(factorSet);
    }


    @Override
    public void setOffGridOverallFactorThreshold(Object threshold) {
        customerOffGridService.setOffGridOverallFactorThreshold(threshold);
    }

    @Override
    public int getOffGridOverallFactorThreshold() {
        return customerOffGridService.getOffGridOverallFactorThreshold();
    }

    @Override
    public void setOffGridFactorThreshold(SetOffGridFactorThresholdRequest request) {
        customerOffGridService.setOffGridFactorThreshold(request);
    }

    @Override
    public List<BassType> getCustomerStatus() {
        return customerOffGridService.getCustomerStatus();
    }

    @Override
    public List<BassType> getEnterpriseValueCategory() {
        return customerOffGridService.getEnterpriseValueCategory();
    }

    @Override
    public OffGridCustStatisticResult getOffGridCustStatistic(String province, OffGridCustStatisticRequest request) {
        if (province != null) {
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (area == null || area.isEmpty()) {
                OffGridCustStatisticResult result = new OffGridCustStatisticResult();
                result.setSyntheticalRatio("");
                result.setSyntheticalValue("");
                result.setTerminalQualityRatio("");
                result.setTerminalQualityValue("");
                result.setNetworkQualityRatio("");
                result.setNetworkQualityValue("");
                result.setCustomerServiceRatio("");
                result.setCustomerServiceValue("");
                result.setBusinessOrderRatio("");
                result.setBusinessOrderValue("");
                result.setInstallationAndMaintenanceServiceRatio("");
                result.setInstallationAndMaintenanceServiceValue("");
                return result;
            }
            request.setProvince(area);
        }
        return customerOffGridService.getOffGridCustStatistic(request);
    }

    @Override
    public OffGridCustListResult getOffGridCustList(@RequestHeader(value = "province", required = false) String province,
                                                    OffGridCustListRequest request) {
        if (province != null) {
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (area == null || area.isEmpty()) {
                OffGridCustListResult result = new OffGridCustListResult();
                result.setPage(request.getPage());
                result.setPagaSize(request.getPageSize());
                result.setTotal(0L);
                result.setList(Collections.emptyList());
                return result;
            }
            request.setProvince(area);
        }
        return customerOffGridService.getOffGridCustList(request);
    }

    @Override
    public String exportOffGridCustomer(OffGridCustExportRequest request, String province, String uid) {
        if (province != null) {
            List<String> area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
            //如果为空，用户没有任何区域权限
            if (area == null || area.isEmpty()) {
                return "用户没有任何区域权限";
            }
            request.setProvince(area);
        }
        request.setUid(uid);
        return customerOffGridService.exportOffGridCustomer(request);
    }

    @Override
    public PageRes queryAnomalyRecordList(Long uid, String province, GatewayAnomalyRecordQuery query) {
        query.setLastday(DateUtil.getYesterdayInt());
        query.formatParamList();
        //query.setLastday(20240512);
        Page<GatewayAnomalyRecord> page = customerOffGridService.queryAnomalyRecordList(query);
        PageRes pageRes = new PageRes<>();
        pageRes.setList(page.getResult());
        pageRes.setTotal(page.getTotal());
        pageRes.setPage(page.getPageNum());
        pageRes.setPageSize(page.getPageSize());
        return pageRes;
    }

    @Override
    public PageRes<GatewayNotOnline> queryNotOnlineList(Long uid, String province, GatewayAnomalyRecordQuery query) {
        logger.info("查询长期离线用户接收到请求：{}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss:sss"));
        query.setLastday(DateUtil.getYesterdayInt());
        query.formatParamList();
        //query.setLastday(20240512);
        Page page = customerOffGridService.queryNotOnline(query);
        logger.info("查询长期离线用户查询完数据：{}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss:sss"));
        PageRes pageRes = new PageRes<>();
        pageRes.setList(page.getResult());
        pageRes.setTotal(page.getTotal());
        pageRes.setPage(page.getPageNum());
        pageRes.setPageSize(page.getPageSize());
        return pageRes;
    }

    @Override
    public PageRes<CustomerUneffect> queryUneffect(Long uid, String province, GatewayAnomalyRecordQuery query) {
        logger.info("查询失效用户接收到请求：{}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss:sss"));
        query.setLastday(DateUtil.getYesterdayInt());
        query.formatParamList();
        //query.setLastday(20240716);
        Page page = customerOffGridService.queryUneffect(query);
        logger.info("查询失效用户查询完数据：{}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss:sss"));
        PageRes pageRes = new PageRes<>();
        pageRes.setList(page.getResult());
        pageRes.setTotal(page.getTotal());
        pageRes.setPage(page.getPageNum());
        pageRes.setPageSize(page.getPageSize());
        return pageRes;
    }
}
