package com.cmiot.report.controller;

import com.cmiot.fgeo.common.error.FGEOException;
import com.cmiot.report.dto.*;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.facade.gateway.GatewayAppFacade;
import com.cmiot.report.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 网关统计分析
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@RestController
@Slf4j
public class GatewayAppController implements GatewayAppFacade {

    @Autowired
    GatewayService gatewayService;

    @Autowired
    DeviceService deviceService;

    @Autowired
    GatewayCountService gatewayCountService;

    @Autowired
    CustomerAllViewService customerAllViewService;

    @Autowired
    NetworkDeviceService networkDeviceService;

    @Override
    public List<GatewayFlowTrendResult> gatewayFlowTrend(GatewayQuery gatewayQuery, String eid) {
        log.info("gatewayFlowTrend, query param: {}, eid: {}", gatewayQuery, eid);
        gatewayQuery.setEid(eid);
        gatewayQuery.initParams();
        if (CollectionUtils.isEmpty(gatewayQuery.getGatewaySnList())) {
            log.error("网关流量趋势图getGatewayFlowTrend参数 sn 为空。");
            throw new FGEOException.BadRequest("参数 sn 为空");
        }
        List<GatewayFlowTrendResult> gatewayFlowTrendResults = gatewayService.getGatewayFlowTrend(gatewayQuery);
        log.info("gatewayFlowTrend, result size: {}", gatewayFlowTrendResults.size());
        return gatewayFlowTrendResults;
    }

    @Override
    public Map typeCpuAndRamAverage(GatewayQuery gatewayQuery, String eid) {
        log.info("typeCpuAndRamAverage, query param: {}, eid: {}", gatewayQuery, eid);
        gatewayQuery.setEid(eid);
        gatewayQuery.initParams();
        if (CollectionUtils.isEmpty(gatewayQuery.getGatewaySnList())) {
            log.info("网关流量趋势图typeCpuAndRamAverage参数 sn 为空。");
            throw new FGEOException.BadRequest();
        }
        Map result = gatewayService.getGatewayCpuAndRamStatistics(gatewayQuery);
        log.info("typeCpuAndRamAverage, result: {}", result);
        return result;
    }

    @Override
    public Map runReport(GatewayQuery gatewayQuery, Long eid) {
        log.info("小程序运行报告, 查询接口入参: {}, eid: {}", gatewayQuery, eid);
        Map result = new HashMap();
        gatewayQuery.setEid(String.valueOf(eid));
        try {
            // 0.企业名称
            String customerName = customerAllViewService.getCustomerName(eid);
            result.put("customerName", StringUtils.isEmpty(customerName) ? "-" : customerName);

            GatewayRunTimeResult gatewayRunTimeData = gatewayService.getGatewayRunTimeData(gatewayQuery);

            // 1.正在运行中的网关数量
            result.put("gatewayNumberRunning", gatewayRunTimeData.getGatewayNumberRunning());

            // 2.平均运行时长,单位小时
            result.put("runDurationAverage", gatewayRunTimeData.getRunDurationAverage());

            // 3.这些网关总的连续运行时长,单位小时
            // 网关超时阈值
            result.put("runDuration", gatewayRunTimeData.getRunDuration());

            // 4.告警网关数
            int alarmGatewayCount = gatewayCountService.getAlarmGatewayCount(gatewayQuery);
            result.put("alarmGatewayCount", alarmGatewayCount);

            // 5.这些组网设备的连续运行时长,单位小时
            // 如每10分钟上报一次,存在一条数据,则在线了10分钟
            // 组网设备超时阈值
            result.put("netRunDuration", gatewayRunTimeData.getNetRunDuration());

            // 时间超长的网关数
            long macOverCount = gatewayRunTimeData.getMacOverCount();
            result.put("gatewayCount", macOverCount);

            // 时间超长的组网设备数
            long subOverCount = gatewayRunTimeData.getSubOverCount();
            result.put("networkDeviceCount", subOverCount);

            // 获取用户周期内通过Wifi连接的下挂设备统计信息
            //Integer subDeviceNumber = deviceService.queryDeviceAppStatRunCount(gatewayQuery);
            //result.put("subDeviceNumber", subDeviceNumber == null ? 0 : subDeviceNumber);

            // 获取用户下挂设备信息
            DeviceAppStatRunVo subDeviceVo = deviceService.queryDeviceAppStatRunSpeed(gatewayQuery);

            // 1.周期内通过Wifi连接的下挂设备数量
            int subDeviceNumber = subDeviceVo.getSubDeviceCount();
            result.put("subDeviceNumber", subDeviceNumber);

            // 2.下挂设备平均网速(单位mb/s,保留两位小数)
            double subDeviceRateAverage = subDeviceVo.getSubDeviceRateAverage();
            result.put("subDeviceRateAverage", subDeviceRateAverage);

            // 3.平均连接比例(保留两位小数)
            double subDeviceConnectRateAvg = subDeviceVo.getSubDeviceConnectRateAvg();
            result.put("subDeviceConnectRateAvg", subDeviceConnectRateAvg);

            // 4.下挂设备使用总流量(上行+下行,单位GB,保留两位小数)
            double subDeviceTraffic = subDeviceVo.getSubDeviceTraffic();
            result.put("subDeviceTraffic", subDeviceTraffic);

            // TODO 访问网站统计 从下挂设备打标网站统计
            // 用户使用Wifi平均时长如果小于访问网站时长,则访问网站时长等于使用Wifi平均时长
            // t_subdevice_app_report_all 下挂设备表中来源于网关周期上报表,每15分钟上报一次,存在一条数据,则在线了15分钟

            // 1.今日/本周/本月/今年 wifi的平均时长
            double wifiConnectTime = subDeviceVo.getSubDeviceWifiConnectTime();
            result.put("wifiConnectTime", wifiConnectTime);

            // 2.当前正在连接wifi上网的用户数
            int wifiUserCount = subDeviceVo.getWifiUserCount();
            result.put("wifiUserCount", wifiUserCount);

            // 3.最常访问的网站
            String favoriteWebsite = subDeviceVo.getFavoriteWebsite();
            result.put("favoriteWebsite", StringUtils.isEmpty(favoriteWebsite) ? "-" : favoriteWebsite);

            // 4.平均访问时长(小时)
            double visitWebTime = subDeviceVo.getSubDeviceWifiVisitWebTime();
            result.put("visitWebTime", visitWebTime);

            // 5.平均访问次数
            double subDeviceVisitWebCount = subDeviceVo.getSubDeviceVisitWebCount();
            result.put("subDeviceVisitWebCount", subDeviceVisitWebCount);

        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("小程序运行报告, 查询接口出参= {}", result);
        return result;
    }

    @Override
    public List<GatewayRunTimeDetail> gatewayList(GatewayQuery gatewayQuery, Long eid) {
        log.info("gatewayList, query param: {}, eid: {}", gatewayQuery, eid);
        gatewayQuery.setEid(String.valueOf(eid));
        gatewayQuery.initParams();
        List<GatewayRunTimeDetail> gatewayRunTimeDetails = gatewayService.getGatewayRunTimeList(gatewayQuery);
        log.info("gatewayList, result: {}", gatewayRunTimeDetails);
        return gatewayRunTimeDetails;
    }

    @Override
    public List<NetworkDeviceRunTimeDetail> networkDeviceList(NetworkDeviceRequset request, Long eid) {
        request.setEid(String.valueOf(eid));
        return networkDeviceService.networkDeviceList(request);
    }

    public List<Object> getGatewayFlowVelocityList(Long eid) {
        log.info("getGatewayFlowVelocityList, eid: {}", eid);
        if (eid == null) {
            log.error("getGatewayFlowVelocityList eid is null");
            throw new FGEOException.BadRequest("eid is null");
        }
        GatewayQuery gatewayQuery = new GatewayQuery();
        gatewayQuery.setEid(String.valueOf(eid));
        return gatewayService.getGatewayFlowVelocityListByEnterprise(gatewayQuery);
    }

    @Override
    public PageRes<Map<String, Object>> getPageGatewayFlowVelocity(Long uid, Long eid, GatewayQuery gatewayQuery) {
        log.info("getPageGatewayFlowVelocity, uid: {}, eid: {}, gatewayQuery：{}", uid, eid, gatewayQuery);
        if (uid == null || eid == null) {
            log.error("getPageGatewayFlowVelocity uid or eid is null");
            throw new FGEOException.BadRequest("uid or eid is not null");
        }
        gatewayQuery.setEid(String.valueOf(eid));
        gatewayQuery.setUid(uid);
        return gatewayService.getPageGatewayFlowVelocity(gatewayQuery);
    }

    @Override
    public RealtimeFlowVelocityMessageVO getPollingRealtimeFlowVelocityMonitorReport(Long eid, GatewayQuery gatewayQuery) {
        log.info("getPollingRealtimeFlowVelocityMonitorReport, eid: {}, gatewayQuery：{}", eid, gatewayQuery);
        if (eid == null || gatewayQuery == null || StringUtils.isEmpty(gatewayQuery.getGatewayMac())) {
            log.error("getPollingRealtimeFlowVelocityMonitorReport eid or mac is null");
            throw new FGEOException.BadRequest("eid or mac is null");
        }
        gatewayQuery.setEid(String.valueOf(eid));
        return gatewayService.getPollingRealtimeFlowVelocityMonitorReport(gatewayQuery);
    }


    @Override
    public List<Map<String, Object>> getGatewayWanConnNumList(Long eid) {
        log.info("getGatewayWanConnNumList, eid: {}", eid);
        if (eid == null) {
            log.error("getGatewayWanConnNumList eid is null");
            throw new FGEOException.BadRequest("eid is null");
        }
        return gatewayService.getGatewayWanConnNumByEnterprise(eid);
    }

    @Override
    public PageRes<Map<String, Object>> getPageGatewayWanConnNum(Long uid, Long eid, GatewayQuery gatewayQuery) {
        log.info("getPageGatewayWanConnNum, uid: {}, eid: {}, gatewayQuery：{}", uid, eid, gatewayQuery);
        if (uid == null || eid == null) {
            log.error("getPageGatewayWanConnNum uid or eid is null");
            throw new FGEOException.BadRequest("uid or eid is not null");
        }
        gatewayQuery.setEid(String.valueOf(eid));
        gatewayQuery.setUid(uid);
        return gatewayService.getPageGatewayWanConnNum(gatewayQuery);
    }

    @Override
    public RealtimeWanConnMessageVO getPollingRealtimeWanConnReport(Long eid, GatewayQuery gatewayQuery) {
        log.info("getPollingRealtimeWanConnReport, eid: {}, gatewayQuery：{}", eid, gatewayQuery);
        if (eid == null || gatewayQuery == null || StringUtils.isEmpty(gatewayQuery.getGatewayMac())) {
            log.error("getPollingRealtimeWanConnReport eid or mac is null");
            throw new FGEOException.BadRequest("eid or mac is null");
        }
        gatewayQuery.setEid(String.valueOf(eid));
        return gatewayService.getPollingRealtimeWanConnReport(gatewayQuery);
    }
}
