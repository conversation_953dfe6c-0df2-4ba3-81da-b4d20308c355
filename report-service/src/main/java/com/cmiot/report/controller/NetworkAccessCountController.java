package com.cmiot.report.controller;

import com.cmiot.report.dto.ServiceStatisticResult;
import com.cmiot.report.dto.customer.*;
import com.cmiot.report.facade.gateway.NetworkAccessCountFacade;
import com.cmiot.report.service.NetworkAccessCountService;
import com.cmiot.report.util.UserAreaUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * @Classname NetworkAccessCountController
 * @Description
 * @Date 2022/8/9 16:11
 * @Created by lei
 * todo
 */
@RestController
@Slf4j
public class NetworkAccessCountController implements NetworkAccessCountFacade {

    @Autowired
    private NetworkAccessCountService networkAccessCountService;

    @Override
    public NetworkUsageChartData getNetworkUsage(String province, NetworkUsageChartRequest request) {
        try {
            if (province != null) {
                String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
                //如果为空，用户没有任何区域权限
                if (StringUtils.isBlank(area)) {
                    NetworkUsageChartData result = new NetworkUsageChartData();
                    result.setxAxis(Collections.emptyList());
                    result.setActualNetworkTrafficData(Collections.emptyList());
                    result.setUseBroadbandRatioData(Collections.emptyList());
                    return result;
                }
                request.setProvince(area);
            }
            return this.networkAccessCountService.getNetworkUsage(request);
        } catch (Exception e) {
            log.error("查询失败, {}",e.getMessage(), e);
            return null;
        }
    }

    @Override
    public NetworkUsagePieData getNetworkUsageDeviceStatus(String province, NetworkUsageChartRequest request) {
        try {
            if (province != null) {
                String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
                //如果为空，用户没有任何区域权限
                if (StringUtils.isBlank(area)) {
                    NetworkUsagePieData result = new NetworkUsagePieData();
                    result.setNormal(0);
                    result.setBusyness(0);
                    result.setLeisure(0);
                    return result;
                }
                request.setProvince(area);
            }
            return this.networkAccessCountService.getNetworkUsageDeviceStatus(request);
        } catch (Exception e) {
            log.error("查询失败, {}",e.getMessage(), e);
            return null;
        }
    }

    @Override
    public NetworkPerferChartData getProgramPreference(String province, NetworkPerferRequest request) {
        try {
            if (province != null) {
                String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
                //如果为空，用户没有任何区域权限
                if (StringUtils.isBlank(area)) {
                    NetworkPerferChartData result = new NetworkPerferChartData();
                    result.setData(Collections.emptyList());
                    result.setListRank(Collections.emptyList());
                    result.setXAxis(Collections.emptyList());
                    return result;
                }
                request.setProvince(area);
            }
            return this.networkAccessCountService.getProgramPreference(request);
        } catch (Exception e) {
            log.error("查询失败, {}",e.getMessage(), e);
            return null;
        }
    }

    @Override
    public NetworkPerferChartData getSitePreference(String province, NetworkPerferRequest request) {
        try {
            if (province != null) {
                String area = UserAreaUtil.userAndSelectIntersection(province, request.getProvince());
                //如果为空，用户没有任何区域权限
                if (StringUtils.isBlank(area)) {
                    NetworkPerferChartData result = new NetworkPerferChartData();
                    result.setData(Collections.emptyList());
                    result.setListRank(Collections.emptyList());
                    result.setXAxis(Collections.emptyList());
                    return result;
                }
                request.setProvince(area);
            }
            return this.networkAccessCountService.getSitePreference(request);
        } catch (Exception e) {
            log.error("查询失败, {}",e.getMessage(), e);
            return null;
        }
    }
}
