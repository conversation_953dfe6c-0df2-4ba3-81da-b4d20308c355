package com.cmiot.report;

import com.cmiot.fgeo.ams.facade.feign.ManageFeignClient;
import com.cmiot.report.config.GlobalExceptionHandler;
import com.cmiot.report.otherserverfeign.ApiServerDeviceFeignClient;
import com.github.pagehelper.dialect.helper.MySqlDialect;
import com.github.pagehelper.page.PageAutoDialect;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR> Date 2018/5/23
 */
@SpringBootApplication
@EnableConfigurationProperties
@MapperScan("com.cmiot.report.mapper")
@Import({
        GlobalExceptionHandler.class
})
@EnableDiscoveryClient
//@EnableFeignClients(basePackages = {"com.cmiot.report.facade.device", "com.cmiot.report.facade.gateway")
@EnableFeignClients(clients = {
        com.cmiot.fileserver.facade.feign.UploadFeignClientV2.class,
        com.cmiot.fileserver.facade.feign.DownLoadFeignClient.class,
        com.cmiot.fgeo.taskmanager.facade.feign.ExportServiceFeignClient.class,
        com.cmiot.fgeo.devicemanager.gatewayapi.GatewayApiServiceFeignClient.class,
        com.cmiot.fgeo.ams.facade.feign.ManageApiFeignClient.class,
        com.cmiot.e.apiservice.facade.ApiServiceFeignClient.class,
        com.cmiot.fgeo.pluginmanager.facade.feign.PluginInfoFeignClient.class,
        com.cmiot.qyzw.analyse.api.feignclient.BlinkPluginStatFeignClient.class,
        com.cmiot.fgeo.ams.facade.feign.EnterpriseFeignClient.class,
        ApiServerDeviceFeignClient.class,
        ManageFeignClient.class
})
@EnableScheduling
public class Application {

    public static void main(String[] args) {
        PageAutoDialect.registerDialectAlias("clickhouse", MySqlDialect.class);
        SpringApplication.run(Application.class, args);
    }

}
