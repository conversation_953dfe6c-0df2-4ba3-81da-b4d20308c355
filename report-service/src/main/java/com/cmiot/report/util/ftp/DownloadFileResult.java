package com.cmiot.report.util.ftp;

/**
 * 下载文件的返回结果
 */
public class DownloadFileResult extends FtpUtilResult {
	/**
	 * 返回保存文件路径
	 */
	private String returnSaveFilePath;

	public DownloadFileResult() {
	}

	public DownloadFileResult(int code, String desc) {
		super(code, desc);
	}

	public DownloadFileResult(String returnSaveFilePath) {
		this.returnSaveFilePath = returnSaveFilePath;
	}

	public DownloadFileResult(int code, String desc, String returnSaveFilePath) {
		super(code, desc);
		this.returnSaveFilePath = returnSaveFilePath;
	}

	public String getReturnSaveFilePath() {
		return returnSaveFilePath;
	}

	public void setReturnSaveFilePath(String returnSaveFilePath) {
		this.returnSaveFilePath = returnSaveFilePath;
	}

	@Override
	public String toString() {
		final StringBuffer sb = new StringBuffer("DownloadFileResult{");
		sb.append("returnSaveFilePath='").append(returnSaveFilePath).append('\'');
		sb.append('}');
		return sb.toString();
	}
}
