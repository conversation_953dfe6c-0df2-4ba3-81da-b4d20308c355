package com.cmiot.report.util;

import cn.hutool.core.collection.CollUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Classname UserAreaUtil
 * @Description
 * @Date 2022/12/21 17:12
 * @Created by lei
 */
public class UserAreaUtil {

    private static String areaSeparator = ",";

    /**
     * 返回 userArea , selectArea交集
     * 如果 userArea为空，返回空
     *
     * @param userArea
     * @param selectArea
     * @return
     */
    public static String userAndSelectIntersection(String userArea, String selectArea) {
        if (StringUtils.isNotBlank(userArea)) {
            if (StringUtils.isBlank(selectArea)) {
                return userArea;
            } else {
                //求交集
                List<String> selectAreaList = Arrays.asList(userArea.split(areaSeparator));
                List<String> areaList = Arrays.asList(selectArea.split(areaSeparator));
                Collection<String> reAreas = CollUtil.intersection(selectAreaList, areaList);
                String intersectionArea = StringUtils.join(reAreas, areaSeparator);
                return intersectionArea;
            }
        } else {
            return "";
        }
    }

    public static List<String> userAndSelectIntersection(String userArea, List<String> selectArea) {
        if (StringUtils.isNotBlank(userArea)) {
            if (selectArea == null || selectArea.isEmpty()) {
                return Arrays.asList(userArea.split(areaSeparator));
            } else {
                //求交集
                List<String> selectAreaList = Arrays.asList(userArea.split(areaSeparator));
                Collection<String> reAreas = CollUtil.intersection(selectAreaList, selectArea);
                return new ArrayList<>(reAreas);
            }
        } else {
            return Collections.emptyList();
        }
    }
}
