package com.cmiot.report.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户权限工共方法
 */
@Slf4j
public class PermissionUtils {

    // 获取用户省市权限交集
    public static List<String> getUserPermission(String province, List<String> provinceList) {
        List<String> list = new ArrayList<>();
        try {
            List<String> userPermissions  = getOwnPermission(province);

            // 用户查询省市数据为空的情况，为用户查询全部省市，只返回用户拥有的权限
            if (CollectionUtils.isEmpty(provinceList)) {
                list.addAll(userPermissions);
            } else {
                // 获取交集
                for (String s : provinceList) {
                    if (userPermissions.contains(s)) {
                        list.add(s);
                    }
                }
            }

        } catch (Exception e) {
            log.error("getUserPermission fail:" + e);
        }
        return list;
    }

    public static List<String> getOwnPermission(String province) {
        List<String> userPermissions = new ArrayList<>();
        try {
            userPermissions  = StringUtils.isEmpty(province) ? new ArrayList<>() : Arrays.stream(
                    province.split(",")).map(i -> i.trim()).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getOwnPermission fail" + e);
        }
        return userPermissions;
    }

    public static boolean userAllPermission(String province) {
        boolean result = false;
        try {
            List<String> userPermissions  = StringUtils.isEmpty(province) ? new ArrayList<>() : Arrays.stream(
                    province.split(",")).map(i -> i.trim()).collect(Collectors.toList());
            if (userPermissions.size() == 35) {
                result = true;
            }
        } catch (Exception e) {
            log.error("userAllPermission fail:" + e);
        }
        return result;
    }

    public static List<String> getResult(String province, List<String> provinces) {
        List<String> objects = new ArrayList<>();
        objects.addAll(provinces);
        try {
            // 用户区域权限与查询区域取交集
            if (!PermissionUtils.userAllPermission(province)) {
                List<String> userPermission = PermissionUtils.getUserPermission(province, provinces);
                if (CollectionUtils.isEmpty(userPermission)) {
                    List<String> ownPermission = PermissionUtils.getOwnPermission(province);
                    return ownPermission;
                } else {
                   return userPermission;
                }
            }
            return objects;
        } catch (Exception e){
            log.error("getResult fail");
        }
        return objects;
    }

    public static void main(String[] args) {
        String province = "110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000";
        List<String> provinces = new ArrayList<>();
        provinces.add("110000");
        List<String> permission = PermissionUtils.getResult(province, provinces);
        System.out.println(permission);
        if (!CollectionUtils.isEmpty(permission)) {
            provinces.clear();
            provinces.addAll(permission);
        }
        System.out.println(permission);
        System.out.println(provinces);

    }


    private static void test1() {
        List<String> provinces = new ArrayList<>();
        provinces.add("1");
        provinces.add("...");
        String province = "1,2,3";
        List<String> permission = PermissionUtils.getResult(province, provinces);
        System.out.println(permission);
        if (!CollectionUtils.isEmpty(permission)) {
            provinces.clear();
            provinces.addAll(permission);
        }
    }

    private static void test2() {
        List<String> cityCodeList = new ArrayList<>();
        // 地市code,地市名称
        Map<String, String> dicCityMap = new HashMap<>();

        Map<String, String> data = new HashMap<>();
        data.put("a", "1");
        data.put("b", "2");
        data.put("c", "3 ");
        String cityCodes = "a,b";
        if(true) {
            // 获取当前省市全部地市
            Set<String> strings = data.keySet();
            List<String> ls = new ArrayList(strings);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(cityCodes)) {
                cityCodeList = (List<String>) CollectionUtils.arrayToList(org.apache.commons.lang3.StringUtils.split(cityCodes, "\\,"));
                if (ls.containsAll(cityCodeList)) {
                    System.out.println(cityCodeList);
                } else {
                    System.out.println(ls);
                }
            } else {
                System.out.println(ls);

            }

        }
    }

    private static void test3() {
        List<String> ls = new ArrayList();
        ls.add("1");
        ls.add("2");
        ls.add("3");
        List<String> citys = new ArrayList();
        citys.add("4");
        citys.add("5");
        if (!CollectionUtils.isEmpty(citys)) {
            if (!ls.containsAll(citys)) {
                citys.clear();
                citys.addAll(ls);
            }
        } else {
            citys.clear();
            citys.addAll(ls);
        }
        System.out.println(citys);
    }


    private static void test4() {
        String provCodes = "";
        String province = "1,2,3,4,5";
        List<String> provList = null;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(provCodes)) {
            provList = (List<String>) CollectionUtils.arrayToList(org.apache.commons.lang3.StringUtils.split(provCodes, "\\,"));
            List<String> userPermission = PermissionUtils.getResult(province, provList);
            provList = new ArrayList<>(provList);
            provList.clear();
            provList.addAll(userPermission);
//            query.setProvList(provList);
//            typeQuery.setProvList(provList);
//            onOffCriteria.andProvinceCodeIn(provList);
            System.out.println(provList);
        } else {
            if (!PermissionUtils.userAllPermission(province)) {
                provList = PermissionUtils.getOwnPermission(province);
//                query.setProvList(provList);
//                typeQuery.setProvList(provList);
//                onOffCriteria.andProvinceCodeIn(provList);
                System.out.println(provList);
            }
        }


        //city多选
        String cityCodes= "";
        List<String> ls = new ArrayList();
        ls.add("1");
        ls.add("2");
        ls.add("3");
        List<String> cityList = null;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(cityCodes)) {
            cityList = (List<String>) CollectionUtils.arrayToList(org.apache.commons.lang3.StringUtils.split(cityCodes, "\\,"));
            if (!ls.containsAll(cityList)) {
                cityList = ls;
            }
            System.out.println(cityList);
//            query.setCityList(cityList);
//            typeQuery.setCityList(cityList);
//            onOffCriteria.andCityCodeIn(cityList);

        } else {
            cityList = ls;
            System.out.println(cityList);
//            query.setCityList(cityList);
//            typeQuery.setCityList(cityList);
//            onOffCriteria.andCityCodeIn(cityList);
        }

    }

    private static void test5() {
        String a = "";
        String province = "110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000";
        if (!org.apache.commons.lang3.StringUtils.isEmpty(a)) {
            String[] split = a.split(",");
            List<String> list = Arrays.asList(split);
            List<String> permission = PermissionUtils.getResult(province, list);
            String[] strings = permission.toArray(new String[permission.size()]);
            System.out.println(strings);
            // deviceQuery.setProvinceCode(strings);
        } else {
            if (!PermissionUtils.userAllPermission(province)) {
                List<String> ownPermission = PermissionUtils.getOwnPermission(province);
                String[] strings = ownPermission.toArray(new String[ownPermission.size()]);
                System.out.println(strings);
            }

        }

    }
}
