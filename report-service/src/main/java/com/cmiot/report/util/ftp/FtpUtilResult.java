package com.cmiot.report.util.ftp;

/**
 * FTP返回结果
 */
public class FtpUtilResult {
	private int code;
	private String desc;

	public FtpUtilResult() {
	}

	public FtpUtilResult(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	@Override
	public String toString() {
		final StringBuffer sb = new StringBuffer("FtpUtilResult{");
		sb.append("code=").append(code);
		sb.append(", desc='").append(desc).append('\'');
		sb.append('}');
		return sb.toString();
	}
}
