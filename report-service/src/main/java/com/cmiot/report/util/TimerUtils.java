package com.cmiot.report.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.TextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 时间戳处理工具。
 *
 * <AUTHOR>
 */
public class TimerUtils {
    private static Logger logger = LoggerFactory.getLogger(TimerUtils.class);

    private static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static final DateTimeFormatter YYYYMMDDHHMMSS = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    public static final DateTimeFormatter YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 获取UUID
     */
    public static String uuid() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString().replace("-", "");
    }
    
    /**
     * 昨天的起始时间戳
     */
	public static Long getBeginDayOfYesterday() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return cal.getTimeInMillis();
    }
	
	/**
	 * 昨天的结束时间戳
	 */
    public static Long getEndDayOfYesterDay() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return cal.getTimeInMillis();
    }

    /**
     * 删除目录文件夹，及下面的所有文件
     *
     * @param dir
     * @return
     */
    public static boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (int i = 0; i < children.length; i++) {
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }
        // 目录此时为空，可以删除
        return dir.delete();
    }

    /**
     * 前一天
     * return yyyyMMdd
     */
    public static String getYYYYMMDD() {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 前二天
     * return yyyyMMdd
     */
    public static String getPreYYYYMMDD() {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -2);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 前一天
     * return yyyy-MM-dd
     */
    public static String getYYYYMMDD2() {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 前二天
     * return yyyy-MM-dd
     */
    public static String getPreYYYYMMDD2() {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -2);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 前一天
     * return yyyy-MM
     */
    public static String getYYYYMM() {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        return sdf.format(calendar.getTime());
    }

    /**
     * 返回当前时间， 格式yyyyMMddHHmmss
     */
    public static String getYYYYMMDDHHMMSS() {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(new Date());
    }

    /**
     * 返回当前时间， 格式yyyy-MM-dd HH:mm:ss
     */
    public static String getYYYYMMDDHHMMSS2() {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date());
    }

    /**
     * 获取当天日期 yyyyMMdd
     */
    public static String getCurrentDayYYYYMMDD() {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        return sdf.format(new Date());
    }

    /**
     * 获取今天和某天的时间戳差
     */
    public static long getCurrentDayYYYYMMDDDifference(String day) {
        long time = 0;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Date today = new Date();
            Date pastDay = sdf.parse(day);
            time = today.getTime() / 1000 - pastDay.getTime() / 1000;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return time;
    }

    /**
     * 获取当前月份  yyyy-MM
     */
    public static String getCurrentDayYYYYMM() {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

        return sdf.format(new Date());
    }

    /**
     * 获取当前月份  yyyyMM
     */
    public static String getCurrentDayYYYYMM2() {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");

        return sdf.format(new Date());
    }

    /**
     * 获取date
     */
    public static Date getStringToDate(String time) {
        Date date = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            date = sdf.parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    public static String unixToString(Integer unixTime, String patten) {
        SimpleDateFormat sdf = new SimpleDateFormat(patten);
        return sdf.format(unixTime * 1000L);
    }

    /**
     * 根据传入时间返回周
     *
     * @param date yyyyMMdd
     * @return week 2018-35
     */
    public static String getWeek(String date) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            calendar.setTime(sdf.parse(date));
            int year = calendar.get(Calendar.YEAR);
            int week = calendar.get(Calendar.WEEK_OF_YEAR);
            return year + "-" + week;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 根据传入时间返回上一周
     *
     * @param date yyyyMMdd
     * @return week 2018-35
     */
    public static String getPreWeek(String date) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            calendar.setTime(sdf.parse(date));
            calendar.add(Calendar.DATE, -7);
            int year = calendar.get(Calendar.YEAR);
            int week = calendar.get(Calendar.WEEK_OF_YEAR);
            return year + "-" + week;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 根据传入时间返回上一周
     *
     * @param date yyyyMMdd
     * @return week 2018-35
     */
    public static String getPreWeek2(String date) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            calendar.setTime(sdf.parse(date));
            calendar.add(Calendar.DATE, -7);
            int year = calendar.get(Calendar.YEAR);
            int week = calendar.get(Calendar.WEEK_OF_YEAR);
            return year + "" + week;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获取当前周的时间区间
     */
    public static String[] getWeekTimeInterval() {
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        int dayOfWeek = calendar1.get(Calendar.DAY_OF_WEEK) - 1;
        System.out.println(dayOfWeek);
        int offset1 = 1 - dayOfWeek;
        int offset2 = 7 - dayOfWeek;
        if (dayOfWeek == 0) {
            offset1 = -6;
            offset2 = 0;
        }
        calendar1.add(Calendar.DATE, offset1);
        calendar2.add(Calendar.DATE, offset2);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastBeginDate = sdf.format(calendar1.getTime());
        String lastEndDate = sdf.format(calendar2.getTime());
        System.out.println(lastBeginDate + "_" + lastEndDate);
        return new String[]{lastBeginDate, lastEndDate};
    }

    /**
     * 获取上一周的时间区间
     */
    public static String[] getPreWeekTimeInterval() {
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        int dayOfWeek = calendar1.get(Calendar.DAY_OF_WEEK) - 1;
        int offset1 = 1 - dayOfWeek;
        int offset2 = 7 - dayOfWeek;
        if (dayOfWeek == 0) {
            offset1 = -6;
            offset2 = 0;
        }
        calendar1.add(Calendar.DATE, offset1 - 7);
        calendar2.add(Calendar.DATE, offset2 - 7);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String lastBeginDate = sdf.format(calendar1.getTime());
        String lastEndDate = sdf.format(calendar2.getTime());
        return new String[]{lastBeginDate, lastEndDate};
    }

    /**
     * 获取传入时间周的时间区间
     */
    public static String[] getWeekTimeIntervalDate(String date) {
        SimpleDateFormat sdf0 = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        try {
            calendar1.setTime(sdf0.parse(date));
            calendar2.setTime(sdf0.parse(date));
            int dayOfWeek = calendar1.get(Calendar.DAY_OF_WEEK) - 1;
            if (dayOfWeek == 0) {
                dayOfWeek = 7;
            }
            int offset1 = 1 - dayOfWeek;
            int offset2 = 7 - dayOfWeek;
            calendar1.add(Calendar.DATE, offset1);
            calendar2.add(Calendar.DATE, offset2);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String lastBeginDate = sdf.format(calendar1.getTime());
            String lastEndDate = sdf.format(calendar2.getTime());
            return new String[]{lastBeginDate, lastEndDate};
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取某一周的开始、结束日期
     *
     * @return 2018-09-10,2018-09-16
     */
    public static String[] getWeekTimeInterval(String date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setWeekDate(Integer.parseInt(date.split("-")[0]), Integer.parseInt(date.split("-")[1]), 1);
        calendar.add(Calendar.DATE, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String begin = sdf.format(calendar.getTime());
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setWeekDate(Integer.parseInt(date.split("-")[0]), Integer.parseInt(date.split("-")[1]), 7);
        calendar2.add(Calendar.DATE, 1);
        String end = sdf.format(calendar2.getTime());

        return new String[]{begin, end};
    }

    /**
     * 传入年月获取当前月最后一天的日期
     *
     * @return 2018-09-30
     */
    public static String getMonthLastDay(String yearMonth) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, Integer.parseInt(yearMonth.split("-")[0]));
        //设置月份
        cal.set(Calendar.MONTH, Integer.parseInt(yearMonth.split("-")[1]) - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());
        return lastDayOfMonth;
    }

    /**
     * 前一天的开始
     * return unix时间戳
     */
    public static long getUnixStampStart() {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_MONTH, -1);// 昨天-1天
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime().getTime() / 1000;
    }

    /**
     * 前一天的结束
     * return unix时间戳
     */
    public static long getUnixStampEnd() {
        Calendar c = Calendar.getInstance();
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime().getTime() / 1000 - 1;
    }

    /**
     * daytime
     * return hbase存储结束时间戳
     */
    public static int getDaytimeHbaseEndTimeStamp(String daytime) {
        int ee = 0;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date daytimeDate = simpleDateFormat.parse(daytime);
            //获取明天的日期
            Calendar c = Calendar.getInstance();
            c.setTime(daytimeDate);
            c.add(Calendar.DAY_OF_MONTH, 1);// 今天+1天
            //将小时至0
            c.set(Calendar.HOUR_OF_DAY, 0);
            //将分钟至0
            c.set(Calendar.MINUTE, 0);
            //将秒至0
            c.set(Calendar.SECOND, 0);
            //将毫秒至0
            c.set(Calendar.MILLISECOND, 0);
            Date tomorrow = c.getTime();
            //获取终止时间的时间戳
            long stopTimeStamp = tomorrow.getTime() / 1000;
            Long et = stopTimeStamp - 1514736000 - 1;
            ee = et.intValue();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return ee;
    }

    public static Integer getYesterday() {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return Integer.valueOf(sdf.format(calendar.getTime()));

    }


    public static Integer getToday() {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        return Integer.valueOf(sdf.format(calendar.getTime()));

    }


    /**
     * 当月的开始
     * return unix时间戳
     */
    public static long getUnixStampMonthStart() {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, 0);
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime().getTime() / 1000;
    }

    /**
     * 前一月
     * return yyyyMM
     */
    public static String getPreMonth() {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        return sdf.format(calendar.getTime());
    }

    /**
     * 当前日期的前一月
     * return yyyyMM
     */
    public static String getPreMonthDate() {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取与当前日期间隔天数的日期
     *
     * @param date 当前日期
     * @param days 间隔天数
     * @return 日期
     */
    public static Date getIntervalDate(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, days);
        return calendar.getTime();
    }

    /**
     * 将20190218231425转化为 格式yyyy-MM-dd HH:mm:ss
     */
    public static String getStringToYYMMDDHHMMSS(String time) {
        String s = null;
        if (time != null && time.length() == 14) {
            s = time.substring(0, 4) + "-" + time.substring(4, 6) + "-" + time.substring(6, 8) + " " + time.substring(8, 10) + ":" + time.substring(10, 12) + ":" + time.substring(12, 14);
        }
        return s;
    }

    /**
     * 将20190218转化为 格式yyyy-MM-dd
     */
    public static String getStringToYYMMDD(String time) {
        String s = null;
        if (!StringUtils.isBlank(time) && time.length() == 8) {
            s = time.substring(0, 4) + "-" + time.substring(4, 6) + "-" + time.substring(6, 8);
        }
        return s;
    }

    public static List<String> getDays(String startTime, String endTime) {

        // 返回的日期集合
        List<String> days = new ArrayList<String>();

        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);

            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                days.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }

        return days;
    }

    public static Date formatDate(String s) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return dateFormat.parse(s);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 输入20190218  输出周的第几天 1是星期天 2是星期一 7是星期六
     */
    public static int getDayOfWeek(String date) {
        int i = 0;
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            calendar.setTime(sdf.parse(date));

            i = calendar.get(Calendar.DAY_OF_WEEK);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return i;
    }

    //将周的起止时间转化为时间戳
    public static List<Long> weekUtil(String weekStartTime, String weekStopTime) {
        List<Long> list = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date weekStartTimeDate = simpleDateFormat.parse(weekStartTime);
            Date weekStopTimeDate = simpleDateFormat.parse(weekStopTime);
            //获取起始时间的时间戳
            long weekStartTimeStamp = weekStartTimeDate.getTime();
            //获取终止日期的时间戳
            Calendar c = Calendar.getInstance();
            c.setTime(weekStopTimeDate);
            c.add(Calendar.DAY_OF_MONTH, 1);// 今天+1天
            //将小时至0
            c.set(Calendar.HOUR_OF_DAY, 0);
            //将分钟至0
            c.set(Calendar.MINUTE, 0);
            //将秒至0
            c.set(Calendar.SECOND, 0);
            //将毫秒至0
            c.set(Calendar.MILLISECOND, 0);
            Date weekStopTimeTomorrow = c.getTime();
            long weekStopTimeStamp = weekStopTimeTomorrow.getTime();
            list.add(weekStartTimeStamp);
            list.add(weekStopTimeStamp);
        } catch (ParseException e) {
            logger.error("invoke weekUtil exception:{}", e);
        }
        return list;
    }

    /**
     * 日期格式字符串转换成时间戳
     *
     * @param dateStr 字符串日期
     * @param format  如：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Long Date2TimeStamp(String dateStr, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.parse(dateStr).getTime() / 1000;
        } catch (Exception e) {
            logger.error("invoke Date2TimeStamp exception:{}", e);
        }
        return 0L;
    }

    /**
     * Java将Unix时间戳转换成指定格式日期字符串
     *
     * @param timestampString 时间戳 如："1473048265";
     * @param formats         要格式化的格式 默认："yyyy-MM-dd HH:mm:ss";
     * @return 返回结果 如："2016-09-05 16:06:42";
     */
    public static String TimeStamp2Date(String timestampString, String formats) {
        try {
            if (TextUtils.isEmpty(formats))
                formats = "yyyy-MM-dd HH:mm:ss";
            long timestampx = Long.parseLong(timestampString);
            Long timestamp = timestampx * 1000;
            String date = new SimpleDateFormat(formats, Locale.CHINA).format(new Date(timestamp));
            return date;
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 取得当前时间戳（精确到秒）
     *
     * @return nowTimeStamp
     */
    public static Long getNowTimeStamp() {
        long time = System.currentTimeMillis();
        long nowTimeStamp = time / 1000;
        return nowTimeStamp;
    }


    /**
     * 将时间字符串转换为Date对象。
     *
     * @param deadline 日期。例如： 2019-12-05
     * @return
     * @throws ParseException
     */
    public static Date getDate(String deadline) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.parse(deadline);
    }

    /**
     * 将当前时间戳，转换为日期字符串。
     *
     * @param createTime 精确到表的时间戳
     * @return 返回示例： 2019-12-06 14:01:01
     */
    public static String getCreateTime(long createTime) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar instance = Calendar.getInstance();
        Date time = instance.getTime();
        time.setTime(createTime);
        return format.format(time);
    }

    public static String getBlankCreateTime(long createTime) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        Calendar instance = Calendar.getInstance();
        Date time = instance.getTime();
        time.setTime(createTime);
        return format.format(time);
    }

    /**
     * 将当前时间戳，转换为日期字符串。
     * 精确到表的时间戳
     *
     * @return 日期字符串
     */
    public static String getTimeFromDate(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        return format.format(date);
    }

    /**
     * LocalDateTime转Date
     */
    public static Date convertToDate(LocalDateTime localDateTime) {
        return new Date(Timestamp.valueOf(localDateTime).getTime());
    }

    /**
     * LocalDateTime转String yyyy-MM-dd HH:mm:ss
     */
    public static String convertToString(LocalDateTime localDateTime) {
        return YYYY_MM_DD_HH_MM_SS.format(localDateTime);
    }

    /**
     * LocalDateTime转String yyyyMMddHHmmss
     */
    public static String convertToStringWithoutBlank(LocalDateTime localDateTime) {
        return YYYYMMDDHHMMSS.format(localDateTime);
    }

    /**
     * LocalDateTime转10位时间戳
     */
    public static String convertToStampOf10(LocalDateTime localDateTime) {
        return String.valueOf(Timestamp.valueOf(localDateTime).getTime() / 1000);
    }

    /**
     * 获取最近的一个周日的日期。格式如： yyyy-MM-dd
     *
     * @return
     */
    public static String getSundayOfWeek() {
        // 获取本周周一的日期
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cld = Calendar.getInstance();
        cld.setFirstDayOfWeek(Calendar.MONDAY);
        cld.setTimeInMillis(System.currentTimeMillis());
        cld.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        // 获取周一的前一天
        int days = cld.get(Calendar.DATE);
        cld.set(Calendar.DATE, days - 1);
        return df.format(cld.getTime());
    }

    public static List<String> getLastThirtyDays(String time) {

        // 返回的日期集合
        List<String> days = new ArrayList<String>();

        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        try {
            Date end = dateFormat.parse(time);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
//            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)

            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(end);
            tempStart.add(Calendar.DATE, -30);

            while (tempStart.before(tempEnd)) {
                days.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }

        return days;
    }

    /**
     * 获取间隔的日期集合
     */
    public static List<String> getDays(String startDate, String endDate, DateTimeFormatter dtf) {
        List<String> list = new LinkedList<>();
        LocalDate start = LocalDate.from(dtf.parse(startDate));
        LocalDate end = LocalDate.from(dtf.parse(endDate));
        while (start.compareTo(end) <= 0) {
            String date = dtf.format(start);
            list.add(date);
            start = start.plusDays(1);
        }
        return list;
    }

    /**
     * 获取间隔的某一天
     */
    public static String oneDay(String date, int days, DateTimeFormatter dtf) {
        LocalDate day = LocalDate.from(dtf.parse(date));
        LocalDate oneDay = day.plusDays(days);
        return oneDay.format(dtf);
    }

    public static void main(String[] args) {
        String startDate = "20200101";
        String endDate = "20200107";
        System.out.println("days = " + getDays(startDate, endDate, YYYYMMDD));
    }

}
