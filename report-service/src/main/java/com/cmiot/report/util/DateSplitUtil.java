package com.cmiot.report.util;

import com.cmiot.report.service.impl.CustomerOrderServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;


public class DateSplitUtil {

    private final static Logger logger = LoggerFactory.getLogger(CustomerOrderServiceImpl.class);

    private static SimpleDateFormat monthDateFormat = new SimpleDateFormat("MM");

    private static SimpleDateFormat YYYY_MM_DD = new SimpleDateFormat("yyyy-MM-dd");

    //将时间段按星期分割
    public static List<Range> splitToWeeks(String startTime, String endTime) {
        List<Range> result = new ArrayList<>();
        try {
            Date start = YYYY_MM_DD.parse(startTime);
            Date end = YYYY_MM_DD.parse(endTime);
            result.add(Range.create(start));
            Date from = new Date(start.getTime() + 7L * 24 * 3600 * 1000);
            Date weekEnd = cn.hutool.core.date.DateUtil.endOfWeek(end);
            while (from.compareTo(weekEnd) <= 0) {
                Date dt = cn.hutool.core.date.DateUtil.beginOfWeek(from);
                last(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
                last(result).setMonth(monthDateFormat.format(new Date(dt.getTime() - 24L * 3600 * 1000)));
                result.add(Range.create(dt));
                from.setTime(from.getTime() + 7L * 24 * 3600 * 1000);
            }
            last(result).end(end);
            last(result).setMonth(monthDateFormat.format(end));
        } catch (Exception e) {
            logger.error("分割周粒度时间异常:{}", e.getMessage(), e);
        }
        return result;
    }

    //将时间段按照月分割
    public static List<Range> splitToMonths(String startTime, String endTime) {
        List<Range> result = new ArrayList<>();
        try {
            Date start = YYYY_MM_DD.parse(startTime);
            Date end = YYYY_MM_DD.parse(endTime);
            result.add(Range.create(start));
            Calendar cal = Calendar.getInstance();
            cal.setTime(start);
            cal.add(Calendar.MONTH, 1);
            Date monthEnd = cn.hutool.core.date.DateUtil.endOfMonth(end);
            while (cal.getTimeInMillis() <= monthEnd.getTime()) {
                Date dt = cn.hutool.core.date.DateUtil.beginOfMonth(cal.getTime());
                last(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
                last(result).setMonth(monthDateFormat.format(new Date(dt.getTime() - 24L * 3600 * 1000)));
                result.add(Range.create(dt));
                cal.add(Calendar.MONTH, 1);
            }
            last(result).end(end);
            last(result).setMonth(monthDateFormat.format(end));
        } catch (Exception e) {
            logger.error("分割月粒度时间异常:{}", e.getMessage(), e);
        }

        return result;
    }

    //获取最后一行
    public static <S> S last(List<S> list) {
        int size = list.size();
        if (size != 0) {
            return list.get(size - 1);
        }
        return null;
    }

    //日期区间
    public static class Range {
        Date start;
        Date end;
        String Month;

        public Range() {

        }

        private Range(Date start) {
            this.start = start;
        }

        public static Range create(Date start) {
            return new Range(start);
        }

        public Range end(Date end) {
            this.end = end;
            return this;
        }

        public void setStart(Date start) {
            this.start = start;
        }

        public void setEnd(Date end) {
            this.end = end;
        }

        public Date getStart() {
            return start;
        }

        public Date getEnd() {
            return end;
        }

        public String getMonth() {
            return Month;
        }

        public void setMonth(String month) {
            Month = month;
        }

        @Override
        public String toString() {
            return "[" + cn.hutool.core.date.DateUtil.format(start, "yyyy-MM-dd") + "," + cn.hutool.core.date.DateUtil.format(end, "yyyy-MM-dd")
                    + "]";
        }
    }
}
