package com.cmiot.report.util.redis;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 基础Redis操作服务类</br>
 * 该类的设计思路是,与Redis命令语法及格式保持一致,以达到使用该类与直接使用Redis命令相似的体验.</br>
 * 由于Redis命令众多,并没有实现全部的命令,而是实现了常用的大部分命令.
 */
public interface BaseRedisOperatorService {

	enum Position {
		BEFORE,
		AFTER
	}

	/**************************************************** key 相关操作 *************************************************/

	/**
	 * 实现命令 : KEYS pattern 查找所有符合 pattern 模式的 key ? 匹配单个字符 * 匹配0到多个字符 [a-c] 匹配a和c [ac] 匹配a到c [^a] 匹配除了a以外的字符
	 * @param pattern
	 *        redis pattern 表达式
	 * @return
	 */
	Set<String> keys(String pattern);

	/**
	 * 实现命令 : DEL key1 [key2 ...] 删除一个或多个key
	 * @param keys
	 * @return
	 */
	Long del(String... keys);

	/**
	 * 实现命令 : UNLINK key1 [key2 ...] 删除一个或多个key
	 * @param keys
	 * @return
	 */
	Long unlink(String... keys);

	/**
	 * 实现命令 : EXISTS key1 [key2 ...] 查看 key 是否存在，返回存在 key 的个数
	 * @param keys
	 * @return
	 */
	Long exists(String... keys);

	/**
	 * 实现命令 : TYPE key 查看 key 的 value 的类型
	 * @param key
	 * @return
	 */
	String type(String key);

	/**
	 * 实现命令 : PERSIST key 取消 key 的超时时间，持久化 key
	 * @param key
	 * @return
	 */
	boolean persist(String key);

	/**
	 * 实现命令 : TTL key 返回给定 key 的剩余生存时间,key不存在返回 null 单位: 秒
	 * @param key
	 * @return
	 */
	Long ttl(String key);

	/**
	 * 实现命令 : PTTL key 返回给定 key 的剩余生存时间,key不存在返回 null 单位: 毫秒
	 * @param key
	 * @return
	 */
	Long pTtl(String key);

	/**
	 * 实现命令 : EXPIRE key 秒 设置key 的生存时间 单位 : 秒
	 * @param key
	 * @return
	 */
	boolean expire(String key, int ttl);

	/**
	 * 实现命令 : PEXPIRE key 毫秒 设置key 的生存时间 单位 : 毫秒
	 * @param key
	 * @return
	 */
	boolean pExpire(String key, Long ttl);

	/**
	 * 实现命令 : EXPIREAT key Unix时间戳(自1970年1月1日以来的秒数) 设置key 的过期时间
	 * @param key
	 * @param date
	 * @return
	 */
	boolean expireAt(String key, Date date);

	/**
	 * 实现命令 : RENAME key newkey 重命名key，如果newKey已经存在，则newKey的原值被覆盖
	 * @param oldKey
	 * @param newKey
	 */
	void rename(String oldKey, String newKey);

	/**
	 * 实现命令 : RENAMENX key newkey 安全重命名key，newKey不存在时才重命名
	 * @param oldKey
	 * @param newKey
	 * @return
	 */
	boolean renameNx(String oldKey, String newKey);

	/************************************************* String 相关操作 *************************************************/

	/**
	 * 实现命令 : SET key value 添加一个持久化的 String 类型的键值对
	 * @param key
	 * @param value
	 */
	void set(String key, Object value);

	/**
	 * 实现命令 : SET key value EX 秒、 setex key value 秒 添加一个 String 类型的键值对，并设置生存时间
	 * @param key
	 * @param value
	 * @param ttl
	 *        key 的生存时间，单位:秒
	 */
	void set(String key, Object value, int ttl);

	/**
	 * 实现命令 : SET key value PX 毫秒 、 psetex key value 毫秒 添加一个 String 类型的键值对，并设置生存时间
	 * @param key
	 * @param value
	 * @param ttl
	 *        key 的生存时间，单位:毫秒
	 */
	void set(String key, Object value, long ttl);

	/**
	 * 实现命令 : SET key value [EX 秒|PX 毫秒] [NX|XX] 添加一个 String 类型的键值对， ttl、timeUnit 不为 null 时设置生存时间 keyIfExist 不为 null 时，设置 NX 或 XX 模式
	 * @param key
	 * @param value
	 * @param ttl
	 *        生存时间
	 * @param timeUnit
	 *        生存时间的单位
	 * @param keyIfExist
	 *        true 表示 xx,key 存在时才添加. false 表示 nx，key 不存在时才添加
	 */
	boolean set(String key, Object value, Long ttl, TimeUnit timeUnit, Boolean keyIfExist);

	/**
	 * 实现命令 : MSET key1 value1 [key2 value2...] 安全批量添加键值对,只要有一个 key 已存在，所有的键值对都不会插入
	 * @param keyValueMap
	 */
	void mSet(Map<String, Object> keyValueMap);

	/**
	 * 实现命令 : MSETNX key1 value1 [key2 value2...] 批量添加键值对
	 * @param keyValueMap
	 */
	void mSetNx(Map<String, Object> keyValueMap);

	/**
	 * 实现命令 : SETRANGE key 下标 str 覆盖 原始 value 的一部分，从指定的下标开始覆盖， 覆盖的长度为指定的字符串的长度。
	 * @param key
	 * @param str
	 *        字符串
	 * @param offset
	 *        开始覆盖的位置，包括开始位置，下标从0开始
	 */
	void setRange(String key, Object str, int offset);

	/**
	 * 实现命令 : APPEND key value 在原始 value 末尾追加字符串
	 * @param key
	 * @param str
	 *        要追加的字符串
	 */
	void append(String key, String str);

	/**
	 * 实现命令 : GETSET key value 设置 key 的 value 并返回旧 value
	 * @param key
	 * @param value
	 */
	Object getSet(String key, Object value);

	/**
	 * 实现命令 : GET key 获取一个key的value
	 * @param key
	 * @return value
	 */
	Object get(String key);

	/**
	 * 实现命令 : MGET key1 [key2...] 获取多个key的value
	 * @param keys
	 * @return value
	 */
	List<Object> mGet(String... keys);

	/**
	 * 实现命令 : GETRANGE key 开始下标 结束下标 获取指定key的value的子串，下标从0开始，包括开始下标，也包括结束下标。
	 * @param key
	 * @param start
	 *        开始下标
	 * @param end
	 *        结束下标
	 * @return
	 */
	String getRange(String key, int start, int end);

	/**
	 * 实现命令 : STRLEN key 获取 key 对应 value 的字符串长度
	 * @param key
	 * @return
	 */
	Long strLen(String key);

	/**
	 * 实现命令 : INCR key 给 value 加 1,value 必须是整数
	 * @param key
	 * @return
	 */
	Long inCr(String key);

	/**
	 * 实现命令 : INCRBY key 整数 给 value 加 上一个整数,value 必须是整数
	 * @param key
	 * @return
	 */
	Long inCrBy(String key, Long number);

	/**
	 * 实现命令 : INCRBYFLOAT key 数 给 value 加上一个小数,value 必须是数
	 * @param key
	 * @return
	 */
	Double inCrByFloat(String key, double number);

	/**
	 * 实现命令 : DECR key 给 value 减去 1,value 必须是整数
	 * @param key
	 * @return
	 */
	Long deCr(String key);

	/**
	 * 实现命令 : DECRBY key 整数 给 value 减去一个整数,value 必须是整数
	 * @param key
	 * @return
	 */
	Long deCcrBy(String key, Long number);

	/************************************************* String 相关操作 *************************************************/

	/************************************************* Hash 相关操作 ***************************************************/

	/**
	 * 实现命令 : HSET key field value 添加 hash 类型的键值对，如果字段已经存在，则将其覆盖。
	 * @param key
	 * @param field
	 * @param value
	 */
	void hSet(String key, String field, Object value);

	/**
	 * 实现命令 : HSET key field1 value1 [field2 value2 ...] 添加 hash 类型的键值对，如果字段已经存在，则将其覆盖。
	 * @param key
	 * @param map
	 */
	void hSet(String key, Map<String, Object> map);

	/**
	 * 实现命令 : HSETNX key field value 添加 hash 类型的键值对，如果字段不存在，才添加
	 * @param key
	 * @param field
	 * @param value
	 */
	boolean hSetNx(String key, String field, Object value);

	/**
	 * 实现命令 : HGET key field 返回 field 对应的值
	 * @param key
	 * @param field
	 * @return
	 */
	Object hGet(String key, String field);

	/**
	 * 实现命令 : HMGET key field1 [field2 ...] 返回 多个 field 对应的值
	 * @param key
	 * @param fields
	 * @return
	 */
	List<Object> hGet(String key, String... fields);

	/**
	 * 实现命令 : HGETALL key 返回所以的键值对
	 * @param key
	 * @return
	 */
	Map<Object, Object> hGetAll(String key);

	/**
	 * 实现命令 : HKEYS key 获取所有的 field
	 * @param key
	 * @return
	 */
	Set<Object> hKeys(String key);

	/**
	 * 实现命令 : HVALS key 获取所有的 value
	 * @param key
	 * @return
	 */
	List<Object> hValue(String key);

	/**
	 * 实现命令 : HDEL key field [field ...] 删除哈希表 key 中的一个或多个指定域，不存在的域将被忽略。
	 * @param key
	 * @param fields
	 */
	Long hDel(String key, Object... fields);

	/**
	 * 实现命令 : HEXISTS key field 删除哈希表 key 中的一个或多个指定域，不存在的域将被忽略。
	 * @param key
	 * @param field
	 */
	boolean hExists(String key, String field);

	/**
	 * 实现命令 : HLEN key 获取 hash 中字段:值对的数量
	 * @param key
	 */
	Long hLen(String key);

	/**
	 * 实现命令 : HSTRLEN key field 获取字段对应值的长度
	 * @param key
	 * @param field
	 */
	Long hStrLen(String key, String field);

	/**
	 * 实现命令 : HINCRBY key field 整数 给字段的值加上一个整数
	 * @param key
	 * @param field
	 * @return 运算后的值
	 */
	Long hInCrBy(String key, String field, long number);

	/**
	 * 实现命令 : HINCRBYFLOAT key field 浮点数 给字段的值加上一个浮点数
	 * @param key
	 * @param field
	 * @return 运算后的值
	 */
	Double hInCrByFloat(String key, String field, Double number);

	/************************************************* Hash 相关操作 ***************************************************/

	/************************************************* List 相关操作 ***************************************************/

	/**
	 * 实现命令 : LPUSH key 元素1 [元素2 ...] 在最左端推入元素
	 * @param key
	 * @param values
	 * @return 执行 LPUSH 命令后，列表的长度。
	 */
	Long lPush(String key, Object... values);

	/**
	 * 实现命令 : RPUSH key 元素1 [元素2 ...] 在最右端推入元素
	 * @param key
	 * @param values
	 * @return 执行 RPUSH 命令后，列表的长度。
	 */
	Long rPush(String key, Object... values);

	/**
	 * 实现命令 : LPOP key 弹出最左端的元素
	 * @param key
	 * @return 弹出的元素
	 */
	Object lPop(String key);

	/**
	 * 实现命令 : RPOP key 弹出最右端的元素
	 * @param key
	 * @return 弹出的元素
	 */
	Object rPop(String key);

	/**
	 * 实现命令 : BLPOP key (阻塞式)弹出最左端的元素，如果 key 中没有元素，将一直等待直到有元素或超时为止
	 * @param key
	 * @param timeout
	 *        等待的时间，单位秒
	 * @return 弹出的元素
	 */
	Object bLPop(String key, int timeout);

	/**
	 * 实现命令 : BRPOP key (阻塞式)弹出最右端的元素，将一直等待直到有元素或超时为止
	 * @param key
	 * @return 弹出的元素
	 */
	Object bRPop(String key, int timeout);

	/**
	 * 实现命令 : LINDEX key index 返回指定下标处的元素，下标从0开始
	 * @param key
	 * @param index
	 * @return 指定下标处的元素
	 */
	Object lIndex(String key, int index);

	/**
	 * 实现命令 : LINSERT key BEFORE|AFTER 目标元素 value 在目标元素前或后插入元素
	 * @param key
	 * @param position
	 * @param pivot
	 * @param value
	 * @return
	 */
	Long lInsert(String key, Position position, Object pivot, Object value);

	/**
	 * 实现命令 : LRANGE key 开始下标 结束下标 获取指定范围的元素,下标从0开始，包括开始下标，也包括结束下标(待验证)
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	List<Object> lRange(String key, int start, int end);

	/**
	 * 实现命令 : LLEN key 获取 list 的长度
	 * @param key
	 * @return
	 */
	Long lLen(String key);

	/**
	 * 实现命令 : LREM key count 元素 删除 count 个指定元素,
	 * @param key
	 * @param count
	 *        count > 0: 从头往尾移除指定元素。count < 0: 从尾往头移除指定元素。count = 0: 移除列表所有的指定元素。(未验证)
	 * @param value
	 * @return
	 */
	Long lLen(String key, int count, Object value);

	/**
	 * 实现命令 : LSET key index 新值 更新指定下标的值,下标从 0 开始，支持负下标，-1表示最右端的元素(未验证)
	 * @param key
	 * @param index
	 * @param value
	 * @return
	 */
	void lSet(String key, int index, Object value);

	/**
	 * 实现命令 : LTRIM key 开始下标 结束下标 裁剪 list。[01234] 的 `LTRIM key 1 -2` 的结果为 [123]
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	void lTrim(String key, int start, int end);

	/**
	 * 实现命令 : RPOPLPUSH 源list 目标list 将 源list 的最右端元素弹出，推入到 目标list 的最左端，
	 * @param sourceKey
	 *        源list
	 * @param targetKey
	 *        目标list
	 * @return 弹出的元素
	 */
	Object rPopLPush(String sourceKey, String targetKey);

	/**
	 * 实现命令 : BRPOPLPUSH 源list 目标list timeout (阻塞式)将 源list 的最右端元素弹出，推入到 目标list 的最左端，如果 源list 没有元素，将一直等待直到有元素或超时为止
	 * @param sourceKey
	 *        源list
	 * @param targetKey
	 *        目标list
	 * @param timeout
	 *        超时时间，单位秒， 0表示无限阻塞
	 * @return 弹出的元素
	 */
	Object bRPopLPush(String sourceKey, String targetKey, int timeout);

	/************************************************* List 相关操作 ***************************************************/

	/************************************************** SET 相关操作 ***************************************************/

	/**
	 * 实现命令 : SADD key member1 [member2 ...] 添加成员
	 * @param key
	 * @param values
	 * @return 添加成功的个数
	 */
	Long sAdd(String key, Object... values);

	/**
	 * 实现命令 : SREM key member1 [member2 ...] 删除指定的成员
	 * @param key
	 * @param values
	 * @return 删除成功的个数
	 */
	Long sRem(String key, Object... values);

	/**
	 * 实现命令 : SCARD key 获取set中的成员数量
	 * @param key
	 * @return
	 */
	Long sCard(String key);

	/**
	 * 实现命令 : SISMEMBER key member 查看成员是否存在
	 * @param key
	 * @param values
	 * @return
	 */
	boolean sIsMember(String key, Object... values);

	/**
	 * 实现命令 : SMEMBERS key 获取所有的成员
	 * @param key
	 * @return
	 */
	Set<Object> sMembers(String key);

	/**
	 * 实现命令 : SMOVE 源key 目标key member 移动成员到另一个集合
	 * @param sourceKey
	 *        源key
	 * @param targetKey
	 *        目标key
	 * @param value
	 * @return
	 */
	boolean sMove(String sourceKey, String targetKey, Object value);

	/**
	 * 实现命令 : SDIFF key [otherKey ...] 求 key 的差集
	 * @param key
	 * @param otherKeys
	 * @return
	 */
	Set<Object> sDiff(String key, String... otherKeys);

	/**
	 * 实现命令 : SDIFFSTORE 目标key key [otherKey ...] 存储 key 的差集
	 * @param targetKey
	 *        目标key
	 * @param key
	 * @param otherKeys
	 * @return
	 */
	Long sDiffStore(String targetKey, String key, String... otherKeys);

	/**
	 * 实现命令 : SINTER key [otherKey ...] 求 key 的交集
	 * @param key
	 * @param otherKeys
	 * @return
	 */
	Set<Object> sInter(String key, String... otherKeys);

	/**
	 * 实现命令 : SINTERSTORE 目标key key [otherKey ...] 存储 key 的交集
	 * @param targetKey
	 *        目标key
	 * @param key
	 * @param otherKeys
	 * @return
	 */
	Long sInterStore(String targetKey, String key, String... otherKeys);

	/**
	 * 实现命令 : SUNION key [otherKey ...] 求 key 的并集
	 * @param key
	 * @param otherKeys
	 * @return
	 */
	Set<Object> sUnion(String key, String... otherKeys);

	/**
	 * 实现命令 : SUNIONSTORE 目标key key [otherKey ...] 存储 key 的并集
	 * @param targetKey
	 *        目标key
	 * @param key
	 * @param otherKeys
	 * @return
	 */
	Long sUnionStore(String targetKey, String key, String... otherKeys);

	/**
	 * 实现命令 : SPOP key [count] 随机删除(弹出)指定个数的成员
	 * @param key
	 * @return
	 */
	Object sPop(String key);

	/**
	 * 实现命令 : SPOP key [count] 随机删除(弹出)指定个数的成员
	 * @param key
	 * @param count
	 *        个数
	 * @return
	 */
	List<Object> sPop(String key, int count);

	/**
	 * 实现命令 : SRANDMEMBER key [count] 随机返回指定个数的成员
	 * @param key
	 * @return
	 */
	Object sRandMember(String key);

	/**
	 * 实现命令 : SRANDMEMBER key [count] 随机返回指定个数的成员 如果 count 为正数，随机返回 count 个不同成员 如果 count 为负数，随机选择 1 个成员，返回 count 个
	 * @param key
	 * @param count
	 *        个数
	 * @return
	 */
	List<Object> sRandMember(String key, int count);

	/************************************************** SET 相关操作 ***************************************************/

	/*********************************************** Sorted SET 相关操作 ***********************************************/

	/**
	 * 实现命令 : ZADD key score member 添加一个 成员/分数 对
	 * @param key
	 * @param value
	 *        成员
	 * @param score
	 *        分数
	 * @return
	 */
	boolean zAdd(String key, double score, Object value);

	/**
	 * 实现命令 : ZREM key member [member ...] 删除成员
	 * @param key
	 * @param values
	 * @return
	 */
	Long zRem(String key, Object... values);

	/**
	 * 实现命令 : ZREMRANGEBYRANK key start stop 删除 start下标 和 end下标间的所有成员 下标从0开始，支持负下标，-1表示最右端成员，包括开始下标也包括结束下标 (未验证)
	 * @param key
	 * @param start
	 *        开始下标
	 * @param end
	 *        结束下标
	 * @return
	 */
	Long zRemRangeByRank(String key, int start, int end);

	/**
	 * 实现命令 : ZREMRANGEBYSCORE key start stop 删除分数段内的所有成员 包括min也包括max (未验证)
	 * @param key
	 * @param min
	 *        小分数
	 * @param max
	 *        大分数
	 * @return
	 */
	Long zRemRangeByScore(String key, double min, double max);

	/**
	 * 实现命令 : ZSCORE key member 获取成员的分数
	 * @param key
	 * @param value
	 * @return
	 */
	Double zScore(String key, Object value);

	/**
	 * 实现命令 : ZINCRBY key 带符号的双精度浮点数 member 增减成员的分数
	 * @param key
	 * @param value
	 * @param delta
	 *        带符号的双精度浮点数
	 * @return
	 */
	Double zInCrBy(String key, Object value, double delta);

	/**
	 * 实现命令 : ZCARD key 获取集合中成员的个数
	 * @param key
	 * @return
	 */
	Long zCard(String key);

	/**
	 * 实现命令 : ZCOUNT key min max 获取某个分数范围内的成员个数，包括min也包括max (未验证)
	 * @param key
	 * @param min
	 *        小分数
	 * @param max
	 *        大分数
	 * @return
	 */
	Long zCount(String key, double min, double max);

	/**
	 * 实现命令 : ZRANK key member 按分数从小到大获取成员在有序集合中的排名
	 * @param key
	 * @param value
	 * @return
	 */
	Long zRank(String key, Object value);

	/**
	 * 实现命令 : ZREVRANK key member 按分数从大到小获取成员在有序集合中的排名
	 * @param key
	 * @param value
	 * @return
	 */
	Long zRevRank(String key, Object value);

	/**
	 * 实现命令 : ZRANGE key start end 获取 start下标到 end下标之间到成员，并按分数从小到大返回 下标从0开始，支持负下标，-1表示最后一个成员，包括开始下标，也包括结束下标(未验证)
	 * @param key
	 * @param start
	 *        开始下标
	 * @param end
	 *        结束下标
	 * @return
	 */
	Set<Object> zRange(String key, int start, int end);

	/**
	 * 实现命令 : ZREVRANGE key start end 获取 start下标到 end下标之间到成员，并按分数从小到大返回 下标从0开始，支持负下标，-1表示最后一个成员，包括开始下标，也包括结束下标(未验证)
	 * @param key
	 * @param start
	 *        开始下标
	 * @param end
	 *        结束下标
	 * @return
	 */
	Set<Object> zRevRange(String key, int start, int end);

	/**
	 * 实现命令 : ZRANGEBYSCORE key min max 获取分数范围内的成员并按从小到大返回 (未验证)
	 * @param key
	 * @param min
	 *        小分数
	 * @param max
	 *        大分数
	 * @return
	 */
	Set<Object> zRangeByScore(String key, double min, double max);

	/**
	 * 实现命令 : ZRANGEBYSCORE key min max LIMIT offset count 分页获取分数范围内的成员并按从小到大返回 包括min也包括max(未验证)
	 * @param key
	 * @param min
	 *        小分数
	 * @param max
	 *        大分数
	 * @param offset
	 *        开始下标，下标从0开始
	 * @param count
	 *        取多少条
	 * @return
	 */
	Set<Object> zRangeByScore(String key, double min, double max, int offset, int count);

	/**
	 * 实现命令 : ZREVRANGEBYSCORE key min max 获取分数范围内的成员并按从大到小返回 (未验证)
	 * @param key
	 * @param min
	 *        小分数
	 * @param max
	 *        大分数
	 * @return
	 */
	Set<Object> zRevRangeByScore(String key, double min, double max);

	/**
	 * 实现命令 : ZREVRANGEBYSCORE key min max LIMIT offset count 分页获取分数范围内的成员并按从大到小返回 包括min也包括max(未验证)
	 * @param key
	 * @param min
	 *        小分数
	 * @param max
	 *        大分数
	 * @param offset
	 *        开始下标，下标从0开始
	 * @param count
	 *        取多少条
	 * @return
	 */
	Set<Object> zRevRangeByScore(String key, double min, double max, int offset, int count);

	/*********************************************** Sorted SET 相关操作 ***********************************************/
}
