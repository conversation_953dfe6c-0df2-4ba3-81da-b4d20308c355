package com.cmiot.report.util;

import java.io.*;
import java.util.Map;

public class CloneUtils {
	  @SuppressWarnings("unchecked")
  	    public static <T extends Serializable> T clone(Map<String, Object> probMac){
  	         
  	        T clonedObj = null;
  	        try {
  	            ByteArrayOutputStream baos = new ByteArrayOutputStream();
  	            ObjectOutputStream oos = new ObjectOutputStream(baos);
  	            oos.writeObject(probMac);
  	            oos.close();
  	             
  	            ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
  	            ObjectInputStream ois = new ObjectInputStream(bais);
  	            clonedObj = (T) ois.readObject();
  	            ois.close();
  	             
  	        }catch (Exception e){
  	            e.printStackTrace();
  	        }
  	         
  	        return clonedObj;
  	    }
}
