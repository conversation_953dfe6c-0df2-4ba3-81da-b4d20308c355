package com.cmiot.report.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

public class AesUtil {

    private static final String CBC_SECRET = "FjEcBYL8P#aJe8KtjgTy!8PY3n8c!44w";
    private static final String CIPHER_MODE = "AES/CBC/PKCS5Padding";
    //private static final Base64.Decoder BASE64_DECODER = Base64.getDecoder();
    //private static final Base64.Encoder BASE64_ENCODER = Base64.getEncoder();

    // /** key **/
    private static SecretKeySpec createKey(String key) {
        byte[] data = null;
        if (key == null) {
            key = "";
        }
        StringBuilder sb = new StringBuilder(32);
        sb.append(key);
        while (sb.length() < 32) {
            sb.append("0");
        }
        if (sb.length() > 32) {
            sb.setLength(32);
        }

        data = sb.toString().getBytes(StandardCharsets.UTF_8);
        return new SecretKeySpec(data, "AES");
    }

    /**
     * create offset
     *
     * @param password
     * @return
     */
    private static IvParameterSpec createIV(String password) {
        byte[] data = null;
        if (password == null) {
            password = "";
        }
        StringBuilder sb = new StringBuilder(16);
        sb.append(password);
        while (sb.length() < 16) {
            sb.append("0");
        }
        if (sb.length() > 16) {
            sb.setLength(16);
        }

        data = sb.toString().getBytes(StandardCharsets.UTF_8);
        return new IvParameterSpec(data);
    }

    /**
     * encrypt to byte[]
     *
     * @param content
     * @param checkPlatform
     * @param iv
     * @return
     */
    private static byte[] encrypt(byte[] content, String checkPlatform, String iv) {
        try {
            SecretKeySpec key = createKey(checkPlatform);
            Cipher cipher = Cipher.getInstance(CIPHER_MODE);
            cipher.init(Cipher.ENCRYPT_MODE, key, createIV(iv));
            byte[] result = cipher.doFinal(content);
            return result;
        } catch (Exception e) {
            System.err.println("Error "+ e.toString());
        }
        return null;
    }

    /**
     * encrypt to byte[]
     *
     * @param content
     * @param key
     * @param iv
     * @return
     */
    private static byte[] encrypt(byte[] content, SecretKeySpec key, String iv) {
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_MODE);
            cipher.init(Cipher.ENCRYPT_MODE, key, createIV(iv));
            byte[] result = cipher.doFinal(content);
            return result;
        } catch (Exception e) {
            System.err.println("Error "+ e.toString());
        }
        return null;
    }

    /**
     * encrypt to Hexadecimal character (base64)
     *
     * @param content
     * @param checkPlatform
     * @param iv
     * @return
     */
    private static String encrypt(String content, String checkPlatform, String iv) {
        byte[] data = null;
        try {
            data = content.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            System.err.println("Error "+ e.toString());
        }
        data = encrypt(data, checkPlatform, iv);
        String result = Base64.encodeBase64String(data);
        //String result = BASE64_ENCODER.encodeToString(data);
        return result;
    }

    /**
     * encrypt to Hexadecimal character (base64)
     *
     * @param content
     * @param key
     * @param iv
     * @return
     */
    private static String encrypt(String content, SecretKeySpec key, String iv) {
        byte[] data = null;
        try {
            data = content.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            System.err.println("Error "+ e.toString());
        }
        data = encrypt(data, key, iv);
        //String result = BASE64_ENCODER.encodeToString(data);
        String result = Base64.encodeBase64String(data);
        return result;
    }

    /**
     * decipher to array
     *
     * @param content
     * @param checkPlatform
     * @param iv
     * @return
     */
    private static byte[] decrypt(byte[] content, String checkPlatform, String iv) {
        try {
            SecretKeySpec key = createKey(checkPlatform);
            Cipher cipher = Cipher.getInstance(CIPHER_MODE);
            cipher.init(Cipher.DECRYPT_MODE, key, createIV(iv));
            byte[] result = cipher.doFinal(content);
            return result;
        } catch (Exception e) {
            System.err.println("Error "+ e.toString());
        }
        return null;
    }

    /**
     * decipher to array
     *
     * @param content
     * @param key
     * @param iv
     * @return
     */
    private static byte[] decrypt(byte[] content, SecretKeySpec key, String iv) {
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_MODE);
            cipher.init(Cipher.DECRYPT_MODE, key, createIV(iv));
            byte[] result = cipher.doFinal(content);
            return result;
        } catch (Exception e) {
            System.err.println("Error "+ e.toString());
        }
        return null;
    }

    /**
     * decipher to String
     *
     * @param content
     * @param checkPlatform
     * @param iv
     * @return
     */
    private static String decrypt(String content, String checkPlatform, String iv) {
        byte[] data = null;
        try {
            data = Base64.decodeBase64(content);
        } catch (Exception e) {
            System.err.println("Error "+ e.toString());
        }
        data = decrypt(data, checkPlatform, iv);
        if (data == null) {
            return null;
        }

        String result = null;
        result = new String(data, StandardCharsets.UTF_8);
        return result;
    }

    /**
     * decipher to String
     *
     * @param content
     * @param key
     * @param iv
     * @return
     */
    private static String decrypt(String content, SecretKeySpec key, String iv) {
        byte[] data = null;
        try {
            //data = BASE64_DECODER.decode(content);
            data = Base64.decodeBase64(content);
        } catch (Exception e) {
            System.err.println("Error "+ e.toString());
        }
        data = decrypt(data, key, iv);
        if (data == null) {
            return null;
        }

        String result = null;
        result = new String(data, StandardCharsets.UTF_8);
        return result;
    }

    /**
     * hex to byte
     *
     * @param hexString
     * @return
     */
    private static byte[] hex2byte(String hexString) {
        String hexStr = "0123456789ABCDEF";
        int len = hexString.length() / 2;
        byte[] bytes = new byte[len];
        byte high = 0;
        byte low = 0;

        for (int i = 0; i < len; i++) {
            high = (byte) ((hexStr.indexOf(hexString.charAt(2 * i))) << 4);
            low = (byte) hexStr.indexOf(hexString.charAt(2 * i + 1));
            bytes[i] = (byte) (high | low);
        }
        return bytes;
    }

    /**
     * to Hexadecimal character
     *
     * @param b
     * @return
     */
    private static String byte2hex(byte[] b) {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }

        }
        return hs.toUpperCase();
    }


    /**
     * 空校验
     * @param str 需要判空的值
     * @return
     */
    public static boolean isEmpty(Object str){
        return null == str || "".equals(str);
    }

    /**
     * String转byte
     * @param str 需要转换的字符串
     * @return
     */
    public static byte[] getBytes(String str){
        if (isEmpty(str)){
            return null;
        }
        try{
            return str.getBytes(StandardCharsets.UTF_8);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    public static SecretKeySpec getSecretKeySpec(String key){
        SecretKeySpec secretKeySpec = new SecretKeySpec(getBytes(key), "AES");
        return secretKeySpec;
    }

    public static String decrypt(String text, String key, String iv, String mode){
        if (isEmpty(text) || isEmpty(key) || isEmpty(iv)) {
            return null;
        }

        // 将密文转换为16字节的字节数组
        byte[] textBytes = java.util.Base64.getDecoder().decode(text);

        try {
            // 创建AES加密器
            Cipher cipher = Cipher.getInstance(mode);

            SecretKeySpec secretKeySpec = getSecretKeySpec(key);

            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, new IvParameterSpec(getBytes(iv)));

            // 解密字节数组
            byte[] decryptedBytes = cipher.doFinal(textBytes);

            // 将明文转换为字符串
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String encrypt(String str){
        return encrypt(str, CBC_SECRET, "0000000000000000");
    }
    public static String decrypt(String str){
        return decrypt(str, CBC_SECRET, "0000000000000000");
    }


}
