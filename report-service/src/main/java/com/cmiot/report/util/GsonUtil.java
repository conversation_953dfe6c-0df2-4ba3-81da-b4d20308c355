package com.cmiot.report.util;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import net.sf.cglib.beans.BeanMap;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * GSON工具类
 */
public class GsonUtil {

	private static Gson gson;

	static {
		gson = new GsonBuilder().registerTypeAdapter(new TypeToken<Map<String, Object>>() {
		}.getType(), new MapTypeAdapter()).create();
	}

	private GsonUtil() {
	}

	/**
	 * 转成json
	 *
	 * @param object
	 * @return
	 */
	public static String GsonString(Object object) {
		return gson.toJson(object);
	}

	/**
	 * 转成bean
	 *
	 * @param gsonString
	 * @param cls
	 * @return
	 */
	public static <T> T GsonToBean(String gsonString, Class<T> cls) {
		// Json字符串转为java对象
		com.fasterxml.jackson.databind.ObjectMapper ob = new com.fasterxml.jackson.databind.ObjectMapper();
		// 忽略未定义字段
		ob.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		// json转bean时忽略大小写
		ob.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
		try {
			return ob.readValue(gsonString, cls);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return gson.fromJson(gsonString, cls);
	}

	/**
	 * 转成list
	 *
	 * @param gsonString
	 * @param cls
	 * @return
	 */
	public static <T> List<T> GsonToList(String gsonString, Class<T> cls) {
		return gson.fromJson(gsonString, new TypeToken<List<T>>() {
		}.getType());
	}

	/**
	 * 转成list中有map的
	 *
	 * @param gsonString
	 * @return
	 */
	public static List<Map<String, Object>> GsonToListMaps(String gsonString) {
		return gson.fromJson(gsonString, new TypeToken<List<Map<String, Object>>>() {
		}.getType());
	}

	/**
	 * 转成map的
	 *
	 * @param gsonString
	 * @return
	 */
	public static Map<String, Object> GsonToMaps(String gsonString) {
		return gson.fromJson(gsonString, new TypeToken<Map<String, Object>>() {
		}.getType());

	}

	/**
	 * 类型适配器
	 */
	static class MapTypeAdapter extends TypeAdapter<Object> {
		@Override
		public Object read(JsonReader in) throws IOException {
			JsonToken token = in.peek();
			switch (token) {
				case BEGIN_ARRAY:
					List<Object> list = new ArrayList<Object>();
					in.beginArray();
					while (in.hasNext()) {
						list.add(read(in));
					}
					in.endArray();
					return list;

				case BEGIN_OBJECT:
					Map<String, Object> map = new LinkedTreeMap<String, Object>();
					in.beginObject();
					while (in.hasNext()) {
						map.put(in.nextName(), read(in));
					}
					in.endObject();
					return map;

				case STRING:
					return in.nextString();

				case NUMBER:
					// 改写数字的处理逻辑,将数字值分为整型与浮点型
					String numberStr = in.nextString();
					if (numberStr.contains(".") || numberStr.contains("e") || numberStr.contains("E")) {
						return Double.parseDouble(numberStr);
					}
					if (Long.parseLong(numberStr) <= Integer.MAX_VALUE) {
						return Integer.parseInt(numberStr);
					}
					return Long.parseLong(numberStr);

				case BOOLEAN:
					return in.nextBoolean();

				case NULL:
					in.nextNull();
					return null;

				default:
					throw new IllegalStateException();
			}
		}

		@Override
		public void write(JsonWriter out, Object value) throws IOException {
			// 序列化无需实现
		}
	}

	public static <T> T mapToObject(Map<String, Object> map, Class<T> beanClass) throws Exception {
		Field[] fields = beanClass.getDeclaredFields();
		for(Field field:fields){
			String name = field.getName();
			Object o = map.get(name);
			if(o == null){
				map.put(name,"-");
			}else{
				map.put(name,String.valueOf(o));
			}
		}
		T bean = beanClass.newInstance();
		BeanMap beanMap = BeanMap.create(bean);
		beanMap.putAll(map);
		return bean;
	}
}
