package com.cmiot.report.util;

import org.apache.http.util.TextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class TimeStampUtil {
    private static Logger logger = LoggerFactory.getLogger(TimeStampUtil.class);

    private static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter YYYYMMDDHHMMSS = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    //将周的起止时间转化为时间戳
    public static List<Long> weekUtil(String weekStartTime,String weekStopTime){
        List<Long> list=new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date weekStartTimeDate = simpleDateFormat.parse(weekStartTime);
            Date weekStopTimeDate = simpleDateFormat.parse(weekStopTime);
            //获取起始时间的时间戳
            long weekStartTimeStamp = weekStartTimeDate.getTime();
            //获取终止日期的时间戳
            Calendar c = Calendar.getInstance();
            c.setTime(weekStopTimeDate);
            c.add(Calendar.DAY_OF_MONTH, 1);// 今天+1天
            //将小时至0
            c.set(Calendar.HOUR_OF_DAY, 0);
            //将分钟至0
            c.set(Calendar.MINUTE, 0);
            //将秒至0
            c.set(Calendar.SECOND, 0);
            //将毫秒至0
            c.set(Calendar.MILLISECOND, 0);
            Date weekStopTimeTomorrow = c.getTime();
            long weekStopTimeStamp = weekStopTimeTomorrow.getTime();
            list.add(weekStartTimeStamp);
            list.add(weekStopTimeStamp);
        } catch (ParseException e) {
            logger.error("invoke weekUtil exception:{}",e);
        }
        return list;
    }

    /**
     * 日期格式字符串转换成时间戳
     *
     * @param dateStr 字符串日期
     * @param format   如：yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static Long Date2TimeStamp(String dateStr, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.parse(dateStr).getTime() / 1000;
        } catch (Exception e) {
            logger.error("invoke Date2TimeStamp exception:{}",e);
        }
        return 0L;
    }

    /**
     * Java将Unix时间戳转换成指定格式日期字符串
     * @param timestampString 时间戳 如："1473048265";
     * @param formats 要格式化的格式 默认："yyyy-MM-dd HH:mm:ss";
     *
     * @return 返回结果 如："2016-09-05 16:06:42";
     */
    public static String TimeStamp2Date(Long timestampString, String formats) {
        if (TextUtils.isEmpty(formats))
            formats = "yyyy-MM-dd HH:mm:ss";
        Long timestamp = timestampString * 1000;
        String date = new SimpleDateFormat(formats, Locale.CHINA).format(new Date(timestamp));
        return date;
    }

    /**
     * 取得当前时间戳（精确到秒）
     *
     * @return nowTimeStamp
     */
    public static Long getNowTimeStamp() {
        long time = System.currentTimeMillis();
        long nowTimeStamp = time / 1000;
        return nowTimeStamp;
    }


	/**
	 * 将时间字符串转换为Date对象。
	 * @param deadline 日期。例如： 2019-12-05
	 * @return
	 * @throws ParseException
	 */
	public static Date getDate(String deadline) throws ParseException {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		return format.parse(deadline);
	}

	/**
	 * 将当前时间戳，转换为日期字符串。
	 * @param createTime 精确到表的时间戳
	 * @return 返回示例： 2019-12-06 14:01:01
	 */
	public static String getCreateTime(long createTime) {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar instance = Calendar.getInstance();
		Date time = instance.getTime();
		time.setTime(createTime);
		return format.format(time);
	}

    public static String getBlankCreateTime(long createTime) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        Calendar instance = Calendar.getInstance();
        Date time = instance.getTime();
        time.setTime(createTime);
        return format.format(time);
    }

	/**
	 * 将当前时间戳，转换为日期字符串。
	 * @param createTime 精确到表的时间戳
	 * @return 返回示例： 2019.12.06 14:01:01
	 */
	public static String getTimeFromDate(Date date) {
		if(date == null) {
			return "";
		}
		SimpleDateFormat format = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
		return format.format(date);
	}

    /**
     * LocalDateTime转Date
     */
    public static Date convertToDate(LocalDateTime localDateTime) {
        return new Date(Timestamp.valueOf(localDateTime).getTime());
    }

    /**
     * LocalDateTime转String yyyy-MM-dd HH:mm:ss
     */
    public static String  convertToString(LocalDateTime localDateTime) {
        return YYYY_MM_DD_HH_MM_SS.format(localDateTime);
    }

    /**
     * LocalDateTime转String yyyyMMddHHmmss
     */
    public static String  convertToStringWithoutBlank(LocalDateTime localDateTime) {
        return YYYYMMDDHHMMSS.format(localDateTime);
    }

    /**
     * LocalDateTime转10位时间戳
     */
    public static String convertToStampOf10(LocalDateTime localDateTime) {
        return String.valueOf(Timestamp.valueOf(localDateTime).getTime() / 1000);
    }

    /**
     * 获取最近的一个周日的日期。格式如： yyyy-MM-dd
     * @return
     */
    public static String getSundayOfWeek() {
    	// 获取本周周一的日期
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		Calendar cld = Calendar.getInstance();
		cld.setFirstDayOfWeek(Calendar.MONDAY);
		cld.setTimeInMillis(System.currentTimeMillis());
		cld.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
		// 获取周一的前一天
		int days = cld.get(Calendar.DATE);
		cld.set(Calendar.DATE, days - 1);
    	return df.format(cld.getTime());
    }

	public static void main(String[] args) throws ParseException {
		// 1575879198
		System.err.println(System.currentTimeMillis());
		System.err.println(getTimeFromDate(Calendar.getInstance().getTime()));
		System.err.println(getCreateTime(System.currentTimeMillis()));

		// 获取本周周一的日期
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		Calendar cld = Calendar.getInstance();
		cld.setFirstDayOfWeek(Calendar.MONDAY);
		cld.setTimeInMillis(System.currentTimeMillis());
		cld.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
		// 获取周一的前一天
		int days = cld.get(Calendar.DATE);
		cld.set(Calendar.DATE, days - 1);
		String sundayOfWeek = df.format(cld.getTime());
		System.out.println(sundayOfWeek);
	}
}
