package com.cmiot.report.util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class UrlTopDoMainUtil {
    private static Logger logger = LoggerFactory.getLogger(UrlTopDoMainUtil.class);
    private static Pattern pattern=null;
    private static volatile UrlTopDoMainUtil instance2;

    private UrlTopDoMainUtil(String top) {
        pattern = Pattern.compile(top, Pattern.CASE_INSENSITIVE);
    }

    public static UrlTopDoMainUtil getIstance(String top) {
        // 定义一个共有的静态方法，返回该类型实例
        if (instance2 == null) {
            // 对象实例化时与否判断（不使用同步代码块，instance不等于null时，直接返回对象，提高运行效率）
            synchronized (UrlTopDoMainUtil.class) {
                // 同步代码块（对象未初始化时，使用同步代码块，保证多线程访问时对象在第一次创建后，不再重复被创建）
                if (instance2 == null) {
                    // 未初始化，则初始instance变量
                    instance2 = new UrlTopDoMainUtil(top);
                }
            }
        }
        return instance2;
    }

    public Pattern getPattern(){
        return pattern;
    }

    public String getTopDomain(String url,Pattern pattern) {
        String result = url;
        try {
            Matcher matcher = pattern.matcher(url);
            if(matcher.find()){
                int count=0;
                for(int i=0;i<url.length();i++){
                    String s=String.valueOf(url.charAt(i));
                    if(s.equals(".")){
                        count++;
                    }
                }
                if(count==1){
                    //一个点，直接过过滤返回
                    result = matcher.group();
                    return result;
                }else if(count==2){
                    //两个点，必须是www.开头
                    if(url.contains("www.")||url.contains(".cn")){
                        result = matcher.group();
                        return result;
                    }
                }else if(count==3){
                    if(url.contains("www.")&&(url.contains(".com.cn")||url.contains(".net.cn")||url.contains(".gov.cn")||url.contains(".org.cn"))){
                        result = matcher.group();
                        return result;
                    }
                }
            }
        } catch (Exception e) {
            logger.info("[getTopDomain ERROR]====>"+e);
        }
        return null;
    }
}
