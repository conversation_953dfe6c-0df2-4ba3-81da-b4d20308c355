package com.cmiot.report.util;

import java.text.DecimalFormat;

import org.apache.commons.lang3.math.NumberUtils;

/**
 * 数字处理工具。
 * @version v103
 * <AUTHOR>
 *
 */
public class MyNumUtils {
	
	public static final DecimalFormat DF = new DecimalFormat("#.00");
	
	/**
	 * 数字保留两位小数。
	 */
	public static double decimalTwo(double data) {
		return NumberUtils.toDouble(DF.format(data));
	}
	
	public static void main(String[] args) {
		double f = 111231.5585;
		System.out.println(MyNumUtils.decimalTwo(f));
	}
}
