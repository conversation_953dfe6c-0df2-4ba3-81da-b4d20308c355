package com.cmiot.report.util;


import com.alibaba.fastjson.JSON;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtil {

    /**
     * 时间格式(yyyyMMdd)
     */
    public final static String MONTH_DATE_PATTERN = "yyyyMMdd";
    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * 时间格式(yyyyMM)
     */
    public final static String MONTH_PATTERN = "yyyyMM";

    /**
     * 时间格式(yyyy-MM)
     */
    public final static String DATE_PATTERN_YYYY_MM = "yyyy-MM";
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    /**
     * 时间格式(yyyyMMddHHmmss)
     */
    public final static String UNSIGNED_DATE_TIME_PATTERN = "yyyyMMddHHmmss";

    public final static String MONTH_DAY = "MM月dd日";

    public final static String YEAR_MONTH = "yyyy年MM月";

    public final static String MONTH_AND_DAY = "MM-dd";

    /**
     * 通过时间秒毫秒数判断两个时间的间隔
     *
     * @param form 开始时间
     * @param to   结束时间
     * @return 相差天数
     */
    public static int differentDays(Date form, Date to) {
        return (int) ((to.getTime() - form.getTime()) / (1000 * 3600 * 24));
    }

    /**
     * 通过时间秒毫秒数判断两个时间的间隔
     *
     * @param form 开始时间
     * @param to   结束时间
     * @return 相差小时数
     */
    public static int differentHours(Date form, Date to) {
        return (int) ((to.getTime() - form.getTime()) / (1000 * 3600));
    }

    /**
     * 通过时间秒毫秒数判断两个时间的间隔
     *
     * @param form 开始时间
     * @param to   结束时间
     * @return 相差分钟数
     */
    public static int differentMinute(Date form, Date to) {
        return (int) ((to.getTime() - form.getTime()) / (1000 * 60));
    }

    /**
     * 判断两个时间相差多少个月
     *
     * @param form 开始时间
     * @param to   结束时间
     * @return 相差月数
     */
    public static int differentMonth(Date form, Date to) {
        Calendar bef = Calendar.getInstance();
        Calendar aft = Calendar.getInstance();
        bef.setTime(form);
        aft.setTime(to);
        int result = aft.get(Calendar.MONTH) - bef.get(Calendar.MONTH);
        int month = (aft.get(Calendar.YEAR) - bef.get(Calendar.YEAR)) * 12;
        return Math.abs(month + result);
    }

    /**
     * 把日期格式化为字符串
     *
     * @param date   日期
     * @param format 格式
     * @return 返回格式化之后的字符串
     */
    public static String dateToString(Date date, String format) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(format);
            return dateFormat.format(date);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 把日期格式化为字符串
     *
     * @param date   日期
     * @param format 格式
     * @return 返回格式化之后的字符串
     */
    public static Date stringToDate(String date, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        try {
            return dateFormat.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String stringToDateString(String date1, String format1, String format) {
        SimpleDateFormat dateFormat1 = new SimpleDateFormat(format1);
        try {
            Date date = dateFormat1.parse(date1);
            return dateToString(date, format);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String getWeekOfYear(String dateSource, String pattern){
        try {
            SimpleDateFormat format = new SimpleDateFormat(pattern);
            Date date = format.parse(dateSource);
            Calendar calendar = Calendar.getInstance();
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
            // 跨年的情况,如果不是从周一开始,算作去年最后一周
            calendar.setMinimalDaysInFirstWeek(7);
            calendar.setTime(date);
            int week = calendar.get(Calendar.WEEK_OF_YEAR);
            SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
            String year = yearFormat.format(date);
            return year + "-" + week + "周";
        } catch (Exception e){
            return null;
        }
    }



    /**
     * 通过传入的日期加指定的天数
     *
     * @param date 日期
     * @param day  天数
     * @return 相加后的天数
     */
    public static Date getNextDay(Date date, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, day);
        return calendar.getTime();
    }

    /**
     * 通过传入的日期加指定的分钟数
     *
     * @param date   日期
     * @param minute 天数
     * @return 相加后的天数
     */
    public static Date getNextMinute(Date date, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTime();
    }

    /**
     * 通过传入的日期加指定的天数
     *
     * @param date 日期
     * @param day  天数
     * @return 相加后的天数
     */
    public static String getNextDay(String date, int day, String format) {
        return dateToString(getNextDay(stringToDate(date, format), day), format);
    }

    /**
     * 通过传入的日期加指定的年数
     *
     * @param date 日期
     * @param year 年数
     * @return 计算后的日期
     */
    public static Date getNextYear(Date date, int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, year);
        return calendar.getTime();
    }

    /**
     * 通过传入的日期加指定的月数
     *
     * @param date  日期
     * @param month 月数
     * @return 计算后的日期
     */
    public static Date getNextMonth(Date date, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, month);
        return calendar.getTime();
    }


    /**
     * 获取当前的时间
     *
     * @return 返回当前的时间
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前的时间（yyyy-MM-dd）
     *
     * @return 返回当前的时间
     */
    public static String getNowDayString() {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATE_PATTERN);
        return dateFormat.format(getNowDate());
    }

    /**
     * 获取30天前的时间（yyyy-MM-dd）
     *
     * @return 返回当前的时间
     */
    public static String getBefore30DayString() {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATE_PATTERN);
        Calendar tempStart = Calendar.getInstance();
        tempStart.add(Calendar.DATE, -30);
        return dateFormat.format(tempStart.getTime());
    }

    public static void main(String[] args) {
        /*System.err.println(getBefore30DayString());
        List<String> daysByDuring = getDaysByDuring("2021-02-01", "2022-04-01");
        for (String str : daysByDuring) {
            System.err.println(str);
        }

        System.out.println(JSON.toJSONString(splitDateList("2021-02-01", "2022-04-01")));*/

       // System.out.println(getWeekOfYear("2022-12-19 00:00:00", DateUtil.DATE_TIME_PATTERN));
        //System.out.println(getWeekOfYear("2022-12-26 00:00:00", DateUtil.DATE_TIME_PATTERN));
        //System.out.println(getWeekOfYear("2023-01-01 00:00:00", DateUtil.DATE_TIME_PATTERN));
        //System.out.println(getWeekOfYear("2023-01-02 00:00:00", DateUtil.DATE_TIME_PATTERN));

        System.out.println(getOffsetMonthLastDay(-1, DATE_PATTERN));

        System.out.println(getOffsetMonthLastDay(-2, DATE_PATTERN));

        System.out.println(getOffsetMonthLastDay(-3, DATE_PATTERN));
        System.out.println(getOffsetMonthLastDay(0, DATE_PATTERN));
        System.out.println(getOffsetMonthLastDay(1, DATE_PATTERN));

        System.out.println(getOffsetMonthLastDay(2, DATE_PATTERN));
    }

    /**
     * 获取当前的时间（yyyy-MM-dd HH:mm:ss）
     *
     * @return 返回当前的时间
     */
    public static String getNowTime() {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATE_TIME_PATTERN);
        return dateFormat.format(getNowDate());
    }

    /**
     * 获取当前的时间
     *
     * @return 返回当前的时间
     */
    public static Date getNowDayDate() {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATE_PATTERN);
        return stringToDate(dateFormat.format(getNowDate()), DateUtil.DATE_PATTERN);
    }

    /**
     * 获得当前时间前几天的日期
     *
     * @param i 天数
     * @return
     */
    public static Date getBeforeDayDate(int i) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -i);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATE_PATTERN);
        return stringToDate(dateFormat.format(cal.getTime()), DateUtil.DATE_PATTERN);
    }

    public static String getBeforeWeekSunday(int i) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.WEEK_OF_YEAR, -i);


        LocalDate localDate = LocalDate.parse(sdf.format(calendar.getTime()), DateTimeFormatter.ofPattern(MONTH_DATE_PATTERN));
        WeekFields weekFields = WeekFields.ISO;
        LocalDate sundayDate = localDate.with(weekFields.dayOfWeek(), 7L);
        return sundayDate.format(DateTimeFormatter.ofPattern(MONTH_DATE_PATTERN));
    }


    /**
     * 获得某天23:59:59点时间
     *
     * @return
     */
    public static Date getTimesnight(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获得某天0点时间
     *
     * @return
     */
    public static Date getTimesmorning(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * @param inputJudgeDate 要判断是否在当天24h内的时间
     * @return boolean
     * @Description 是否为当天24h内
     * <AUTHOR>
     */
    public static boolean isToday(Date inputJudgeDate) {
        boolean flag = false;
        //获取当前系统时间
        long longDate = System.currentTimeMillis();
        Date nowDate = new Date(longDate);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = dateFormat.format(nowDate);
        String subDate = format.substring(0, 10);
        //定义每天的24h时间范围
        String beginTime = subDate + " 00:00:00";
        String endTime = subDate + " 23:59:59";
        Date paseBeginTime = null;
        Date paseEndTime = null;
        try {
            paseBeginTime = dateFormat.parse(beginTime);
            paseEndTime = dateFormat.parse(endTime);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (inputJudgeDate.after(paseBeginTime) && inputJudgeDate.before(paseEndTime)) {
            flag = true;
        }
        return flag;
    }

    /**
     * 把日期格式化为字符串
     *
     * @param date 日期
     * @return 返回格式化之后的字符串
     */
    public static Date stringToDateFormat(String date) {
        String format = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        try {
            return dateFormat.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 时间戳转date
     *
     * @return
     */
    public static Date timeToDate(Long time) {
        //时间戳转化为Sting或Date
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String d = format.format(time);
        Date date = null;
        try {
            date = format.parse(d);
            return date;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 日期格式转换
     *
     * @param input yyyy-MM-dd
     * @return yyyyMMdd
     */
    public static String formatInputs(String input) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN);
        SimpleDateFormat sdf2 = new SimpleDateFormat(MONTH_DATE_PATTERN);
        try {
            Date parse = sdf.parse(input);
            return sdf2.format(parse);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 日期格式转换
     *
     * @param input yyyy-MM-dd
     * @return yyyyMMdd
     */
    public static List<String> getDaysByDuring(String startDate, String endDate) {
        List<String> days = new ArrayList<>();
        Integer loop = differentDays(stringToDate(startDate, DATE_PATTERN), stringToDate(endDate, DATE_PATTERN));
        for (int i = 0; i <= loop; i++) {
            days.add(getNextDay(startDate, i, DATE_PATTERN));
        }
        return days;
    }

    public static List<String> getDaysByDuring(String startDate, String endDate, String datepattern) {
        List<String> days = new ArrayList<>();
        Integer loop = differentDays(stringToDate(startDate, datepattern), stringToDate(endDate, datepattern));
        for (int i = 0; i <= loop; i++) {
            days.add(getNextDay(startDate, i, datepattern));
        }
        return days;
    }

    /**
     * 计算两个日期间隔天数
     *
     * @param start
     * @param end
     * @return
     * @throws Exception
     */
    public static int daysBetween(String start, String end) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN);
        Calendar cal = Calendar.getInstance();
        cal.setTime(sdf.parse(start));
        long starttime = cal.getTimeInMillis();
        cal.setTime(sdf.parse(end));
        long endtime = cal.getTimeInMillis();
        return Integer.parseInt(String.valueOf((endtime - starttime) / (1000 * 3600 * 24)));
    }

    /**
     * 日期格式转换
     *
     * @param input yyyyMMdd
     * @return yyyy-MM-dd
     */
    public static String formatInput(String input) {
        SimpleDateFormat sdf = new SimpleDateFormat(MONTH_DATE_PATTERN);
        SimpleDateFormat sdf2 = new SimpleDateFormat(DATE_PATTERN);
        try {
            Date parse = sdf.parse(input);
            return sdf2.format(parse);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String formatInputMonth(String input) {
        SimpleDateFormat sdf = new SimpleDateFormat(MONTH_PATTERN);
        SimpleDateFormat sdf2 = new SimpleDateFormat(DATE_PATTERN_YYYY_MM);
        try {
            Date parse = sdf.parse(input);
            return sdf2.format(parse);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 日期格式转换
     *
     * @param input yyyyMMdd
     * @return yyyy-MM-dd
     */
    public static String formatToyyyyMMdd(String input) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN);
        SimpleDateFormat sdf2 = new SimpleDateFormat(MONTH_DATE_PATTERN);
        try {
            Date parse = sdf.parse(input);
            return sdf2.format(parse);
        } catch (ParseException e) {
            return null;
        }
    }


    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    /**
     * 获取两个日期中间的全部日期集合
     *
     * @param start
     * @param end
     * @return
     */
    public static List<String> splitDateList(String start, String end) {
        List<String> listDate = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN);
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(sdf.parse(start));
            Date endDate = sdf.parse(end);

            while (calendar.getTime().before(endDate) || calendar.getTime().equals(end)) {
                listDate.add(sdf.format(calendar.getTime()));
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            listDate.add(end);
            return listDate;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listDate;
    }

    public static String getYesterday() {
        SimpleDateFormat sdf = new SimpleDateFormat(MONTH_DATE_PATTERN);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return sdf.format(calendar.getTime());
    }

    public static Integer getYesterdayInt() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return Integer.valueOf(sdf.format(calendar.getTime()));
    }

    public static String getBeforeDay(int i, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, -i);
        return sdf.format(calendar.getTime());
    }

    public static String getLastWeekDay() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.WEEK_OF_YEAR, -1);

        LocalDate localDate = LocalDate.parse(sdf.format(calendar.getTime()), DateTimeFormatter.ofPattern(MONTH_DATE_PATTERN));
        WeekFields weekFields = WeekFields.ISO;
        LocalDate sundayDate = localDate.with(weekFields.dayOfWeek(), 7L);
        return sundayDate.format(DateTimeFormatter.ofPattern(MONTH_DATE_PATTERN));

    }
    public static Integer getLastWeekDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.WEEK_OF_YEAR, -1);
        return Integer.valueOf(sdf.format(calendar.getTime()));
    }

    public static int getDayAtWeekStart(Integer date) {
        LocalDate localDate = LocalDate.parse(String.valueOf(date), DateTimeFormatter.ofPattern(MONTH_DATE_PATTERN));
        WeekFields weekFields= WeekFields.ISO;
        LocalDate mondayDate = localDate.with(weekFields.dayOfWeek(), 1L);
        return Integer.parseInt(mondayDate.format(DateTimeFormatter.ofPattern(MONTH_DATE_PATTERN)));
    }

    public static int getDayAtWeekEnd(Integer date) {
        LocalDate localDate = LocalDate.parse(String.valueOf(date), DateTimeFormatter.ofPattern(MONTH_DATE_PATTERN));
        WeekFields weekFields= WeekFields.ISO;
        LocalDate sundayDate  = localDate.with(weekFields.dayOfWeek(), 7L);
        return Integer.parseInt(sundayDate.format(DateTimeFormatter.ofPattern(MONTH_DATE_PATTERN)));
    }

    public static String getLastMonth() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -1);
        return sdf.format(calendar.getTime());
    }

    public static String getLastMonth(Integer month) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -month);
        return sdf.format(calendar.getTime());
    }

    public static Integer getLastMonthDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -1);
        return Integer.valueOf(sdf.format(calendar.getTime()));
    }

    public static String getLastMonthFirstDay() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMmm");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return sdf.format(calendar.getTime());
    }



    public static String getOffsetMonthLastDay(int i, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, i);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return sdf.format(calendar.getTime());
    }

    public static String getOffsetMonth(String monthEnd, int offset) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(monthEnd));
        calendar.add(Calendar.MONTH, offset);
        return sdf.format(calendar.getTime());
    }

    public static String formatFromTo(String day, String patternFrom, String patternTo) {
        SimpleDateFormat sdf = new SimpleDateFormat(patternFrom);
        SimpleDateFormat sdf2 = new SimpleDateFormat(patternTo);
        try {
            Date parse = sdf.parse(day);
            return sdf2.format(parse);
        } catch (ParseException e) {
            return null;
        }
    }


}