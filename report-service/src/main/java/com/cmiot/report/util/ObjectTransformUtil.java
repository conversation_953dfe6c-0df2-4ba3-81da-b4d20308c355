package com.cmiot.report.util;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 对象装换工具类
 */
public class ObjectTransformUtil {

    /**
     * 对象转为map,将属性值为null的去掉
     */
    public static Map<String,Object> objectToMap(Object object) throws IllegalAccessException {
        Map<String,Object> map = new HashMap<>();
        Field[] fields = object.getClass().getDeclaredFields();
        for (int i=0,len = fields.length;i<len;i++) {
            if (!fields[i].isSynthetic() && !fields[i].getName().equals("$jacocoData")) {
                String varName = fields[i].getName();
                //获取原来的访问控制权限
                boolean accessFlag = fields[i].isAccessible();
                //临时修改可访问
                fields[i].setAccessible(true);
                Object o = fields[i].get(object);
                if (o != null) {
                    map.put(varName, o);
                    //恢复访问控制权限
                    fields[i].setAccessible(accessFlag);
                }
            }
        }
        return map;
    }
}
