package com.cmiot.report.util.ftp;

/**
 * FTP枚举类
 */
public enum FtpUtilEnum {
	/**
	 * FTP服务连接已被断开
	 */
	FAIL_5(5, "切换到FTP文件目录失败"),
	/**
	 * FTP服务连接已被断开
	 */
	FAIL_4(4, "FTP服务上未找到匹配的文件"),
	/**
	 * FTP服务连接已被断开
	 */
	FAIL_3(3, "FTP服务连接已被断开"),
	/**
	 * 从FTP服务获取文件失败
	 */
	FAIL_2(2, "从FTP服务获取文件失败"),
	/**
	 * 获取参数失败
	 */
	FAIL_1(1, "获取参数失败"),
	/**
	 * 操作成功
	 */
	FAIL_0(0, "操作成功");

	private Integer code;
	private String desc;

	FtpUtilEnum(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public int code() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String desc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}
}
