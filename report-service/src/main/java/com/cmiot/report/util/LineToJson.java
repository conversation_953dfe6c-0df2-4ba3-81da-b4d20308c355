package com.cmiot.report.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

public class LineToJson {

	public static List<String> baseFieldType = Arrays.asList(new String[]{"java.lang.String", "java.lang.Integer", "java.lang.Double","java.lang.Double[]","java.lang.Integer[]","java.lang.String[]"}) ;
	public static List<String> interfaceFieldType = Arrays.asList(new String[]{"java.util.List"}) ;
	/**
	 *  获取javabean的字段
	 * @param
	 * @return 字段数组
	 */
public static int getField(Class demo, String[] data, JSONObject jsonObject,int i) {
		
		try {
			Field[] fields=demo.getDeclaredFields();
			for(Field f:fields) {
				if (!f.isSynthetic() && !f.getName().equals("$jacocoData")) {
					int index = f.getGenericType().getTypeName().indexOf("<") == -1 ? f.getGenericType().getTypeName().length() : f.getGenericType().getTypeName().indexOf("<");
					String type = f.getGenericType().getTypeName().substring(0, index);    //获取属性的类型
					if (baseFieldType.contains(type)) {
						//基础类型
						jsonObject.put(f.getName(), data[i]);
						i++;
					} else if (interfaceFieldType.contains(type)) {
						//封装类型
						String clsname = f.getGenericType().getTypeName().substring(index + 1, f.getGenericType().getTypeName().indexOf(">"));
						//list size
						int listSize = "".equals(data[i]) ? 0 : Integer.parseInt(data[i]);
						JSONArray array = new JSONArray();
						jsonObject.put(f.getName(), array);
						i++;
						for (int k = 0; k < listSize; k++) {
							JSONObject object = new JSONObject();
							array.add(object);
							int m = getField(Class.forName(clsname), data, object, i);
							i = m;
						}
					} else {
						//自定义类型
						JSONObject object = new JSONObject();
						jsonObject.put(f.getName(), object);
						int m = getField(Class.forName(type), data, object, i);
						i = m;
					}
				}
			}
			//System.out.println(jsonObject.toString());
			return i;
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		}
		return 0;
	}
}
