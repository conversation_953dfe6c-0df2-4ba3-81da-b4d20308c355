package com.cmiot.report.util;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class StringUtil {
	/**
	 * 固定大小的任务线程池。
	 */
	public static final ExecutorService THREAD_POOL = Executors.newSingleThreadExecutor();
	
	/**
	 * 地市编码与地市名称的关系表。
	 */
	public static final Map<String, String> CODE_CITY_MAP = new HashMap<String, String>();

	public static final Map<String, String> CITY_CODE_MAP = new HashMap<>(32);

	private static String secretStr = "****";

	static {
		CODE_CITY_MAP.put("11", "苏州");
		CODE_CITY_MAP.put("12", "淮安");
		CODE_CITY_MAP.put("13", "宿迁");
		CODE_CITY_MAP.put("14", "南京");
		CODE_CITY_MAP.put("15", "连云港");
		CODE_CITY_MAP.put("16", "徐州");
		CODE_CITY_MAP.put("17", "常州");
		CODE_CITY_MAP.put("18", "镇江");
		CODE_CITY_MAP.put("19", "无锡");
		CODE_CITY_MAP.put("20", "南通");
		CODE_CITY_MAP.put("21", "泰州");
		CODE_CITY_MAP.put("22", "盐城");
		CODE_CITY_MAP.put("23", "扬州");
		CODE_CITY_MAP.put("99", "江苏");

		CODE_CITY_MAP.forEach((code, city) -> CITY_CODE_MAP.put(city, code));
	}

	/**
	 * 字符串转换为数字，异常则取默认值。
	 * @param str
	 * @param def
	 * @return
	 */
	public static int toInt(String str, int def) {
		try {
			return Integer.parseInt(str);
		} catch (Exception e) {
			return def;
		}
	}

	/**
	 * 空字符串赋默认值
	 * @param str
	 * @param def
	 * @return
	 */
	public static String toStandardString(String str, String def) {
		if(StringUtils.isBlank(str)){
			return def;
		}else {
			return str;
		}
	}

	/**
	 * 字符串转换为长整型，异常则取默认值。
	 * @param str
	 * @param def
	 * @return
	 */
	public static long toLong(String str, long def) {
		try {
			return Long.parseLong(str);
		} catch (Exception e) {
			return def;
		}
	}

	public static String phoneNumber(String phoneNumber){

		if(phoneNumber != null && phoneNumber.trim().length() == 11){

			return phoneNumber.substring(0, 3)+ secretStr +phoneNumber.substring(7);
		}else{
			return phoneNumber;
		}

	}
	/**
	 * 网关
	 * */
	public static String macOrSN(String ms){

		if(ms == null || ms.length() < 5){
			return ms;
		}else{
			int total = ms.length() - 4;
			int left = total / 2 + total %2 ;
			return ms.substring(0, left) + secretStr + ms.substring(left + 4);

		}
	}
	/**
	 *  下挂设备MAC 替换7-10位
	 * */
	public static String deviceMac(String mac){
		if(mac == null || mac.length() < 10){
			return mac;
		}else{
			return mac.substring(0, 6) + secretStr + mac.substring(10);
		}
	}
	/**
	 *  去除MAC冒号，并且都转为大写
	 * */
	public static String dealMac(String mac){

		return mac.replaceAll("\\:", "").toUpperCase();
	}
	

	// 将文件服务器地址中的IP端口，替换为指定文件中的地址。
	public static String changeUrl(String inputUrl, String fileServerUrl) {
		// 如果配置的文件服务器内容为null或者提取的IP和地址为null，都将原url返回
		if(fileServerUrl == null) {
			return inputUrl;
		} 
		String iPandPort = getIPandPort(fileServerUrl);
		if(iPandPort == null) {
			return inputUrl;
		}
		
		String newInputUrl = null;
		if(inputUrl.startsWith("http://")) {
			int indexOf = inputUrl.indexOf("/", 7);
			newInputUrl = new StringBuffer()
					.append("http://")
					.append(iPandPort)
					.append(inputUrl.substring(indexOf))
					.toString();
		} else if(fileServerUrl.startsWith("https://")) {
			int indexOf = inputUrl.indexOf("/", 8);
			newInputUrl = new StringBuffer()
					.append("https://")
					.append(iPandPort)
					.append(inputUrl.substring(indexOf))
					.toString();
		}
		return newInputUrl;
	}
	// 获取一个地址的IP和端口
	private static String getIPandPort(String fileServerUrl) {
		String ipAndPort = null;
		if(fileServerUrl.startsWith("http://")) {
			int indexOf = fileServerUrl.indexOf("/", 7);
			ipAndPort = fileServerUrl.substring(7, indexOf);
		} else if(fileServerUrl.startsWith("https://")) {
			int indexOf = fileServerUrl.indexOf("/", 8);
			ipAndPort = fileServerUrl.substring(8, indexOf);
		}
		return ipAndPort;
	}

	public static void main(String[] args) {
		String fileServerUrl = "http://***************:8019/up";
		String inputUrl = "http://************:8019/upload/file/2019/12/25/snap_20191017_20191117(5).txt";
		
		System.err.println(changeUrl(inputUrl,fileServerUrl));
	}

	 public static String getRandomString(int length){
		    //定义一个字符串（A-Z，a-z，0-9）即62位；
		    String str="zxcvbnmlkjhgfdsaqwertyuiopQWERTYUIOPASDFGHJKLZXCVBNM1234567890";
		    //由Random生成随机数
		        Random random=new Random();
		        StringBuffer sb=new StringBuffer();
		        //长度为几就循环几次
		        for(int i=0; i<length; ++i){
		          //产生0-61的数字
		          int number=random.nextInt(62);
		          //将产生的数字通过length次承载到sb中
		          sb.append(str.charAt(number));
		        }
		        //将承载的字符转换成字符串
		        return sb.toString().toUpperCase();
		  }
}
