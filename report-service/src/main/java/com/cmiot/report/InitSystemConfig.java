package com.cmiot.report;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.parser.ParserConfig;
import com.cmiot.report.bean.DicAreaInfo;
import com.cmiot.report.bean.DicAreaInfoExample;
import com.cmiot.report.bean.DicFactory;
import com.cmiot.report.bean.DicFactoryExample;
import com.cmiot.report.bean.DicPppoeErrorTable;
import com.cmiot.report.bean.DicPppoeErrorTableExample;
import com.cmiot.report.mapper.DicAreaInfoMapper;
import com.cmiot.report.mapper.DicFactoryMapper;
import com.cmiot.report.mapper.DicPppoeErrorTableMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 系统启动初始化。
 *
 * <AUTHOR>
 */
@Component
@Order(value = 0)
@Slf4j
public class InitSystemConfig implements CommandLineRunner, EnvironmentAware {

    /**
     * 加载区域信息表t_dic_area_info ： gcode -> gname
     */
    public static final Map<String, String> AREA_CODE_NAME = new HashMap<>();

    /**
     * 加载网关code和名称
     */
    public static final Map<String, String> FACTORY_CODE_NAME = new HashMap<>();

    /**
     * 厂商ID与厂商编码
     */
    public static final Map<Long, String> FACTORY_ID_CODE = new HashMap<>();

    /**
     * PPPOE拨号失败字典信息
     */
    public static final Map<String, String> PPPOE_ERROR_CODE_DESC = new HashMap<>();

    /**
     * 区域码表
     */
    @Autowired
    private DicAreaInfoMapper dicAreaInfoMapper;

    /**
     * 网关厂商表
     */
    @Autowired
    private DicFactoryMapper dicFactoryMapper;

    /**
     * pppoe拨号状态码字典
     */
    @Autowired
    private DicPppoeErrorTableMapper dicPppoeErrorTableMapper;

    /**
     * 缓存全量的区域信息
     */
    public static final List<DicAreaInfo> AREA_INFO_LIST = new ArrayList<>();

    /**
     * 项目启动运行
     */
    @Override
    public void run(String... args) throws Exception {
        log.info("InitSystemConfig args : {}. ", args.length);
        ParserConfig.getGlobalInstance().setSafeMode(true);

        // 初始化区域信息
        DicAreaInfoExample example = new DicAreaInfoExample();
        List<DicAreaInfo> areaInfoList = dicAreaInfoMapper.selectByExample(example);
        Map<String, String> collect = areaInfoList.stream()
                .collect(Collectors.toMap(DicAreaInfo::getGcode, DicAreaInfo::getGname));
        AREA_CODE_NAME.clear();
        AREA_CODE_NAME.putAll(collect);
        AREA_INFO_LIST.addAll(areaInfoList);

        // 初始化网关厂商表
        DicFactoryExample exampleFact = new DicFactoryExample();
        List<DicFactory> factList = dicFactoryMapper.selectByExample(exampleFact);
        //Map<String, String> collect2 = factList.stream()
        //        .collect(Collectors.toMap(DicFactory::getFactoryCode, DicFactory::getFactoryName));

        Map<String, String> collect2 = new HashMap<>();
        for (DicFactory dicFactory : factList) {
            String factoryCode = dicFactory.getFactoryCode();
            String factoryName = dicFactory.getFactoryName();
            if (StringUtils.isEmpty(factoryCode)) factoryCode = dicFactory.getFactoryId() + "";
            collect2.put(factoryCode, factoryName);
        }

        FACTORY_CODE_NAME.clear();
        FACTORY_CODE_NAME.putAll(collect2);
        //
        FACTORY_ID_CODE.clear();
        FACTORY_ID_CODE.putAll(factList.stream().collect(Collectors.toMap(DicFactory::getFactoryId, DicFactory::getFactoryCode)));

        // 加载pppoe拨号失败状态码及其名称
        DicPppoeErrorTableExample examplePetInfo = new DicPppoeErrorTableExample();
        List<DicPppoeErrorTable> dpetInfoList = dicPppoeErrorTableMapper.selectByExample(examplePetInfo);
        PPPOE_ERROR_CODE_DESC.putAll(dpetInfoList.stream().collect(Collectors.toMap(DicPppoeErrorTable::getPppoeError, DicPppoeErrorTable::getPppoeErrorDesc)));

        log.info("InitSystemConfig over... ");
    }

    @Override
    public void setEnvironment(Environment environment) {
    }
}
