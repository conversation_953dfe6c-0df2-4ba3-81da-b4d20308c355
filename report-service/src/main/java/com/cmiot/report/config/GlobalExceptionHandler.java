package com.cmiot.report.config;

import com.cmiot.fgeo.common.error.DefaultErrMsg;
import com.cmiot.fgeo.common.error.ErrMsg;
import com.cmiot.fgeo.common.error.FGEOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * @Classname GlobalExceptionHandler
 * @Description
 * @Date 2023/8/15 15:23
 * @Created by lei
 */
@RestControllerAdvice(
        annotations = {RestController.class}
)
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(com.cmiot.fgeo.common.error.GlobalExceptionHandler.class);

    public GlobalExceptionHandler() {
    }

    @ExceptionHandler({FGEOException.BadRequest.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ErrMsg exceptionHandler(FGEOException.BadRequest e) {
        return e.getErrMsg();
    }

    @ExceptionHandler({FGEOException.Unauthorized.class})
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ErrMsg exceptionHandler(FGEOException.Unauthorized e) {
        return e.getErrMsg();
    }

    @ExceptionHandler({FGEOException.Forbidden.class})
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ErrMsg exceptionHandler(FGEOException.Forbidden e) {
        return e.getErrMsg();
    }

    @ExceptionHandler({FGEOException.NotFound.class})
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ErrMsg exceptionHandler(FGEOException.NotFound e) {
        return e.getErrMsg();
    }

    @ExceptionHandler({FGEOException.NotAcceptable.class})
    @ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
    public ErrMsg exceptionHandler(FGEOException.NotAcceptable e) {
        return e.getErrMsg();
    }

    @ExceptionHandler({FGEOException.InternalServerError.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ErrMsg exceptionHandler(FGEOException.InternalServerError e) {
        return e.getErrMsg();
    }

    @ExceptionHandler({Exception.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ErrMsg exceptionHandler(Exception e) {
        log.info("Undefined exception: {}", e.getMessage(), e);
        return DefaultErrMsg.UnknownError;
    }
}
