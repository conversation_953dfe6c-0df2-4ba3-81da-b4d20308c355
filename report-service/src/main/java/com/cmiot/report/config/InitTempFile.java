package com.cmiot.report.config;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * @Classname InitTempFile
 * @Description
 * @Date 2022/9/23 10:38
 * @Created by lei
 */
@Component
public class InitTempFile implements CommandLineRunner {
    private final static Logger logger = LoggerFactory.getLogger(InitTempFile.class);

    /**
     * 临时目录
     */
    @Value("${export.tmp.local}")
    private String tmpPath;

    @Override
    public void run(String... args) throws Exception {
        try {
            if (StringUtils.isNotBlank(tmpPath)) {
                FileUtils.forceMkdir(new File(tmpPath));
            }
        } catch (Exception e) {
            logger.info("init local temp file error: {}, {}", tmpPath, e.getMessage(), e);
        }
    }
}
