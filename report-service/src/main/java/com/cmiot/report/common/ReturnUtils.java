package com.cmiot.report.common;


import com.cmiot.report.common.bean.MesgCode;
import com.cmiot.report.common.bean.ReturnInfo;
import com.cmiot.report.common.bean.ReturnWithParam;

public class ReturnUtils {


    /**
     * 成功
     */
    public static final String CODE_SUCCESS = "0";

    /**
     * 提示失败
     */
    public static final String CODE_FAIL = "-1";


    /**
     * 成功
     *
     * @return 结果
     */
    public static ReturnInfo success() {
        return returnInfo(CODE_SUCCESS, "操作成功");
    }

    /**
     * 成功
     *
     * @param message 消息
     * @return 结果
     */
    public static ReturnInfo success(String message) {
        return returnInfo(CODE_SUCCESS, message);
    }

    /**
     * 成功
     *
     * @return 结果
     */
    public static <T> ReturnWithParam<T> success(T data) {
        return returnWithParam(CODE_SUCCESS, "操作成功", data);
    }

    /**
     * 成功
     *
     * @param message 消息
     * @return 结果
     */
    public static <T> ReturnWithParam<T> success(String message, T data) {
        return returnWithParam(CODE_SUCCESS, message, data);
    }

    /**
     * 失败
     *
     * @param message 消息
     * @return 结果
     */
    public static <T> ReturnWithParam<T> fail(String message) {
        return returnWithParam(CODE_FAIL, message, null);
    }


    /**
     * 返回失败信息
     * @param code 错误代码
     * @param msg 错误信息
     * @param <T> 返回数据类型
     * @return
     */
    public static <T> ReturnWithParam<T> fail(String code, String msg) {
        return returnWithParam(code, msg, null);
    }


    /**
     * 失败
     *
     * @param msgCode 错误码
     * @return 结果
     */
    public static <T> ReturnWithParam<T> fail(MesgCode msgCode) {
        return returnWithParam(msgCode.code(), msgCode.msg(), null);
    }

    /**
     * 错误
     *
     * @param message 消息
     * @return 结果
     */
    public static <T> ReturnWithParam<T> error(String message) {
        return returnWithParam(CODE_FAIL, message, null);
    }

    /**
     * 返回结果
     *
     * @param code    code
     * @param message 消息
     * @return 结果
     */
    public static ReturnInfo returnInfo(String code, String message) {
        ReturnInfo result = new ReturnInfo();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    /**
     * 返回带数据的结果
     */
    public static <T> ReturnWithParam<T> returnWithParam(String code, String message, T data) {
        ReturnWithParam<T> result = new ReturnWithParam<>();
        result.setCode(code);
        result.setMessage(message);
        result.setData(data);
        return result;
    }

}

