package com.cmiot.report.common.bean;


import com.cmiot.report.common.ReturnUtils;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ReturnInfo {

    /**
     * 返回编码 成功：0
     */
    private String code;

    /**
     * 返回信息
     */
    private String message;


    public ReturnInfo() {
    }

    public ReturnInfo(String code, String message) {
        this.code = code;
        this.message = message;
    }


    public boolean isSuccess() {
        return ReturnUtils.CODE_SUCCESS.equals(code);
    }
}
