package com.cmiot.report.common.enums;

/**
 * 描述：组网统计导出类型
 *
 * <AUTHOR>
 * @date 2019/12/06
 */
public enum NetworkTaskExportTypeEnum {
    /** 新装用户 */
    INC(0),
    /** 存量用户 */
    ALL(1),
    /** 拍照用户 */
    SNAP(2),
    /** 拍照用户(非千兆路由器用户) */
    SNAP_GIGA(3)
    ;
    private Integer type;

    NetworkTaskExportTypeEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
