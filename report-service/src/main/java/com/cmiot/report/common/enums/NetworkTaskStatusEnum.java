package com.cmiot.report.common.enums;

/**
 * 描述：组网统计任务状态
 *
 * <AUTHOR>
 * @date 2019/12/06
 */
public enum NetworkTaskStatusEnum {
    /** 计算中 */
    RUNNING(1),
    /** 成功 */
    SUCCESS(2),
    /** 失败 */
    FAIL(3)
    ;
    private Integer status;

    NetworkTaskStatusEnum(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
