package com.cmiot.report.common.enums;


public enum OffGridFactorIndicatorEnum {

    // 1-终端质量因素
    // 11-客户网关活跃率≤70%
    ONE2ONE(11),
    // 12-客户CPU长期占用高网关数量占比＞30%
    ONE2TWO(12),
    // 13-客户内存长期占用高网关数量占比＞30%
    ONE2THREE(13),


    // 2-网络质量因素
    // 21-接收光功率弱光网关数量占比＞20%
    TWO2ONE(21),
    // 22-接收光功率弱光网关数量占比＞20%
    TWO2TWO(22),
    // 23-接收光功率弱光网关数量占比＞20%
    TWO2THREE(23),
    // 24-接收光功率弱光网关数量占比＞20%
    TWO2FOUR(24),


    // 3-客户服务因素
    // 31-近3月投诉数量＞3次
    THREE2ONE(31),
    // 32-近1年投诉数量＞6次
    THREE2TWO(32),
    // 33-累计投诉量＞10次
    THREE2THREE(33),


    // 4-业务订购因素
    // 41-企业业务类型为企宽业务/互联网专线/商务快线订购的最近业务订购日期距离业务失效日期都≤1/3
    FOUR2ONE(41),
    // 42-30天内业务退订数量＞业务总量的1/2
    FOUR2TWO(42),
    // 43-平均订购周期＜1年
    FOUR2THREE(43),
    // 44-最短订购周期＜30天
    FOUR2FOUR(44),
    // 45-一年以上累计订购次数≤2
    FOUR2FIVE(45),


    // 5-装维服务因素
    // 51-平均工单创建到装维预约时长＞48小时
    FIVE2ONE(51),
    // 52-装维当月累计上门处理次数＞3次
    FIVE2TWO(52),
    // 53-装维预约时间与实际上门处理时间＞72小时
    FIVE2THREE(53);


    private int type;

    OffGridFactorIndicatorEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public static String getFactorIndicatorDesc(int type) {
        String desc;
        switch (type) {
            // 1-终端质量因素
            case 11:
                //desc = "客户网关活跃率≤70%|0.2";
                desc = "客户网关活跃率≤70%";
                break;
            case 12:
                //desc = "客户CPU长期占用高网关数量占比＞30%|0.4";
                desc = "客户CPU长期占用高网关数量占比＞30%";
                break;
            case 13:
                //desc = "客户内存长期占用高网关数量占比＞30%|0.4";
                desc = "客户内存长期占用高网关数量占比＞30%";
                break;

            // 2-网络质量因素
            case 21:
                //desc = "接收光功率弱光网关数量占比＞20%|0.25";
                desc = "接收光功率弱光网关数量占比＞20%";
                break;
            case 22:
                //desc = "网关WLAN弱覆盖数量占比＞20%|0.15";
                desc = "网关WLAN弱覆盖数量占比＞20%";
                break;
            case 23:
                //desc = "网关平均时延较高的网关数占比>20%|0.4";
                desc = "网关平均时延较高的网关数占比>20%";
                break;
            case 24:
                //desc = "网络测速速率小于办理带宽90%的网关数量>20%|0.2";
                desc = "网络测速速率小于办理带宽90%的网关数量>20%";
                break;

            // 3-客户服务因素
            case 31:
                //desc = "近3月投诉数量＞3次|0.6";
                desc = "近3月投诉数量＞3次";
                break;
            case 32:
                //desc = "近1年投诉数量＞6次|0.3";
                desc = "近1年投诉数量＞6次";
                break;
            case 33:
                //desc = "累计投诉量＞10次|0.1";
                desc = "累计投诉量＞10次";
                break;

            // 4-业务订购因素
            case 41:
                //desc = "企业业务类型为企宽业务/互联网专线订购的最近业务订购日期距离业务失效日期都≤1/3|0.5";
                desc = "企业业务类型为企宽业务/互联网专线订购的最近业务订购日期距离业务失效日期都≤1/3";
                break;
            case 42:
                //desc = "30天内业务退订数量＞业务总量的1/2|0.2";
                desc = "30天内业务退订数量＞业务总量的1/2";
                break;
            case 43:
                //desc = "平均订购周期＜1年|0.1";
                desc = "平均订购周期＜1年";
                break;
            case 44:
                //desc = "最短订购周期＜30天|0.1";
                desc = "最短订购周期＜30天";
                break;
            case 45:
                //desc = "一年以上累计订购次数≤2|0.1";
                desc = "一年以上累计订购次数≤2";
                break;

            // 5-装维服务因素
            case 51:
                //desc = "平均工单创建到装维预约时长＞48小时|0.3";
                desc = "平均工单创建到装维预约时长＞48小时";
                break;
            case 52:
                //desc = "装维当月累计上门处理次数＞3次|0.4";
                desc = "装维当月累计上门处理次数＞3次";
                break;
            case 53:
                //desc = "装维预约时间与实际上门处理时间＞72小时|0.3";
                desc = "装维预约时间与实际上门处理时间＞72小时";
                break;

            default:
                desc = "";
                break;
        }
        return desc;
    }
}
