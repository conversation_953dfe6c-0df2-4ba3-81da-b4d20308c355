package com.cmiot.report.common.bean;


public class ReturnWithParam<T> extends ReturnInfo {

    /**
     * 数据对象
     */
    private T data;

    public ReturnWithParam() {
    }

    public ReturnWithParam(String code, String message, T data) {
        super(code, message);
        this.data = data;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
