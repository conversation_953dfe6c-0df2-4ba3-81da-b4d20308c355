package com.cmiot.report.common;

import com.cmiot.report.util.TimeStampUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Date 2018/5/28
 */
public class ComUtils {

    /**
     * 前n天
     * return yyyyMMdd
     * */
    public static String getPreDaysYYYYMMDD(int n){

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -n);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 前一天
     * return yyyyMMdd
     * */
    public static String getYYYYMMDD(){

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 前n天
     * return yyyyMMdd
     * */
    public static String getYYYYMMDDHHMMSS(int days){

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -days);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(calendar.getTime());
    }

    /**
     * 前二天
     * return yyyyMMdd
     * */
    public static String getPreYYYYMMDD(){

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -2);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 前一天
     * return yyyy-MM-dd
     * */
    public static String getYYYYMMDD2(){

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 前二天
     * return yyyy-MM-dd
     * */
    public static String getPreYYYYMMDD2(){

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -2);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 前一天
     * return yyyy-MM
     * */
    public static String getYYYYMM(){

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMM");
        return sdf.format(calendar.getTime());
    }
    /**
     * 返回当前时间， 格式yyyyMMddHHmmss
     * */
    public static String getYYYYMMDDHHMMSS(){

        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(new Date());
    }
    /**
     * 返回当前时间， 格式yyyy-MM-dd HH:mm:ss
     * */
    public static String getYYYYMMDDHHMMSS2(){

        SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date());
    }
    /**
     * 获取当天日期 yyyyMMdd
     * */
    public static String getCurrentDayYYYYMMDD(){

        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMMdd");

        return sdf.format(new Date());
    }
    /**
     * 获取今天和某天的时间戳差
     * */
    public static long getCurrentDayYYYYMMDDDifference(String day){
        long time=0;
        try {
            SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMMdd");
            Date today=new Date();
            Date pastDay = sdf.parse(day);
            time = today.getTime()/1000 - pastDay.getTime()/1000;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return time;
    }

    /**
     * 获取当前月份  yyyy-MM
     * */
    public static String getCurrentDayYYYYMM(){

        SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM");

        return sdf.format(new Date());
    }

    /**
     * 获取当前月份  yyyyMM
     * */
    public static String getCurrentDayYYYYMM2(){

        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMM");

        return sdf.format(new Date());
    }

    /**
     * 获取date
     * */
    public static Date getStringToDate(String time){
        Date date=null;
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd");
        try {
            date = sdf.parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 根据传入时间返回周
     * @param date yyyyMMdd
     * @return week 2018-35
     * */
    public static String getWeek(String date){
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            calendar.setTime(sdf.parse(date));
            int year =calendar.get(Calendar.YEAR);
            int week =calendar.get(Calendar.WEEK_OF_YEAR);
            return year +"-"+ week;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }
    /**
     * 根据传入时间返回上一周
     * @param date yyyyMMdd
     * @return week 2018-35
     * */
    public static String getPreWeek(String date){
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            calendar.setTime(sdf.parse(date));
            calendar.add(Calendar.DATE, -7);
            int year =calendar.get(Calendar.YEAR);
            int week =calendar.get(Calendar.WEEK_OF_YEAR);
            return year +"-"+ week;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }
    /**
     * 根据传入时间返回上一周
     * @param date yyyyMMdd
     * @return week 2018-35
     * */
    public static String getPreWeek2(String date){
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            calendar.setTime(sdf.parse(date));
            calendar.add(Calendar.DATE, -7);
            int year =calendar.get(Calendar.YEAR);
            int week =calendar.get(Calendar.WEEK_OF_YEAR);
            return year +""+ week;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }
    /**
     * 获取当前周的时间区间
     * */
    public static String[] getWeekTimeInterval() {
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        int dayOfWeek = calendar1.get(Calendar.DAY_OF_WEEK) - 1;
        System.out.println(dayOfWeek);
        int offset1 = 1 - dayOfWeek;
        int offset2 = 7 - dayOfWeek;
        if(dayOfWeek == 0){
            offset1 = -6;
            offset2 = 0;
        }
        calendar1.add(Calendar.DATE, offset1 );
        calendar2.add(Calendar.DATE, offset2 );
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd");
        String lastBeginDate = sdf.format(calendar1.getTime());
        String lastEndDate = sdf.format(calendar2.getTime());
        System.out.println(lastBeginDate +"_"+lastEndDate);
        return new String[]{ lastBeginDate , lastEndDate};
    }
    /**
     * 获取上一周的时间区间
     * */
    public static String[] getPreWeekTimeInterval() {
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        int dayOfWeek = calendar1.get(Calendar.DAY_OF_WEEK) - 1;
        int offset1 = 1 - dayOfWeek;
        int offset2 = 7 - dayOfWeek;
        if(dayOfWeek == 0){
            offset1 = -6;
            offset2 = 0;
        }
        calendar1.add(Calendar.DATE, offset1-7);
        calendar2.add(Calendar.DATE, offset2-7);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMMdd");
        String lastBeginDate = sdf.format(calendar1.getTime());
        String lastEndDate = sdf.format(calendar2.getTime());
        return new String[]{ lastBeginDate , lastEndDate};
    }

    /**
     * 获取上一周的时间区间
     * */
    public static String[] getPreWeekTimeInterval2() {
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        int dayOfWeek = calendar1.get(Calendar.DAY_OF_WEEK) - 1;
        int offset1 = 1 - dayOfWeek;
        int offset2 = 7 - dayOfWeek;
        if(dayOfWeek == 0){
            offset1 = -6;
            offset2 = 0;
        }
        calendar1.add(Calendar.DATE, offset1-7);
        calendar2.add(Calendar.DATE, offset2-7);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd");
        String lastBeginDate = sdf.format(calendar1.getTime());
        String lastEndDate = sdf.format(calendar2.getTime());
        return new String[]{ lastBeginDate , lastEndDate};
    }

    /**
     * 获取传入时间周的时间区间
     * */
    public static String[] getWeekTimeIntervalDate(String date) {
        SimpleDateFormat sdf0 = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        try {
            calendar1.setTime(sdf0.parse(date));
            calendar2.setTime(sdf0.parse(date));
            int dayOfWeek = calendar1.get(Calendar.DAY_OF_WEEK) - 1;
            if(dayOfWeek==0){
                dayOfWeek=7;
            }
            int offset1 = 1 - dayOfWeek;
            int offset2 = 7 - dayOfWeek;
            calendar1.add(Calendar.DATE, offset1 );
            calendar2.add(Calendar.DATE, offset2 );
            SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd");
            String lastBeginDate = sdf.format(calendar1.getTime());
            String lastEndDate = sdf.format(calendar2.getTime());
            return new String[]{ lastBeginDate , lastEndDate};
        } catch(ParseException e){
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取某一周的开始、结束日期
     * @param 2018-37
     * @return 2018-09-10,2018-09-16
     * */
    public static String[] getWeekTimeInterval(String date){
        Calendar calendar = Calendar.getInstance();
        calendar.setWeekDate(Integer.parseInt(date.split("-")[0]), Integer.parseInt(date.split("-")[1]) , 1);
        calendar.add(Calendar.DATE, 1);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd");
        String begin = sdf.format(calendar.getTime());
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setWeekDate(Integer.parseInt(date.split("-")[0]), Integer.parseInt(date.split("-")[1]) , 7);
        calendar2.add(Calendar.DATE, 1);
        String end = sdf.format(calendar2.getTime());

        return new String[]{begin,end};
    }
    /**
     * 传入年月获取当前月最后一天的日期
     * @param 2018-09
     * @return 2018-09-30
     * */
    public static String getMonthLastDay(String yearMonth){
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, Integer.parseInt(yearMonth.split("-")[0]));
        //设置月份
        cal.set(Calendar.MONTH, Integer.parseInt(yearMonth.split("-")[1]) - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());
        return lastDayOfMonth;
    }
    public static void main(String[] args) {
        LocalDate today = LocalDate.now();
        String date = Constants.YYYY_MM_DD.format(today.with(DayOfWeek.TUESDAY));
        System.out.println(date);
    }

    /**
     * 前一天的开始
     * return unix时间戳
     * */
    public static long getUnixStampStart(){
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_MONTH, -1);// 昨天-1天
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime().getTime()/1000;
    }

    /**
     * 前一天的结束
     * return unix时间戳
     * */
    public static long getUnixStampEnd(){
        Calendar c = Calendar.getInstance();
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime().getTime()/1000-1;
    }
    /**
     * daytime
     * return hbase存储开始时间戳
     * */
    public static int getDaytimeHbaseStartTimeStamp(String daytime){
        //获取起始时间的时间戳
        Long startTime = TimeStampUtil.Date2TimeStamp(daytime, "yyyy-MM-dd");
        Long st=startTime-1514736000;
        int s=st.intValue();
        return  s;
    }

    /**
     * daytime
     * return hbase存储结束时间戳
     * */
    public static int getDaytimeHbaseEndTimeStamp(String daytime){
        int ee=0;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date daytimeDate = simpleDateFormat.parse(daytime);
            //获取明天的日期
            Calendar c = Calendar.getInstance();
            c.setTime(daytimeDate);
            c.add(Calendar.DAY_OF_MONTH, 1);// 今天+1天
            //将小时至0
            c.set(Calendar.HOUR_OF_DAY, 0);
            //将分钟至0
            c.set(Calendar.MINUTE, 0);
            //将秒至0
            c.set(Calendar.SECOND, 0);
            //将毫秒至0
            c.set(Calendar.MILLISECOND, 0);
            Date tomorrow = c.getTime();
            //获取终止时间的时间戳
            long stopTimeStamp = tomorrow.getTime()/1000;
            Long et=stopTimeStamp-1514736000-1;
            ee = et.intValue();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return  ee;
    }

    /**
     * 当月的开始
     * return unix时间戳
     * */
    public static long getUnixStampMonthStart(){
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH,0);
        c.set(Calendar.DAY_OF_MONTH,1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime().getTime()/1000;
    }
    /**
     * 前一月
     * return yyyyMM
     * */
    public static String getPreMonth(){

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMM");
        return sdf.format(calendar.getTime());
    }
    /**
     * 当前日期的前一月
     * return yyyyMM
     * */
    public static String getPreMonthDate(){

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 将20190218231425转化为 格式yyyy-MM-dd HH:mm:ss
     * */
    public static String getStringToYYMMDDHHMMSS(String time){
        String s=null;
        if(time!=null&&time.length()==14){
            s=time.substring(0,4)+"-"+time.substring(4,6)+"-"+time.substring(6,8)+" "+time.substring(8,10)+":"+time.substring(10,12)+":"+time.substring(12,14);
        }
        return s;
    }

    /**
     * 将20190218转化为 格式yyyy-MM-dd
     * */
    public static String getStringToYYMMDD(String time){
        String s=null;
        if(!StringUtils.isBlank(time)&&time.length()==8){
            s=time.substring(0,4)+"-"+time.substring(4,6)+"-"+time.substring(6,8);
        }
        return s;
    }

    public static List<String> getDays(String startTime, String endTime) {

        // 返回的日期集合
        List<String> days = new ArrayList<String>();

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);

            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                days.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }

        return days;
    }
    public static Date formatDate(String s){
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return dateFormat.parse(s);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 输入20190218  输出周的第几天 1是星期天 2是星期一 7是星期六
     * */
    public static int getDayOfWeek(String date){
        int i=0;
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            calendar.setTime(sdf.parse(date));

            i =calendar.get(Calendar.DAY_OF_WEEK);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return i;
    }
    /**
     * 获取两指定日期之间的时间间隔天数
     * @param
     * @return
     */
    public static Integer getBlankDay(int day1,String day2){
        Integer blankDay=null;
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");
        try {
            Date date1 = sdf.parse(String.valueOf(day1));
            Date date2= sdf.parse(day2);
            Calendar cal = Calendar.getInstance();
            cal.setTime(date1);
            long time1 = cal.getTimeInMillis();
            cal.setTime(date2);
            long time2 = cal.getTimeInMillis();
            long between_days=(time2-time1)/(1000*3600*24);
            blankDay=Integer.parseInt(String.valueOf(between_days));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return blankDay;
    }
}
