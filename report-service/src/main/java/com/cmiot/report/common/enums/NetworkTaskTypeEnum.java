package com.cmiot.report.common.enums;

/**
 * 描述：组网统计任务类型
 *
 * <AUTHOR>
 * @date 2019/12/06
 */
public enum NetworkTaskTypeEnum {
    /**
     * 手动触发周任务
     */
    WEEK_MANUAL(1),
    /**
     * 手动触发月任务
     */
    MONTH_MANUAL(2),
    /**
     * 定时周任务
     */
    WEEK_TIMING(3),
    /**
     * 未知
     */
    UNKNOWN(0);
    private Integer type;

    NetworkTaskTypeEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public static NetworkTaskTypeEnum getByType(Integer type) {
        for (NetworkTaskTypeEnum value : NetworkTaskTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return UNKNOWN;
    }

    public static String getTaskComment(Integer type) {
        String comment;
        switch (type) {
            case 1:
                comment = "手动触发周任务";
                break;
            case 2:
                comment = "手动触发月任务";
                break;
            case 3:
                comment = "定时周任务";
                break;
            default:
                comment = "未知类型";
                break;
        }
        return comment;
    }
}
