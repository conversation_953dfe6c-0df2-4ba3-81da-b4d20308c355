package com.cmiot.report.common;

import javax.servlet.http.HttpServletRequest;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

/**
 * <AUTHOR>
 * Date 2018/5/23
 */
public class HttpUtil {

    public static void downloadHttpFile(String fileurl, String localPath, String fileName) {
        URL url;
        FileOutputStream out = null;
        InputStream ins = null;
        try {
            url = new URL(fileurl);
            URLConnection con = url.openConnection();
            out = new FileOutputStream(localPath+fileName);
            ins = con.getInputStream();
            byte[] b = new byte[1024];
            int i;
            while((i=ins.read(b))!=-1){
                out.write(b, 0, i);
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if(ins!=null){
                try{
                    ins.close();
                }catch (Exception e1){

                }

            }
            if(out!=null){
                try{
                    out.close();
                }catch (Exception e1){

                }

            }
        }

    }

    // 通过请求对象，获取访问路径
    private static String getRequestURI(HttpServletRequest request, boolean includeContextPath) {
        String requestURI = request.getRequestURI();
        if (includeContextPath) {
            return requestURI;
        } else {
            String contextPath = request.getContextPath();
            return requestURI.replace(contextPath, "");
        }
    }

    public static String getFullRequestURI(HttpServletRequest request) {
        return getRequestURI(request, true);
    }

}
