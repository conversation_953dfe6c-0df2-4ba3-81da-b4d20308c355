package com.cmiot.report.common;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.*;
import java.net.SocketException;


/**
 * <AUTHOR> Date 2018/5/23
 */
public class FtpUtil {

	private static Logger logger = LoggerFactory.getLogger(FtpUtil.class);
	private static final String FTPEXCEPTION = "ftp download exception";
	private static final String ENCODE = "UTF-8";

	/**
	 * 获取FTPClient对象
	 *
	 * @param ftpHost     FTP主机服务器
	 * @param ftpPassword FTP 登录密码
	 * @param ftpUserName FTP登录用户名
	 * @param ftpPort     FTP端口 默认为21
	 * @return
	 */
	public static FTPClient getFTPClient(String ftpHost, String ftpUserName, String ftpPassword, int ftpPort) {
		FTPClient ftpClient = new FTPClient();
		try {
			ftpClient = new FTPClient();
			ftpClient.connect(ftpHost, ftpPort);// 连接FTP服务器
			ftpClient.login(ftpUserName, ftpPassword);// 登陆FTP服务器
			if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
				logger.error("未连接到FTP，用户名或密码错误。");
				ftpClient.disconnect();
			} else {
				System.out.println("FTP连接成功。");
			}
		} catch (SocketException e) {
			logger.error(e.getMessage(), e);
			logger.error("FTP的IP地址可能错误，请正确配置。");
		} catch (IOException e) {
			logger.error(e.getMessage(), e);
			logger.error("FTP的端口错误,请正确配置。");
		}
		return ftpClient;
	}

	/*
	 * 从FTP服务器下载文件
	 *
	 * @param ftpHost FTP IP地址
	 * 
	 * @param ftpUserName FTP 用户名
	 * 
	 * @param ftpPassword FTP用户名密码
	 * 
	 * @param ftpPort FTP端口
	 * 
	 * @param ftpPath FTP服务器中文件所在路径 格式： ftptest/aa
	 * 
	 * @param localPath 下载到本地的位置 格式：H:/download
	 * 
	 * @param fileName 文件名称
	 */
	public static void downloadFtpFile(String ftpHost, String ftpUserName, String ftpPassword, int ftpPort,
			String ftpPath, String localPath, String fileName) {

		FTPClient ftpClient = null;
		OutputStream os = null;
		try {
			ftpClient = getFTPClient(ftpHost, ftpUserName, ftpPassword, ftpPort);
			ftpClient.setControlEncoding(ENCODE); // 中文支持
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
			ftpClient.enterLocalPassiveMode();
			ftpClient.changeWorkingDirectory(ftpPath);

			File localFile = new File(localPath + File.separatorChar + fileName);
			os = new FileOutputStream(localFile);
			ftpClient.retrieveFile(fileName, os);
			os.close();
			ftpClient.logout();

		} catch (FileNotFoundException e) {
			logger.error("没有找到" + ftpPath + "文件");
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		} catch (SocketException e) {
			logger.error("连接FTP失败.");
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		} catch (IOException e) {
			logger.error("文件读取错误。");
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		}

	}

	/**
	 * Description: 向FTP服务器上传文件
	 * 
	 * @param ftpHost     FTP服务器hostname
	 * @param ftpUserName 账号
	 * @param ftpPassword 密码
	 * @param ftpPort     端口
	 * @param ftpPath     FTP服务器中文件所在路径 格式： ftptest/aa
	 * @param fileName    ftp文件名称
	 * @param input       文件流
	 * @return 成功返回true，否则返回false
	 */
	public static boolean uploadFile(String ftpHost, String ftpUserName, String ftpPassword, int ftpPort,
			String ftpPath, String fileName, InputStream input) {
		boolean success = false;
		FTPClient ftpClient = null;
		try {
			int reply;
			ftpClient = getFTPClient(ftpHost, ftpUserName, ftpPassword, ftpPort);
			reply = ftpClient.getReplyCode();
			if (!FTPReply.isPositiveCompletion(reply)) {
				ftpClient.disconnect();
				return success;
			}
			ftpClient.setControlEncoding(ENCODE); // 中文支持
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
			ftpClient.enterLocalPassiveMode();
			ftpClient.changeWorkingDirectory(ftpPath);

			ftpClient.storeFile(fileName, input);

			input.close();
			ftpClient.logout();
			success = true;
		} catch (Exception e) {
			logger.error("uploadFile异常" + e);
		} finally {
			if (ftpClient != null && ftpClient.isConnected()) {
				try {
					ftpClient.disconnect();
				} catch (Exception ioe) {
					logger.info("ftpClient.disconnect()异常" + ioe);
				}
			} else {
				logger.info("ftpClient为空或者未连接");
			}
		}
		return success;
	}

	/*
	 * 从FTP服务器下载某目录下多个文件
	 *
	 * @param ftpHost FTP IP地址
	 * 
	 * @param ftpUserName FTP 用户名
	 * 
	 * @param ftpPassword FTP用户名密码
	 * 
	 * @param ftpPort FTP端口
	 * 
	 * @param ftpPath FTP服务器中文件所在路径 格式： ftptest/aa
	 * 
	 * @param localPath 下载到本地的位置 格式：H:/download
	 */
	public static void downloadFtpFiles(String ftpHost, String ftpUserName, String ftpPassword, int ftpPort,
			String ftpPath, String localPath, String fileNameFilter) {

		FTPClient ftpClient = null;
		OutputStream os = null;
		try {
			ftpClient = getFTPClient(ftpHost, ftpUserName, ftpPassword, ftpPort);
			ftpClient.setControlEncoding(ENCODE); // 中文支持
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
			ftpClient.enterLocalPassiveMode();
			ftpClient.changeWorkingDirectory(ftpPath);
			FTPFile[] fs = ftpClient.listFiles();
			for (FTPFile ff : fs) {
				if (fileNameFilter != null && !"".equals(fileNameFilter) && !ff.getName().contains(fileNameFilter)) {
					continue;
				}
				File dir = new File(localPath);
				if (!dir.exists()) {
					dir.mkdirs();
				}
				File localFile = new File(localPath + File.separatorChar + ff.getName());
				os = new FileOutputStream(localFile);
				ftpClient.retrieveFile(ff.getName(), os);
				os.close();
			}
			ftpClient.logout();

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		} catch (SocketException e) {
			logger.error("连接FTP失败.");
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		} catch (IOException e) {
			logger.error("文件读取错误。");
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		}
	}

	/*
	 * 从FTP服务器下载某目录下多个文件
	 *
	 * @param ftpHost FTP IP地址
	 * 
	 * @param ftpUserName FTP 用户名
	 * 
	 * @param ftpPassword FTP用户名密码
	 * 
	 * @param ftpPort FTP端口
	 * 
	 * @param ftpPath FTP服务器中文件所在路径 格式： ftptest/aa
	 * 
	 * @param localPath 下载到本地的位置 格式：H:/download
	 * 
	 */
	public static void downloadFtpFiles(String ftpHost, String ftpUserName, String ftpPassword, int ftpPort,
			String ftpPath, String localPath, String filePrefix, String fileSuffix) {

		FTPClient ftpClient = null;
		OutputStream os = null;
		try {
			ftpClient = getFTPClient(ftpHost, ftpUserName, ftpPassword, ftpPort);
			ftpClient.setControlEncoding(ENCODE); // 中文支持
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
			ftpClient.enterLocalPassiveMode();
			ftpClient.changeWorkingDirectory(ftpPath);
			FTPFile[] fs = ftpClient.listFiles();
			for (FTPFile ff : fs) {
				if (!StringUtils.isEmpty(filePrefix) && !ff.getName().contains(filePrefix)) {
					continue;
				}
				if (!StringUtils.isEmpty(fileSuffix) && !ff.getName().contains(fileSuffix)) {
					continue;
				}
				File dir = new File(localPath);
				if (!dir.exists()) {
					dir.mkdirs();
				}
				File localFile = new File(localPath + File.separatorChar + ff.getName());
				os = new FileOutputStream(localFile);
				ftpClient.retrieveFile(ff.getName(), os);
				os.close();
			}
			ftpClient.logout();

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		} catch (SocketException e) {
			logger.error("连接FTP失败.");
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		} catch (IOException e) {
			logger.error("文件读取错误。");
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		}
	}

	/*
	 * 从FTP服务器删除多个文件
	 *
	 * @param ftpHost FTP IP地址
	 * 
	 * @param ftpUserName FTP 用户名
	 * 
	 * @param ftpPassword FTP用户名密码
	 * 
	 * @param ftpPort FTP端口
	 * 
	 * @param ftpPath FTP服务器中文件所在路径 格式： ftptest/aa
	 * 
	 * 
	 */
	public static void deleteFile(String ftpHost, String ftpUserName, String ftpPassword, int ftpPort, String ftpPath,
			String filePrefix, String fileSuffix) {
		FTPClient ftpClient = null;
		try {
			ftpClient = getFTPClient(ftpHost, ftpUserName, ftpPassword, ftpPort);
			ftpClient.setControlEncoding(ENCODE); // 中文支持
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
			ftpClient.enterLocalPassiveMode();
			ftpClient.changeWorkingDirectory(ftpPath);
			FTPFile[] fs = ftpClient.listFiles();
			for (FTPFile ff : fs) {
				if (!StringUtils.isEmpty(filePrefix) && !ff.getName().contains(filePrefix)) {
					continue;
				}
				if (!StringUtils.isEmpty(fileSuffix) && !ff.getName().contains(fileSuffix)) {
					continue;
				}
				// 清理file文件
				ftpClient.deleteFile(ftpPath + "/" + ff.getName());
			}
			ftpClient.logout();
		} catch (FileNotFoundException e) {
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		} catch (SocketException e) {
			logger.error("连接FTP失败.");
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		} catch (IOException e) {
			logger.error("文件读取错误。");
			logger.error(e.getMessage(), e);
			throw new RuntimeException(FTPEXCEPTION);
		}
	}
}
