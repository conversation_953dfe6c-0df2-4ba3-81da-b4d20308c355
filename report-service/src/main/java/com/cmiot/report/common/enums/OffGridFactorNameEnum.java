package com.cmiot.report.common.enums;


public enum OffGridFactorNameEnum {

    // 1-终端质量因素
    ONE(1),
    // 2-网络质量因素
    TWO(2),
    // 3-客户服务因素
    THREE(3),
    // 4-业务订购因素
    FOUR(4),
    // 5-装维服务因素
    FIVE(5);

    private int type;

    OffGridFactorNameEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public static String getFactorName(int type) {
        String desc;
        switch (type) {
            case 1:
                desc = "终端质量因素";
                break;
            case 2:
                desc = "网络质量因素";
                break;
            case 3:
                desc = "客户服务因素";
                break;
            case 4:
                desc = "业务订购因素";
                break;
            default:
                desc = "装维服务因素";
                break;
        }
        return desc;
    }
}
