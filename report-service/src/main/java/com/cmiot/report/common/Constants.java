package com.cmiot.report.common;

import java.time.format.DateTimeFormatter;

public interface Constants {

	public static final String DATA = "data";

	public static final String TOTAL = "total";

	public static final String PAGE = "page";

	public static final String PAGESIZE = "pageSize";

	public static final String VIXTEL_FILE_DOWNLOAD_STATE = "vixtel_file_download_state"; // 0:正在下载  1:下载完成

	public static final String  STATISTICS_GATEWAY_TOTAL_NUM = "gateway_total_num";
	public static final String  STATISTICS_DEVICE_TOTAL_NUM = "device_total_num";
	public static final String  STATISTICS_WIRED_DEVICE_NUM = "wired_device_num";
	public static final String  STATISTICS_WIRLESS_DEVICE_NUM = "wireless_device_num";
	public static final String  STATISTICS_LAST_EXECUTE_TIME = "last_execute_time";

	public static final String PHONE_PREFIX="phoneNumber_";

	public static final String BUSINESS_CODE_WBAND = "wband";
	public static final String BUSINESS_CODE_WBAND_2 = "宽带";
	public static final String BUSINESS_CODE_OTTTV = "otttv";
	public static final String BUSINESS_CODE_VOIP = "voip";
	public static final String BUSINESS_CODE_IPTV = "iptv";

	public static final String BUSINESS_TYPE_XINZHUANG = "1";
	public static final String BUSINESS_TYPE_CHAIJI = "2";
	public static final String BUSINESS_TYPE_HUANJI = "6";
	public static final String BUSINESS_TYPE_YIJI = "8";
	public static final String BUSINESS_TYPE_XINZHUANG_TUIDING = "10";
	public static final String BUSINESS_TYPE_HUANJI_TUIDING = "11";
	public static final String BUSINESS_TYPE_YIJI_TUIDING = "12";
	public static final String BUSINESS_TYPE_BUDAN= "13";

	public static final String CRM_RESPONSE_SUCCESS = "0";//crm请求，成功返回码
	public static final String CRM_RESPONSE_NOT_MOBILE = "***********";//crm请求，非移动号码返回码
	public static final String CRM_RESPONSE_TIMEOUT = "-20";//cr响应超时
	public static final String CRM_REQUEST_STATUS_SUCCESS = "1";//请求成功，并返回带宽
	public static final String CRM_REQUEST_STATUS_NO_DATA = "2";//请求成功，但无带宽数据
	public static final String CRM_REQUEST_STATUS_NOT_MOBILE = "3";//请求成功，但宽带账号非移动
	public static final String CRM_REQUEST_STATUS_TIMEOUT = "4";//请求超时
	public static final String CRM_RESPONSE_STATUS_TIMEOUT = "5";//crm响应超时
	public static final String CRM_REQUEST_STATUS_OTHER = "6";//其他

	DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	DateTimeFormatter YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");

	String ACCOUNT = "account";
	String ADMIN = "admin";
	String CHECK_STATUS = "checkStatus";
}
