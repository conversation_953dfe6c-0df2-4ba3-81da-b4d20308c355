package com.cmiot.report.dto;

import lombok.Data;
import lombok.ToString;

import java.util.List;

@ToString
@Data
public class EnterpriseDeviceVo {

    /**
     * 设备账号
     */
    private String account;

    private String sn;

    private String mac;

    private String manufacturer;

    private String deviceModel;

    private String groupName;

    private String groupId;

    private Integer isOnline;

    private String id;

    private Integer deviceType;

    // 1表示WiFi6设备、2表示AC+AP、3表示FTTR
    private Integer networkType;

    // 显示主从设备
    private Integer devicePattern;

    // 主设备Id
    private String parentId;

    // 从设备
    private List<EnterpriseDeviceVo> children;

}
