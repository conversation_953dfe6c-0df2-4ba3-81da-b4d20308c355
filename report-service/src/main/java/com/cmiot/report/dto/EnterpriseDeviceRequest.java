package com.cmiot.report.dto;

import lombok.Data;

import java.util.List;

@Data
public class EnterpriseDeviceRequest {

    private String sn;

    private String mac;

    private List<String> groupIds;

    private Integer deviceType;

    private Long enterpriseId;

    private Integer page;

    private Integer pageSize;

    // 组网设备类型， 2表示AC+AP、3表示FTTR、4表示路由器
    private Integer networkType;

    // 组网型号类型
    private List<Integer> modelList;
}
