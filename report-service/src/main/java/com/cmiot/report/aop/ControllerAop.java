package com.cmiot.report.aop;

import com.cmiot.fgeo.common.error.FGEOException;
import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.report.common.HttpUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;


//@Aspect
//@Component
public class ControllerAop {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HttpServletRequest request;

    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *) " +
            "|| within(@org.springframework.stereotype.Controller *)")
    private void allMethodOfController() {
    }

    @Around("allMethodOfController()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        String requestURI = HttpUtil.getFullRequestURI(request);
        String method = request.getMethod();
        Object[] argsBefore = joinPoint.getArgs();
        Object[] args = new Object[argsBefore.length];
        // 移除无法被序列化的参数
        for (int i = 0; i < argsBefore.length; i++) {
            Object argBefore = argsBefore[i];
            if (argBefore instanceof ServletRequest || argBefore instanceof ServletResponse
                    || argBefore instanceof MultipartFile) {
                continue;
            }
            args[i] = argBefore;
        }
        String parameter;
        try {
            parameter = JSONUtil.toJSONString(args);
        } catch (Exception e) {
            parameter = Arrays.toString(args);
        }
        logger.info("{}, {} ,请求参数: {}", method, requestURI, parameter);
        long startTime = System.currentTimeMillis();
        try {
            Object proceed = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            if (proceed == null) {
                logger.info("{}, {} 响应结果: {}", method, requestURI, "null");
            } else {
                if (proceed.getClass() != Void.class) {
                    logger.info("{} 响应结果: {}", requestURI, JSONUtil.toJSONString(proceed));
                }
                logger.debug("{} 请求处理时间: {} ms", requestURI, endTime - startTime);
            }
            return proceed;
        } catch (FGEOException fe) {
            logger.info("{} 响应结果: {}", requestURI, fe.getMessage());
            throw fe;
        } catch (Exception e) {
            logger.error("{} 异常:", requestURI, e);
            throw e;
        }
    }
}
