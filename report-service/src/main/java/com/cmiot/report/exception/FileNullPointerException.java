package com.cmiot.report.exception;

/**
 * 文件未找到异常
 * 
 * <AUTHOR>
 *
 */
public class FileNullPointerException extends Exception {

	private static final long serialVersionUID = 1L;

	private String code;

	public FileNullPointerException() {
		super();
	}

	public FileNullPointerException(String message, Throwable cause) {
		super(message, cause);
	}

	public FileNullPointerException(String code, String message, Throwable cause) {
		super(message, cause);
		this.code = code;
	}

	public FileNullPointerException(String code, String message) {
		super(message);
		this.code = code;
	}

	public FileNullPointerException(String message) {
		super(message);
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

}
