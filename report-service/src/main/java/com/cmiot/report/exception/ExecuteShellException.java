package com.cmiot.report.exception;

/**
 * 脚本执行异常
 * 
 * <AUTHOR>
 *
 */
public class ExecuteShellException extends Exception {

	private static final long serialVersionUID = 1L;

	private String code;

	public ExecuteShellException() {
		super();
	}

	public ExecuteShellException(String message, Throwable cause) {
		super(message, cause);
	}

	public ExecuteShellException(String code, String message, Throwable cause) {
		super(message, cause);
		this.code = code;
	}

	public ExecuteShellException(String code, String message) {
		super(message);
		this.code = code;
	}

	public ExecuteShellException(String message) {
		super(message);
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

}
