package com.cmiot.report.otherserverfeign;

import com.cmiot.fgeo.devicemanager.gatewayapi.dto.DeviceGroupResponse;
import com.cmiot.report.dto.EnterpriseDeviceRequest;
import com.cmiot.report.dto.EnterpriseDeviceResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Classname ApiServerDeviceFeignClient
 * @Description
 * @Date 2022/9/28 18:45
 * @Created by lei
 */
@FeignClient(
        name = "apiservice",
        url = "${endpoints.apiService}",
        contextId = "apiServerDeviceFeignClient"
)
public interface ApiServerDeviceFeignClient {

    @GetMapping("/v1/e/device/group/device-type-list")
    List<DeviceGroupResponse> queryGroupDeviceByType(@RequestHeader("uid") Long uid,
                                                     @RequestHeader("eid") Long eid,
                                                     @RequestParam("type") String type,
                                                     @RequestParam("status") String status);

    @GetMapping("/v1/e/device/deviceList")
    EnterpriseDeviceResponse enterpriseDeviceList(@RequestBody EnterpriseDeviceRequest request,
                                                  @RequestHeader("uid") String uid,
                                                  @RequestHeader("eid") Long enterpriseId);
}
