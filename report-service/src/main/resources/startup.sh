#!/bin/bash

cd `dirname $0`
DEPLOY_DIR=`pwd`
CONF_PATH="$DEPLOY_DIR"
JAVA_OPTS="-Xmx1G -Xms1G -XX:NewSize=256m -XX:MaxNewSize=256m -XX:SurvivorRatio=8 -XX:MaxTenuringThreshold=2 -XX:TargetSurvivorRatio=50 -XX:+CMSClassUnloadingEnabled -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC"
PID=`ps -ef | grep java | grep   report-service-*.jar |awk '{print $2}'`
if [ -n "$PID" ]; then
    echo "ERROR: report-service already started!"
    echo "PID: $PID"
    exit 1
fi

echo -e "Starting report-service ...\c"
nohup java -Dfastjson.parser.safeMode=true -Dspring.config.location=$CONF_PATH/application.yml -Dlogging.config=$CONF_PATH/logback.xml $JAVA_OPTS -jar report-service-*.jar > /dev/null 2>&1 &

echo ""
echo "OK!"
PIDS=`ps -ef | grep java | grep report-service-*.jar | awk '{print $2}'`
echo "PID: $PIDS"
