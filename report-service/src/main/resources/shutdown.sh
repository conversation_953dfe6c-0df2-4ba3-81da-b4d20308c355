#!/bin/sh
AppPid=0

getAppPid(){
    javaps=`ps -ef | grep report-service-*.jar | grep -v "$0" | grep -v "grep"`
    if [ -n "$javaps" ]; then
        AppPid=`echo $javaps | awk '{print $2}'`
    else
        AppPid=0
    fi
}

shutdown(){
    getAppPid
    echo "================================================================================================================"
    if [ $AppPid -ne 0 ]; then
        echo "Stopping report-service(PID=$AppPid)..."
        kill -9 $AppPid
        if [ $? -eq 0 ]; then
            echo "report-service stopped successful!"
            echo "================================================================================================================"
        else
            echo "report-service stopped failed!"
            echo "================================================================================================================"
        fi
        getAppPid
        if [ $AppPid -ne 0 ]; then
            shutdown
        fi
    else
        echo "report-service is not running!"
        echo "================================================================================================================"
    fi
}

shutdown


