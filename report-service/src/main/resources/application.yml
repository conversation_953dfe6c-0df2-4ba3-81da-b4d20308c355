server:
  port: 8081
debug: false
spring:
  application:
    name: ge-report
  cloud:
    nacos:
      discovery:
        server‐addr: 172.19.1.73:8848,172.19.1.74:8848,172.19.1.76:8848
    kubernetes:
      discovery:
        enabled: false
      loadbalancer:
        enabled: false
    discovery:
      enabled: false
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    clickhouse:
      driverClassName: ru.yandex.clickhouse.ClickHouseDriver
      url: ********************************************
      userName: manage
      password: kisE7BxW5gURrvnC
#      url: ********************************************
#      userName: manage
#      password: CisE7Bxx5gURrxnK
      initialSize: 10
      maxActive: 100
      minIdle: 10
      maxWait: 6000
      validationQuery: SELECT 1
  redis:
    host: **********
    port: 6407
    password: kfzisgreatman100
    database: 0
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml,classpath*:/auto/*.xml
  configuration:
    log‐impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 导出临时文件目录
# use-pri-url 1 使用内网地址，否则使用外网地址
export:
  use-pri-url: 0
  tmp:
    local: C:/lei/test/20220830
network:
  threshold:
    floor: 10
    upper: 80

# 网关&组网超时设备列表,超时时间(小时)
device:
  list:
    mac-overtime: 72
    sub-overtime: 72

#目标营销客户分数 (0, 100]
customer:
  marketing:
    score: 80
gateway:
  model:
    int: 1,2,3,4,5,6,7,8,0
    str: 型号一 , 型号二 , 型号三 , 型号四 , 型号五 , 型号六 , 型号七 , 型号八 , 未知型号
endpoints:
  apiService: apiservice:9080
  ams: ams:8081
