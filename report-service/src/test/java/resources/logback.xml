<?xml version="1.0" encoding="UTF-8" ?>
<!-- 日志组件启动时，打印调试信息，并监控此文件变化，周期300秒 -->
<configuration scan="true" scanPeriod="300 seconds" debug="false">
    <!--针对jul的性能优化 -->
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>
    <!-- 配置文件，包括此文件内的所有变量的配置 -->
    <property name="LOG_PATH" value="logs"/>
    <property name="APP_NAME" value="ge-report"/>
    <!-- contextName主要是为了区分在一个web容器下部署多个应用启用jmx时，不会出现混乱 -->
    <contextName>${APP_NAME}</contextName>
    <!-- ***************************************************************** -->
    <!-- 配置输出到控制台，仅在开发测试时启用输出到控制台  -->
    <!-- ***************************************************************** -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{0}:%L- %msg%n\n</pattern>
        </encoder>
    </appender>
    <root>
        <appender-ref ref="console"/>
    </root>
    <!-- ***************************************************************** -->
    <!-- info级别的日志appender -->
    <!-- ***************************************************************** -->
    <appender name="report-info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_NAME}-info-30dt.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_NAME}-info-30dt.log.%d{yyyy-MM-dd}.%i
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>1024MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{0}:%L- %msg%n\n</pattern>
        </encoder>
    </appender>
    <!-- ***************************************************************** -->
    <!-- error级别日志appender -->
    <!-- ***************************************************************** -->
    <appender name="report-error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_NAME}-error-30dt.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_NAME}-error-30dt.%d{yyyy-MM-dd}.%i
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>1024MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{0}:%L- %msg%n\n</pattern>
        </encoder>
    </appender>
    <!-- 根日志logger -->
    <root level="info">
        <appender-ref ref="report-info"/>
        <appender-ref ref="report-error"/>
    </root>
    <logger name="com.cmiot.report.mapper" level="debug" />
</configuration>