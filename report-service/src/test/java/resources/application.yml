server:
  port: 8090
debug: false
spring:
  application:
    name: ge-report
  cloud:
    nacos:
      discovery:
        server‐addr: 172.19.1.73:8848,172.19.1.74:8848,172.19.1.76:8848
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    clickhouse:
      driverClassName: ru.yandex.clickhouse.ClickHouseDriver
      url: ********************************************
      userName: ck
      password: ck2022
      initialSize: 10
      maxActive: 100
      minIdle: 10
      maxWait: 6000
      validationQuery: SELECT 1
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml,classpath*:/auto/*.xml

# 导出临时文件目录
# use-pri-url 1 使用内网地址，否则使用外网地址
export:
  use-pri-url: 0
  tmp:
    local: C:/lei/test/20220830
network:
  threshold:
    floor: 10
    upper: 80
gateway:
  model:
   int: 0,8,7,6,5,4,3,2,1
   str:  未知类型 , 类型8 , 类型7 , 类型6 , 类型5 , 类型4 , 类型3 , 类型2 , 类型1


