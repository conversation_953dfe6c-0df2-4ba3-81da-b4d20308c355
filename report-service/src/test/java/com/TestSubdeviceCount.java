package com;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import com.cmiot.report.Application;
import com.cmiot.report.bean.DeviceAllByCount;
import com.cmiot.report.bean.DeviceAllExample;
import com.cmiot.report.bean.DeviceCumulativeAllCount;
import com.cmiot.report.bean.DeviceCumulativeAllExample;
import com.cmiot.report.mapper.DeviceAllMapper;
import com.cmiot.report.mapper.DeviceCumulativeAllMapper;
import com.cmiot.report.util.DateUtil;

import lombok.extern.slf4j.Slf4j;

@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestSubdeviceCount {

    @Autowired
    private DeviceAllMapper deviceAllMapper;
    
    @Autowired
    private DeviceCumulativeAllMapper deviceCumulativeAllMapper;
    
    @Test
    public void testDeviceCumulativeAll() {
    	List<String> provinces = new ArrayList<>();
        List<String> citys = new ArrayList<>();
        List<String> vendors = new ArrayList<>();
        // 1有线 2无线
        int accessMode = 1;
        // time,area,vendor
        String type = "time";
        String startTime = "2022-06-01";
        String endTime = "2022-06-20";
        
        // 厂商编码转换为long
        List<Long> vendorsIdList = vendors.stream()
        		.mapToLong(x -> NumberUtils.toLong(x, 0))
        		.filter(x -> x > 0).boxed()
        		.collect(Collectors.toList());
        // 无线状态
        List<String> wirelessList = new ArrayList<String>();
        wirelessList.add("2.4G");
        wirelessList.add("5G");
        
        
        // 查询条件组合
        DeviceCumulativeAllExample example = new DeviceCumulativeAllExample();
        DeviceCumulativeAllExample.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(provinces)) criteria.andProvinceCodeIn(provinces);
        if (!CollectionUtils.isEmpty(citys)) criteria.andCityCodeIn(citys);
        if (!CollectionUtils.isEmpty(vendorsIdList)) criteria.andFactoryIdIn(vendorsIdList);
        // 无线设置
        if(2 == accessMode) {
        	criteria.andWlanRadioTypeIn(wirelessList);
        } else if(1 == accessMode) {
        	criteria.andWlanRadioTypeNotIn(wirelessList);
        }
        // yyyy-MM-dd HH:mm:ss
        String yesterday = DateUtil.getYesterday();
        System.err.println(yesterday);
        Date startTimeDate = DateUtil.stringToDate(yesterday + "000000", DateUtil.UNSIGNED_DATE_TIME_PATTERN);
        Date endTimeDate = DateUtil.stringToDate(yesterday + "232359", DateUtil.UNSIGNED_DATE_TIME_PATTERN);
        if(startTimeDate != null && endTimeDate != null) {
        	criteria.andPdateBetween(startTimeDate, endTimeDate);
        }
        
        // 统计新增量
        List<DeviceCumulativeAllCount> allCountList = deviceCumulativeAllMapper.countDistinctMacByExample(example);
        
        log.error("-------------------- start ----------------------");
        for(DeviceCumulativeAllCount ans : allCountList) {
        	log.error("data : {}", ans.toString());
        }
        log.error("-------------------- over ----------------------");
    }
    
    /**
     * 网关质量分析—网关告警阈值设置
     */

    @Test
    public void testSelectDeviceAllCountByExample() {
    	
        List<String> provinces = new ArrayList<>();
        List<String> vendors = new ArrayList<>();
        String startTime = "2022-05-01";
        String endTime = "2022-06-20";

        DeviceAllExample example = new DeviceAllExample();
        DeviceAllExample.Criteria criteria = example.createCriteria();

        if (!CollectionUtils.isEmpty(provinces)) criteria.andProvinceCodeIn(provinces);
        if (!CollectionUtils.isEmpty(vendors)) criteria.andGatewayVendorIn(vendors);

        // yyyy-MM-dd HH:mm:ss
        Date startTimeDate = DateUtil.stringToDate(startTime + " 00:00:00", DateUtil.DATE_TIME_PATTERN);
        Date endTimeDate = DateUtil.stringToDate(endTime + " 23:23:59", DateUtil.DATE_TIME_PATTERN);
        if(startTimeDate != null && endTimeDate != null) {
        	criteria.andSampleTimeBetween(startTimeDate, endTimeDate);
        }
    	
    	List<DeviceAllByCount> countList = deviceAllMapper.selectDeviceAllCountByExample(example);
    	
    	log.error("-------------------- start ----------------------");
    	for(DeviceAllByCount dabc:countList) {
    		log.error("data : {}", dabc);
    	}
    	log.error("-------------------- over ----------------------");
    	
    }

}
