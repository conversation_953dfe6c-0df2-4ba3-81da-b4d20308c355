package com;


import com.alibaba.fastjson.JSON;
import com.cmiot.report.Application;
import com.cmiot.report.bean.*;
import com.cmiot.report.dto.*;
import com.cmiot.report.mapper.GatewayAlarmDetailMapper;
import com.cmiot.report.mapper.GatewayTotalCountMapper;
import com.cmiot.report.service.GatewayCountService;
import com.cmiot.report.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.platform.commons.logging.Logger;
import org.junit.platform.commons.logging.LoggerFactory;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class GatewayCountTest {

    private static final Logger logger = LoggerFactory.getLogger(GatewayCountTest.class);

    @Autowired
    private GatewayCountService gatewayCountService;

    @Autowired
    private GatewayAlarmDetailMapper gatewayAlarmDetailMapper;

    @Autowired
    private GatewayTotalCountMapper gatewayTotalCountMapper;

    /**
     * 网关质量分析—网关告警阈值设置
     */

    @Test
    public void setAlarmThreshold() {
        AlarmThresholdRequest request = new AlarmThresholdRequest();
        request.setDnsAnalysisDuration(new BigDecimal(5));
        request.setCpuRamHighUseNum(new BigDecimal(5));
        request.setToAlarmTimes(new BigDecimal(5));
        request.setHttpResponseDuration(new BigDecimal(5));;
        request.setTcpHandshakeDuration(new BigDecimal(5));
        gatewayCountService.setAlarmThreshold(request);
    }


    /**
     * 网关质量分析—网关告警阈值查询
     */
    @Test
    public void getAlarmThreshold() {
        AlarmThresholdResult result = gatewayCountService.getAlarmThreshold();
    }

    /**
     * 网关质量分析—网关告警分析
     */
    @Test
    public void getGatewayAlarmCount() {
        GatewayAlarmRequst request = new GatewayAlarmRequst();
        request.setStartTime("2022-06-01");
        request.setEndTime("2022-06-08");
        request.setProvince("350000,130000");
        //request.setType("area");
        request.setType("vendor");
        request.setVendor("14");

//        GatewayAlarmCountResult result = gatewayCountService.getGatewayAlarmCount(request);
//        System.err.println(JSON.toJSONString(result));
    }


    /**
     * 网关质量分析—网关告警列表
     */
    @Test
    public void getGatewayAlarmList() {
        GatewayAlarmListRequst request = new GatewayAlarmListRequst();
        request.setStartTime("2022-06-01");
        request.setEndTime("2022-06-08");
        request.setProvince("350000,130000");
        request.setVendor("14");
        request.setPage(2);
        request.setPageSize(2);

//        GatewayAlarmListResult result = gatewayCountService.getGatewayAlarmList(request);
//        System.err.println(JSON.toJSONString(result));
    }

    /**
     * 网关质量分析—网关Pon口光功率分析
     */
    @Test
    public void getGatewayPonStatistic() {
        PonStatisticRequst request = new PonStatisticRequst();
        request.setTime("2022-06-19");
        //request.setProvince("110000");
        //request.setCity("110113,110109,110117");
        // 类型：区域1,厂商2,型号3
        request.setType("1");
        //request.setVendor("10,1");
        //request.setModel("49,1,2,3");
        PonStatisticResult result = gatewayCountService.getGatewayPonStatistic(request);
    }


    /**
     * 网关质量分析—网关Pon口光功率列表查询
     */
    @Test
    public void getGatewayPonList() {
        PonListRequst request = new PonListRequst();
        //request.setSn("TESSN1002692");
        request.setProvince("110000");
        //request.setCity("110101,110102");
        //request.setVendor("1,5,8");
        request.setPage(1);
        request.setPageSize(20);
        PonListResult result = gatewayCountService.getGatewayPonList(request);
    }

    /**
     * 网关质量分析—网关Pon口光功率列表导出
     */
    @Test
    public void getGatewayPonListExport() {
        PonListExportRequest request = new PonListExportRequest();
        request.setUid("194096223181877277");
        //request.setSn("TESSN1002692");
        request.setProvince("110000");
        //request.setCity("110101,110102");
        //request.setVendor("1,5,8");
        String taskName = gatewayCountService.getGatewayPonListExport(request);
    }

    @Test
    public void GatewayTotalCountTest() {
        PonStatisticRequst request = new PonStatisticRequst();
        request.setModel("12");
        request.setTime("2022-06-03");
        request.setProvince("23442");
        request.setCity("21992");
        request.setType("1");
        request.setVendor("1234");

        String provCodes = request.getProvince();
        String cityCodes = request.getCity();
        String vendorIds = request.getVendor();
        String modelIds = request.getModel();

        // yyyy-MM-dd
        String inputTime = request.getTime();

        // 区域-1,厂商-2,型号-3
        String type = request.getType();

        // yyyyMMdd
        long time = Long.parseLong(DateUtil.formatInputs(inputTime));

        //GatewayTotalCountExample example = new GatewayTotalCountExample();
        //GatewayTotalCountExample.Criteria criteria = example.createCriteria();

        GatewayTotalCountExample totalExample = new GatewayTotalCountExample();
        GatewayTotalCountExample.Criteria totalCriteria = totalExample.createCriteria();

        List<String> provList = null;
        if (StringUtils.isNotBlank(provCodes)) {
            provList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(provCodes, "\\,"));
            //criteria.andProvinceCodeIn(provList);
            totalCriteria.andProvinceCodeIn(provList);
        }

        List<String> cityCodeList = null;
        if (StringUtils.isNotBlank(cityCodes)) {
            cityCodeList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(cityCodes, "\\,"));
            //criteria.andCityCodeIn(cityCodeList);
            totalCriteria.andCityCodeIn(cityCodeList);
        }

        List<Long> vendorIdList = null;
        if (StringUtils.isNotBlank(vendorIds)) {
            List<String> vendorIdStrList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(vendorIds, "\\,"));
            vendorIdList = vendorIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
            //criteria.andFactoryIdIn(vendorIdList);
            totalCriteria.andFactoryIdIn(vendorIdList);
        }

        List<Long> modelIdList = null;
        if (StringUtils.isNotBlank(modelIds)) {
            List<String> modelIdStrList = (List<String>) CollectionUtils.arrayToList(StringUtils.split(modelIds, "\\,"));
            modelIdList = modelIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
            totalCriteria.andFactoryModelIdIn(modelIdList);
        }

        //criteria.andGdateEqualTo(time);
        totalCriteria.andGdateEqualTo(time);
        //List<GatewayTotalCount> counts = gatewayTotalCountMapper.selectByExample(totalExample);

        List<GatewayTotalCountDTO> list = gatewayTotalCountMapper.selectByCityExample(totalExample);
    }


    @Test
    public void testInsertAlarmDetailData() {
        ArrayList<GatewayAlarmDetail> list = new ArrayList<>();

        GatewayAlarmDetail detail = new GatewayAlarmDetail();


        int count = gatewayAlarmDetailMapper.insertBatch(list);

    }


}
