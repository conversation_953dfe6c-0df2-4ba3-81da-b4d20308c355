package com.cmiot.report.test.customer;

import com.alibaba.fastjson.JSON;
import com.cmiot.report.Application;
import com.cmiot.report.dto.customer.*;
import com.cmiot.report.service.NetworkAccessCountService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Classname TestCustomerMarketing
 * @Description
 * @Date 2022/8/30 9:13
 * @Created by lei
 */
@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestAccessNetwork {


    @Autowired
    private NetworkAccessCountService networkAccessCountService;

    @Test
    public void testAll() {
        NetworkUsageChartRequest request = new NetworkUsageChartRequest();
        request.setStartTime("2022-08-08");
        request.setEndTime("2022-09-06");
        request.setType("3");
        request.setTimeType("3");
        NetworkUsageChartData data = networkAccessCountService.getNetworkUsage(request);
        System.out.println(JSON.toJSONString(data));
    }

    @Test
    public void testAllTime() {
        NetworkUsageChartRequest request = new NetworkUsageChartRequest();
        request.setStartTime("2022-09-09");
        request.setEndTime("2022-09-15");
        request.setType("2");
        request.setTimeType("2");
        request.setSelectValue("");
        NetworkUsagePieData data = networkAccessCountService.getNetworkUsageDeviceStatus(request);
        System.out.println(JSON.toJSONString(data));
    }
}
