package com.cmiot.report.test;

import com.cmiot.report.Application;
import com.cmiot.report.bean.GatewayReboot;
import com.cmiot.report.bean.GatewayRebootDetail;
import com.cmiot.report.bean.GatewayRebootDetailExample;
import com.cmiot.report.bean.GatewayRebootExample;
import com.cmiot.report.facade.dto.plugin.PluginInstallAnalysisParam;
import com.cmiot.report.facade.dto.plugin.PluginInstallAnalysisRes;
import com.cmiot.report.mapper.GatewayRebootDetailMapper;
import com.cmiot.report.mapper.GatewayRebootMapper;
import com.cmiot.report.service.PluginReportService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class GatewayRebootMapperTest {
    @Autowired
    private GatewayRebootMapper gatewayRebootMapper;
    @Autowired
    private GatewayRebootDetailMapper gatewayRebootDetailMapper;
    @Autowired
    private PluginReportService pluginReportService;

    @Test
    public void list(){
        GatewayRebootDetailExample example = new GatewayRebootDetailExample();
        List<GatewayRebootDetail> list = gatewayRebootDetailMapper.selectByExample(example);
        log.info("list={}",list);
    }


    @Test
    public void getInstallationAnalysis(){
        PluginInstallAnalysisParam param = new PluginInstallAnalysisParam();
        //param.set
        LocalDate localDate = LocalDate.now();
        PluginInstallAnalysisRes res = pluginReportService.getInstallationAnalysis(param,localDate);
        log.info("res={}",res);
    }
}
