package com.cmiot.report.test.testcode;

import java.util.ArrayList;
import java.util.List;

public class CodeTestSolvedOrderExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CodeTestSolvedOrderExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(String value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(String value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(String value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(String value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(String value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(String value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLike(String value) {
            addCriterion("customer_id like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotLike(String value) {
            addCriterion("customer_id not like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<String> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<String> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(String value1, String value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(String value1, String value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersIsNull() {
            addCriterion("install_orders is null");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersIsNotNull() {
            addCriterion("install_orders is not null");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersEqualTo(Integer value) {
            addCriterion("install_orders =", value, "installOrders");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersNotEqualTo(Integer value) {
            addCriterion("install_orders <>", value, "installOrders");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersGreaterThan(Integer value) {
            addCriterion("install_orders >", value, "installOrders");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersGreaterThanOrEqualTo(Integer value) {
            addCriterion("install_orders >=", value, "installOrders");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersLessThan(Integer value) {
            addCriterion("install_orders <", value, "installOrders");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersLessThanOrEqualTo(Integer value) {
            addCriterion("install_orders <=", value, "installOrders");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersIn(List<Integer> values) {
            addCriterion("install_orders in", values, "installOrders");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersNotIn(List<Integer> values) {
            addCriterion("install_orders not in", values, "installOrders");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersBetween(Integer value1, Integer value2) {
            addCriterion("install_orders between", value1, value2, "installOrders");
            return (Criteria) this;
        }

        public Criteria andInstallOrdersNotBetween(Integer value1, Integer value2) {
            addCriterion("install_orders not between", value1, value2, "installOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersIsNull() {
            addCriterion("maintenance_solved_orders is null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersIsNotNull() {
            addCriterion("maintenance_solved_orders is not null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersEqualTo(Integer value) {
            addCriterion("maintenance_solved_orders =", value, "maintenanceSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersNotEqualTo(Integer value) {
            addCriterion("maintenance_solved_orders <>", value, "maintenanceSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersGreaterThan(Integer value) {
            addCriterion("maintenance_solved_orders >", value, "maintenanceSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersGreaterThanOrEqualTo(Integer value) {
            addCriterion("maintenance_solved_orders >=", value, "maintenanceSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersLessThan(Integer value) {
            addCriterion("maintenance_solved_orders <", value, "maintenanceSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersLessThanOrEqualTo(Integer value) {
            addCriterion("maintenance_solved_orders <=", value, "maintenanceSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersIn(List<Integer> values) {
            addCriterion("maintenance_solved_orders in", values, "maintenanceSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersNotIn(List<Integer> values) {
            addCriterion("maintenance_solved_orders not in", values, "maintenanceSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_solved_orders between", value1, value2, "maintenanceSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedOrdersNotBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_solved_orders not between", value1, value2, "maintenanceSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersIsNull() {
            addCriterion("maintenance_solved_ext_orders is null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersIsNotNull() {
            addCriterion("maintenance_solved_ext_orders is not null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersEqualTo(Integer value) {
            addCriterion("maintenance_solved_ext_orders =", value, "maintenanceSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersNotEqualTo(Integer value) {
            addCriterion("maintenance_solved_ext_orders <>", value, "maintenanceSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersGreaterThan(Integer value) {
            addCriterion("maintenance_solved_ext_orders >", value, "maintenanceSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersGreaterThanOrEqualTo(Integer value) {
            addCriterion("maintenance_solved_ext_orders >=", value, "maintenanceSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersLessThan(Integer value) {
            addCriterion("maintenance_solved_ext_orders <", value, "maintenanceSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersLessThanOrEqualTo(Integer value) {
            addCriterion("maintenance_solved_ext_orders <=", value, "maintenanceSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersIn(List<Integer> values) {
            addCriterion("maintenance_solved_ext_orders in", values, "maintenanceSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersNotIn(List<Integer> values) {
            addCriterion("maintenance_solved_ext_orders not in", values, "maintenanceSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_solved_ext_orders between", value1, value2, "maintenanceSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSolvedExtOrdersNotBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_solved_ext_orders not between", value1, value2, "maintenanceSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersIsNull() {
            addCriterion("complaints_solved_orders is null");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersIsNotNull() {
            addCriterion("complaints_solved_orders is not null");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersEqualTo(Integer value) {
            addCriterion("complaints_solved_orders =", value, "complaintsSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersNotEqualTo(Integer value) {
            addCriterion("complaints_solved_orders <>", value, "complaintsSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersGreaterThan(Integer value) {
            addCriterion("complaints_solved_orders >", value, "complaintsSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersGreaterThanOrEqualTo(Integer value) {
            addCriterion("complaints_solved_orders >=", value, "complaintsSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersLessThan(Integer value) {
            addCriterion("complaints_solved_orders <", value, "complaintsSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersLessThanOrEqualTo(Integer value) {
            addCriterion("complaints_solved_orders <=", value, "complaintsSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersIn(List<Integer> values) {
            addCriterion("complaints_solved_orders in", values, "complaintsSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersNotIn(List<Integer> values) {
            addCriterion("complaints_solved_orders not in", values, "complaintsSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersBetween(Integer value1, Integer value2) {
            addCriterion("complaints_solved_orders between", value1, value2, "complaintsSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedOrdersNotBetween(Integer value1, Integer value2) {
            addCriterion("complaints_solved_orders not between", value1, value2, "complaintsSolvedOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersIsNull() {
            addCriterion("complaints_solved_ext_orders is null");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersIsNotNull() {
            addCriterion("complaints_solved_ext_orders is not null");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersEqualTo(Integer value) {
            addCriterion("complaints_solved_ext_orders =", value, "complaintsSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersNotEqualTo(Integer value) {
            addCriterion("complaints_solved_ext_orders <>", value, "complaintsSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersGreaterThan(Integer value) {
            addCriterion("complaints_solved_ext_orders >", value, "complaintsSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersGreaterThanOrEqualTo(Integer value) {
            addCriterion("complaints_solved_ext_orders >=", value, "complaintsSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersLessThan(Integer value) {
            addCriterion("complaints_solved_ext_orders <", value, "complaintsSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersLessThanOrEqualTo(Integer value) {
            addCriterion("complaints_solved_ext_orders <=", value, "complaintsSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersIn(List<Integer> values) {
            addCriterion("complaints_solved_ext_orders in", values, "complaintsSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersNotIn(List<Integer> values) {
            addCriterion("complaints_solved_ext_orders not in", values, "complaintsSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersBetween(Integer value1, Integer value2) {
            addCriterion("complaints_solved_ext_orders between", value1, value2, "complaintsSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andComplaintsSolvedExtOrdersNotBetween(Integer value1, Integer value2) {
            addCriterion("complaints_solved_ext_orders not between", value1, value2, "complaintsSolvedExtOrders");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersIsNull() {
            addCriterion("service_orders is null");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersIsNotNull() {
            addCriterion("service_orders is not null");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersEqualTo(Integer value) {
            addCriterion("service_orders =", value, "serviceOrders");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersNotEqualTo(Integer value) {
            addCriterion("service_orders <>", value, "serviceOrders");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersGreaterThan(Integer value) {
            addCriterion("service_orders >", value, "serviceOrders");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_orders >=", value, "serviceOrders");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersLessThan(Integer value) {
            addCriterion("service_orders <", value, "serviceOrders");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersLessThanOrEqualTo(Integer value) {
            addCriterion("service_orders <=", value, "serviceOrders");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersIn(List<Integer> values) {
            addCriterion("service_orders in", values, "serviceOrders");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersNotIn(List<Integer> values) {
            addCriterion("service_orders not in", values, "serviceOrders");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersBetween(Integer value1, Integer value2) {
            addCriterion("service_orders between", value1, value2, "serviceOrders");
            return (Criteria) this;
        }

        public Criteria andServiceOrdersNotBetween(Integer value1, Integer value2) {
            addCriterion("service_orders not between", value1, value2, "serviceOrders");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersIsNull() {
            addCriterion("total_orders is null");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersIsNotNull() {
            addCriterion("total_orders is not null");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersEqualTo(Integer value) {
            addCriterion("total_orders =", value, "totalOrders");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersNotEqualTo(Integer value) {
            addCriterion("total_orders <>", value, "totalOrders");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersGreaterThan(Integer value) {
            addCriterion("total_orders >", value, "totalOrders");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_orders >=", value, "totalOrders");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersLessThan(Integer value) {
            addCriterion("total_orders <", value, "totalOrders");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersLessThanOrEqualTo(Integer value) {
            addCriterion("total_orders <=", value, "totalOrders");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersIn(List<Integer> values) {
            addCriterion("total_orders in", values, "totalOrders");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersNotIn(List<Integer> values) {
            addCriterion("total_orders not in", values, "totalOrders");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersBetween(Integer value1, Integer value2) {
            addCriterion("total_orders between", value1, value2, "totalOrders");
            return (Criteria) this;
        }

        public Criteria andTotalOrdersNotBetween(Integer value1, Integer value2) {
            addCriterion("total_orders not between", value1, value2, "totalOrders");
            return (Criteria) this;
        }

        public Criteria andGdateIsNull() {
            addCriterion("gdate is null");
            return (Criteria) this;
        }

        public Criteria andGdateIsNotNull() {
            addCriterion("gdate is not null");
            return (Criteria) this;
        }

        public Criteria andGdateEqualTo(Integer value) {
            addCriterion("gdate =", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotEqualTo(Integer value) {
            addCriterion("gdate <>", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThan(Integer value) {
            addCriterion("gdate >", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateGreaterThanOrEqualTo(Integer value) {
            addCriterion("gdate >=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThan(Integer value) {
            addCriterion("gdate <", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateLessThanOrEqualTo(Integer value) {
            addCriterion("gdate <=", value, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateIn(List<Integer> values) {
            addCriterion("gdate in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotIn(List<Integer> values) {
            addCriterion("gdate not in", values, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateBetween(Integer value1, Integer value2) {
            addCriterion("gdate between", value1, value2, "gdate");
            return (Criteria) this;
        }

        public Criteria andGdateNotBetween(Integer value1, Integer value2) {
            addCriterion("gdate not between", value1, value2, "gdate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}