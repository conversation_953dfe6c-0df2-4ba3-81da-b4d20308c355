package com.cmiot.report.test;

import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.report.Application;
import com.cmiot.report.dto.GatewayOverviewResult;
import com.cmiot.report.facade.dto.gateway.GatewaySubDeviceInfoDto;
import com.cmiot.report.service.GatewayCountService;
import com.cmiot.report.service.GatewayService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Classname TestGatewayCountService
 * @Description
 * @Date 2022/12/27 15:43
 * @Created by lei
 */
@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestGatewayCountService {

    @Autowired
    private GatewayCountService gatewayCountService;

    @Autowired
    private GatewayService gatewayService;

    @Test
    public void testGatewayOverview() {
        String p = "110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000";
        List<String> provinceCode = Arrays.asList(p.split(","));
        GatewayOverviewResult gatewayOverviewResult = gatewayCountService.getOverviewCount(provinceCode);
        System.out.println(gatewayOverviewResult);
    }

/*
    @Test
    public void testGetSubDevNum() {
        List<String> gatewaySns = new ArrayList<>();
        gatewaySns.add("CIOT22B00030");
        gatewaySns.add("CIOTTEST0001");
        List<GatewaySubDeviceInfoDto> gatewaySubDeviceInfoDtos = this.gatewayService.getGatewaySubDeviceOnlineNum(gatewaySns);
        System.out.println(JSONUtil.toJSONString(gatewaySubDeviceInfoDtos));
    }
*/

}
