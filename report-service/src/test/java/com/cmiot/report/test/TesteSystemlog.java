package com.cmiot.report.test;

import com.alibaba.fastjson.JSON;
import com.cmiot.report.Application;
import com.cmiot.report.controller.NetworkAccessCountController;
import com.cmiot.report.controller.SystemLogController;
import com.cmiot.report.dto.customer.*;
import com.cmiot.report.dto.syslog.SysLogDetailQueryRequest;
import com.cmiot.report.dto.syslog.SysLogDetailQueryResult;
import com.cmiot.report.dto.syslog.SysLogQueryRequest;
import com.cmiot.report.dto.syslog.SysLogQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Classname TesteSystemlog
 * @Description
 * @Date 2022/8/16 10:45
 * @Created by lei
 */
@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TesteSystemlog {

    @Autowired
    private NetworkAccessCountController networkAccessCountController;

    @Autowired
    private SystemLogController systemLogController;

    @Test
    void testLiebiao() {
        SysLogQueryRequest sysLogQueryRequest = new SysLogQueryRequest();
        //sysLogQueryRequest.setStartTime(DateUtil.getBeforeDayDate(60));
       // sysLogQueryRequest.setEndTime(new Date());
       // sysLogQueryRequest.setRoleId(1L);
        sysLogQueryRequest.setPage(1L);
        sysLogQueryRequest.setPageSize(10L);
        SysLogQueryResult sysLogQueryResult =
                this.systemLogController.querySystemLogPage(sysLogQueryRequest);
        System.out.println(JSON.toJSON(sysLogQueryResult));
    }

    @Test
    void testDetail() {
        SysLogDetailQueryRequest sysLogQueryRequest = new SysLogDetailQueryRequest();
        sysLogQueryRequest.setPage(1L);
        sysLogQueryRequest.setPageSize(10L);
        SysLogDetailQueryResult sysLogQueryResult =
                this.systemLogController.querySystemLogDetailPage("9999100001", sysLogQueryRequest);
        System.out.println(JSON.toJSON(sysLogQueryResult));
    }

    @Test
    public void testNetUsage() {
        NetworkUsageChartRequest request = new NetworkUsageChartRequest();
        request.setStartTime("2022-01-01");
        request.setEndTime("2022-09-01");
        request.setType("3");
        NetworkUsageChartData data = this.networkAccessCountController.getNetworkUsage("", request);
        System.out.println(JSON.toJSON(data));
    }

    @Test
    public void testNetUsageDevice() {
        NetworkUsageChartRequest request = new NetworkUsageChartRequest();
        request.setStartTime("2022-01-01");
        request.setEndTime("2022-09-01");
        request.setType("1");
        request.setSelectValue("北京市");
        NetworkUsagePieData data = this.networkAccessCountController.getNetworkUsageDeviceStatus("", request);
        System.out.println(JSON.toJSON(data));
    }

    @Test
    public void testMediaPerfer() {
        NetworkPerferRequest request = new NetworkPerferRequest();
        request.setStartTime("2022-01-01");
        request.setEndTime("2022-09-01");
        NetworkPerferChartData data = this.networkAccessCountController.getProgramPreference("", request);
        System.out.println(JSON.toJSON(data));
    }

    @Test
    public void testNetworkPerfer() {
        NetworkPerferRequest request = new NetworkPerferRequest();
        request.setStartTime("2022-01-01");
        request.setEndTime("2022-09-01");
        NetworkPerferChartData data = this.networkAccessCountController.getSitePreference("", request);
        System.out.println(JSON.toJSON(data));
    }
}
