package com.cmiot.report.test.customer;

import com.alibaba.fastjson.JSON;
import com.cmiot.report.Application;
import com.cmiot.report.dto.ExportDataResult;
import com.cmiot.report.dto.customer.*;
import com.cmiot.report.service.CustomerMarketingService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Classname TestCustomerMarketing
 * @Description
 * @Date 2022/8/30 9:13
 * @Created by lei
 */
@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestCustomerMarketing {


    @Autowired
    private CustomerMarketingService customerMarketingService;

    @Test
    public void testAll() {
        TargetCustomerDistributionRequest request = new TargetCustomerDistributionRequest();
        request.setTime("2022-08-20");
        request.setProvince("330000");
       // request.setProvince("330000,510000,440000");
       // request.setCity("331000,330300,330900,330100");
        CustomerMarketingDistributionData data = this.customerMarketingService.targetCustomerDistribution(request);
        System.out.println(JSON.toJSONString(data));
    }


    @Test
    public void testDayChange() {
        ChangesInTargetUsersRequest request = new ChangesInTargetUsersRequest();
        request.setStartTime("2022-08-01");
        request.setEndTime("2022-08-31");
        request.setProvince("330000");
        //request.setProvince("330000,510000");
        request.setCity("331000,330300,330900,330100");
        ChangesInTargetUsersData data = this.customerMarketingService.changesInTargetUsers(request);
        System.out.println(JSON.toJSONString(data));
    }


    @Test
    public void testList() {
        MarketingCustomerListRequest request = new MarketingCustomerListRequest();
        request.setUid(1L);
        request.setPage(1);
        request.setPageSize(10);

        request.setProvince("330000");

        request.setProvince("330000,510000,440000");
        //request.setCity("331000,330300,330900,330100");


        //request.setEnterpriseValueCategory("6");
       // request.setCustomerStatus("10");
        //request.setBusiness("客户名称4");
        request.setGroupCustomer("1");
        request.setMarketingExponentStart("60");
        request.setMarketingExponentEnd("66");
        MarketingCustomerListData data = this.customerMarketingService.precisionMarketingCustomerList(request);
        System.out.println(JSON.toJSONString(data));
    }

    @Test
    public void testExport() throws InterruptedException {
        MarketingCustomerListRequest request = new MarketingCustomerListRequest();
        request.setUid(1L);

       // request.setProvince("330000");

        //request.setProvince("330000,510000,440000");
        //request.setCity("331000,330300,330900,330100");


        //request.setEnterpriseValueCategory("6");
        // request.setCustomerStatus("10");
        //request.setBusiness("客户名称4");
        //request.setGroupCustomer("1");
        //request.setMarketingExponentStart("60");
        //request.setMarketingExponentEnd("66");
        ExportDataResult data = this.customerMarketingService.precisionMarketingCustomerListExport(request);
        System.out.println(JSON.toJSONString(data));
        Thread.sleep(60 *1000);
    }
}
