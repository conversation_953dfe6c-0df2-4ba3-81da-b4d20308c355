package com.cmiot.report.test;


import com.alibaba.fastjson.JSON;
import com.cmiot.report.Application;
import com.cmiot.report.dto.*;
import com.cmiot.report.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestMiniProgramReport {
    // 小程序报告


    @Autowired
    GatewayService gatewayService;

    @Autowired
    DeviceService deviceService;

    @Autowired
    GatewayCountService gatewayCountService;

    @Autowired
    CustomerAllViewService customerAllViewService;

    @Autowired
    NetworkDeviceService networkDeviceService;

    // 运行报告
    @Test
    public void report() {
        // 入参
        GatewayQuery gatewayQuery = new GatewayQuery();
        String eid = "1";
        gatewayQuery.setEid(eid);
        gatewayQuery.setStartTime("2022-09-06");
        gatewayQuery.setEndTime("2022-09-06");
        gatewayQuery.setOverTime(70);


        // ------------ //

        Map result = new HashMap();

        try {
            // 0.企业名称
            String customerName = customerAllViewService.getCustomerName(Long.parseLong(eid));
            result.put("customerName", StringUtils.isEmpty(customerName) ? "-" : customerName);

            GatewayRunTimeResult gatewayRunTimeData = gatewayService.getGatewayRunTimeData(gatewayQuery);
            log.info("gatewayRunTimeData:{}", JSON.toJSONString(gatewayRunTimeData));

            // 1.正在运行中的网关数量
            result.put("gatewayNumberRunning", gatewayRunTimeData.getGatewayNumberRunning());

            // 2.平均运行时长,单位小时
            result.put("runDurationAverage", gatewayRunTimeData.getRunDurationAverage());

            // 3.这些网关总的连续运行时长,单位小时
            // 网关超时阈值
            result.put("runDuration", gatewayRunTimeData.getRunDuration());

            // 4.告警网关数
            int alarmGatewayCount = gatewayCountService.getAlarmGatewayCount(gatewayQuery);
            result.put("alarmGatewayCount", alarmGatewayCount);

            // 5.这些组网设备的连续运行时长,单位小时
            // 如每10分钟上报一次,存在一条数据,则在线了10分钟
            // 组网设备超时阈值
            result.put("netRunDuration", gatewayRunTimeData.getNetRunDuration());

            // 时间超长的网关数
            long macOverCount = gatewayRunTimeData.getMacOverCount();
            result.put("gatewayCount", macOverCount);

            // 时间超长的组网设备数
            long subOverCount = gatewayRunTimeData.getSubOverCount();
            result.put("networkDeviceCount", subOverCount);

            // 获取用户周期内通过Wifi连接的下挂设备统计信息
            //Integer subDeviceNumber = deviceService.queryDeviceAppStatRunCount(gatewayQuery);
            //result.put("subDeviceNumber", subDeviceNumber == null ? 0 : subDeviceNumber);

            // 获取用户下挂设备信息
            DeviceAppStatRunVo subDeviceVo = deviceService.queryDeviceAppStatRunSpeed(gatewayQuery);
            log.info("subDeviceVo:{}", JSON.toJSONString(subDeviceVo));

            // 1.周期内通过Wifi连接的下挂设备数量
            int subDeviceNumber = subDeviceVo.getSubDeviceCount();
            result.put("subDeviceNumber", subDeviceNumber);

            // 2.下挂设备平均网速(单位mb/s,保留两位小数)
            double subDeviceRateAverage = subDeviceVo.getSubDeviceRateAverage();
            result.put("subDeviceRateAverage", subDeviceRateAverage);

            // 3.平均连接比例(保留两位小数)
            double subDeviceConnectRateAvg = subDeviceVo.getSubDeviceConnectRateAvg();
            result.put("subDeviceConnectRateAvg", subDeviceConnectRateAvg);

            // 4.下挂设备使用总流量(上行+下行,单位GB,保留两位小数)
            double subDeviceTraffic = subDeviceVo.getSubDeviceTraffic();
            result.put("subDeviceTraffic", subDeviceTraffic);

            // TODO 访问网站统计 从下挂设备打标网站统计
            // 用户使用Wifi平均时长如果小于访问网站时长,则访问网站时长等于使用Wifi平均时长
            // t_subdevice_app_report_all 下挂设备表中来源于网关周期上报表,每15分钟上报一次,存在一条数据,则在线了15分钟

            // 1.今日/本周/本月/今年 wifi的平均时长
            double wifiConnectTime = subDeviceVo.getSubDeviceWifiConnectTime();
            result.put("wifiConnectTime", wifiConnectTime);

            // 2.当前正在连接wifi上网的用户数
            int wifiUserCount = subDeviceVo.getWifiUserCount();
            result.put("wifiUserCount", wifiUserCount);

            // 3.最常访问的网站
            String favoriteWebsite = subDeviceVo.getFavoriteWebsite();
            result.put("favoriteWebsite", StringUtils.isEmpty(favoriteWebsite) ? "-" : favoriteWebsite);

            // 4.平均访问时长(小时)
            double visitWebTime = subDeviceVo.getSubDeviceWifiVisitWebTime();
            result.put("visitWebTime", visitWebTime);

            // 5.平均访问次数
            double subDeviceVisitWebCount = subDeviceVo.getSubDeviceVisitWebCount();
            result.put("subDeviceVisitWebCount", subDeviceVisitWebCount);

            log.info("result:{}", JSON.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 超时网关列表
    @Test
    public void gatewayList() {
        // 入参
        GatewayQuery gatewayQuery = new GatewayQuery();
        String eid = "1";
        gatewayQuery.setEid(eid);
        gatewayQuery.setStartTime("2022-05-06");
        gatewayQuery.setEndTime("2022-09-06");
        List<GatewayRunTimeDetail> gatewayRunTimeDetails = gatewayService.getGatewayRunTimeList(gatewayQuery);
    }

    // 超时组网设备列表
    @Test
    public void netDeviceList() {
        NetworkDeviceRequset request = new NetworkDeviceRequset();
        request.setStartTime("2022-09-06");
        request.setEndTime("2022-09-06");
        request.setEid(String.valueOf(1));

        networkDeviceService.networkDeviceList(request);
    }

}
