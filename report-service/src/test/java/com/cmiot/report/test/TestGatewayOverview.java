package com.cmiot.report.test;

import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.report.Application;
import com.cmiot.report.dto.device.DeviceAppStatCount;
import com.cmiot.report.facade.dto.enterprise.WeekReportOverviewResult;
import com.cmiot.report.facade.dto.enterprise.WeekReportSubDeviceResult;
import com.cmiot.report.facade.dto.enterprise.WeekReportVisitsResult;
import com.cmiot.report.service.DeviceService;
import com.cmiot.report.service.EnterpriseWeekReportService;
import com.cmiot.report.service.GatewayCountService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Classname TestGatewayOverview
 * @Description
 * @Date 2022/12/27 9:13
 * @Created by lei
 */
@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestGatewayOverview {

    private GatewayCountService gatewayCountService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private EnterpriseWeekReportService enterpriseWeekReportService;

    @Test
    public void test() {
        DeviceAppStatCount deviceAppStatCount = this.deviceService.queryDeviceAppStatCount(157175087961739339L);
        System.out.println(JSONUtil.toJSONString(deviceAppStatCount));
    }

    @Test
    public void test1() {
        WeekReportOverviewResult weekReportOverviewResult = enterpriseWeekReportService.getWeekReportOverview(1L);
        System.out.println(JSONUtil.toJSONString(weekReportOverviewResult));
    }


    @Test
    public void test2() {
        WeekReportVisitsResult weekReportVisitsResult = enterpriseWeekReportService.getWeekReportVisits(1L);
        System.out.println(JSONUtil.toJSONString(weekReportVisitsResult));
    }

    @Test
    public void test3() {
        WeekReportSubDeviceResult weekReportSubDeviceResult = enterpriseWeekReportService.getWeekReportSubDevice(1L);
        System.out.println(JSONUtil.toJSONString(weekReportSubDeviceResult));
    }
}
