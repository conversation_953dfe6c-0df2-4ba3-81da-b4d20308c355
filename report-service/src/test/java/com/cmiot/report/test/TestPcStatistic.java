package com.cmiot.report.test;

import com.cmiot.fgeo.common.dto.PageInfoDto;
import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.report.Application;
import com.cmiot.report.dto.enterprise.DeviceCpuRamDataResult;
import com.cmiot.report.dto.enterprise.DeviceFlowDataResult;
import com.cmiot.report.facade.dto.enterprise.*;
import com.cmiot.report.facade.enterprise.DeviceGraphDataResult;
import com.cmiot.report.facade.enterprise.DeviceOverviewResult;
import com.cmiot.report.facade.enterprise.SubDeivceGraphDataResult;
import com.cmiot.report.service.EnterpriseComprehensiveService;
import com.cmiot.report.service.EnterpriseDeviceStatisticService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Classname TestPcStatistic
 * @Description
 * @Date 2023/8/15 15:12
 * @Created by lei
 */
@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestPcStatistic {
    private final static Logger logger = LoggerFactory.getLogger(TestPcStatistic.class);

    @Autowired
    private EnterpriseDeviceStatisticService enterpriseDeviceStatisticService;



    @Autowired
    private EnterpriseComprehensiveService enterpriseComprehensiveService;

    @Test
    public void getDeviceOverview() {
        Long eid = 332485204863459354L;
        logger.info("getDeviceOverview, eid: {}", eid);
        DeviceOverviewResult result = this.enterpriseDeviceStatisticService.getDeviceOverview(eid);
        logger.info("getDeviceOverview, result: {}", JSONUtil.toJSONString(result));
    }


    @Test
    public void getDeviceGraphData() {
        Long eid = 332485204863459354L;
        logger.info("getDeviceGraphData, eid: {}", eid);
        List<DeviceGraphDataResult> result = this.enterpriseDeviceStatisticService.getDeviceGraphData(eid);
        logger.info("getDeviceGraphData, result: {}", JSONUtil.toJSONString(result));
    }

    @Test
    public void getSubDeivceGraphData() {
        Long eid = 332485204863459354L;
        Integer dateType = 2;
        logger.info("getSubDeivceGraphData, eid: {}, dateType: {}", eid, dateType);
        SubDeivceGraphDataResult result = this.enterpriseDeviceStatisticService.getStaDeivceGraphData(eid, dateType);
        logger.info("getSubDeivceGraphData, result: {}", JSONUtil.toJSONString(result));
    }

    @Test
    public void deviceFlow() {
        Long eid = 332485204863459354L;
        String startTime = "2023-08-10";
        String endTime = "2023-08-15";
        logger.info("deviceFlow, eid: {}, startTime: {}, endTime: {}", eid, startTime, endTime);
        DeviceFlowDataResult result = this.enterpriseDeviceStatisticService.deviceFlow(eid, startTime, endTime);
        logger.info("deviceFlow, result: {}", JSONUtil.toJSONString(result));
    }

    @Test
    public void getCpuRam() {
        Long eid = 332485204863459354L;
        String startTime = "2023-08-10";
        String endTime = "2023-08-14";
        logger.info("getCpuRam, eid: {}, startTime: {}, endTime: {}", eid, startTime, endTime);
        DeviceCpuRamDataResult result = this.enterpriseDeviceStatisticService.getCpuRam(eid, startTime, endTime);
        logger.info("getCpuRam, result: {}", JSONUtil.toJSONString(result));
    }


    @Test
    public void getComprehensive() {
        Long eid = 1L;
        logger.info("getComprehensive, eid: {}", eid);
        EnterpriseComprehensiveResult result = this.enterpriseComprehensiveService.getComprehensive(eid);
        logger.info("getComprehensive, result: {}", JSONUtil.toJSONString(result));
    }

    @Test
    public void getComprehensiveGateway() {
        testQueryType("idle");
        testQueryType("highLoading");
        testQueryType("lowLoading");
/*

        testQueryType("base");

        testQueryType("idle");
        testQueryType("overLife");
        testQueryType("bandwidthNonsupport");
        testQueryType("netBadPing");
        testQueryType("cpuRam");
        testQueryType("netBadSpeed");
        */
    }


    public void testQueryType(String qType) {
        Long eid = 1L;
        logger.info("getComprehensiveGateway, eid: {}", eid);
        GetComprehensiveGatewayParam param = new GetComprehensiveGatewayParam();
        param.setEid(eid);
        param.setPage(1);
        param.setPageSize(10);
        param.setQueryType(qType);
        PageInfoDto<ComprehensiveGatewayResult> result =
                this.enterpriseComprehensiveService.getComprehensiveGateway(param);
        logger.info("getComprehensiveGateway, result: {}", JSONUtil.toJSONString(result));
    }


    @Test
    public void getComprehensivePackage() {
        Long eid = 1L;
        logger.info("getComprehensivePackage, eid: {}", eid);
        GetComprehensivePackageParam param = new GetComprehensivePackageParam();
        param.setEid(eid);
        param.setPage(1);
        param.setPageSize(10);
        PageInfoDto<ComprehensivePackageResult> result = this.enterpriseComprehensiveService.getComprehensivePackage(param);
        logger.info("getComprehensivePackage, result: {}", JSONUtil.toJSONString(result));
    }


    /*            case "highLoading" : {
            }
            case "lowLoading" : {
            }
            case "idle" : {
            }
            case "overLife" : {
            }
            case "bandwidthNonsupport" : {
            }
            case "netBadPing" : {
            }
            case "netBadSpeed" : {
            }
            case "cpuRam" : {
            }*/
}
