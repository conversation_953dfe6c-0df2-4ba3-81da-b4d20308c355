package com.cmiot.report.controller;

import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.report.Application;
import com.cmiot.report.bean.plugin.EkitRestartAlertAnalysisParam;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.facade.dto.plugin.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

import static org.junit.Assert.*;

@Slf4j
@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PluginReportControllerTest {

    @Autowired
    private PluginReportController pluginReportController;

    @Test
    public void getInstallationAnalysis() {
        PluginInstallAnalysisParam param = new PluginInstallAnalysisParam();
        param.setStatisticType(2);
        param.setPlugin("com.rex4.test");
        PluginInstallAnalysisRes res = pluginReportController.getInstallationAnalysis(param);
        log.info("res={}",res);
    }

    @Test
    public void ekitRestartAlertAnalysis() {
        EkitRestartAlertAnalysisParam param= new EkitRestartAlertAnalysisParam();
        param.setPage(1);
        param.setPageSize(10);
        PageRes<EkitRestartAlertAnalysisRes> res = pluginReportController.ekitRestartAlertAnalysis(param);
        log.info("res={}",res);
    }

    @Test
    public void ekitRestartNumberList() throws ParseException {
        EkitRestartNumberListParam param= new EkitRestartNumberListParam();
        param.setAlarmTime(new SimpleDateFormat("yyyy年MM月dd日").parse("2023年10月30日"));
        param.setSn("CIOT22B00030");
        List<String> res = pluginReportController.ekitRestartNumberList(param);
        log.info("res={}",res);
    }


    @Test
    public void pluginOverViewAll() throws ParseException {
        PageRes<PluginCompositeOverview> pageRes =
                this.pluginReportController.getPluginInfoOverview(1, 1, 1, 10);
        log.info("pageRes = {}", JSONUtil.toJSONString(pageRes));
    }

    @Test
    public void pluginOverViewDetail() throws ParseException {
        GetPluginDetailOverviewPar getPluginDetailOverviewPar = new GetPluginDetailOverviewPar();
        getPluginDetailOverviewPar.setId(10019);
        getPluginDetailOverviewPar.setStartTime("2024-01-01");
        getPluginDetailOverviewPar.setEndTime("2024-02-22");
        PluginDetailOverview pluginDetailOverview =
                this.pluginReportController.getPluginDetailOverview(getPluginDetailOverviewPar);
        log.info("pluginDetailOverview = {}", JSONUtil.toJSONString(pluginDetailOverview));
    }
}