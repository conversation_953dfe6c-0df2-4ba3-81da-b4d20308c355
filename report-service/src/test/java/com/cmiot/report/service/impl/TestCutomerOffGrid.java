package com.cmiot.report.service.impl;

import com.cmiot.fgeo.common.util.JSONUtil;
import com.cmiot.report.Application;
import com.cmiot.report.dto.GatewayAnomalyRecordQuery;
import com.cmiot.report.facade.dto.PageRes;
import com.cmiot.report.service.CustomerOffGridService;
import com.cmiot.report.util.DateUtil;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * @Classname TestCutomerOffGrid
 * @Description
 * @Date 2024/10/24 19:49
 * @Created by lei
 */
@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestCutomerOffGrid {
    @Autowired
    private CustomerOffGridService customerOffGridService;

    @Test
    public void test() {
        Long uid = 1L;
        GatewayAnomalyRecordQuery query = new GatewayAnomalyRecordQuery();
        query.setPage(1);
        query.setPageSize(10);
        log.info("查询失效用户接收到请求：{}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss:sss"));
        //query.setLastday(DateUtil.getYesterdayInt());
        query.setLastday(20241024);
        query.formatParamList();
        //query.setLastday(20240716);
        Page page = customerOffGridService.queryUneffect(query);
        log.info("查询失效用户查询完数据：{}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss:sss"));
        PageRes pageRes = new PageRes<>();
        pageRes.setList(page.getResult());
        pageRes.setTotal(page.getTotal());
        pageRes.setPage(page.getPageNum());
        pageRes.setPageSize(page.getPageSize());
        System.out.println(JSONUtil.toJSONString(pageRes));
    }


    @Test
    public void test2() {
        Long uid = 1L;
        GatewayAnomalyRecordQuery query = new GatewayAnomalyRecordQuery();
        log.info("查询长期离线用户接收到请求：{}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss:sss"));
        query.setLastday(20241020);
        query.formatParamList();
        //query.setLastday(20240512);
        Page page = customerOffGridService.queryNotOnline(query);
        log.info("查询长期离线用户查询完数据：{}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss:sss"));
        PageRes pageRes = new PageRes<>();
        pageRes.setList(page.getResult());
        pageRes.setTotal(page.getTotal());
        pageRes.setPage(page.getPageNum());
        pageRes.setPageSize(page.getPageSize());
        System.out.println(JSONUtil.toJSONString(pageRes));
    }
}
