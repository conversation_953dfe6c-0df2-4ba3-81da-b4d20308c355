package com;

import com.cmiot.report.Application;
import com.cmiot.report.dto.ServiceStatisticRequest;
import com.cmiot.report.service.ServiceStatisticService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class ServiceStatisticTest {

    @Autowired
    private ServiceStatisticService service;


    @Test
    public void getCustomerServiceStatistic() {
        ServiceStatisticRequest request = new ServiceStatisticRequest();

        //List<String> provList = Arrays.asList("500000");
        //List<String> cityList = Arrays.asList("500102", "500235", "500231");
        List<String> provList = new ArrayList<>();
        List<String> cityList = new ArrayList<>();
        request.setProvince(provList);
        request.setCity(cityList);
        request.setStartDate("2022-09-20");
        request.setEndDate("2022-09-20");

        //service.getCustomerServiceStatistic("", request);
    }
}
