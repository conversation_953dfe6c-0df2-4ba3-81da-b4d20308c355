package com;

import com.cmiot.report.Application;
import com.cmiot.report.dto.*;
import com.cmiot.report.service.CustomerOffGridService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CustomerOffGridTest {

    // 离网预警分析

    @Autowired
    private CustomerOffGridService service;

    // 1、获取离网因素权重和阈值
    @Test
    public void getOffGridFactorThreshold() {
        service.getOffGridFactorThreshold();
    }


    // 2、获取离网因素详情
    @Test
    public void getOffGridFactorThresholdDetail() {
        String factorKey = "1";
        service.getOffGridFactorThresholdDetail(factorKey);
    }


    // 3、设置综合离网指数阈值
    @Test
    public void setOffGridOverallFactorThreshold() {
        int threshold = 50;
        service.setOffGridOverallFactorThreshold(threshold);
    }


    // 4、获取综合离网指数信息
    @Test
    public void getOffGridOverallFactorThreshold() {
        service.getOffGridOverallFactorThreshold();
    }


    // 5、设置离网因素权重阈值
    @Test
    public void setOffGridFactorThreshold() {
        List<OffGridFactorThresholdSet> factors = new ArrayList<>();

        for (int i = 1; i <= 5; i++) {
            OffGridFactorThresholdSet thresholdSet = new OffGridFactorThresholdSet();
            thresholdSet.setFactorKey(i + "");
            thresholdSet.setWeight(Double.parseDouble("0." + i));
            thresholdSet.setThreshold(i * 10);
            factors.add(thresholdSet);
        }

        SetOffGridFactorThresholdRequest request=new SetOffGridFactorThresholdRequest();
        request.setFactors(factors);
        service.setOffGridFactorThreshold(request);
    }


    // 获取客户状态
    @Test
    public void getCustomerStatus() {
        service.getCustomerStatus();
    }


    // 获取企业价值分类
    @Test
    public void getEnterpriseValueCategory() {
        service.getEnterpriseValueCategory();
    }

    // 查询离网预计信息统计值
    @Test
    public void getOffGridCustStatistic() {
        OffGridCustStatisticRequest request = new OffGridCustStatisticRequest();
        List<String> provList = Arrays.asList("500000");
        List<String> cityList = Arrays.asList("500102", "500235", "500231");
        request.setProvince(provList);
        request.setCity(cityList);
        service.getOffGridCustStatistic(request);
    }


    // 查询离网预警信息列表
    @Test
    public void getOffGridCustList() {
        OffGridCustListRequest request = new OffGridCustListRequest();
        List<String> provList = Arrays.asList("500000");
        List<String> cityList = Arrays.asList("500102", "500235", "500231");

        //List<String> provList = Arrays.asList("500000", "120000");
        //List<String> cityList = new ArrayList<>();

        String customerName = "中移物联网";
        String customerId = "1001";
        String valueCategory = "A1";
        String customerStatus = "1";
        int overallStart = 30;
        int overallEnd = 50;
        int terminalQualityStart = 10;
        int terminalQualityEnd = 40;

        request.setProvince(provList);
        request.setCity(cityList);
        request.setBusiness(customerName);
        request.setGroupCustomer(customerId);
        request.setEnterpriseValueCategory(valueCategory);
        request.setCustomerStatus(customerStatus);
        request.setSyntheticalStart(overallStart);
        request.setSyntheticalEnd(overallEnd);
        request.setTerminalQualityStart(terminalQualityStart);
        request.setTerminalQualityEnd(terminalQualityEnd);
        //service.getOffGridCustList("", request);
    }


    // 导出离网预计信息列表
    @Test
    public void exportOffGridCustomer() {
        OffGridCustExportRequest request = new OffGridCustExportRequest();
        List<String> provList = Arrays.asList("500000");
        List<String> cityList = Arrays.asList("500102", "500235", "500231");

        //List<String> provList = Arrays.asList("500000", "120000");
        //List<String> cityList = new ArrayList<>();

        String customerName = "中移物联网";
        String customerId = "1001";
        String valueCategory = "A1";
        String customerStatus = "1";
        int overallStart = 30;
        int overallEnd = 50;
        int terminalQualityStart = 10;
        int terminalQualityEnd = 40;

        request.setProvince(provList);
        request.setCity(cityList);
        request.setBusiness(customerName);
        request.setGroupCustomer(customerId);
        request.setEnterpriseValueCategory(valueCategory);
        request.setCustomerStatus(customerStatus);
        request.setSyntheticalStart(overallStart);
        request.setSyntheticalEnd(overallEnd);
        request.setTerminalQualityStart(terminalQualityStart);
        request.setTerminalQualityEnd(terminalQualityEnd);
        request.setUid("***********");
        service.exportOffGridCustomer(request);
    }

}
