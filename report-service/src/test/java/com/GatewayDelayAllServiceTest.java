package com;


import com.cmiot.report.Application;
import com.cmiot.report.GatewayOldResult;
import com.cmiot.report.controller.GatewayOldCountController;
import com.cmiot.report.dto.AlarmThresholdRequest;
import com.cmiot.report.dto.GatewayDelayDeviceResult;
import com.cmiot.report.dto.GatewayQuery;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class GatewayDelayAllServiceTest {

    @Autowired
    private GatewayOldCountController gatewayOldCountController;


    @Test
    public void oldGatewayOverview() {
        GatewayQuery gatewayQuery = new GatewayQuery();
        String province = "510000";

        gatewayQuery.setStartTime("2025-06-01");
        gatewayQuery.setEndTime("2025-06-02");
        gatewayQuery.setProvince(province);
        //String province = "110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000";
        String city = "510100";
        gatewayQuery.setCity(city);
        String vendor = "7";
        gatewayQuery.setVendor(vendor);
        String model = "42";
        gatewayQuery.setModel(model);

        GatewayOldResult gatewayOldResult = gatewayOldCountController.oldGatewayOverview(gatewayQuery, province);

        System.out.println(gatewayOldResult.toString());
    }

    @Test
    public void rangeOldGatewayNumRatio() {
        GatewayQuery gatewayQuery = new GatewayQuery();
        String province = "510000";

        gatewayQuery.setStartTime("2025-06-01");
        gatewayQuery.setEndTime("2025-06-02");
        gatewayQuery.setGroupType(5);
        gatewayQuery.setProvince(province);
        //String province = "110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000";
        String city = "510100,510700";
        gatewayQuery.setCity(city);
//        String vendor = "7";
//        gatewayQuery.setVendor(vendor);
//        String model = "380";
//        gatewayQuery.setModel(model);

        Map<String, Object> map = gatewayOldCountController.rangeOldGatewayNumRatio(gatewayQuery, province);
        System.out.println("result" + map);
    }

    @Test
    public void oldGatewayDatalist() {
        GatewayQuery gatewayQuery = new GatewayQuery();
        String province = "500000";

        gatewayQuery.setStartTime("2025-06-01");
        gatewayQuery.setEndTime("2025-06-02");
        gatewayQuery.setProvince(province);
        gatewayQuery.setPage(1);
        gatewayQuery.setPageSize(3);
        //String province = "110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000";
//        String city = "510100,510700";
//        gatewayQuery.setCity(city);
//        String vendor = "7";
//        gatewayQuery.setVendor(vendor);
//        String model = "380";
//        gatewayQuery.setModel(model);

        Map<String, Object> map = gatewayOldCountController.oldGatewayDatalist(gatewayQuery, province);
        System.out.println("result" + map);
    }

    @Test
    public void queryDelayDevicesBySn() {
        GatewayQuery gatewayQuery = new GatewayQuery();
        String province = "510000";

        gatewayQuery.setStartTime("2025-06-01");
        gatewayQuery.setEndTime("2025-06-02");
        gatewayQuery.setProvince(province);
        gatewayQuery.setSn("cqdesn65");
        //String province = "110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000";
//        String city = "510100,510700";
//        gatewayQuery.setCity(city);
//        String vendor = "7";
//        gatewayQuery.setVendor(vendor);
//        String model = "380";
//        gatewayQuery.setModel(model);

        List<GatewayDelayDeviceResult> gatewayDelayDeviceResults = gatewayOldCountController.queryDelayDevicesBySn(gatewayQuery);
        System.out.println("result" + gatewayDelayDeviceResults);
    }




}
