package com;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.stream.Collectors;

import com.cmiot.report.dto.SubDeviceStatisticResult;
import com.cmiot.report.dto.SubDeviceStatisticResult.HangingDeviceRange;

public class TestStream {
	public static void main(String[] args) {
		
		List<SubDeviceStatisticResult.HangingDeviceRange> hangingDeviceRange = new ArrayList<>();
		hangingDeviceRange.add(new SubDeviceStatisticResult.HangingDeviceRange("重庆市", 30));
		hangingDeviceRange.add(new SubDeviceStatisticResult.HangingDeviceRange("重庆市", 24));
		hangingDeviceRange.add(new SubDeviceStatisticResult.HangingDeviceRange("江苏省", 24));
		hangingDeviceRange.add(new SubDeviceStatisticResult.HangingDeviceRange("江苏省", 24));
		hangingDeviceRange.add(new SubDeviceStatisticResult.HangingDeviceRange("江苏省", 24));
		hangingDeviceRange.add(new SubDeviceStatisticResult.HangingDeviceRange("安徽省", 88));
		
		List<HangingDeviceRange> collect = hangingDeviceRange.stream()
				.collect(Collectors.groupingBy(x -> x.getName(), Collectors.summingLong(x -> x.getNum()))).entrySet()
				.stream().map(x -> new SubDeviceStatisticResult.HangingDeviceRange(x.getKey(), x.getValue()))
				.sorted(Comparator.comparingLong(HangingDeviceRange::getNum).reversed())
				.collect(Collectors.toList());
		
		for(HangingDeviceRange data : collect) {
			System.err.println(data);
		}
		
		long count = hangingDeviceRange.stream().mapToLong(HangingDeviceRange::getNum).sum();
		System.err.println(count);
	}
	
	
	
	public static void test() {
		Long startTime = getBeginDayOfYesterday();
        Long endTime = getEndDayOfYesterDay();
        
        Date dateStart = new Date(startTime);
        Date dateEnd = new Date(endTime);
        
        System.err.println(dateStart);
        System.err.println(dateEnd);
	}
	
	public static Long getBeginDayOfYesterday() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return cal.getTimeInMillis();
    }
	
    public static Long getEndDayOfYesterDay() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return cal.getTimeInMillis();
    }
}
