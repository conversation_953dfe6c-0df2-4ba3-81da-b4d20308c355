package com;

import com.cmiot.e.apiservice.facade.vo.DeviceInfoVo;
import com.cmiot.report.Application;
import com.cmiot.report.bean.DeviceAllByCount;
import com.cmiot.report.bean.DeviceAllExample;
import com.cmiot.report.bean.DeviceCumulativeAllCount;
import com.cmiot.report.bean.DeviceCumulativeAllExample;
import com.cmiot.report.mapper.DeviceAllMapper;
import com.cmiot.report.mapper.DeviceCumulativeAllMapper;
import com.cmiot.report.util.DateUtil;
import com.cmiot.report.util.redis.RedisOperatorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestRedis {

    @Autowired
    private RedisOperatorService redisOperatorService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Test
    public void testDeviceCumulativeAll() {
        String key = "GATEWAY_ONLINE_UDP:802278B00030";
//        String value = this.redisOperatorService.get(key, String.class);
        // 设置 Key 的序列化方式为 StringRedisSerializer
        this.redisTemplate.setKeySerializer(new StringRedisSerializer());
//        this.redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Object.class));
        this.redisTemplate.setValueSerializer(new StringRedisSerializer());

        // 设置 Hash 的 key 和 value 序列化方式
//        this.redisTemplate.setHashKeySerializer(new StringRedisSerializer());
//        this.redisTemplate.setHashValueSerializer(new Jackson2JsonRedisSerializer<>(Object.class));
//        this.redisTemplate.setHashValueSerializer(new StringRedisSerializer());

        this.redisTemplate.afterPropertiesSet();
        Object o = redisTemplate.opsForValue().get(key);
        String value = (String) o;
        System.out.println(value);
    }


    @Test
    public void 测试redis操作() {
        // 设置 Hash 的 key 和 value 序列化方式
        this.redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        this.redisTemplate.setHashValueSerializer(new StringRedisSerializer());
        this.redisTemplate.afterPropertiesSet();

//        String date = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now());
//        String key = "ekit-portal:gatewayOnlineTime:" + date + ":" + "802278B00030";
//        Long size = redisTemplate.opsForHash().size(key);
//        System.out.println(size);

        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        // 获取本周一的日期
        LocalDate thisMonday = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 计算上周一的日期
        LocalDate lastMonday = thisMonday.minusWeeks(1);
        for (int i = 1; i <= 7; i++) {
            String yyyyMMdd = formatter.format(lastMonday);
            LocalDate finalLastMonday = lastMonday;
            List<Object> objects = redisTemplate.executePipelined((RedisCallback<List<byte[]>>) connection -> {
                String hkey = "ekit-portal:gatewayOnlineTimeStatistics:" + yyyyMMdd;
                //从list生成mac集合
                List<String> macs = new ArrayList<>();
                macs.add("802278B00030");
                macs.add("802278B00031");
//                String joinFields = String.join(" ", macs);
                byte[][] macBytesArray = new byte[macs.size()][];
                for (int n = 0; n < macs.size(); n++) {
                    macBytesArray[n] = macs.get(n).getBytes(StandardCharsets.UTF_8);
                }
                List<byte[]> bytes = connection.hMGet(hkey.getBytes(StandardCharsets.UTF_8), macBytesArray);

//                for (byte[] aByte : bytes) {
//                    System.out.println(new String(aByte));
//                }
                return bytes;
            });
            // 打印或处理返回的结果
            objects.forEach(obj -> {
                if (obj instanceof byte[]) {
                    String value = new String((byte[]) obj, StandardCharsets.UTF_8);
                    log.info("{}-executePipelined1==>{}", yyyyMMdd, value);
//                    System.out.println(value);
                } else {
//                    log.info("{}-executePipelined2==>{}", yyyyMMdd, obj);
                    List<Integer> list = (ArrayList<Integer>) obj;
                    int sum = list.stream()
                            .filter(num -> num != null)
                            .mapToInt(Integer::intValue)
                            .sum();
                    System.out.println(sum);
                }
            });
            lastMonday = lastMonday.plusDays(1);
        }
    }

}
