package com;

import com.cmiot.report.Application;
import com.cmiot.report.dto.EnterCustDistOverView;
import com.cmiot.report.dto.EnterCustomerRequest;
import com.cmiot.report.dto.NewCustomerOverView;
import com.cmiot.report.service.CustomerAllViewService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@TestPropertySource({"classpath:application.yml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CustomerAllViewTest {

    // 企业全景视图

    @Autowired
    private CustomerAllViewService customerAllViewService;

    @Test
    public void test() {
        EnterCustDistOverView view = new EnterCustDistOverView();
        System.err.println(view);
        NewCustomerOverView nview=new NewCustomerOverView();
        System.err.println(nview);
    }

    // 1、企业客户区域分布
    @Test
    public void testEnterpriseCustomerDistribution() {
        EnterCustomerRequest request = new EnterCustomerRequest();

        List<String> provList = Arrays.asList("500000");
        //List<String> cityList = Arrays.asList("500102", "500235", "500231");
        List<String> cityList = Arrays.asList("500101", "500102", "500103", "500104", "500105", "500106", "500107", "500108", "500109", "500110", "500111", "500112", "500113", "500114", "500115", "500116", "500117", "500118", "500119", "500120", "500151", "500152", "500153", "500154", "500155", "500156", "500229", "500230", "500231", "500233", "500235", "500236", "500237", "500238", "500240", "500241", "500242", "500243");

        //List<String> provList = Arrays.asList("500000", "120000");
        //List<String> provList = new ArrayList<>();
        //List<String> cityList = new ArrayList<>();

        request.setProvince(provList);
        request.setCity(cityList);
        //request.setBusinessType("1");
        request.setStartDate("2022-09-01");
        request.setEndDate("2022-09-04");

        customerAllViewService.getEnterpriseCustomerDistribution(request);
    }

    // 2、新增企业客户统计
    @Test
    public void testNewCustomerStatistic() {
        EnterCustomerRequest request = new EnterCustomerRequest();

        List<String> provList = Arrays.asList("500000");
        List<String> cityList = Arrays.asList("500102", "500235", "500231");

        //List<String> provList = Arrays.asList("500000", "120000");
        //List<String> cityList = new ArrayList<>();

        request.setProvince(provList);
        request.setCity(cityList);
        request.setBusinessType("1");
        request.setStartDate("2022-07-21");
        request.setEndDate("2022-07-25");

        customerAllViewService.getNewCustomerStatistic(request);
    }

    // 3、企业存量客户统计
    @Test
    public void testCustomerStatistic() {
        EnterCustomerRequest request = new EnterCustomerRequest();

        //List<String> provList = Arrays.asList("500000");
        //List<String> cityList = Arrays.asList("500102", "500235", "500231");

        //List<String> provList = Arrays.asList("500000", "120000");
        //List<String> cityList = new ArrayList<>();

        List<String> provList = new ArrayList<>();
        List<String> cityList = new ArrayList<>();

        request.setProvince(provList);
        request.setCity(cityList);
        //request.setBusinessType("1");
        request.setStartDate("2021-09-06");
        request.setEndDate("2022-09-05");

        customerAllViewService.getCustomerStatistic(request);
    }

    // 4、企业行业新增客户统计
    @Test
    public void testNewIndustryCustomerStatistic() {
        EnterCustomerRequest request = new EnterCustomerRequest();

        //List<String> provList = Arrays.asList("500000");
        //List<String> cityList = Arrays.asList("500102", "500235", "500231");

        //List<String> provList = Arrays.asList("500000", "120000");
        List<String> provList = new ArrayList<>();
        List<String> cityList = new ArrayList<>();

        request.setProvince(provList);
        request.setCity(cityList);
        //request.setBusinessType("1");
        request.setStartDate("2022-07-01");
        request.setEndDate("2022-07-31");

        customerAllViewService.getNewIndustryCustomerStatistic(request);
    }

}
