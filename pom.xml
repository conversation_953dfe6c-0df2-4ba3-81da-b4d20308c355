<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.4.13</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<groupId>com.cmiot.report</groupId>
	<artifactId>report-parent</artifactId>
	<version>1.0.0</version>
	<packaging>pom</packaging>
	<modules>
		<module>report-facade</module>
		<module>report-dao</module>
		<module>report-service</module>
	</modules>

	<properties>
		<java.version>1.8</java.version>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.tartget>1.8</maven.compiler.tartget>
		<project.version>1.0.0</project.version>
		<spring.version>2.3.12.RELEASE</spring.version>
		<analysis.version>1.0.0</analysis.version>
		<clickhouse.version>0.3.1-patch</clickhouse.version>
		<log4j2.version>2.17.1</log4j2.version>
		<spring-cloud.version>2020.0.5</spring-cloud.version>
		<spring-cloud-alibaba.version>2.2.7.RELEASE</spring-cloud-alibaba.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>${spring-cloud-alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<profiles>
	<profile>
		<id>dev</id>
		<distributionManagement>
			<repository>
				<id>maven-releases</id>
				<name>releases</name>
				<url>http://10.12.4.13:8081/repository/maven-releases/</url>
			</repository>
			<snapshotRepository>
				<id>maven-snapshots</id>
				<name>snapshots</name>
				<url>http://10.12.4.13:8081/repository/maven-snapshots/</url>
			</snapshotRepository>
		</distributionManagement>
	</profile>
	</profiles>
</project>