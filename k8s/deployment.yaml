apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ge-report
    app.kubernetes.io/instance: ge-report
    app.kubernetes.io/name: ge-report
  name: ge-report
  namespace: default
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: ge-report
      app.kubernetes.io/instance: ge-report
      app.kubernetes.io/name: ge-report
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: ge-report
        app.kubernetes.io/instance: ge-report
        app.kubernetes.io/name: ge-report
    spec:
      containers:
        - env:
            - name: FORMAT_MESSAGES_PATTERN_DISABLE_LOOKUPS
              value: 'true'
          image: @{artifact}['download_url']
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /actuator/health
              port: 8081
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          name: ge-report
          ports:
            - containerPort: 8081
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /actuator/health
              port: 8081
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 1Gi
            requests:
              cpu: 500m
              memory: 1Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /opt/fgeo/logs/
              name: log-volume
            - mountPath: /opt/fgeo/conf/application.yaml
              name: conf-volume
              subPath: application.yaml
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: harborsecret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /opt/logs/zqzw/
            type: Directory
          name: log-volume
        - configMap:
            defaultMode: 420
            name: ge-report-configmap
          name: conf-volume