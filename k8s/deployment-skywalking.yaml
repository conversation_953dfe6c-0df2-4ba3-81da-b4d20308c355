apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ge-report
    app.kubernetes.io/instance: ge-report
    app.kubernetes.io/name: ge-report
  name: ge-report
  namespace: default
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: ge-report
      app.kubernetes.io/instance: ge-report
      app.kubernetes.io/name: ge-report
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: ge-report
        app.kubernetes.io/instance: ge-report
        app.kubernetes.io/name: ge-report
    spec:
      #构建初始化镜像(通过初始化镜像的方式集成SkyWalking Agent)
      initContainers:
        - image: hub.iot.chinamobile.com/apache/skywalking-java-agent:8.9.0-java8
          name: sw-agent-sidecar
          imagePullPolicy: IfNotPresent
          command: ["sh"]
          args:
            [
                "-c",
                "set -ex;cd /skywalking;mkdir -p /opt/skywalking/agent;cp -r /skywalking/agent/* /opt/skywalking/agent;",
            ]
          volumeMounts:
            - mountPath: /opt/skywalking/agent
              name: sw-agent
      containers:
        - env:
            - name: FORMAT_MESSAGES_PATTERN_DISABLE_LOOKUPS
              value: 'true'
            - name: JAVA_TOOL_OPTIONS
              value: -javaagent:/opt/skywalking/agent/skywalking-agent.jar
            - name: SW_AGENT_NAME
              value: ge-report
            - name: SW_AGENT_COLLECTOR_BACKEND_SERVICES
              value: 172.19.1.79:11800
          image: @{artifact}['download_url']
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /actuator/health
              port: 8081
              scheme: HTTP
            initialDelaySeconds: 180
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ge-report
          ports:
            - containerPort: 8081
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /actuator/health
              port: 8081
              scheme: HTTP
            initialDelaySeconds: 180
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 500m
              memory: 1Gi
            requests:
              cpu: 500m
              memory: 1Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /opt/fgeo/logs/
              name: log-volume
            - mountPath: /opt/fgeo/conf/application.yaml
              name: conf-volume
              subPath: application.yaml
            - mountPath: /opt/skywalking/agent
              name: sw-agent
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: harborsecret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /opt/logs/zqzw/
            type: Directory
          name: log-volume
        - configMap:
            defaultMode: 420
            name: ge-report-configmap
          name: conf-volume
        - name: sw-agent
          emptyDir: { }