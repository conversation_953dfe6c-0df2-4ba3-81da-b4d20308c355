apiVersion: v1
kind: ConfigMap
metadata:
  name: ge-report-configmap
  namespace: default
  labels:
    app: ge-report
data:
  application.yaml: |-
    server:
      port: 8081
    debug: false
    spring:
      application:
        name: ge-report
      datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        clickhouse:
          driverClassName: ru.yandex.clickhouse.ClickHouseDriver
          url: ********************************************
          userName: ck
          password: ck2022
          initialSize: 10
          maxActive: 100
          minIdle: 10
          maxWait: 6000
          validationQuery: SELECT 1
      redis:
        host: **********
        port: 6407
        password: kfzisgreatman100
        database: 0
    mybatis-plus:
      mapper-locations: classpath*:/mapper/*.xml,classpath*:/auto/*.xml

    # 导出临时文件目录
    # use-pri-url 1 使用内网地址，否则使用外网地址
    export:
      use-pri-url: 0
      tmp:
        local: /opt/fgeo
    network:
      threshold:
        floor: 10
        upper: 80

    # 网关&组网超时设备列表,超时时间(小时)
    device:
      list:
        mac-overtime: 72
        sub-overtime: 72

    #目标营销客户分数 (0, 100]
    customer:
      marketing:
        score: 80

