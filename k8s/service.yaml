apiVersion: v1
kind: Service
metadata:
  labels:
    app: ge-report
    app.kubernetes.io/instance: ge-report
    app.kubernetes.io/name: ge-report
  name: ge-report
  namespace: default
spec:
  ports:
    - name: http
      port: 8081
      protocol: TCP
      targetPort: 8081
  selector:
    app: ge-report
    app.kubernetes.io/instance: ge-report
    app.kubernetes.io/name: ge-report
  sessionAffinity: None
  type: ClusterIP